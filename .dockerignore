# 忽略node_modules
node_modules/
**/node_modules/

# 忽略构建输出（但保留dist目录，因为需要复制）
!dist/
build/
coverage/

# 忽略日志文件
*.log
logs/
test-logs/

# 忽略测试结果
test-results/

# 忽略examples目录
examples/
**/examples/
**/examples/**/*
libs/common/src/redis/examples/
apps/*/examples/

# 忽略测试文件
**/*.spec.ts
**/*.test.ts
**/*.e2e-spec.ts
test/
tests/

# 忽略脚本文件
scripts/
**/scripts/

# 忽略文档
docs/
docs/README.md
*.md

# 忽略IDE配置
.vscode/
.idea/
*.swp
*.swo

# 忽略环境文件
.env*
!.env.example

# 忽略Git相关
.git/
.gitignore
.gitattributes

# 忽略Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 忽略临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# 忽略包管理器文件（但保留package-lock.json用于Docker构建）
!package-lock.json
yarn.lock
pnpm-lock.yaml

# 忽略ESLint和其他配置
.eslintrc*
.eslintignore
.jshintignore
.prettierrc*
.editorconfig

# 忽略TypeScript配置
tsconfig*.json
!tsconfig.json
!apps/*/tsconfig*.json
!libs/*/tsconfig*.json
