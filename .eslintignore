# 忽略构建输出
dist/
build/
coverage/

# 忽略依赖
node_modules/

# 忽略所有examples目录
examples/
**/examples/
**/examples/**/*

# 忽略特定的examples目录
libs/common/src/redis/examples/
apps/gateway/examples/
apps/auth/examples/
apps/*/examples/

# 忽略测试文件
**/*.spec.ts
**/*.test.ts
**/*.e2e-spec.ts
test/
tests/

# 忽略脚本文件
scripts/
**/scripts/

# 忽略文档
docs/

# 忽略示例和临时文件
**/*.example.ts
**/*.example.js
**/*.tmp
**/*.temp

# 忽略日志文件
*.log
logs/

# 忽略IDE配置
.vscode/
.idea/

# 忽略环境文件
.env*
!.env.example
