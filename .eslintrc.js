module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin'],
  extends: [
    '@nestjs/eslint-config-nestjs',
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: [
    '.eslintrc.js',
    'dist/',
    'node_modules/',
    'coverage/',
    // 忽略所有examples目录
    'examples/',
    '**/examples/',
    '**/examples/**/*',
    'libs/common/src/redis/examples/',
    'apps/gateway/examples/',
    'apps/auth/examples/',
    'apps/*/examples/',
    // 忽略测试文件
    '**/*.spec.ts',
    '**/*.test.ts',
    '**/*.e2e-spec.ts',
    'test/',
    'tests/',
    // 忽略脚本文件
    'scripts/',
    '**/scripts/',
    // 忽略文档
    'docs/',
    // 忽略示例文件
    '**/*.example.ts',
    '**/*.example.js',
  ],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    // 解决IDE警告："本地捕获异常的 'throw'"
    'no-throw-literal': 'off',
    '@typescript-eslint/no-throw-literal': 'off',
    // 允许在try-catch块中抛出异常，这是NestJS异常处理的标准模式
    '@typescript-eslint/only-throw-error': 'off',
    'prettier/prettier': [
      'error',
      {
        endOfLine: 'auto',
      },
    ],
  },
  // 为examples目录设置特殊规则
  overrides: [
    {
      files: [
        'examples/**/*',
        '**/examples/**/*',
        'libs/common/src/redis/examples/**/*',
        'apps/*/examples/**/*',
      ],
      rules: {
        // 在examples目录中禁用所有规则
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-unused-vars': 'off',
        '@typescript-eslint/no-empty-function': 'off',
        '@typescript-eslint/no-inferrable-types': 'off',
        '@typescript-eslint/ban-types': 'off',
        'prefer-const': 'off',
        'no-var': 'off',
        'no-console': 'off',
        'no-debugger': 'off',
        'no-unused-expressions': 'off',
        'no-unreachable': 'off',
        'no-undef': 'off',
      },
    },
  ],
};
