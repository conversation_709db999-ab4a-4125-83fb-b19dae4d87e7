{
  // TypeScript 配置
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "typescript.suggest.autoImports": false,
  "typescript.validate.enable": true,
  
  // ESLint 配置
  "eslint.enable": true,
  "eslint.workingDirectories": [
    "apps/gateway",
    "apps/auth",
    "libs/common",
    "libs/shared"
  ],
  
  // 文件排除配置 - 隐藏examples目录
  "files.exclude": {
    "**/examples": true,
    "**/examples/**": true,
    "libs/common/src/redis/examples": true,
    "apps/*/examples": true,
    "**/node_modules": true,
    "**/dist": true,
    "**/coverage": true,
    "**/*.log": true
  },
  
  // 搜索排除配置
  "search.exclude": {
    "**/examples": true,
    "**/examples/**": true,
    "libs/common/src/redis/examples": true,
    "apps/*/examples": true,
    "**/node_modules": true,
    "**/dist": true,
    "**/coverage": true,
    "**/*.log": true
  },
  
  // TypeScript 错误排除
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "typescript.suggest.includeCompletionsForModuleExports": false,
  
  // 监视文件排除
  "files.watcherExclude": {
    "**/examples/**": true,
    "libs/common/src/redis/examples/**": true,
    "apps/*/examples/**": true,
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/coverage/**": true
  },
  
  // 语言特定设置
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": true
    }
  },
  
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  
  // 问题匹配器排除
  "problems.decorations.enabled": true,
  
  // 自动保存
  "files.autoSave": "onFocusChange",
  
  // 编辑器配置
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  
  // Git 配置
  "git.ignoreLimitWarning": true,
  
  // 终端配置
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  
  // 扩展配置
  "extensions.ignoreRecommendations": false
}
