/**
 * 荣誉墙服务
 * 基于old项目honorWall.js业务逻辑迁移
 */

import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@app/game-constants';
import { HonorRepository } from '@activity/common/repositories/honor.repository';
import { HonorTaskStatus } from '@activity/common/schemas/honor.schema';

@Injectable()
export class HonorService {
  private readonly logger = new Logger(HonorService.name);

  constructor(
    private readonly honorRepository: HonorRepository,
  ) {}

  /**
   * 获取荣誉墙信息
   */
  async getHonorInfo(uid: string, serverId: string): Promise<any> {
    try {
      const honor = await this.honorRepository.getOrCreateHonor(uid, serverId);
      
      return {
        uid,
        currentLevel: honor.currentLevel,
        totalExperience: honor.totalExperience,
        honorTasks: honor.honorTasks,
        totalTasksCompleted: honor.totalTasksCompleted,
        totalRewardsClaimed: honor.totalRewardsClaimed,
        specialAchievements: honor.specialAchievements,
        levelHistory: honor.levelHistory,
        availableRewards: honor.honorTasks.filter(task => task.status === HonorTaskStatus.COMPLETED),
      };
    } catch (error) {
      this.logger.error('获取荣誉墙信息失败', error);
      throw error;
    }
  }

  /**
   * 领取荣誉任务奖励
   * 对应old项目中的getHonorTaskReward方法 - 这是缺失的核心功能
   */
  async getHonorTaskReward(uid: string, serverId: string, taskId: number): Promise<any> {
    try {
      const honor = await this.honorRepository.getOrCreateHonor(uid, serverId);
      
      // 检查任务是否存在
      const task = honor.getHonorTask(taskId);
      if (!task) {
        throw new NotFoundException({
          code: ErrorCode.HONOR_TASK_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HONOR_TASK_NOT_FOUND],
        });
      }

      // 检查任务是否可以领取奖励
      if (!honor.canClaimReward(taskId)) {
        throw new BadRequestException({
          code: ErrorCode.HONOR_TASK_NOT_COMPLETED,
          message: ErrorMessages[ErrorCode.HONOR_TASK_NOT_COMPLETED],
        });
      }

      // 领取奖励
      const success = honor.claimTaskReward(taskId);
      if (!success) {
        throw new BadRequestException({
          code: ErrorCode.HONOR_REWARD_CLAIM_FAILED,
          message: ErrorMessages[ErrorCode.HONOR_REWARD_CLAIM_FAILED],
        });
      }

      // 获取奖励经验
      const rewardExp = this.getTaskRewardExperience(taskId);
      const leveledUp = honor.addExperience(rewardExp);

      await honor.save();

      // TODO: 发放奖励到玩家背包

      this.logger.log(`荣誉任务奖励领取成功: ${uid}, 任务: ${taskId}, 经验: ${rewardExp}`);

      return {
        success: true,
        taskId,
        rewards: task.rewards,
        experienceGained: rewardExp,
        leveledUp,
        newLevel: leveledUp ? honor.currentLevel : undefined,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('领取荣誉任务奖励失败', error);
      throw error;
    }
  }

  /**
   * 更新荣誉任务进度
   * 对应old项目中的updateHonorTaskProgress方法
   */
  async updateHonorTaskProgress(uid: string, serverId: string, taskId: number, progress: number): Promise<any> {
    try {
      const honor = await this.honorRepository.getOrCreateHonor(uid, serverId);
      
      // 更新任务进度
      const success = honor.updateTaskProgress(taskId, progress);
      if (!success) {
        this.logger.warn(`荣誉任务进度更新失败: ${uid}, 任务: ${taskId}`);
        return { success: false };
      }

      await honor.save();

      const task = honor.getHonorTask(taskId);
      const isCompleted = task && task.status === HonorTaskStatus.COMPLETED;

      this.logger.log(`荣誉任务进度更新: ${uid}, 任务: ${taskId}, 进度: ${progress}, 完成: ${isCompleted}`);

      return {
        success: true,
        taskId,
        currentProgress: task?.currentProgress,
        targetProgress: task?.targetProgress,
        isCompleted,
        canClaimReward: isCompleted,
      };
    } catch (error) {
      this.logger.error('更新荣誉任务进度失败', error);
      throw error;
    }
  }

  /**
   * 添加荣誉任务
   */
  async addHonorTask(uid: string, serverId: string, taskConfig: any): Promise<any> {
    try {
      const honor = await this.honorRepository.getOrCreateHonor(uid, serverId);
      
      // 检查任务是否已存在
      const existingTask = honor.getHonorTask(taskConfig.taskId);
      if (existingTask) {
        throw new BadRequestException({
          code: ErrorCode.HONOR_TASK_ALREADY_EXISTS,
          message: ErrorMessages[ErrorCode.HONOR_TASK_ALREADY_EXISTS],
        });
      }

      // 添加新任务
      const newTask = {
        taskId: taskConfig.taskId,
        taskType: taskConfig.taskType,
        currentProgress: 0,
        targetProgress: taskConfig.targetProgress,
        status: HonorTaskStatus.IN_PROGRESS,
        startTime: Date.now(),
        completeTime: 0,
        claimTime: 0,
        rewards: taskConfig.rewards,
      };

      honor.honorTasks.push(newTask);
      await honor.save();

      this.logger.log(`荣誉任务添加成功: ${uid}, 任务: ${taskConfig.taskId}`);

      return {
        success: true,
        task: newTask,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('添加荣誉任务失败', error);
      throw error;
    }
  }

  /**
   * 获取荣誉等级奖励
   */
  async getHonorLevelReward(uid: string, serverId: string, level: number): Promise<any> {
    try {
      const honor = await this.honorRepository.getOrCreateHonor(uid, serverId);
      
      // 检查等级是否有效
      if (level > honor.currentLevel) {
        throw new BadRequestException({
          code: ErrorCode.HONOR_LEVEL_NOT_REACHED,
          message: ErrorMessages[ErrorCode.HONOR_LEVEL_NOT_REACHED],
        });
      }

      // 检查等级奖励是否已领取
      const levelRecord = honor.levelHistory.find(record => record.level === level);
      if (!levelRecord) {
        throw new NotFoundException({
          code: ErrorCode.HONOR_LEVEL_RECORD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HONOR_LEVEL_RECORD_NOT_FOUND],
        });
      }

      // TODO: 检查奖励是否已领取
      // TODO: 发放等级奖励

      this.logger.log(`荣誉等级奖励领取: ${uid}, 等级: ${level}`);

      return {
        success: true,
        level,
        rewards: levelRecord.levelRewards,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('获取荣誉等级奖励失败', error);
      throw error;
    }
  }

  /**
   * 触发荣誉任务检查
   * 对应old项目中的triggerHonorTask方法
   */
  async triggerHonorTask(uid: string, serverId: string, triggerType: number, param?: any): Promise<any> {
    try {
      const honor = await this.honorRepository.getOrCreateHonor(uid, serverId);
      let updatedTasks = 0;

      // 遍历所有进行中的荣誉任务
      for (const task of honor.honorTasks) {
        if (task.status !== HonorTaskStatus.IN_PROGRESS) continue;
        
        // 检查任务类型是否匹配
        if (task.taskType === triggerType) {
          // 根据任务类型更新进度
          const progressIncrement = this.calculateProgressIncrement(triggerType, param);
          const newProgress = task.currentProgress + progressIncrement;
          
          if (honor.updateTaskProgress(task.taskId, newProgress)) {
            updatedTasks++;
          }
        }
      }

      if (updatedTasks > 0) {
        await honor.save();
        this.logger.log(`荣誉任务触发: ${uid}, 类型: ${triggerType}, 更新任务数: ${updatedTasks}`);
      }

      return {
        success: updatedTasks > 0,
        triggerType,
        updatedTasksCount: updatedTasks,
        param,
      };
    } catch (error) {
      this.logger.error('触发荣誉任务失败', error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 获取任务奖励经验
   */
  private getTaskRewardExperience(taskId: number): number {
    // TODO: 从配置表获取
    return 100; // 默认100经验
  }

  /**
   * 计算进度增量
   */
  private calculateProgressIncrement(triggerType: number, param?: any): number {
    // TODO: 根据触发类型和参数计算进度增量
    switch (triggerType) {
      case 1: // 完成比赛
        return 1;
      case 2: // 获得胜利
        return 1;
      case 3: // 签约球员
        return 1;
      case 4: // 升级球员
        return param?.levels || 1;
      case 5: // 获得金币
        return param?.amount || 1;
      default:
        return 1;
    }
  }
}
