# 认证服务 API 文档

## 📋 API 概览

认证服务提供完整的用户认证、授权和管理功能。所有API遵循RESTful设计原则，使用JSON格式进行数据交换。

## 🔐 认证方式

### Bearer Token
```http
Authorization: Bearer <jwt_token>
```

### API Key
```http
X-API-Key: <api_key>
```

## 📊 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2023-12-01T10:00:00.000Z",
  "requestId": "req_123456789"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "AUTH_001",
    "message": "认证失败",
    "details": "用户名或密码错误"
  },
  "timestamp": "2023-12-01T10:00:00.000Z",
  "requestId": "req_123456789"
}
```

## 🚪 认证端点

### 用户注册

**POST** `/auth/register`

注册新用户账户。

**请求体:**
```json
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "profile": {
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "country": "US",
    "language": "en"
  },
  "acceptTerms": true,
  "marketingConsent": false
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123456",
      "username": "john_doe",
      "email": "<EMAIL>",
      "status": "active",
      "emailVerified": false
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJSUzI1NiIs...",
      "refreshToken": "rt_abcdef123456",
      "tokenType": "Bearer",
      "expiresIn": 3600,
      "expiresAt": "2023-12-01T11:00:00.000Z"
    }
  },
  "message": "注册成功"
}
```

### 用户登录

**POST** `/auth/login`

用户身份验证登录。

**请求体:**
```json
{
  "identifier": "john_doe",
  "password": "SecurePass123!",
  "mfaCode": "123456",
  "rememberMe": true,
  "deviceInfo": {
    "type": "web",
    "name": "Chrome on Windows",
    "fingerprint": "fp_abcdef123456",
    "userAgent": "Mozilla/5.0...",
    "ipAddress": "*************"
  }
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123456",
      "username": "john_doe",
      "email": "<EMAIL>",
      "roles": ["user", "player"],
      "permissions": ["game:play", "profile:read"],
      "profile": {
        "firstName": "John",
        "lastName": "Doe",
        "avatar": "https://cdn.example.com/avatars/123456.jpg"
      },
      "gameProfile": {
        "level": 15,
        "experience": 12500,
        "coins": 5000,
        "premiumUntil": "2024-01-01T00:00:00.000Z"
      }
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJSUzI1NiIs...",
      "refreshToken": "rt_abcdef123456",
      "tokenType": "Bearer",
      "expiresIn": 3600,
      "expiresAt": "2023-12-01T11:00:00.000Z"
    },
    "session": {
      "id": "session_789012",
      "deviceId": "device_345678",
      "lastActivity": "2023-12-01T10:00:00.000Z"
    }
  },
  "message": "登录成功"
}
```

### 令牌刷新

**POST** `/auth/refresh`

刷新访问令牌。

**请求体:**
```json
{
  "refreshToken": "rt_abcdef123456"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "tokens": {
      "accessToken": "eyJhbGciOiJSUzI1NiIs...",
      "refreshToken": "rt_newtoken789012",
      "tokenType": "Bearer",
      "expiresIn": 3600,
      "expiresAt": "2023-12-01T11:00:00.000Z"
    }
  },
  "message": "令牌刷新成功"
}
```

### 用户登出

**POST** `/auth/logout`

用户登出，撤销令牌。

**请求体:**
```json
{
  "refreshToken": "rt_abcdef123456",
  "allDevices": false
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "loggedOut": true,
    "devicesLoggedOut": 1
  },
  "message": "登出成功"
}
```

### 密码重置

**POST** `/auth/reset-password`

发起密码重置流程。

**请求体:**
```json
{
  "email": "<EMAIL>"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "resetTokenSent": true,
    "expiresIn": 3600
  },
  "message": "密码重置邮件已发送"
}
```

### 确认密码重置

**POST** `/auth/confirm-reset`

使用重置令牌设置新密码。

**请求体:**
```json
{
  "token": "reset_token_123456",
  "newPassword": "NewSecurePass456!"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "passwordReset": true
  },
  "message": "密码重置成功"
}
```

## 👤 用户管理端点

### 获取当前用户信息

**GET** `/users/me`

获取当前认证用户的详细信息。

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123456",
      "username": "john_doe",
      "email": "<EMAIL>",
      "profile": {
        "firstName": "John",
        "lastName": "Doe",
        "avatar": "https://cdn.example.com/avatars/123456.jpg",
        "dateOfBirth": "1990-01-01",
        "country": "US",
        "timezone": "America/New_York",
        "language": "en"
      },
      "gameProfile": {
        "level": 15,
        "experience": 12500,
        "coins": 5000,
        "premiumUntil": "2024-01-01T00:00:00.000Z",
        "achievements": ["first_goal", "champion"],
        "preferences": {
          "notifications": true,
          "autoSave": true
        }
      },
      "security": {
        "mfaEnabled": true,
        "trustedDevices": ["device_123", "device_456"],
        "lastPasswordChange": "2023-11-01T10:00:00.000Z"
      },
      "status": "active",
      "emailVerified": true,
      "phoneVerified": false,
      "roles": ["user", "player"],
      "permissions": ["game:play", "profile:read", "profile:write"],
      "createdAt": "2023-01-01T10:00:00.000Z",
      "lastLoginAt": "2023-12-01T10:00:00.000Z"
    }
  }
}
```

### 更新用户信息

**PUT** `/users/me`

更新当前用户的个人信息。

**请求体:**
```json
{
  "profile": {
    "firstName": "John",
    "lastName": "Smith",
    "avatar": "https://cdn.example.com/avatars/new_123456.jpg",
    "country": "CA",
    "timezone": "America/Toronto",
    "language": "en"
  },
  "gameProfile": {
    "preferences": {
      "notifications": false,
      "autoSave": true,
      "theme": "dark"
    }
  }
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123456",
      "profile": {
        "firstName": "John",
        "lastName": "Smith",
        "country": "CA",
        "timezone": "America/Toronto"
      },
      "updatedAt": "2023-12-01T10:30:00.000Z"
    }
  },
  "message": "用户信息更新成功"
}
```

### 修改密码

**PUT** `/users/me/password`

修改当前用户密码。

**请求体:**
```json
{
  "currentPassword": "SecurePass123!",
  "newPassword": "NewSecurePass456!",
  "mfaCode": "123456"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "passwordChanged": true,
    "tokensRevoked": true
  },
  "message": "密码修改成功"
}
```

## 🔒 多因子认证端点

### 启用MFA

**POST** `/users/me/mfa/enable`

为当前用户启用多因子认证。

**请求体:**
```json
{
  "method": "totp",
  "secret": "JBSWY3DPEHPK3PXP",
  "code": "123456"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "mfaEnabled": true,
    "backupCodes": [
      "12345678",
      "87654321",
      "11223344"
    ],
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  },
  "message": "MFA启用成功"
}
```

### 禁用MFA

**POST** `/users/me/mfa/disable`

禁用当前用户的多因子认证。

**请求体:**
```json
{
  "password": "SecurePass123!",
  "code": "123456"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "mfaDisabled": true
  },
  "message": "MFA已禁用"
}
```

## 📱 设备管理端点

### 获取设备列表

**GET** `/users/me/devices`

获取当前用户的所有登录设备。

**响应:**
```json
{
  "success": true,
  "data": {
    "devices": [
      {
        "id": "device_123456",
        "name": "Chrome on Windows",
        "type": "web",
        "fingerprint": "fp_abcdef123456",
        "trusted": true,
        "current": true,
        "lastActivity": "2023-12-01T10:00:00.000Z",
        "location": {
          "country": "US",
          "city": "New York",
          "ip": "*************"
        },
        "createdAt": "2023-11-01T10:00:00.000Z"
      }
    ],
    "total": 1
  }
}
```

### 撤销设备

**DELETE** `/users/me/devices/:deviceId`

撤销指定设备的访问权限。

**响应:**
```json
{
  "success": true,
  "data": {
    "deviceRevoked": true,
    "sessionsTerminated": 2
  },
  "message": "设备访问已撤销"
}
```

## 🛡️ 管理员端点

### 管理员仪表板

**GET** `/admin/dashboard`

获取管理员仪表板数据（需要管理员权限）。

**响应:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalUsers": 1250,
      "activeUsers": 980,
      "newUsersToday": 25,
      "activeSessions": 156,
      "securityAlerts": 3,
      "systemHealth": "healthy"
    },
    "userMetrics": {
      "totalUsers": 1250,
      "activeUsers": 980,
      "inactiveUsers": 270,
      "newUsersToday": 25,
      "newUsersThisWeek": 180,
      "newUsersThisMonth": 750,
      "userGrowthRate": 12.5
    },
    "sessionMetrics": {
      "activeSessions": 156,
      "totalSessions": 2340,
      "averageSessionDuration": 1800,
      "sessionsToday": 340,
      "deviceBreakdown": {
        "web": 60,
        "mobile": 30,
        "desktop": 10
      }
    },
    "securityMetrics": {
      "totalAlerts": 3,
      "criticalAlerts": 0,
      "resolvedAlerts": 15,
      "failedLoginAttempts": 12,
      "blockedIPs": 2,
      "mfaAdoptionRate": 68.5
    },
    "systemMetrics": {
      "cpuUsage": 45.2,
      "memoryUsage": 67.8,
      "diskUsage": 23.4,
      "networkTraffic": 12.5,
      "responseTime": 120,
      "uptime": 99.9
    },
    "recentActivities": [
      {
        "id": "activity_123",
        "type": "user_login",
        "user": {
          "username": "john_doe",
          "email": "<EMAIL>"
        },
        "description": "用户登录",
        "timestamp": "2023-12-01T10:00:00.000Z",
        "ipAddress": "*************"
      }
    ],
    "alerts": [
      {
        "id": "alert_123",
        "type": "performance",
        "severity": "warning",
        "message": "CPU使用率较高",
        "timestamp": "2023-12-01T10:00:00.000Z"
      }
    ]
  },
  "message": "获取仪表板数据成功"
}
```

### 系统状态

**GET** `/admin/system/status`

获取系统状态信息。

**响应:**
```json
{
  "success": true,
  "data": {
    "overallStatus": "healthy",
    "uptime": 99.9,
    "startTime": "2023-11-01T00:00:00.000Z",
    "currentTime": "2023-12-01T10:00:00.000Z",
    "database": {
      "status": "healthy",
      "connections": 5,
      "responseTime": 25,
      "size": 1024,
      "lastCheck": "2023-12-01T10:00:00.000Z"
    },
    "redis": {
      "status": "healthy",
      "usedMemory": 64,
      "connectedClients": 5,
      "hitRate": 95.5,
      "lastCheck": "2023-12-01T10:00:00.000Z"
    },
    "services": [
      {
        "name": "auth-service",
        "status": "running",
        "version": "1.0.0",
        "startTime": "2023-11-01T00:00:00.000Z",
        "uptime": 2592000,
        "cpuUsage": 0.5,
        "memoryUsage": 256
      }
    ],
    "resources": {
      "cpuUsage": 45.2,
      "memoryUsage": 67.8,
      "availableMemory": 2048,
      "totalMemory": 8192,
      "diskUsage": 23.4,
      "loadAverage": [1.2, 1.1, 1.0]
    },
    "performance": {
      "averageResponseTime": 120,
      "requestsPerSecond": 50,
      "errorRate": 0.5,
      "throughput": 10.5,
      "concurrentUsers": 25,
      "activeConnections": 30
    },
    "security": {
      "status": "secure",
      "securityEventsToday": 3,
      "blockedIPs": 2,
      "failedLoginAttempts": 5,
      "suspiciousActivities": 1,
      "sslStatus": "valid"
    },
    "backup": {
      "lastBackup": "2023-11-30T02:00:00.000Z",
      "status": "completed",
      "backupSize": 256,
      "nextScheduledBackup": "2023-12-02T02:00:00.000Z",
      "retentionDays": 30,
      "availableBackups": 15
    }
  },
  "message": "获取系统状态成功"
}
```

### 用户管理

**GET** `/admin/users`

获取用户列表（需要管理员权限）。

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `search`: 搜索关键词
- `status`: 用户状态过滤
- `role`: 角色过滤
- `sortBy`: 排序字段 (默认: createdAt)
- `sortOrder`: 排序方向 (asc/desc, 默认: desc)

**响应:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user_123456",
        "username": "john_doe",
        "email": "<EMAIL>",
        "status": "active",
        "roles": ["user", "player"],
        "lastLoginAt": "2023-12-01T10:00:00.000Z",
        "createdAt": "2023-01-01T10:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "pages": 8
    }
  }
}
```

**PUT** `/admin/users/:id/status`

更新用户状态。

**请求体:**
```json
{
  "status": "suspended",
  "reason": "违反社区规则"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123456",
      "status": "suspended",
      "updatedAt": "2023-12-01T10:00:00.000Z"
    }
  },
  "message": "用户状态更新成功"
}
```

**PUT** `/admin/users/:id/roles`

分配用户角色。

**请求体:**
```json
{
  "roleIds": ["role_123", "role_456"]
}
```

**POST** `/admin/users/:id/reset-password`

重置用户密码。

**请求体:**
```json
{
  "newPassword": "NewPassword123!",
  "forceChange": true
}
```

**POST** `/admin/users/:id/lock`

锁定用户账户。

**请求体:**
```json
{
  "reason": "安全原因",
  "duration": 86400
}
```

**DELETE** `/admin/users/:id/lock`

解锁用户账户。

**DELETE** `/admin/users/:id/mfa`

禁用用户MFA。

**请求体:**
```json
{
  "reason": "用户请求"
}
```

**GET** `/admin/users/:id/sessions`

获取用户会话列表。

**DELETE** `/admin/users/:id/sessions/:sessionId`

终止用户会话。

**POST** `/admin/users/bulk`

批量操作用户。

**请求体:**
```json
{
  "userIds": ["user_123", "user_456"],
  "operation": {
    "type": "status",
    "value": "suspended",
    "reason": "批量处理"
  }
}
```

### 系统维护

**POST** `/admin/actions/maintenance`

执行系统维护操作。

**请求体:**
```json
{
  "action": "clear_cache",
  "description": "清理系统缓存以提高性能",
  "parameters": {
    "cacheType": "all",
    "force": true
  },
  "targets": ["auth-service"],
  "force": false
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "action": "clear_cache",
    "status": "completed",
    "result": {
      "cleared": true,
      "cacheType": "all"
    },
    "timestamp": "2023-12-01T10:00:00.000Z"
  },
  "message": "系统维护执行成功"
}
```

**POST** `/admin/actions/backup`

创建系统备份。

**响应:**
```json
{
  "success": true,
  "data": {
    "id": "backup_1701421200000",
    "path": "/backups/backup_1701421200000",
    "size": 0,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "type": "full",
    "status": "completed"
  },
  "message": "系统备份创建成功"
}
```

### 系统日志

**GET** `/admin/logs/system`

获取系统日志。

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `level`: 日志级别 (info/warn/error)
- `startDate`: 开始日期
- `endDate`: 结束日期

**响应:**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log_123",
        "level": "info",
        "message": "系统日志消息",
        "timestamp": "2023-12-01T10:00:00.000Z",
        "service": "auth-service",
        "context": "SystemService"
      }
    ],
    "total": 1000,
    "page": 1,
    "limit": 20,
    "hasMore": true
  },
  "message": "获取系统日志成功"
}
```

### 系统设置

**GET** `/admin/settings`

获取系统设置。

**响应:**
```json
{
  "success": true,
  "data": {
    "general": {
      "siteName": "Football Manager",
      "siteUrl": "http://localhost:3001",
      "timezone": "Asia/Shanghai",
      "language": "zh-CN"
    },
    "security": {
      "sessionTimeout": 1800,
      "maxLoginAttempts": 5,
      "passwordPolicy": {
        "minLength": 8,
        "requireUppercase": true,
        "requireNumbers": true,
        "requireSpecialChars": true
      }
    },
    "performance": {
      "cacheEnabled": true,
      "cacheTtl": 3600,
      "rateLimitEnabled": true,
      "rateLimitMax": 100
    },
    "monitoring": {
      "metricsEnabled": true,
      "loggingLevel": "info",
      "auditEnabled": true
    }
  },
  "message": "获取系统设置成功"
}
```

**PUT** `/admin/settings`

更新系统设置。

**请求体:**
```json
{
  "security": {
    "sessionTimeout": 3600,
    "maxLoginAttempts": 3
  },
  "performance": {
    "rateLimitMax": 200
  }
}
```

### 统计分析

**GET** `/admin/statistics/overview`

获取统计概览。

**查询参数:**
- `timeRange`: 时间范围 (1d/7d/30d/90d/1y, 默认: 30d)

**响应:**
```json
{
  "success": true,
  "data": {
    "users": {
      "total": 1250,
      "active": 980,
      "new": 25,
      "growthRate": 12.5
    },
    "sessions": {
      "total": 2340,
      "active": 156,
      "averageDuration": 1800
    },
    "security": {
      "events": 15,
      "alerts": 3,
      "mfaAdoption": 68.5
    },
    "performance": {
      "responseTime": 120,
      "uptime": 99.9,
      "errorRate": 0.5
    }
  },
  "message": "获取统计概览成功"
}
```

**GET** `/admin/statistics/users`

获取用户统计数据。

**GET** `/admin/statistics/sessions`

获取会话统计数据。

**GET** `/admin/statistics/security`

获取安全统计数据。

**GET** `/admin/statistics/performance`

获取性能统计数据。

### 安全管理

**GET** `/admin/security/alerts`

获取安全警告。

**响应:**
```json
{
  "success": true,
  "data": {
    "alerts": [
      {
        "id": "alert_123",
        "type": "security",
        "severity": "high",
        "title": "异常登录活动",
        "message": "检测到来自新地理位置的登录尝试",
        "timestamp": "2023-12-01T10:00:00.000Z",
        "resolved": false,
        "userId": "user_123",
        "ipAddress": "*************"
      }
    ],
    "total": 5,
    "unresolved": 2
  },
  "message": "获取安全警告成功"
}
```

**GET** `/admin/security/events`

获取安全事件。

**GET** `/admin/security/blocked-ips`

获取被阻止的IP列表。

### 审计管理

**GET** `/admin/audit/logs`

获取审计日志。

**查询参数:**
- `page`: 页码
- `limit`: 每页数量
- `type`: 事件类型
- `userId`: 用户ID
- `startDate`: 开始日期
- `endDate`: 结束日期

**响应:**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "audit_123",
        "type": "user_login",
        "userId": "user_123",
        "targetUserId": null,
        "details": {
          "ipAddress": "*************",
          "userAgent": "Mozilla/5.0...",
          "success": true
        },
        "ipAddress": "*************",
        "userAgent": "Mozilla/5.0...",
        "timestamp": "2023-12-01T10:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 500,
      "pages": 25
    }
  },
  "message": "获取审计日志成功"
}
```

### 管理员通知

**GET** `/admin/notifications`

获取管理员通知。

**响应:**
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "notif_123",
        "type": "security",
        "title": "安全警告",
        "message": "检测到异常登录活动",
        "severity": "high",
        "read": false,
        "createdAt": "2023-12-01T10:00:00.000Z"
      }
    ],
    "unreadCount": 2
  },
  "message": "获取管理员通知成功"
}
```

**POST** `/admin/notifications/:id/read`

标记通知为已读。

### 角色管理

**GET** `/admin/roles`

获取系统角色列表。

**响应:**
```json
{
  "success": true,
  "data": {
    "roles": [
      {
        "id": "role_123456",
        "name": "player",
        "displayName": "球员",
        "description": "普通游戏玩家角色",
        "permissions": [
          {
            "id": "perm_123",
            "name": "game:play",
            "resource": "game",
            "action": "play"
          }
        ],
        "userCount": 1250,
        "system": true,
        "createdAt": "2023-01-01T10:00:00.000Z"
      }
    ],
    "total": 5
  }
}
```

## 📊 错误代码

| 错误代码 | HTTP状态 | 描述 |
|---------|---------|------|
| AUTH_001 | 401 | 认证失败 |
| AUTH_002 | 401 | 令牌无效或过期 |
| AUTH_003 | 403 | 权限不足 |
| AUTH_004 | 429 | 请求过于频繁 |
| USER_001 | 400 | 用户名已存在 |
| USER_002 | 400 | 邮箱已存在 |
| USER_003 | 404 | 用户不存在 |
| USER_004 | 400 | 密码不符合要求 |
| MFA_001 | 400 | MFA验证码错误 |
| MFA_002 | 400 | MFA未启用 |
| DEVICE_001 | 400 | 设备不受信任 |
| DEVICE_002 | 404 | 设备不存在 |
| ADMIN_001 | 403 | 管理员权限不足 |
| ADMIN_002 | 400 | 管理员操作参数错误 |
| ADMIN_003 | 409 | 管理员操作冲突 |
| ADMIN_004 | 500 | 系统维护操作失败 |
| SYSTEM_001 | 500 | 系统状态检查失败 |
| SYSTEM_002 | 503 | 系统服务不可用 |
| SYSTEM_003 | 500 | 备份操作失败 |
| AUDIT_001 | 400 | 审计日志查询参数错误 |
| AUDIT_002 | 404 | 审计记录不存在 |
| SECURITY_001 | 400 | 安全事件查询参数错误 |
| SECURITY_002 | 404 | 安全事件不存在 |
| STATS_001 | 400 | 统计查询参数错误 |
| STATS_002 | 500 | 统计数据生成失败 |

## 🔄 状态码说明

- **200**: 请求成功
- **201**: 资源创建成功
- **400**: 请求参数错误
- **401**: 未认证或认证失败
- **403**: 权限不足
- **404**: 资源不存在
- **409**: 资源冲突
- **429**: 请求过于频繁
- **500**: 服务器内部错误

这个API文档提供了认证服务的完整接口说明，包括请求格式、响应格式和错误处理。
