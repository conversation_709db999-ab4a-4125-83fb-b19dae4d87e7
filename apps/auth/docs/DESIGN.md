# 认证服务设计文档

## 🎯 服务概述

认证服务是足球经理游戏的核心安全组件，负责用户身份验证、授权管理、会话控制和安全审计。采用微服务架构，提供高性能、高可用的认证解决方案。

## 🏗️ 架构设计

### 核心架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        认证服务架构                              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │  认证控制器  │  │  授权控制器  │  │  用户控制器  │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │  认证服务    │  │  授权服务    │  │  会话服务    │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │  用户仓储    │  │  角色仓储    │  │  权限仓储    │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   MongoDB   │  │    Redis    │  │   消息队列   │              │
│  │  (用户数据)  │  │ (会话缓存)   │  │  (事件通知)  │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
```

## 🔐 核心功能

### 1. 用户认证

#### 多种认证方式
- **密码认证**: 传统用户名/密码登录
- **JWT令牌**: 无状态令牌认证
- **OAuth 2.0**: 第三方社交登录（Google、Facebook、微信等）
- **多因子认证 (MFA)**: TOTP、SMS、邮件验证
- **生物识别**: 指纹、面部识别（移动端）
- **API密钥**: 服务间认证

#### 安全特性
- **密码策略**: 复杂度要求、历史密码检查
- **账户锁定**: 失败次数限制、时间锁定
- **设备管理**: 设备指纹、可信设备
- **地理位置**: IP地理位置验证
- **风险评估**: 登录行为分析

### 2. 授权管理

#### 基于角色的访问控制 (RBAC)
- **角色定义**: 预定义和自定义角色
- **权限分配**: 细粒度权限控制
- **角色继承**: 层级角色关系
- **动态权限**: 基于上下文的权限

#### 基于属性的访问控制 (ABAC)
- **用户属性**: 等级、VIP状态、地区等
- **资源属性**: 敏感度、所有者、类型等
- **环境属性**: 时间、地点、设备等
- **策略引擎**: 复杂授权规则

### 3. 会话管理

#### 会话特性
- **多设备支持**: 同时登录多个设备
- **会话限制**: 最大并发会话数
- **会话共享**: 跨服务会话状态
- **会话持久化**: Redis集群存储
- **会话安全**: 会话劫持防护

#### 令牌管理
- **访问令牌**: 短期有效的JWT
- **刷新令牌**: 长期有效的刷新机制
- **令牌撤销**: 黑名单机制
- **令牌轮换**: 自动令牌更新

## 📊 数据模型

### 用户模型

```typescript
interface User {
  id: string;                    // 用户唯一标识
  username: string;              // 用户名
  email: string;                 // 邮箱地址
  phone?: string;                // 手机号码
  passwordHash: string;          // 密码哈希
  salt: string;                  // 密码盐值
  
  // 个人信息
  profile: {
    firstName: string;           // 名
    lastName: string;            // 姓
    avatar?: string;             // 头像URL
    dateOfBirth?: Date;          // 出生日期
    gender?: 'male' | 'female' | 'other'; // 性别
    country?: string;            // 国家
    timezone?: string;           // 时区
    language: string;            // 首选语言
  };
  
  // 游戏相关
  gameProfile: {
    level: number;               // 用户等级
    experience: number;          // 经验值
    coins: number;               // 游戏币
    premiumUntil?: Date;         // 会员到期时间
    achievements: string[];      // 成就列表
    preferences: any;            // 游戏偏好设置
  };
  
  // 安全设置
  security: {
    mfaEnabled: boolean;         // 是否启用MFA
    mfaSecret?: string;          // MFA密钥
    trustedDevices: string[];    // 可信设备列表
    lastPasswordChange: Date;    // 最后密码修改时间
    passwordHistory: string[];   // 历史密码哈希
    loginAttempts: number;       // 登录尝试次数
    lockedUntil?: Date;          // 锁定到期时间
  };
  
  // 状态信息
  status: 'active' | 'inactive' | 'suspended' | 'deleted';
  emailVerified: boolean;        // 邮箱是否验证
  phoneVerified: boolean;        // 手机是否验证
  roles: string[];               // 用户角色
  permissions: string[];         // 直接权限
  
  // 审计字段
  createdAt: Date;               // 创建时间
  updatedAt: Date;               // 更新时间
  lastLoginAt?: Date;            // 最后登录时间
  lastActiveAt?: Date;           // 最后活跃时间
}
```

### 角色模型

```typescript
interface Role {
  id: string;                    // 角色ID
  name: string;                  // 角色名称
  displayName: string;           // 显示名称
  description: string;           // 角色描述
  
  // 权限
  permissions: Permission[];     // 角色权限
  inherits: string[];            // 继承的角色
  
  // 约束条件
  constraints: {
    maxUsers?: number;           // 最大用户数
    validFrom?: Date;            // 有效开始时间
    validUntil?: Date;           // 有效结束时间
    conditions?: any;            // 其他条件
  };
  
  // 元数据
  system: boolean;               // 是否系统角色
  category: string;              // 角色分类
  priority: number;              // 优先级
  
  createdAt: Date;
  updatedAt: Date;
}
```

### 权限模型

```typescript
interface Permission {
  id: string;                    // 权限ID
  name: string;                  // 权限名称
  resource: string;              // 资源类型
  action: string;                // 操作类型
  
  // 权限约束
  conditions?: {
    attributes?: any;            // 属性条件
    timeRange?: {                // 时间范围
      start: string;             // 开始时间
      end: string;               // 结束时间
    };
    ipRange?: string[];          // IP范围
    deviceTypes?: string[];      // 设备类型
  };
  
  // 元数据
  category: string;              // 权限分类
  description: string;           // 权限描述
  system: boolean;               // 是否系统权限
  
  createdAt: Date;
  updatedAt: Date;
}
```

## 🔧 技术栈

### 后端技术
- **框架**: NestJS + TypeScript
- **数据库**: MongoDB (用户数据) + Redis (会话缓存)
- **认证**: JWT + Passport.js
- **加密**: bcrypt + crypto
- **验证**: class-validator + class-transformer
- **文档**: Swagger/OpenAPI
- **测试**: Jest + Supertest

### 安全技术
- **密码哈希**: bcrypt (成本因子12)
- **JWT签名**: RS256 (RSA + SHA256)
- **MFA**: speakeasy (TOTP)
- **加密**: AES-256-GCM
- **随机数**: crypto.randomBytes
- **哈希**: SHA-256

## 🚀 API设计

### 认证端点

```typescript
// 用户注册
POST /auth/register
{
  username: string;
  email: string;
  password: string;
  profile: UserProfile;
}

// 用户登录
POST /auth/login
{
  identifier: string;  // 用户名或邮箱
  password: string;
  mfaCode?: string;    // MFA验证码
  deviceInfo?: DeviceInfo;
}

// 令牌刷新
POST /auth/refresh
{
  refreshToken: string;
}

// 用户登出
POST /auth/logout
{
  refreshToken?: string;
  allDevices?: boolean;
}

// 密码重置
POST /auth/reset-password
{
  email: string;
}

// 确认密码重置
POST /auth/confirm-reset
{
  token: string;
  newPassword: string;
}
```

### 用户管理端点

```typescript
// 获取用户信息
GET /users/me

// 更新用户信息
PUT /users/me
{
  profile: Partial<UserProfile>;
}

// 修改密码
PUT /users/me/password
{
  currentPassword: string;
  newPassword: string;
}

// 启用MFA
POST /users/me/mfa/enable
{
  secret: string;
  code: string;
}

// 禁用MFA
POST /users/me/mfa/disable
{
  password: string;
  code: string;
}
```

### 管理端点

```typescript
// 管理员仪表板
GET /admin/dashboard
{
  overview: DashboardOverview;
  userMetrics: UserMetrics;
  sessionMetrics: SessionMetrics;
  securityMetrics: SecurityMetrics;
  systemMetrics: SystemMetrics;
  recentActivities: Activity[];
  alerts: Alert[];
}

// 系统管理
GET /admin/system/status
GET /admin/system/health
GET /admin/system/metrics
POST /admin/actions/maintenance
POST /admin/actions/backup
GET /admin/logs/system
GET /admin/settings
PUT /admin/settings

// 用户管理
GET /admin/users
GET /admin/users/:id
PUT /admin/users/:id/status
PUT /admin/users/:id/roles
POST /admin/users/:id/reset-password
POST /admin/users/:id/lock
DELETE /admin/users/:id/lock
DELETE /admin/users/:id/mfa
GET /admin/users/:id/sessions
DELETE /admin/users/:id/sessions/:sessionId
DELETE /admin/users/:id
POST /admin/users/:id/restore
POST /admin/users/bulk

// 统计分析
GET /admin/statistics/overview
GET /admin/statistics/users
GET /admin/statistics/sessions
GET /admin/statistics/security
GET /admin/statistics/performance
GET /admin/statistics/business

// 安全管理
GET /admin/security/alerts
GET /admin/security/events
GET /admin/security/threats
GET /admin/security/blocked-ips
POST /admin/security/scan

// 审计管理
GET /admin/audit/logs
GET /admin/audit/events
POST /admin/audit/export

// 角色管理
GET /admin/roles
POST /admin/roles
PUT /admin/roles/:id
DELETE /admin/roles/:id

// 权限管理
GET /admin/permissions
POST /admin/permissions
PUT /admin/permissions/:id
DELETE /admin/permissions/:id

// 管理员通知
GET /admin/notifications
POST /admin/notifications/:id/read
```

## 📈 性能要求

### 响应时间目标
- **登录**: < 200ms (P95)
- **令牌验证**: < 50ms (P95)
- **用户查询**: < 100ms (P95)
- **权限检查**: < 30ms (P95)

### 吞吐量目标
- **并发登录**: 1000+ QPS
- **令牌验证**: 10000+ QPS
- **用户查询**: 5000+ QPS

### 可用性目标
- **服务可用性**: 99.9%
- **数据一致性**: 强一致性
- **故障恢复**: < 30秒

## 🛡️ 安全要求

### 数据保护
- **敏感数据加密**: 密码、MFA密钥等
- **传输加密**: TLS 1.3
- **数据脱敏**: 日志中的敏感信息
- **备份加密**: 数据库备份加密

### 访问控制
- **最小权限原则**: 用户只获得必需权限
- **权限分离**: 管理员权限分级
- **审计日志**: 所有操作记录
- **异常检测**: 异常登录行为

### 合规要求
- **GDPR**: 数据保护和隐私
- **SOC 2**: 安全控制
- **ISO 27001**: 信息安全管理

## 🔄 部署架构

### 微服务部署
- **容器化**: Docker + Kubernetes
- **服务发现**: Consul/Eureka
- **负载均衡**: Nginx/HAProxy
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

### 高可用设计
- **多实例部署**: 至少3个实例
- **数据库集群**: MongoDB副本集
- **缓存集群**: Redis Cluster
- **故障转移**: 自动故障检测和切换

这个设计文档涵盖了认证服务的核心架构、功能特性、数据模型、API设计和安全要求。请您审查这个设计，如果有任何需要调整或补充的地方，请告诉我，确认后我将开始实施代码编写。
