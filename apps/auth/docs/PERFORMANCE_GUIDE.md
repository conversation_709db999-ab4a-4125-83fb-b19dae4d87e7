# 认证服务性能优化指南

## 📊 性能概述

认证服务采用多种性能优化策略，确保高并发场景下的稳定性和响应速度。本指南详细介绍了性能特性、优化策略和监控方法。

## 🚀 性能特性

### 1. 缓存策略

#### Redis缓存
- **会话缓存**: 用户会话信息缓存
- **权限缓存**: 用户角色和权限缓存
- **限流缓存**: 速率限制计数器缓存
- **配置缓存**: 系统配置信息缓存

```typescript
// 缓存配置示例
const CACHE_CONFIG = {
  SESSION_TTL: 30 * 60, // 30分钟
  PERMISSION_TTL: 60 * 60, // 1小时
  RATE_LIMIT_TTL: 15 * 60, // 15分钟
  CONFIG_TTL: 24 * 60 * 60, // 24小时
};

// 缓存使用示例
@Injectable()
export class UserService {
  async getUserPermissions(userId: string): Promise<string[]> {
    const cacheKey = `user:permissions:${userId}`;
    
    // 尝试从缓存获取
    let permissions = await this.redisService.get(cacheKey);
    
    if (!permissions) {
      // 缓存未命中，从数据库查询
      permissions = await this.userRepository.getUserPermissions(userId);
      
      // 存入缓存
      await this.redisService.setex(
        cacheKey, 
        CACHE_CONFIG.PERMISSION_TTL, 
        JSON.stringify(permissions)
      );
    }
    
    return JSON.parse(permissions);
  }
}
```

#### 缓存模式

##### Cache-Aside模式
```typescript
@Injectable()
export class CacheAsideService {
  async getData(key: string): Promise<any> {
    // 1. 先查缓存
    let data = await this.cache.get(key);
    
    if (!data) {
      // 2. 缓存未命中，查数据库
      data = await this.database.find(key);
      
      // 3. 写入缓存
      await this.cache.set(key, data, TTL);
    }
    
    return data;
  }
  
  async updateData(key: string, data: any): Promise<void> {
    // 1. 更新数据库
    await this.database.update(key, data);
    
    // 2. 删除缓存
    await this.cache.del(key);
  }
}
```

##### Write-Through模式
```typescript
@Injectable()
export class WriteThroughService {
  async updateData(key: string, data: any): Promise<void> {
    // 1. 同时更新缓存和数据库
    await Promise.all([
      this.cache.set(key, data, TTL),
      this.database.update(key, data)
    ]);
  }
}
```

### 2. 数据库优化

#### 索引策略
```javascript
// MongoDB索引配置
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "status": 1, "createdAt": -1 });
db.users.createIndex({ "roles": 1 });

db.sessions.createIndex({ "userId": 1 });
db.sessions.createIndex({ "expiresAt": 1 }, { expireAfterSeconds: 0 });
db.sessions.createIndex({ "deviceFingerprint": 1 });

db.auditLogs.createIndex({ "userId": 1, "timestamp": -1 });
db.auditLogs.createIndex({ "event": 1, "timestamp": -1 });
```

#### 查询优化
```typescript
@Injectable()
export class OptimizedUserService {
  // 使用投影减少数据传输
  async getUserProfile(userId: string): Promise<UserProfile> {
    return this.userModel
      .findById(userId)
      .select('username email profile status roles')
      .lean() // 返回普通对象而非Mongoose文档
      .exec();
  }
  
  // 使用聚合管道优化复杂查询
  async getUserStats(userId: string): Promise<UserStats> {
    return this.userModel.aggregate([
      { $match: { _id: new ObjectId(userId) } },
      {
        $lookup: {
          from: 'sessions',
          localField: '_id',
          foreignField: 'userId',
          as: 'sessions'
        }
      },
      {
        $project: {
          username: 1,
          sessionCount: { $size: '$sessions' },
          lastLogin: { $max: '$sessions.createdAt' }
        }
      }
    ]);
  }
}
```

### 3. 连接池优化

#### MongoDB连接池
```typescript
// MongoDB连接配置
const mongoConfig = {
  uri: process.env.MONGODB_URI,
  options: {
    maxPoolSize: 50, // 最大连接数
    minPoolSize: 5,  // 最小连接数
    maxIdleTimeMS: 30000, // 连接空闲时间
    serverSelectionTimeoutMS: 5000, // 服务器选择超时
    socketTimeoutMS: 45000, // Socket超时
    bufferMaxEntries: 0, // 禁用缓冲
    bufferCommands: false,
  }
};
```

#### Redis连接池
```typescript
// Redis连接配置
const redisConfig = {
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT),
  password: process.env.REDIS_PASSWORD,
  db: 0,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  maxmemoryPolicy: 'allkeys-lru',
  // 连接池配置
  pool: {
    min: 5,
    max: 50,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200,
  }
};
```

### 4. 异步处理

#### 队列处理
```typescript
@Injectable()
export class AsyncTaskService {
  constructor(
    @InjectQueue('email') private emailQueue: Queue,
    @InjectQueue('audit') private auditQueue: Queue,
  ) {}
  
  // 异步发送邮件
  async sendWelcomeEmail(userId: string, email: string): Promise<void> {
    await this.emailQueue.add('welcome', {
      userId,
      email,
      template: 'welcome'
    }, {
      delay: 1000, // 延迟1秒
      attempts: 3, // 重试3次
      backoff: 'exponential'
    });
  }
  
  // 异步记录审计日志
  async logUserAction(userId: string, action: string, details: any): Promise<void> {
    await this.auditQueue.add('log', {
      userId,
      action,
      details,
      timestamp: new Date()
    }, {
      priority: 1, // 低优先级
      removeOnComplete: 100, // 保留最近100个完成的任务
      removeOnFail: 50 // 保留最近50个失败的任务
    });
  }
}
```

#### 批量处理
```typescript
@Injectable()
export class BatchProcessingService {
  private batchBuffer: any[] = [];
  private batchSize = 100;
  private batchTimeout = 5000; // 5秒
  
  constructor() {
    // 定时处理批量数据
    setInterval(() => {
      this.processBatch();
    }, this.batchTimeout);
  }
  
  async addToBatch(data: any): Promise<void> {
    this.batchBuffer.push(data);
    
    if (this.batchBuffer.length >= this.batchSize) {
      await this.processBatch();
    }
  }
  
  private async processBatch(): Promise<void> {
    if (this.batchBuffer.length === 0) return;
    
    const batch = this.batchBuffer.splice(0, this.batchSize);
    
    try {
      await this.auditLogModel.insertMany(batch);
    } catch (error) {
      console.error('批量处理失败:', error);
      // 重新加入队列或记录错误
    }
  }
}
```

## 📈 性能监控

### 1. 响应时间监控

```typescript
@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PerformanceInterceptor.name);
  
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const startTime = Date.now();
    
    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        
        // 记录慢查询
        if (duration > 1000) {
          this.logger.warn(`慢请求检测: ${request.method} ${request.url} - ${duration}ms`);
        }
        
        // 发送性能指标
        this.sendMetrics(request.url, duration);
      })
    );
  }
  
  private sendMetrics(endpoint: string, duration: number): void {
    // 发送到监控系统
    // 例如: Prometheus, DataDog, New Relic
  }
}
```

### 2. 资源使用监控

```typescript
@Injectable()
export class ResourceMonitorService {
  private readonly logger = new Logger(ResourceMonitorService.name);
  
  @Cron('*/30 * * * * *') // 每30秒执行一次
  async monitorResources(): Promise<void> {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    // 内存使用率
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    
    if (memoryUsagePercent > 80) {
      this.logger.warn(`内存使用率过高: ${memoryUsagePercent.toFixed(2)}%`);
    }
    
    // 检查Redis连接
    const redisInfo = await this.redisService.info('memory');
    const redisMemory = this.parseRedisMemory(redisInfo);
    
    if (redisMemory.usedPercent > 80) {
      this.logger.warn(`Redis内存使用率过高: ${redisMemory.usedPercent}%`);
    }
  }
  
  private parseRedisMemory(info: string): { used: number; total: number; usedPercent: number } {
    // 解析Redis内存信息
    const lines = info.split('\r\n');
    const usedMemory = parseInt(lines.find(line => line.startsWith('used_memory:'))?.split(':')[1] || '0');
    const totalMemory = parseInt(lines.find(line => line.startsWith('total_system_memory:'))?.split(':')[1] || '0');
    
    return {
      used: usedMemory,
      total: totalMemory,
      usedPercent: (usedMemory / totalMemory) * 100
    };
  }
}
```

### 3. 数据库性能监控

```typescript
@Injectable()
export class DatabaseMonitorService {
  @Cron('0 */5 * * * *') // 每5分钟执行一次
  async monitorDatabase(): Promise<void> {
    // 监控慢查询
    const slowQueries = await this.mongoConnection.db.admin().command({
      currentOp: true,
      'secs_running': { $gte: 5 } // 运行超过5秒的查询
    });
    
    if (slowQueries.inprog.length > 0) {
      this.logger.warn(`发现${slowQueries.inprog.length}个慢查询`);
    }
    
    // 监控连接数
    const serverStatus = await this.mongoConnection.db.admin().command({ serverStatus: 1 });
    const connections = serverStatus.connections;
    
    if (connections.current > connections.available * 0.8) {
      this.logger.warn(`数据库连接数过高: ${connections.current}/${connections.available}`);
    }
  }
}
```

## ⚡ 性能优化策略

### 1. 请求优化

#### 请求合并
```typescript
@Injectable()
export class RequestBatchingService {
  private pendingRequests = new Map<string, Promise<any>>();
  
  async getUserData(userId: string): Promise<any> {
    // 如果已有相同请求在处理，直接返回Promise
    if (this.pendingRequests.has(userId)) {
      return this.pendingRequests.get(userId);
    }
    
    const promise = this.fetchUserData(userId);
    this.pendingRequests.set(userId, promise);
    
    // 请求完成后清理
    promise.finally(() => {
      this.pendingRequests.delete(userId);
    });
    
    return promise;
  }
  
  private async fetchUserData(userId: string): Promise<any> {
    // 实际的数据获取逻辑
    return this.characterService.findById(userId);
  }
}
```

#### 数据预加载
```typescript
@Injectable()
export class DataPreloadService {
  @Cron('0 */10 * * * *') // 每10分钟预加载一次
  async preloadFrequentData(): Promise<void> {
    // 预加载热门用户数据
    const activeUsers = await this.characterService.getActiveUsers(100);
    
    for (const user of activeUsers) {
      const cacheKey = `user:profile:${user.id}`;
      const exists = await this.redisService.exists(cacheKey);
      
      if (!exists) {
        const profile = await this.characterService.getUserProfile(user.id);
        await this.redisService.setex(cacheKey, 3600, JSON.stringify(profile));
      }
    }
  }
}
```

### 2. 内存优化

#### 对象池
```typescript
class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;
  
  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    
    // 预创建对象
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }
  
  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }
  
  release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }
}

// 使用示例
const bufferPool = new ObjectPool(
  () => Buffer.alloc(1024),
  (buffer) => buffer.fill(0),
  50
);
```

#### 内存泄漏检测
```typescript
@Injectable()
export class MemoryLeakDetector {
  private memorySnapshots: number[] = [];
  
  @Cron('0 */1 * * * *') // 每分钟检查一次
  checkMemoryLeak(): void {
    const memoryUsage = process.memoryUsage();
    this.memorySnapshots.push(memoryUsage.heapUsed);
    
    // 保留最近10个快照
    if (this.memorySnapshots.length > 10) {
      this.memorySnapshots.shift();
    }
    
    // 检测内存增长趋势
    if (this.memorySnapshots.length >= 5) {
      const trend = this.calculateTrend(this.memorySnapshots.slice(-5));
      
      if (trend > 1024 * 1024) { // 增长超过1MB
        this.logger.warn(`检测到可能的内存泄漏，增长趋势: ${trend} bytes`);
      }
    }
  }
  
  private calculateTrend(values: number[]): number {
    // 简单的线性回归计算趋势
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, x) => sum + x * y, 0);
    const sumX2 = values.reduce((sum, _, x) => sum + x * x, 0);
    
    return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  }
}
```

### 3. 网络优化

#### 响应压缩
```typescript
// 启用gzip压缩
app.use(compression({
  level: 6, // 压缩级别
  threshold: 1024, // 超过1KB才压缩
  filter: (req, res) => {
    // 自定义压缩过滤器
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  }
}));
```

#### HTTP/2支持
```typescript
// HTTP/2配置
const httpsOptions = {
  key: fs.readFileSync('path/to/private-key.pem'),
  cert: fs.readFileSync('path/to/certificate.pem'),
  allowHTTP1: true // 向后兼容HTTP/1.1
};

const server = http2.createSecureServer(httpsOptions, app);
```

## 📊 性能基准测试

### 1. 负载测试

```bash
# 使用Artillery进行负载测试
npm install -g artillery

# 创建测试配置文件 load-test.yml
config:
  target: 'http://localhost:3001'
  phases:
    - duration: 60
      arrivalRate: 10
    - duration: 120
      arrivalRate: 50
    - duration: 60
      arrivalRate: 100

scenarios:
  - name: "登录测试"
    flow:
      - post:
          url: "/api/v1/auth/login"
          json:
            identifier: "<EMAIL>"
            password: "password123"

# 运行测试
artillery run load-test.yml
```

### 2. 性能基准

#### 目标性能指标

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 响应时间 | < 200ms | 95%的请求 |
| 吞吐量 | > 1000 RPS | 每秒请求数 |
| 并发用户 | > 10000 | 同时在线用户 |
| 内存使用 | < 512MB | 稳定状态下 |
| CPU使用 | < 70% | 平均使用率 |
| 错误率 | < 0.1% | 系统错误率 |

#### 性能测试脚本
```typescript
// 性能测试工具
export class PerformanceTester {
  async testLoginPerformance(concurrency: number, duration: number): Promise<TestResult> {
    const startTime = Date.now();
    const promises: Promise<any>[] = [];
    const results: any[] = [];
    
    for (let i = 0; i < concurrency; i++) {
      promises.push(this.performLoginTest(duration, results));
    }
    
    await Promise.all(promises);
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    return {
      totalRequests: results.length,
      successfulRequests: results.filter(r => r.success).length,
      averageResponseTime: results.reduce((sum, r) => sum + r.responseTime, 0) / results.length,
      throughput: results.length / (totalTime / 1000),
      errorRate: results.filter(r => !r.success).length / results.length
    };
  }
  
  private async performLoginTest(duration: number, results: any[]): Promise<void> {
    const endTime = Date.now() + duration;
    
    while (Date.now() < endTime) {
      const startTime = Date.now();
      
      try {
        await this.authService.login({
          identifier: '<EMAIL>',
          password: 'password123'
        });
        
        results.push({
          success: true,
          responseTime: Date.now() - startTime
        });
      } catch (error) {
        results.push({
          success: false,
          responseTime: Date.now() - startTime,
          error: error.message
        });
      }
    }
  }
}
```

## 🔧 性能调优建议

### 1. 生产环境配置

```env
# Node.js性能配置
NODE_ENV=production
NODE_OPTIONS="--max-old-space-size=4096 --optimize-for-size"

# 集群模式
CLUSTER_MODE=true
CLUSTER_WORKERS=4

# 缓存配置
REDIS_MAX_MEMORY=2gb
REDIS_MAX_MEMORY_POLICY=allkeys-lru

# 数据库配置
MONGODB_MAX_POOL_SIZE=50
MONGODB_MIN_POOL_SIZE=5
```

### 2. 监控和告警

```typescript
// 性能告警配置
const PERFORMANCE_THRESHOLDS = {
  RESPONSE_TIME: 1000, // 1秒
  MEMORY_USAGE: 80, // 80%
  CPU_USAGE: 70, // 70%
  ERROR_RATE: 1, // 1%
  QUEUE_SIZE: 1000 // 队列大小
};

@Injectable()
export class PerformanceAlertService {
  @Cron('*/30 * * * * *') // 每30秒检查一次
  async checkPerformance(): Promise<void> {
    const metrics = await this.collectMetrics();
    
    if (metrics.averageResponseTime > PERFORMANCE_THRESHOLDS.RESPONSE_TIME) {
      await this.sendAlert('响应时间过长', metrics);
    }
    
    if (metrics.memoryUsage > PERFORMANCE_THRESHOLDS.MEMORY_USAGE) {
      await this.sendAlert('内存使用率过高', metrics);
    }
    
    if (metrics.errorRate > PERFORMANCE_THRESHOLDS.ERROR_RATE) {
      await this.sendAlert('错误率过高', metrics);
    }
  }
}
```

### 3. 持续优化

- **定期性能审查**: 每月进行性能分析
- **代码优化**: 识别和优化热点代码
- **架构优化**: 根据使用模式调整架构
- **容量规划**: 基于增长预测进行容量规划
- **技术升级**: 及时升级依赖和框架版本

通过这些性能优化策略，认证服务能够在高并发场景下保持稳定的性能表现。
