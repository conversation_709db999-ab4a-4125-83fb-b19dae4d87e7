# 认证服务安全指南

## 🔒 安全概述

认证服务采用多层安全防护策略，确保用户数据和系统安全。本指南详细介绍了安全特性、最佳实践和安全配置。

## 🛡️ 安全特性

### 1. 认证安全

#### JWT令牌安全
- **算法**: HS256 (HMAC with SHA-256)
- **过期时间**: 访问令牌1小时，刷新令牌30天
- **令牌轮换**: 每次刷新都会生成新的令牌对
- **令牌黑名单**: 支持令牌撤销和黑名单机制

```typescript
// JWT配置示例
{
  secret: process.env.JWT_SECRET, // 至少32字符的强密钥
  signOptions: {
    expiresIn: '1h',
    issuer: 'football-manager-auth',
    audience: 'football-manager-app'
  }
}
```

#### 密码安全
- **哈希算法**: bcrypt with salt rounds 12
- **密码策略**: 最少8位，包含大小写字母、数字和特殊字符
- **密码历史**: 防止重复使用最近5个密码
- **密码强度检测**: 实时密码强度评估

```typescript
// 密码策略配置
const PASSWORD_POLICY = {
  MIN_LENGTH: 8,
  MAX_LENGTH: 128,
  REQUIRE_UPPERCASE: true,
  REQUIRE_LOWERCASE: true,
  REQUIRE_NUMBERS: true,
  REQUIRE_SPECIAL_CHARS: true,
  HISTORY_COUNT: 5
};
```

### 2. 多因子认证 (MFA)

#### TOTP支持
- **算法**: TOTP (Time-based One-Time Password)
- **密钥长度**: 32字节
- **时间窗口**: 30秒
- **容错窗口**: ±1个时间窗口

#### 备用代码
- **数量**: 10个一次性备用代码
- **格式**: 8位数字
- **使用限制**: 每个代码只能使用一次

```typescript
// MFA配置示例
const MFA_CONFIG = {
  issuer: 'Football Manager',
  algorithm: 'sha1',
  digits: 6,
  period: 30,
  window: 1
};
```

### 3. 访问控制

#### 基于角色的访问控制 (RBAC)
- **系统角色**: super_admin, system_admin, user_admin, user
- **游戏角色**: team_owner, general_manager, head_coach, scout
- **权限粒度**: 资源级别的细粒度权限控制

#### 权限矩阵

| 角色 | 用户管理 | 系统配置 | 游戏管理 | 数据访问 |
|------|---------|---------|---------|---------|
| super_admin | ✅ | ✅ | ✅ | ✅ |
| system_admin | ✅ | ✅ | ❌ | ✅ |
| user_admin | ✅ | ❌ | ❌ | ❌ |
| team_owner | ❌ | ❌ | ✅ | ✅ |
| user | ❌ | ❌ | ❌ | ❌ |

### 4. 输入验证和过滤

#### 数据验证
- **DTO验证**: 使用class-validator进行严格的数据验证
- **类型检查**: TypeScript静态类型检查
- **长度限制**: 所有输入字段都有长度限制
- **格式验证**: 邮箱、手机号、URL等格式验证

#### 安全过滤
- **SQL注入防护**: 参数化查询和ORM保护
- **XSS防护**: 输入过滤和输出编码
- **路径遍历防护**: 文件路径验证
- **CSRF防护**: CSRF令牌验证

```typescript
// 安全验证示例
@Injectable()
export class SecurityValidator {
  // SQL注入检测
  detectSqlInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(--|\/\*|\*\/|;)/,
      /(\b(OR|AND)\b.*=.*)/i,
    ];
    return sqlPatterns.some(pattern => pattern.test(input));
  }

  // XSS检测
  detectXss(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/i,
      /on\w+\s*=/i,
    ];
    return xssPatterns.some(pattern => pattern.test(input));
  }
}
```

### 5. 速率限制

#### 全局限流
- **默认限制**: 每分钟100个请求
- **突发限制**: 允许短时间内的突发请求
- **IP级别**: 基于真实IP地址的限流

#### 端点特定限流

| 端点 | 限制 | 时间窗口 | 说明 |
|------|------|---------|------|
| /auth/login | 5次 | 15分钟 | 防止暴力破解 |
| /auth/register | 3次 | 1小时 | 防止批量注册 |
| /auth/reset-password | 3次 | 1小时 | 防止邮件轰炸 |
| /mfa/verify | 10次 | 5分钟 | 防止MFA暴力破解 |

```typescript
// 限流配置示例
const RATE_LIMITS = {
  LOGIN: { MAX_ATTEMPTS: 5, WINDOW: 15 * 60 * 1000 },
  REGISTER: { MAX_ATTEMPTS: 3, WINDOW: 60 * 60 * 1000 },
  PASSWORD_RESET: { MAX_ATTEMPTS: 3, WINDOW: 60 * 60 * 1000 },
  MFA_VERIFY: { MAX_ATTEMPTS: 10, WINDOW: 5 * 60 * 1000 }
};
```

### 6. 会话管理

#### 会话安全
- **会话ID**: 加密的随机会话标识符
- **会话过期**: 空闲30分钟自动过期
- **并发控制**: 限制同一用户的并发会话数
- **设备绑定**: 会话与设备指纹绑定

#### 设备管理
- **设备指纹**: 基于User-Agent、IP等生成设备指纹
- **设备注册**: 新设备需要邮箱验证
- **设备限制**: 每个用户最多10个活跃设备
- **异常检测**: 检测异常登录行为

```typescript
// 会话配置示例
const SESSION_CONFIG = {
  IDLE_TIMEOUT: 30 * 60 * 1000, // 30分钟
  MAX_SESSIONS_PER_USER: 5,
  MAX_DEVICES_PER_USER: 10,
  REQUIRE_DEVICE_VERIFICATION: true
};
```

## 🔐 加密和数据保护

### 1. 数据加密

#### 传输加密
- **HTTPS**: 强制使用HTTPS传输
- **TLS版本**: 最低TLS 1.2
- **证书**: 使用有效的SSL证书
- **HSTS**: 启用HTTP严格传输安全

#### 存储加密
- **敏感数据**: AES-256-GCM加密存储
- **密钥管理**: 使用环境变量管理加密密钥
- **密钥轮换**: 定期轮换加密密钥
- **数据库加密**: MongoDB字段级加密

```typescript
// 加密配置示例
const ENCRYPTION_CONFIG = {
  ALGORITHM: 'aes-256-gcm',
  KEY_LENGTH: 32,
  IV_LENGTH: 16,
  TAG_LENGTH: 16,
  ITERATIONS: 100000
};
```

### 2. 敏感信息处理

#### 数据分类
- **高敏感**: 密码、支付信息、身份证号
- **中敏感**: 邮箱、手机号、真实姓名
- **低敏感**: 用户名、游戏数据、偏好设置

#### 数据掩码
```typescript
// 敏感信息掩码示例
const maskEmail = (email: string) => {
  const [local, domain] = email.split('@');
  return `${local[0]}***${local[local.length - 1]}@${domain}`;
};

const maskPhone = (phone: string) => {
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};
```

## 🚨 安全监控和审计

### 1. 安全事件监控

#### 监控事件
- **登录失败**: 连续登录失败尝试
- **异常访问**: 异常IP或设备访问
- **权限提升**: 权限变更操作
- **数据访问**: 敏感数据访问记录
- **系统变更**: 系统配置变更

#### 告警机制
```typescript
// 安全告警配置
const SECURITY_ALERTS = {
  FAILED_LOGIN_THRESHOLD: 5,
  SUSPICIOUS_IP_THRESHOLD: 10,
  PRIVILEGE_ESCALATION: true,
  DATA_BREACH_DETECTION: true,
  REAL_TIME_MONITORING: true
};
```

### 2. 审计日志

#### 日志内容
- **用户操作**: 所有用户操作记录
- **系统事件**: 系统级别的安全事件
- **数据变更**: 敏感数据的变更记录
- **访问记录**: API访问和权限检查记录

#### 日志格式
```json
{
  "timestamp": "2023-12-01T10:00:00.000Z",
  "level": "SECURITY",
  "event": "LOGIN_FAILED",
  "userId": "507f1f77bcf86cd799439011",
  "ip": "*************",
  "userAgent": "Mozilla/5.0...",
  "details": {
    "reason": "invalid_password",
    "attempts": 3
  }
}
```

## ⚙️ 安全配置

### 1. 环境变量配置

```env
# JWT安全配置
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置
ENCRYPTION_MASTER_KEY=your-32-byte-master-encryption-key
ENCRYPTION_ALGORITHM=aes-256-gcm

# 密码策略
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL_CHARS=true

# 会话配置
SESSION_TIMEOUT=1800000
MAX_SESSIONS_PER_USER=5

# 限流配置
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=5

# 安全头配置
SECURITY_HEADERS_ENABLED=true
CORS_ENABLED=true
CORS_ORIGIN=https://yourdomain.com
```

### 2. 安全头配置

```typescript
// 安全头配置
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

## 🔍 安全测试

### 1. 自动化安全测试

```bash
# 运行安全测试套件
npm run test:security

# 依赖漏洞扫描
npm audit

# 代码安全扫描
npm run lint:security
```

### 2. 渗透测试检查清单

#### 认证测试
- [ ] 暴力破解防护
- [ ] 会话管理安全
- [ ] 密码策略验证
- [ ] MFA绕过测试

#### 授权测试
- [ ] 垂直权限提升
- [ ] 水平权限提升
- [ ] 直接对象引用
- [ ] 功能级访问控制

#### 输入验证测试
- [ ] SQL注入测试
- [ ] XSS攻击测试
- [ ] 命令注入测试
- [ ] 路径遍历测试

## 📋 安全检查清单

### 部署前检查
- [ ] 所有敏感信息已从代码中移除
- [ ] 环境变量正确配置
- [ ] HTTPS证书有效
- [ ] 安全头正确设置
- [ ] 数据库访问权限最小化
- [ ] 日志记录功能正常
- [ ] 监控告警配置完成

### 定期安全检查
- [ ] 依赖包安全更新
- [ ] 密钥轮换
- [ ] 访问日志审查
- [ ] 权限配置审查
- [ ] 安全事件分析
- [ ] 备份恢复测试

## 🚨 安全事件响应

### 1. 事件分类

#### 高危事件
- 数据泄露
- 权限提升攻击
- 系统入侵
- 大规模暴力破解

#### 中危事件
- 异常登录行为
- 权限滥用
- 可疑API调用
- 配置错误

#### 低危事件
- 单次登录失败
- 正常权限变更
- 常规API访问
- 用户操作记录

### 2. 响应流程

1. **事件检测**: 自动监控系统检测到安全事件
2. **事件评估**: 评估事件的严重程度和影响范围
3. **即时响应**: 采取紧急措施阻止攻击
4. **调查分析**: 深入分析事件原因和影响
5. **修复加固**: 修复漏洞并加强防护
6. **总结改进**: 总结经验并改进安全措施

## 📞 安全联系方式

如发现安全漏洞，请通过以下方式联系：

- **安全邮箱**: <EMAIL>
- **紧急热线**: +86-xxx-xxxx-xxxx
- **漏洞报告**: https://security.footballmanager.com/report

## 📚 相关资源

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [JWT Security Best Practices](https://tools.ietf.org/html/rfc8725)
- [Node.js Security Checklist](https://blog.risingstack.com/node-js-security-checklist/)
