# 认证服务故障排除指南

## 🔍 概述

本指南提供了认证服务常见问题的诊断和解决方法，帮助开发者和运维人员快速定位和解决问题。

## 🚨 常见问题分类

### 1. 认证相关问题

#### 问题：JWT令牌验证失败

**症状**:
- 返回401 Unauthorized错误
- 错误消息："无效的令牌"或"令牌已过期"

**可能原因**:
1. JWT密钥配置错误
2. 令牌格式不正确
3. 令牌已过期
4. 时钟同步问题

**诊断步骤**:
```bash
# 1. 检查JWT配置
echo $JWT_SECRET
# 确保密钥长度至少32字符

# 2. 验证令牌格式
node -e "
const jwt = require('jsonwebtoken');
const token = 'YOUR_TOKEN_HERE';
try {
  const decoded = jwt.decode(token, {complete: true});
  console.log('Token header:', decoded.header);
  console.log('Token payload:', decoded.payload);
} catch (error) {
  console.error('Token decode error:', error.message);
}
"

# 3. 检查系统时间
date
timedatectl status
```

**解决方案**:
```typescript
// 检查JWT配置
@Injectable()
export class JwtConfigValidator {
  validateConfig(): void {
    const secret = this.configService.get('JWT_SECRET');
    
    if (!secret || secret.length < 32) {
      throw new Error('JWT_SECRET必须至少32字符');
    }
    
    const expiresIn = this.configService.get('JWT_EXPIRES_IN');
    if (!expiresIn) {
      throw new Error('JWT_EXPIRES_IN未配置');
    }
  }
}

// 令牌验证增强
@Injectable()
export class EnhancedJwtService {
  async verifyToken(token: string): Promise<any> {
    try {
      return jwt.verify(token, this.configService.get('JWT_SECRET'), {
        issuer: 'football-manager-auth',
        audience: 'football-manager-app',
        clockTolerance: 60 // 允许60秒时钟偏差
      });
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('令牌已过期');
      } else if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('无效的令牌');
      } else {
        throw new UnauthorizedException('令牌验证失败');
      }
    }
  }
}
```

#### 问题：用户登录失败

**症状**:
- 正确的用户名密码无法登录
- 返回"无效的凭据"错误

**诊断步骤**:
```bash
# 1. 检查用户状态
mongo
use football_manager_auth
db.users.findOne({email: "<EMAIL>"}, {status: 1, emailVerified: 1})

# 2. 检查密码哈希
db.users.findOne({email: "<EMAIL>"}, {password: 1})

# 3. 检查登录日志
db.auditLogs.find({
  userId: ObjectId("USER_ID"),
  event: "LOGIN_FAILED"
}).sort({timestamp: -1}).limit(5)
```

**解决方案**:
```typescript
// 增强登录诊断
@Injectable()
export class LoginDiagnosticService {
  async diagnoseLoginFailure(identifier: string, password: string): Promise<string> {
    // 1. 检查用户是否存在
    const user = await this.characterService.findByIdentifier(identifier);
    if (!user) {
      return '用户不存在';
    }
    
    // 2. 检查用户状态
    if (user.status !== 'active') {
      return `用户状态异常: ${user.status}`;
    }
    
    // 3. 检查邮箱验证
    if (!user.emailVerified) {
      return '邮箱未验证';
    }
    
    // 4. 检查账户锁定
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      return `账户已锁定至: ${user.lockedUntil}`;
    }
    
    // 5. 验证密码
    const isPasswordValid = await this.cryptoService.verifyPassword(password, user.password);
    if (!isPasswordValid) {
      return '密码错误';
    }
    
    return '登录应该成功';
  }
}
```

### 2. 数据库连接问题

#### 问题：MongoDB连接失败

**症状**:
- 应用启动时连接超时
- 间歇性数据库操作失败

**诊断步骤**:
```bash
# 1. 检查MongoDB服务状态
systemctl status mongod
# 或
brew services list | grep mongodb

# 2. 测试连接
mongo mongodb://localhost:27017/football_manager_auth

# 3. 检查连接字符串
echo $MONGODB_URI

# 4. 检查网络连接
telnet localhost 27017
```

**解决方案**:
```typescript
// 连接健康检查
@Injectable()
export class DatabaseHealthService {
  async checkConnection(): Promise<boolean> {
    try {
      await this.mongoConnection.db.admin().ping();
      return true;
    } catch (error) {
      this.logger.error('数据库连接失败:', error);
      return false;
    }
  }
  
  async reconnect(): Promise<void> {
    try {
      await this.mongoConnection.close();
      await this.mongoConnection.connect();
      this.logger.log('数据库重连成功');
    } catch (error) {
      this.logger.error('数据库重连失败:', error);
      throw error;
    }
  }
}

// 连接重试机制
const mongooseOptions = {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferMaxEntries: 0,
  bufferCommands: false,
  // 自动重连
  autoReconnect: true,
  reconnectTries: Number.MAX_VALUE,
  reconnectInterval: 1000,
};
```

### 3. Redis连接问题

#### 问题：Redis连接失败

**症状**:
- 缓存功能不工作
- 限流功能失效

**诊断步骤**:
```bash
# 1. 检查Redis服务
redis-cli ping
# 应该返回 PONG

# 2. 检查Redis配置
redis-cli config get "*"

# 3. 检查内存使用
redis-cli info memory

# 4. 检查连接数
redis-cli info clients
```

**解决方案**:
```typescript
// Redis健康检查
@Injectable()
export class RedisHealthService {
  async checkHealth(): Promise<{status: string, details: any}> {
    try {
      const start = Date.now();
      await this.redisService.ping();
      const latency = Date.now() - start;
      
      const info = await this.redisService.info();
      const memory = this.parseRedisInfo(info, 'memory');
      const clients = this.parseRedisInfo(info, 'clients');
      
      return {
        status: 'healthy',
        details: {
          latency,
          memory: {
            used: memory.used_memory_human,
            peak: memory.used_memory_peak_human,
          },
          clients: {
            connected: clients.connected_clients,
            blocked: clients.blocked_clients,
          }
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: { error: error.message }
      };
    }
  }
}

// Redis连接恢复
@Injectable()
export class RedisRecoveryService {
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  
  async handleConnectionError(error: Error): Promise<void> {
    this.logger.error('Redis连接错误:', error);
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
      
      this.logger.log(`${delay}ms后尝试重连Redis (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(async () => {
        try {
          await this.redisService.connect();
          this.reconnectAttempts = 0;
          this.logger.log('Redis重连成功');
        } catch (reconnectError) {
          await this.handleConnectionError(reconnectError);
        }
      }, delay);
    } else {
      this.logger.error('Redis重连失败，已达到最大重试次数');
      // 启用降级模式
      this.enableFallbackMode();
    }
  }
  
  private enableFallbackMode(): void {
    // 启用内存缓存作为降级方案
    this.logger.warn('启用Redis降级模式');
  }
}
```

### 4. 性能问题

#### 问题：响应时间过长

**症状**:
- API响应时间超过1秒
- 用户体验差

**诊断步骤**:
```bash
# 1. 检查系统资源
top
htop
free -h
df -h

# 2. 检查网络延迟
ping localhost
ping redis-server
ping mongodb-server

# 3. 分析慢查询
# MongoDB慢查询
mongo
db.setProfilingLevel(2, { slowms: 100 })
db.system.profile.find().sort({ts: -1}).limit(5)

# Redis慢查询
redis-cli slowlog get 10
```

**解决方案**:
```typescript
// 性能监控中间件
@Injectable()
export class PerformanceMonitoringInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const startTime = process.hrtime.bigint();
    
    return next.handle().pipe(
      tap(() => {
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒
        
        if (duration > 1000) {
          this.logger.warn(`慢请求: ${request.method} ${request.url} - ${duration}ms`);
          
          // 记录详细信息
          this.logSlowRequest(request, duration);
        }
      })
    );
  }
  
  private async logSlowRequest(request: any, duration: number): Promise<void> {
    const details = {
      method: request.method,
      url: request.url,
      duration,
      userAgent: request.get('User-Agent'),
      ip: request.ip,
      timestamp: new Date(),
    };
    
    // 异步记录到数据库
    await this.auditService.logSlowRequest(details);
  }
}

// 查询优化建议
@Injectable()
export class QueryOptimizationService {
  async analyzeSlowQueries(): Promise<any[]> {
    // 分析MongoDB慢查询
    const slowQueries = await this.mongoConnection.db
      .collection('system.profile')
      .find({ ts: { $gte: new Date(Date.now() - 3600000) } }) // 最近1小时
      .sort({ ts: -1 })
      .toArray();
    
    return slowQueries.map(query => ({
      command: query.command,
      duration: query.millis,
      timestamp: query.ts,
      suggestion: this.generateOptimizationSuggestion(query)
    }));
  }
  
  private generateOptimizationSuggestion(query: any): string {
    if (query.planSummary === 'COLLSCAN') {
      return '建议添加索引以避免全表扫描';
    }
    
    if (query.millis > 1000) {
      return '考虑优化查询条件或添加复合索引';
    }
    
    return '查询性能正常';
  }
}
```

### 5. 内存泄漏问题

#### 问题：内存使用持续增长

**症状**:
- 应用内存使用不断增长
- 最终导致OOM错误

**诊断步骤**:
```bash
# 1. 监控内存使用
node --inspect app.js
# 然后在Chrome中打开 chrome://inspect

# 2. 生成堆快照
kill -USR2 <node_process_id>

# 3. 分析内存使用
node --max-old-space-size=4096 --inspect app.js
```

**解决方案**:
```typescript
// 内存监控服务
@Injectable()
export class MemoryMonitorService {
  private memorySnapshots: number[] = [];
  
  @Cron('*/60 * * * * *') // 每分钟检查
  checkMemoryUsage(): void {
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    
    this.memorySnapshots.push(heapUsedMB);
    
    // 保留最近10个快照
    if (this.memorySnapshots.length > 10) {
      this.memorySnapshots.shift();
    }
    
    // 检测内存泄漏
    if (this.memorySnapshots.length >= 5) {
      const trend = this.calculateMemoryTrend();
      
      if (trend > 10) { // 每分钟增长超过10MB
        this.logger.warn(`检测到内存泄漏，增长趋势: ${trend}MB/min`);
        this.triggerGarbageCollection();
      }
    }
    
    // 内存使用过高告警
    if (heapUsedMB > 1024) { // 超过1GB
      this.logger.error(`内存使用过高: ${heapUsedMB}MB`);
    }
  }
  
  private calculateMemoryTrend(): number {
    const recent = this.memorySnapshots.slice(-5);
    const first = recent[0];
    const last = recent[recent.length - 1];
    return (last - first) / recent.length;
  }
  
  private triggerGarbageCollection(): void {
    if (global.gc) {
      global.gc();
      this.logger.log('手动触发垃圾回收');
    }
  }
}

// 对象池防止内存泄漏
class SafeObjectPool<T> {
  private pool: T[] = [];
  private maxSize: number;
  
  constructor(
    private createFn: () => T,
    private resetFn: (obj: T) => void,
    maxSize = 100
  ) {
    this.maxSize = maxSize;
  }
  
  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }
  
  release(obj: T): void {
    if (this.pool.length < this.maxSize) {
      this.resetFn(obj);
      this.pool.push(obj);
    }
    // 超过最大大小时直接丢弃，防止内存泄漏
  }
}
```

## 🛠️ 调试工具

### 1. 日志分析工具

```bash
# 实时查看日志
tail -f logs/application.log

# 过滤错误日志
grep "ERROR" logs/application.log | tail -20

# 分析登录失败
grep "LOGIN_FAILED" logs/security.log | wc -l

# 查看慢请求
grep "慢请求" logs/performance.log | tail -10
```

### 2. 数据库调试

```javascript
// MongoDB调试查询
// 启用查询分析
db.setProfilingLevel(2, { slowms: 100 });

// 查看执行计划
db.users.find({email: "<EMAIL>"}).explain("executionStats");

// 查看索引使用情况
db.users.getIndexes();

// 分析集合统计
db.users.stats();
```

### 3. Redis调试

```bash
# 监控Redis命令
redis-cli monitoring

# 查看键空间信息
redis-cli info keyspace

# 分析内存使用
redis-cli --bigkeys

# 查看慢查询
redis-cli slowlog get 10
```

## 📊 监控和告警

### 1. 健康检查端点

```typescript
@Controller('health')
export class HealthController {
  constructor(
    private readonly databaseHealth: DatabaseHealthService,
    private readonly redisHealth: RedisHealthService,
    private readonly memoryMonitor: MemoryMonitorService,
  ) {}
  
  @Get()
  async getHealth(): Promise<any> {
    const checks = await Promise.allSettled([
      this.databaseHealth.checkConnection(),
      this.redisHealth.checkHealth(),
      this.checkMemoryUsage(),
    ]);
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: checks[0].status === 'fulfilled' ? checks[0].value : false,
        redis: checks[1].status === 'fulfilled' ? checks[1].value : false,
        memory: checks[2].status === 'fulfilled' ? checks[2].value : false,
      }
    };
    
    // 如果任何检查失败，标记为不健康
    if (Object.values(health.checks).some(check => !check)) {
      health.status = 'unhealthy';
    }
    
    return health;
  }
  
  private async checkMemoryUsage(): Promise<boolean> {
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
    return heapUsedMB < 1024; // 小于1GB认为正常
  }
}
```

### 2. 告警配置

```typescript
// 告警服务
@Injectable()
export class AlertService {
  async sendAlert(type: string, message: string, details?: any): Promise<void> {
    const alert = {
      type,
      message,
      details,
      timestamp: new Date().toISOString(),
      severity: this.getSeverity(type),
    };
    
    // 发送到多个渠道
    await Promise.all([
      this.sendEmailAlert(alert),
      this.sendSlackAlert(alert),
      this.logAlert(alert),
    ]);
  }
  
  private getSeverity(type: string): 'low' | 'medium' | 'high' | 'critical' {
    const severityMap = {
      'memory_leak': 'high',
      'database_down': 'critical',
      'redis_down': 'high',
      'slow_response': 'medium',
      'login_failure': 'low',
    };
    
    return severityMap[type] || 'medium';
  }
}
```

## 📝 故障处理流程

### 1. 紧急故障处理

1. **立即响应** (0-5分钟)
   - 确认故障影响范围
   - 启动应急预案
   - 通知相关人员

2. **快速恢复** (5-30分钟)
   - 尝试快速修复
   - 启用备用系统
   - 回滚到稳定版本

3. **根因分析** (30分钟-2小时)
   - 收集日志和监控数据
   - 分析故障原因
   - 制定永久解决方案

4. **后续改进** (2小时后)
   - 实施永久修复
   - 更新监控和告警
   - 总结经验教训

### 2. 故障预防

- **定期健康检查**: 每日检查系统健康状态
- **性能监控**: 持续监控关键指标
- **容量规划**: 提前规划系统容量
- **灾难恢复**: 定期测试备份和恢复流程
- **代码审查**: 严格的代码审查流程
- **自动化测试**: 完善的测试覆盖

通过这个故障排除指南，可以快速定位和解决认证服务中的常见问题，确保系统的稳定运行。
