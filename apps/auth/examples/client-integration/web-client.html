<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>足球经理认证系统 - Web客户端示例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .user-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .user-info h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .user-info p {
            margin-bottom: 8px;
            color: #666;
        }

        .hidden {
            display: none;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e1e5e9;
        }

        .tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: #666;
            transition: color 0.3s;
        }

        .tab.active {
            color: #667eea;
            border-bottom: 2px solid #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }

        .mfa-setup {
            text-align: center;
        }

        .qr-code {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .backup-codes {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .backup-codes h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .backup-codes ul {
            list-style: none;
            columns: 2;
            column-gap: 20px;
        }

        .backup-codes li {
            font-family: monospace;
            font-size: 14px;
            color: #856404;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚽ 足球经理</h1>
            <p>认证系统 Web 客户端示例</p>
        </div>

        <div id="status" class="status hidden"></div>

        <!-- 登录/注册选项卡 -->
        <div id="auth-section">
            <div class="tabs">
                <button class="tab active" onclick="switchTab('login')">登录</button>
                <button class="tab" onclick="switchTab('register')">注册</button>
            </div>

            <!-- 登录表单 -->
            <div id="login-tab" class="tab-content active">
                <form id="login-form">
                    <div class="form-group">
                        <label for="login-identifier">用户名或邮箱</label>
                        <input type="text" id="login-identifier" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">密码</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <div class="form-group">
                        <label for="mfa-code">MFA验证码（如果已启用）</label>
                        <input type="text" id="mfa-code" placeholder="6位数字验证码">
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="remember-me">
                        <label for="remember-me">记住我</label>
                    </div>
                    <button type="submit" class="btn">登录</button>
                </form>
            </div>

            <!-- 注册表单 -->
            <div id="register-tab" class="tab-content">
                <form id="register-form">
                    <div class="form-group">
                        <label for="register-username">用户名</label>
                        <input type="text" id="register-username" required>
                    </div>
                    <div class="form-group">
                        <label for="register-email">邮箱</label>
                        <input type="email" id="register-email" required>
                    </div>
                    <div class="form-group">
                        <label for="register-password">密码</label>
                        <input type="password" id="register-password" required>
                    </div>
                    <div class="form-group">
                        <label for="register-confirm-password">确认密码</label>
                        <input type="password" id="register-confirm-password" required>
                    </div>
                    <div class="form-group">
                        <label for="first-name">姓</label>
                        <input type="text" id="first-name" required>
                    </div>
                    <div class="form-group">
                        <label for="last-name">名</label>
                        <input type="text" id="last-name" required>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="accept-terms" required>
                        <label for="accept-terms">我同意服务条款</label>
                    </div>
                    <button type="submit" class="btn">注册</button>
                </form>
            </div>
        </div>

        <!-- 用户信息区域 -->
        <div id="user-section" class="hidden">
            <div class="user-info">
                <h3>👤 用户信息</h3>
                <p><strong>用户名:</strong> <span id="user-username"></span></p>
                <p><strong>邮箱:</strong> <span id="user-email"></span></p>
                <p><strong>角色:</strong> <span id="user-roles"></span></p>
                <p><strong>MFA状态:</strong> <span id="mfa-status"></span></p>
            </div>

            <div class="tabs">
                <button class="tab active" onclick="switchUserTab('profile')">个人资料</button>
                <button class="tab" onclick="switchUserTab('mfa')">多因子认证</button>
                <button class="tab" onclick="switchUserTab('sessions')">会话管理</button>
            </div>

            <!-- 个人资料 -->
            <div id="profile-tab" class="tab-content active">
                <button class="btn btn-secondary" onclick="updateProfile()">更新资料</button>
                <button class="btn btn-secondary" onclick="changePassword()">修改密码</button>
            </div>

            <!-- MFA设置 -->
            <div id="mfa-tab" class="tab-content">
                <div id="mfa-disabled" class="mfa-setup">
                    <p>多因子认证未启用</p>
                    <button class="btn" onclick="setupMFA()">启用MFA</button>
                </div>
                <div id="mfa-enabled" class="hidden">
                    <p>✅ 多因子认证已启用</p>
                    <button class="btn btn-secondary" onclick="regenerateBackupCodes()">重新生成备用码</button>
                    <button class="btn btn-secondary" onclick="disableMFA()">禁用MFA</button>
                </div>
                <div id="mfa-setup-process" class="hidden">
                    <div class="qr-code">
                        <p>请使用认证器应用扫描二维码：</p>
                        <div id="qr-code-container"></div>
                        <p>或手动输入密钥：<code id="manual-key"></code></p>
                    </div>
                    <div class="form-group">
                        <label for="totp-code">输入验证码</label>
                        <input type="text" id="totp-code" placeholder="6位数字验证码">
                    </div>
                    <button class="btn" onclick="enableMFA()">确认启用</button>
                    <button class="btn btn-secondary" onclick="cancelMFASetup()">取消</button>
                    <div id="backup-codes-display" class="backup-codes hidden">
                        <h4>⚠️ 备用码（请妥善保存）</h4>
                        <ul id="backup-codes-list"></ul>
                    </div>
                </div>
            </div>

            <!-- 会话管理 -->
            <div id="sessions-tab" class="tab-content">
                <div id="sessions-list"></div>
                <button class="btn btn-secondary" onclick="loadSessions()">刷新会话</button>
                <button class="btn btn-secondary" onclick="logoutAllDevices()">登出所有设备</button>
            </div>

            <button class="btn btn-secondary" onclick="logout()">登出</button>
        </div>
    </div>

    <script>
        // 认证客户端类
        class AuthClient {
            constructor(baseURL = 'http://localhost:3001') {
                this.baseURL = baseURL;
                this.accessToken = localStorage.getItem('accessToken');
                this.refreshToken = localStorage.getItem('refreshToken');
            }

            async request(url, options = {}) {
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers,
                    },
                    ...options,
                };

                if (this.accessToken) {
                    config.headers.Authorization = `Bearer ${this.accessToken}`;
                }

                try {
                    const response = await fetch(`${this.baseURL}${url}`, config);
                    
                    if (response.status === 401 && this.refreshToken) {
                        // 尝试刷新令牌
                        await this.refreshAccessToken();
                        config.headers.Authorization = `Bearer ${this.accessToken}`;
                        return fetch(`${this.baseURL}${url}`, config);
                    }

                    return response;
                } catch (error) {
                    throw error;
                }
            }

            async login(credentials) {
                const response = await this.request('/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({
                        ...credentials,
                        deviceInfo: {
                            type: 'web',
                            name: 'Web Browser',
                            fingerprint: `web-${Date.now()}`,
                            userAgent: navigator.userAgent,
                            ipAddress: '127.0.0.1',
                        },
                    }),
                });

                const data = await response.json();
                
                if (response.ok) {
                    this.accessToken = data.data.tokens.accessToken;
                    this.refreshToken = data.data.tokens.refreshToken;
                    localStorage.setItem('accessToken', this.accessToken);
                    localStorage.setItem('refreshToken', this.refreshToken);
                }

                return data;
            }

            async register(userData) {
                const response = await this.request('/auth/register', {
                    method: 'POST',
                    body: JSON.stringify(userData),
                });

                return response.json();
            }

            async refreshAccessToken() {
                const response = await fetch(`${this.baseURL}/auth/refresh`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        refreshToken: this.refreshToken,
                    }),
                });

                if (response.ok) {
                    const data = await response.json();
                    this.accessToken = data.data.tokens.accessToken;
                    this.refreshToken = data.data.tokens.refreshToken;
                    localStorage.setItem('accessToken', this.accessToken);
                    localStorage.setItem('refreshToken', this.refreshToken);
                } else {
                    this.logout();
                }
            }

            async getProfile() {
                const response = await this.request('/users/me');
                return response.json();
            }

            async getMfaStatus() {
                const response = await this.request('/auth/mfa/status');
                return response.json();
            }

            async setupMfa() {
                const response = await this.request('/auth/mfa/setup', {
                    method: 'POST',
                });
                return response.json();
            }

            async enableMfa(secret, code) {
                const response = await this.request('/auth/mfa/enable', {
                    method: 'POST',
                    body: JSON.stringify({ secret, code }),
                });
                return response.json();
            }

            async getSessions() {
                const response = await this.request('/users/me/sessions');
                return response.json();
            }

            logout() {
                this.accessToken = null;
                this.refreshToken = null;
                localStorage.removeItem('accessToken');
                localStorage.removeItem('refreshToken');
                showAuthSection();
            }

            isAuthenticated() {
                return !!this.accessToken;
            }
        }

        // 全局变量
        const authClient = new AuthClient();
        let mfaSetupData = null;

        // 页面加载时检查登录状态
        window.addEventListener('load', async () => {
            if (authClient.isAuthenticated()) {
                try {
                    await loadUserInfo();
                    showUserSection();
                } catch (error) {
                    showStatus('登录状态已过期，请重新登录', 'error');
                    authClient.logout();
                }
            }
        });

        // 切换选项卡
        function switchTab(tabName) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(`${tabName}-tab`).classList.add('active');
        }

        function switchUserTab(tabName) {
            document.querySelectorAll('#user-section .tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('#user-section .tab-content').forEach(content => content.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(`${tabName}-tab`).classList.add('active');
        }

        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.classList.remove('hidden');
            
            setTimeout(() => {
                statusEl.classList.add('hidden');
            }, 5000);
        }

        // 显示认证区域
        function showAuthSection() {
            document.getElementById('auth-section').classList.remove('hidden');
            document.getElementById('user-section').classList.add('hidden');
        }

        // 显示用户区域
        function showUserSection() {
            document.getElementById('auth-section').classList.add('hidden');
            document.getElementById('user-section').classList.remove('hidden');
        }

        // 登录表单处理
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const credentials = {
                identifier: document.getElementById('login-identifier').value,
                password: document.getElementById('login-password').value,
                mfaCode: document.getElementById('mfa-code').value || null,
                rememberMe: document.getElementById('remember-me').checked,
            };

            try {
                const result = await authClient.login(credentials);
                
                if (result.success) {
                    showStatus('登录成功！', 'success');
                    await loadUserInfo();
                    showUserSection();
                } else {
                    showStatus(result.message || '登录失败', 'error');
                }
            } catch (error) {
                showStatus('网络错误，请稍后重试', 'error');
            }
        });

        // 注册表单处理
        document.getElementById('register-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const password = document.getElementById('register-password').value;
            const confirmPassword = document.getElementById('register-confirm-password').value;
            
            if (password !== confirmPassword) {
                showStatus('密码确认不匹配', 'error');
                return;
            }

            const userData = {
                username: document.getElementById('register-username').value,
                email: document.getElementById('register-email').value,
                password: password,
                confirmPassword: confirmPassword,
                profile: {
                    firstName: document.getElementById('first-name').value,
                    lastName: document.getElementById('last-name').value,
                    language: 'zh',
                },
                acceptTerms: document.getElementById('accept-terms').checked,
            };

            try {
                const result = await authClient.register(userData);
                
                if (result.success) {
                    showStatus('注册成功！请登录', 'success');
                    switchTab('login');
                } else {
                    showStatus(result.message || '注册失败', 'error');
                }
            } catch (error) {
                showStatus('网络错误，请稍后重试', 'error');
            }
        });

        // 加载用户信息
        async function loadUserInfo() {
            try {
                const profile = await authClient.getProfile();
                const mfaStatus = await authClient.getMfaStatus();
                
                if (profile.success) {
                    const user = profile.data.user;
                    document.getElementById('user-username').textContent = user.username;
                    document.getElementById('user-email').textContent = user.email;
                    document.getElementById('user-roles').textContent = user.roles?.join(', ') || '无';
                    document.getElementById('mfa-status').textContent = mfaStatus.data.enabled ? '已启用' : '未启用';
                    
                    // 更新MFA界面
                    if (mfaStatus.data.enabled) {
                        document.getElementById('mfa-disabled').classList.add('hidden');
                        document.getElementById('mfa-enabled').classList.remove('hidden');
                    } else {
                        document.getElementById('mfa-disabled').classList.remove('hidden');
                        document.getElementById('mfa-enabled').classList.add('hidden');
                    }
                }
            } catch (error) {
                throw error;
            }
        }

        // MFA相关函数
        async function setupMFA() {
            try {
                const result = await authClient.setupMfa();
                
                if (result.success) {
                    mfaSetupData = result.data;
                    
                    // 显示QR码（这里简化为显示URL）
                    document.getElementById('qr-code-container').innerHTML = 
                        `<img src="${result.data.qrCodeUrl}" alt="QR Code" style="max-width: 200px;">`;
                    document.getElementById('manual-key').textContent = result.data.manualEntryKey;
                    
                    document.getElementById('mfa-disabled').classList.add('hidden');
                    document.getElementById('mfa-setup-process').classList.remove('hidden');
                } else {
                    showStatus(result.message || 'MFA设置失败', 'error');
                }
            } catch (error) {
                showStatus('网络错误，请稍后重试', 'error');
            }
        }

        async function enableMFA() {
            const code = document.getElementById('totp-code').value;
            
            if (!code || code.length !== 6) {
                showStatus('请输入6位验证码', 'error');
                return;
            }

            try {
                const result = await authClient.enableMfa(mfaSetupData.secret, code);
                
                if (result.success) {
                    showStatus('MFA启用成功！', 'success');
                    
                    // 显示备用码
                    const backupCodesList = document.getElementById('backup-codes-list');
                    backupCodesList.innerHTML = '';
                    result.data.backupCodes.forEach(code => {
                        const li = document.createElement('li');
                        li.textContent = code;
                        backupCodesList.appendChild(li);
                    });
                    document.getElementById('backup-codes-display').classList.remove('hidden');
                    
                    setTimeout(() => {
                        document.getElementById('mfa-setup-process').classList.add('hidden');
                        document.getElementById('mfa-enabled').classList.remove('hidden');
                        loadUserInfo();
                    }, 5000);
                } else {
                    showStatus(result.message || 'MFA启用失败', 'error');
                }
            } catch (error) {
                showStatus('网络错误，请稍后重试', 'error');
            }
        }

        function cancelMFASetup() {
            document.getElementById('mfa-setup-process').classList.add('hidden');
            document.getElementById('mfa-disabled').classList.remove('hidden');
            mfaSetupData = null;
        }

        // 会话管理
        async function loadSessions() {
            try {
                const result = await authClient.getSessions();
                
                if (result.success) {
                    const sessionsList = document.getElementById('sessions-list');
                    sessionsList.innerHTML = '<h4>活跃会话</h4>';
                    
                    result.data.sessions.forEach(session => {
                        const sessionDiv = document.createElement('div');
                        sessionDiv.style.cssText = 'border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;';
                        sessionDiv.innerHTML = `
                            <p><strong>设备:</strong> ${session.deviceInfo?.name || '未知'}</p>
                            <p><strong>IP:</strong> ${session.ipAddress}</p>
                            <p><strong>最后活动:</strong> ${new Date(session.lastActivity).toLocaleString()}</p>
                            <button onclick="terminateSession('${session.id}')" class="btn btn-secondary" style="margin-top: 10px;">终止会话</button>
                        `;
                        sessionsList.appendChild(sessionDiv);
                    });
                }
            } catch (error) {
                showStatus('加载会话失败', 'error');
            }
        }

        // 登出
        function logout() {
            authClient.logout();
            showStatus('已登出', 'info');
        }

        // 其他功能的占位符函数
        function updateProfile() {
            showStatus('个人资料更新功能开发中...', 'info');
        }

        function changePassword() {
            showStatus('修改密码功能开发中...', 'info');
        }

        function regenerateBackupCodes() {
            showStatus('重新生成备用码功能开发中...', 'info');
        }

        function disableMFA() {
            showStatus('禁用MFA功能开发中...', 'info');
        }

        function terminateSession(sessionId) {
            showStatus('终止会话功能开发中...', 'info');
        }

        function logoutAllDevices() {
            showStatus('登出所有设备功能开发中...', 'info');
        }
    </script>
</body>
</html>
