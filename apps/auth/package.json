{"name": "@football-manager/auth-service", "version": "1.0.0", "description": "足球经理认证服务 - 提供用户认证、授权和会话管理", "author": "Football Manager Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "setup": "ts-node scripts/setup.ts", "init-data": "ts-node scripts/init-data.ts", "seed:permissions": "ts-node scripts/seed-permissions.ts", "seed:roles": "ts-node scripts/seed-roles.ts", "cleanup:sessions": "ts-node scripts/cleanup-sessions.ts", "cleanup:audit-logs": "ts-node scripts/cleanup-audit-logs.ts", "test:performance": "ts-node scripts/performance-test.ts", "test:security": "npm run test -- --testPathPattern=security", "test:unit": "npm run test -- --testPathPattern=spec.ts", "test:integration": "npm run test:e2e", "examples": "node examples/run-examples.js", "examples:basic": "node examples/basic-usage/user-registration.js && node examples/basic-usage/user-login.js", "examples:mfa": "node examples/mfa-integration/totp-setup.js", "examples:roles": "node examples/role-permissions/game-roles.js", "examples:web": "open examples/client-integration/web-client.html"}, "dependencies": {"@nestjs/cache-manager": "^2.1.1", "passport-google-oauth20": "^2.0.0", "express-rate-limit": "^7.1.5", "cache-manager-redis-store": "^3.0.1", "nodemailer": "^6.9.7", "twilio": "^4.19.3", "nest-winston": "^1.9.4"}, "devDependencies": {"@types/express": "^4.17.17", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/passport-google-oauth20": "^2.0.14", "@types/nodemailer": "^6.4.14", "mongodb-memory-server": "^9.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}