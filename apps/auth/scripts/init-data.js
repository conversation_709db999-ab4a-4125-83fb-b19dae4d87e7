/**
 * 基础数据初始化脚本
 * 创建基础权限、角色和管理员用户
 */

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const dotenv = require('dotenv');
const database = require('./utils/database');
const config = require('./utils/config');
const Logger = require('./utils/logger');

// 加载环境变量
dotenv.config();

const logger = new Logger('InitData');

// 用户状态枚举
const UserStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
  DELETED: 'deleted'
};

// 定义数据模型Schema
const permissionSchema = new mongoose.Schema({
  name: { type: String, unique: true, sparse: true }, // 添加name字段
  resource: { type: String, required: true },
  action: { type: String, required: true },
  displayName: { type: String, required: true },
  description: String,
  category: String,
  dangerous: { type: Boolean, default: false },
  level: { type: Number, default: 1 },
  enabled: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 添加复合索引
permissionSchema.index({ resource: 1, action: 1 }, { unique: true });

const roleSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true },
  displayName: { type: String, required: true },
  description: String,
  permissions: [String],
  system: { type: Boolean, default: false },
  category: String,
  priority: { type: Number, default: 0 },
  color: String,
  icon: String,
  enabled: { type: Boolean, default: true },
  inherits: [String],
  constraints: mongoose.Schema.Types.Mixed,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const userSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  passwordHash: { type: String, required: true },
  salt: String,
  status: { type: String, enum: Object.values(UserStatus), default: UserStatus.ACTIVE },
  emailVerified: { type: Boolean, default: false },
  roles: [String],
  permissions: [String],
  profile: {
    firstName: String,
    lastName: String,
    language: { type: String, default: 'zh' },
    avatar: String,
    timezone: String,
    dateFormat: String
  },
  security: {
    mfaEnabled: { type: Boolean, default: false },
    mfaSecret: String,
    backupCodes: [String],
    lastPasswordChange: Date,
    passwordHistory: [String],
    loginAttempts: { type: Number, default: 0 },
    lockedUntil: Date,
    lastLogin: Date,
    lastLoginIp: String,
    trustedDevices: [String]
  },
  preferences: {
    notifications: {
      email: { type: Boolean, default: true },
      sms: { type: Boolean, default: false },
      push: { type: Boolean, default: true }
    },
    privacy: {
      profileVisible: { type: Boolean, default: true },
      activityVisible: { type: Boolean, default: false }
    }
  },
  metadata: {
    registrationIp: String,
    registrationUserAgent: String,
    source: String,
    referrer: String
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 基础权限数据
const basePermissions = [
  // 用户权限
  { resource: 'user', action: 'create', displayName: '创建用户', category: 'user_management' },
  { resource: 'user', action: 'read', displayName: '查看用户', category: 'user_management' },
  { resource: 'user', action: 'update', displayName: '更新用户', category: 'user_management' },
  { resource: 'user', action: 'delete', displayName: '删除用户', category: 'user_management' },
  { resource: 'user', action: 'list', displayName: '用户列表', category: 'user_management' },
  
  // 角色权限
  { resource: 'role', action: 'create', displayName: '创建角色', category: 'role_management' },
  { resource: 'role', action: 'read', displayName: '查看角色', category: 'role_management' },
  { resource: 'role', action: 'update', displayName: '更新角色', category: 'role_management' },
  { resource: 'role', action: 'delete', displayName: '删除角色', category: 'role_management' },
  { resource: 'role', action: 'list', displayName: '角色列表', category: 'role_management' },
  
  // 权限管理
  { resource: 'permission', action: 'create', displayName: '创建权限', category: 'permission_management' },
  { resource: 'permission', action: 'read', displayName: '查看权限', category: 'permission_management' },
  { resource: 'permission', action: 'update', displayName: '更新权限', category: 'permission_management' },
  { resource: 'permission', action: 'delete', displayName: '删除权限', category: 'permission_management' },
  { resource: 'permission', action: 'list', displayName: '权限列表', category: 'permission_management' },
  
  // 游戏权限
  { resource: 'game', action: 'play', displayName: '参与游戏', category: 'game' },
  { resource: 'game', action: 'manage', displayName: '管理游戏', category: 'game' },
  { resource: 'team', action: 'create', displayName: '创建团队', category: 'game' },
  { resource: 'team', action: 'join', displayName: '加入团队', category: 'game' },
  { resource: 'team', action: 'manage', displayName: '管理团队', category: 'game' },
  
  // 系统权限
  { resource: 'system', action: 'admin', displayName: '系统管理', category: 'system', dangerous: true, level: 10 },
  { resource: 'audit', action: 'read', displayName: '查看审计日志', category: 'system' },
  { resource: 'security', action: 'manage', displayName: '安全管理', category: 'system', dangerous: true, level: 9 },
];

// 基础角色数据
const baseRoles = [
  {
    name: 'super_admin',
    displayName: '超级管理员',
    description: '拥有系统所有权限的超级管理员',
    permissions: ['*'],
    system: true,
    category: 'system',
    priority: 100,
    color: '#F44336'
  },
  {
    name: 'admin',
    displayName: '管理员',
    description: '系统管理员，拥有大部分管理权限',
    permissions: [
      'user:create', 'user:read', 'user:update', 'user:delete', 'user:list',
      'role:create', 'role:read', 'role:update', 'role:delete', 'role:list',
      'permission:read', 'permission:list',
      'audit:read'
    ],
    system: true,
    category: 'system',
    priority: 90,
    color: '#FF9800'
  },
  {
    name: 'moderator',
    displayName: '版主',
    description: '内容管理员，负责游戏内容管理',
    permissions: [
      'user:read', 'user:update', 'user:list',
      'game:manage',
      'team:manage'
    ],
    system: true,
    category: 'moderation',
    priority: 70,
    color: '#2196F3'
  },
  {
    name: 'player',
    displayName: '球员',
    description: '游戏玩家，可以参与游戏和管理自己的团队',
    permissions: [
      'game:play',
      'team:create',
      'team:join'
    ],
    system: true,
    category: 'game',
    priority: 30,
    color: '#4CAF50'
  },
  {
    name: 'user',
    displayName: '普通用户',
    description: '基础用户角色',
    permissions: [],
    system: true,
    category: 'basic',
    priority: 10,
    color: '#9E9E9E'
  }
];

/**
 * 哈希密码
 */
async function hashPassword(password) {
  const saltRounds = config.get('security.bcryptRounds', 12);
  const salt = await bcrypt.genSalt(saltRounds);
  const hash = await bcrypt.hash(password, salt);
  return { hash, salt };
}

/**
 * 创建权限
 */
async function createPermissions() {
  logger.subtitle('创建基础权限...');

  // 检查模型是否已存在
  let Permission;
  try {
    Permission = mongoose.model('Permission');
  } catch (error) {
    Permission = mongoose.model('Permission', permissionSchema);
  }

  let createdCount = 0;
  let skippedCount = 0;

  for (const permData of basePermissions) {
    try {
      const existing = await Permission.findOne({
        resource: permData.resource,
        action: permData.action
      });

      if (existing) {
        skippedCount++;
        continue;
      }

      // 添加name字段
      const permissionData = {
        ...permData,
        name: `${permData.resource}:${permData.action}`
      };

      await Permission.create(permissionData);
      createdCount++;
      logger.log(`权限创建成功: ${permData.resource}:${permData.action}`);
    } catch (error) {
      logger.warn(`权限创建失败: ${permData.resource}:${permData.action} - ${error.message}`);
    }
  }

  logger.success(`权限创建完成: 新建 ${createdCount} 个, 跳过 ${skippedCount} 个`);
}

/**
 * 创建角色
 */
async function createRoles() {
  logger.subtitle('创建基础角色...');

  // 检查模型是否已存在
  let Role;
  try {
    Role = mongoose.model('Role');
  } catch (error) {
    Role = mongoose.model('Role', roleSchema);
  }

  let createdCount = 0;
  let skippedCount = 0;

  for (const roleData of baseRoles) {
    try {
      const existing = await Role.findOne({ name: roleData.name });

      if (existing) {
        skippedCount++;
        continue;
      }

      await Role.create(roleData);
      createdCount++;
      logger.log(`角色创建成功: ${roleData.name}`);
    } catch (error) {
      logger.warn(`角色创建失败: ${roleData.name} - ${error.message}`);
    }
  }

  logger.success(`角色创建完成: 新建 ${createdCount} 个, 跳过 ${skippedCount} 个`);
}

/**
 * 创建管理员用户
 */
async function createAdminUser() {
  logger.subtitle('创建默认管理员用户...');

  // 检查模型是否已存在
  let User;
  try {
    User = mongoose.model('User');
  } catch (error) {
    User = mongoose.model('User', userSchema);
  }

  const adminData = {
    username: 'admin',
    email: '<EMAIL>',
    password: 'Admin123!@#'
  };

  try {
    const existing = await User.findOne({
      $or: [
        { username: adminData.username },
        { email: adminData.email }
      ]
    });

    if (existing) {
      logger.warn('管理员用户已存在，跳过创建');
      return;
    }

    const { hash, salt } = await hashPassword(adminData.password);

    const userData = {
      username: adminData.username,
      email: adminData.email,
      passwordHash: hash,
      salt,
      status: UserStatus.ACTIVE,
      emailVerified: true,
      roles: ['super_admin'],
      permissions: [],
      profile: {
        firstName: 'System',
        lastName: 'Administrator',
        language: 'zh'
      },
      security: {
        mfaEnabled: false,
        lastPasswordChange: new Date(),
        passwordHistory: [hash],
        loginAttempts: 0
      },
      metadata: {
        registrationIp: '127.0.0.1',
        source: 'system_init'
      }
    };

    const user = await User.create(userData);
    logger.success(`管理员用户创建成功: ${user.username} (${user._id})`);
  } catch (error) {
    logger.error('创建管理员用户失败:', error);
    throw error;
  }
}

/**
 * 主初始化函数
 */
async function initializeData() {
  try {
    logger.start('开始初始化基础数据...');
    
    // 连接数据库
    await database.connect();
    
    // 创建基础权限
    await createPermissions();
    
    // 创建基础角色
    await createRoles();
    
    // 创建管理员用户
    await createAdminUser();
    
    logger.complete('基础数据初始化完成！');
    
    // 显示初始化结果
    logger.separator();
    logger.info('🎯 系统已准备就绪，包含以下功能:');
    logger.log('   • 完整的用户认证和授权系统');
    logger.log('   • 基于角色的权限控制 (RBAC)');
    logger.log('   • 安全审计和风险评估');
    logger.log('   • 会话管理和JWT令牌');
    logger.log('   • 足球经理游戏专用权限和角色');
    logger.log('');
    logger.info('🔑 默认管理员账户:');
    logger.log('   用户名: admin');
    logger.log('   邮箱: <EMAIL>');
    logger.log('   密码: Admin123!@#');
    logger.separator();
    
  } catch (error) {
    logger.error('基础数据初始化失败:', error);
    throw error;
  } finally {
    await database.disconnect();
  }
}

// 运行初始化
if (require.main === module) {
  initializeData().catch((error) => {
    console.error('初始化失败:', error);
    process.exit(1);
  });
}

module.exports = { initializeData };
