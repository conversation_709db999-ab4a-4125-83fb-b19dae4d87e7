import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { MongooseModule } from '@nestjs/mongoose';

import { Auth<PERSON>ontroller } from './auth.controller';
import { MfaController } from './mfa.controller';
import { AuthService } from './auth.service';
import { JwtService } from './jwt.service';
import { MfaService } from './mfa.service';

import { UsersModule } from '../users/users.module';
import { SessionModule } from '../../core/session/session.module';
import { SecurityModule } from '../../core/security/security.module';
import { CoreModule } from '../../core/shared/core.module';

// 新增：角色认证模块
import { CharacterAuthModule } from '../character-auth/character-auth.module';

// 新增：用户历史模块
import { UserHistoryModule } from '../user-history/user-history.module';

import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';

@Module({
  imports: [
    ConfigModule,
    
    // Passport 模块
    PassportModule.register({ 
      defaultStrategy: 'jwt',
      session: false 
    }),
    
    // JWT 模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('auth.jwt.secret'),
        signOptions: {
          expiresIn: configService.get<string>('auth.jwt.accessTokenTTL'),
          issuer: configService.get<string>('auth.jwt.issuer'),
          audience: configService.get<string>('auth.jwt.audience'),
          algorithm: configService.get<string>('auth.jwt.algorithm') as any,
        },
      }),
      inject: [ConfigService],
    }),
    
    // 依赖模块
    CoreModule,
    UsersModule,
    SessionModule,
    SecurityModule,

    // 新增：角色认证模块
    CharacterAuthModule,

    // 新增：用户历史模块
    UserHistoryModule,
  ],
  controllers: [AuthController, MfaController],
  providers: [
    AuthService,
    JwtService,
    MfaService,
    JwtStrategy,
    LocalStrategy,
  ],
  exports: [
    AuthService,
    JwtService,
    MfaService,
    PassportModule,
    JwtModule,

    // 新增：导出角色认证模块
    CharacterAuthModule,

    // 新增：导出用户历史模块
    UserHistoryModule,
  ],
})
export class AuthModule {}
