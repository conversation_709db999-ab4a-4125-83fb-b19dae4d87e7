import { ApiProperty } from '@nestjs/swagger';

/**
 * 角色信息DTO
 */
export class CharacterInfoDto {
  @ApiProperty({ description: '角色ID', example: 'char_123456' })
  characterId: string;

  @ApiProperty({ description: '角色名称', example: '勇敢的冒险者' })
  name: string;

  @ApiProperty({ description: '角色等级', example: 25 })
  level: number;

  @ApiProperty({ description: '所属区服ID', example: 'server_001' })
  serverId: string;

  @ApiProperty({ description: '所属用户ID', example: 'user_123456' })
  userId: string;
}

/**
 * 区服信息DTO
 */
export class ServerInfoDto {
  @ApiProperty({ description: '区服ID', example: 'server_001' })
  id: string;

  @ApiProperty({ description: '区服名称', example: '新手村' })
  name: string;

  @ApiProperty({ description: '区服状态', example: 'active' })
  status: string;

  @ApiProperty({ description: '开服时间', example: '2024-01-01T00:00:00.000Z' })
  openTime: Date;

  @ApiProperty({ description: '最大玩家数量', example: 10000 })
  maxPlayers: number;
}

/**
 * 会话信息DTO
 */
export class SessionInfoDto {
  @ApiProperty({ description: '会话ID', example: 'session_123456789' })
  id: string;

  @ApiProperty({ description: '会话过期时间', example: '2024-01-15T14:30:00.000Z' })
  expiresAt: Date;
}

/**
 * 角色登录数据DTO
 */
export class CharacterLoginDataDto {
  @ApiProperty({ description: '角色级Token', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' })
  characterToken: string;

  @ApiProperty({ description: 'Token有效期（秒）', example: 14400 })
  expiresIn: number;

  @ApiProperty({ description: 'Token过期时间', example: '2024-01-15T14:30:00.000Z' })
  expiresAt: Date;

  @ApiProperty({ description: '角色信息', type: CharacterInfoDto })
  character: CharacterInfoDto;

  @ApiProperty({ description: '区服信息', type: ServerInfoDto })
  server: ServerInfoDto;

  @ApiProperty({ description: '会话信息', type: SessionInfoDto })
  session: SessionInfoDto;
}

/**
 * 角色登录响应DTO
 */
export class CharacterLoginResponseDto {
  @ApiProperty({ description: '请求是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '进入区服成功' })
  message: string;

  @ApiProperty({ description: '角色登录数据', type: CharacterLoginDataDto })
  data: CharacterLoginDataDto;
}
