import { IsString, IsOptional, IsNotEmpty, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 角色登录请求DTO
 */
export class CharacterLoginDto {
  @ApiProperty({
    description: '区服ID',
    example: 'server_001',
    minLength: 1,
    maxLength: 50,
  })
  @IsString({ message: '区服ID必须为字符串' })
  @IsNotEmpty({ message: '区服ID不能为空' })
  @Length(1, 50, { message: '区服ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({
    description: '角色ID（可选，不指定则使用默认角色）',
    example: 'char_123456',
    required: false,
    minLength: 1,
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: '角色ID必须为字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId?: string;
}
