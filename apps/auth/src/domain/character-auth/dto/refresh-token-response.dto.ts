import { ApiProperty } from '@nestjs/swagger';

/**
 * 刷新Token数据DTO
 */
export class RefreshTokenDataDto {
  @ApiProperty({ description: '新的角色Token', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' })
  characterToken: string;

  @ApiProperty({ description: 'Token类型', example: 'Bearer' })
  tokenType: string;

  @ApiProperty({ description: 'Token有效期（秒）', example: 14400 })
  expiresIn: number;
}

/**
 * 刷新Token响应DTO
 */
export class RefreshTokenResponseDto {
  @ApiProperty({ description: '请求是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '角色Token刷新成功' })
  message: string;

  @ApiProperty({ description: '刷新Token数据', type: RefreshTokenDataDto })
  data: RefreshTokenDataDto;
}
