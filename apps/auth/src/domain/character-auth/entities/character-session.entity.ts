import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';



/**
 * 角色会话实体
 * 管理角色在特定区服的会话状态
 */
@Schema({
  timestamps: true,
  collection: 'character_sessions',
})
export class CharacterSession {
  @ApiProperty({ description: '会话ID' })
  id?: string;

  @ApiProperty({ description: '用户ID' })
  @Prop({ 
    required: true, 
    index: true,
    comment: '关联的用户ID'
  })
  userId: string;

  @ApiProperty({ description: '角色ID' })
  @Prop({ 
    required: true, 
    index: true,
    comment: '当前会话的角色ID'
  })
  characterId: string;

  @ApiProperty({ description: '服务器ID' })
  @Prop({ 
    required: true, 
    index: true,
    comment: '当前所在的服务器ID'
  })
  serverId: string;

  @ApiProperty({ description: '服务器名称' })
  @Prop({ 
    required: true,
    comment: '服务器显示名称'
  })
  serverName: string;

  @ApiProperty({ description: '设备信息' })
  @Prop({ 
    required: false,
    comment: '客户端设备信息'
  })
  deviceInfo?: string;

  @ApiProperty({ description: 'WebSocket连接ID' })
  @Prop({ 
    required: false,
    comment: '当前WebSocket连接的ID'
  })
  socketId?: string;

  @ApiProperty({ description: 'IP地址' })
  @Prop({ 
    required: false,
    comment: '客户端IP地址'
  })
  ipAddress?: string;

  @ApiProperty({ description: '用户代理' })
  @Prop({ 
    required: false,
    comment: '客户端User-Agent'
  })
  userAgent?: string;

  @ApiProperty({ description: '会话过期时间' })
  @Prop({ 
    required: true,
    index: true,
    comment: '会话过期时间'
  })
  expiresAt: Date;

  @ApiProperty({ description: '最后活动时间' })
  @Prop({ 
    required: true,
    default: Date.now,
    comment: '最后一次活动时间'
  })
  lastActivity: Date;

  @ApiProperty({ description: '会话是否活跃' })
  @Prop({ 
    required: true,
    default: true,
    index: true,
    comment: '会话状态：true=活跃，false=已结束'
  })
  active: boolean;

  @ApiProperty({ description: '创建时间' })
  createdAt?: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt?: Date;
}

export const CharacterSessionSchema = SchemaFactory.createForClass(CharacterSession);

// 创建复合索引
CharacterSessionSchema.index({ userId: 1, serverId: 1 });
CharacterSessionSchema.index({ characterId: 1, serverId: 1 });
CharacterSessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL索引
CharacterSessionSchema.index({ active: 1, expiresAt: 1 });

// 虚拟字段
CharacterSessionSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// 确保虚拟字段被序列化
CharacterSessionSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

// 中间件：保存前验证
CharacterSessionSchema.pre('save', function(next) {
  // 确保过期时间在未来
  if (this.expiresAt <= new Date()) {
    next(new Error('会话过期时间必须在未来'));
    return;
  }

  // 更新最后活动时间
  if (this.isModified() && !this.isNew) {
    this.lastActivity = new Date();
  }

  next();
});

// 静态方法：查找活跃会话
CharacterSessionSchema.statics.findActiveSessions = function(userId: string, serverId?: string) {
  const query: any = {
    userId,
    active: true,
    expiresAt: { $gt: new Date() },
  };

  if (serverId) {
    query.serverId = serverId;
  }

  return this.find(query).sort({ lastActivity: -1 });
};

// 实例方法：检查会话是否有效
CharacterSessionSchema.methods.isValid = function(): boolean {
  return this.active && this.expiresAt > new Date();
};

// 实例方法：延长会话
CharacterSessionSchema.methods.extend = function(additionalSeconds: number = 14400): void {
  this.expiresAt = new Date(Date.now() + additionalSeconds * 1000);
  this.lastActivity = new Date();
};

// 实例方法：终止会话
CharacterSessionSchema.methods.terminate = function(): void {
  this.active = false;
  this.lastActivity = new Date();
};

// 扩展CharacterSessionDocument接口以包含自定义方法
export interface CharacterSessionDocument extends Omit<CharacterSession, 'id'>, Document {
  isValid(): boolean;
  extend(additionalSeconds?: number): void;
  terminate(): void;
}
