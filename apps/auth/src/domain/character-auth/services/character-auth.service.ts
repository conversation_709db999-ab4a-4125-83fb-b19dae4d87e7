import { Injectable, Logger, NotFoundException, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 核心服务
import { RedisService } from '@common/redis';
import { UsersService } from '../../users/users.service';

// 角色会话服务
import { CharacterSessionService } from './character-session.service';

// 用户历史服务
import { UserHistorySyncService } from '../../user-history/services/user-history-sync.service';

// 类型定义
import { JwtPayload, CharacterTokenPair,JwtService } from '../../auth/jwt.service';



/**
 * 角色认证服务
 * 负责角色登录、角色登出、角色会话管理等核心功能
 */
@Injectable()
export class CharacterAuthService {
  private readonly logger = new Logger(CharacterAuthService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly characterSessionService: CharacterSessionService,
    private readonly userHistorySyncService: UserHistorySyncService,
  ) {}

  /**
   * 生成角色Token（纯认证逻辑）
   *
   * 注意：此方法不验证角色存在性，由调用方（网关层）保证角色信息的正确性
   *
   * @param tokenRequest 角色Token生成请求
   * @returns 角色Token响应
   */
  async generateCharacterToken(tokenRequest: {
    userId: string;
    characterId: string;
    serverId: string;
    characterName: string;
    sessionData?: any;
  }): Promise<CharacterLoginResponse> {
    this.logger.log(`🔑 生成角色Token: userId=${tokenRequest.userId}, characterId=${tokenRequest.characterId}`);

    try {
      // 1. 验证用户存在（基础验证）
      const user = await this.usersService.findById(tokenRequest.userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 2. 检查现有会话并终止
      await this.terminateExistingCharacterSessions(tokenRequest.userId);

      // 3. 创建角色会话
      const session = await this.characterSessionService.createSession({
        userId: tokenRequest.userId,
        characterId: tokenRequest.characterId,
        serverId: tokenRequest.serverId,
        serverName: `区服${tokenRequest.serverId}`, // 默认值
        deviceInfo: tokenRequest.sessionData?.deviceInfo,
        expiresAt: new Date(Date.now() + 4 * 3600 * 1000), // 4小时后过期
      });

      // 4. 生成角色Token（纯认证逻辑）
      const accountPayload = {
        sub: tokenRequest.userId,
        username: user.username,
        email: user.email,
        roles: user.roles || [],
        permissions: user.permissions || [],
        sessionId: session.id,
      };

      const characterTokenPair = this.jwtService.generateCharacterTokenPair(
        accountPayload,
        {
          characterId: tokenRequest.characterId,
          serverId: tokenRequest.serverId,
          sessionId: session.id,
        },
      );

      // 5. 记录登录历史
      await this.recordCharacterLogin(
        tokenRequest.userId,
        tokenRequest.serverId,
        tokenRequest.characterId
      );

      // 6. 同步用户历史记录（使用提供的角色信息）
      await this.userHistorySyncService.syncOnCharacterLogin(
        tokenRequest.userId,
        tokenRequest.serverId,
        `区服${tokenRequest.serverId}`,
        tokenRequest.characterId,
        {
          characterId: tokenRequest.characterId,
          name: tokenRequest.characterName,
          userId: tokenRequest.userId,
          serverId: tokenRequest.serverId,
        }
      );

      this.logger.log(`✅ 角色Token生成成功: characterId=${tokenRequest.characterId}`);

      return {
        characterToken: characterTokenPair.characterToken,
        expiresIn: characterTokenPair.expiresIn,
        expiresAt: characterTokenPair.expiresAt,
        character: {
          characterId: tokenRequest.characterId,
          name: tokenRequest.characterName,
          level: 1, // 默认值，实际值由网关层提供
          serverId: tokenRequest.serverId,
          userId: tokenRequest.userId,
        },
        server: {
          id: tokenRequest.serverId,
          name: `区服${tokenRequest.serverId}`, // 默认值，实际值由网关层提供
          status: 'active',
          openTime: new Date('2024-01-01'),
          maxPlayers: 10000,
        },
        session: {
          id: session.id,
          expiresAt: session.expiresAt,
        },
      };
    } catch (error) {
      this.logger.error(`❌ 角色Token生成失败: characterId=${tokenRequest.characterId}`, error);
      throw error;
    }
  }



  /**
   * 角色登出 (Character Logout)
   * 使用角色Token → 清理会话和状态
   */
  async characterLogout(characterToken: string): Promise<void> {
    this.logger.log('角色登出请求');

    try {
      const payload = await this.jwtService.verifyCharacterToken(characterToken);

      // 1. 终止角色会话
      await this.characterSessionService.terminateSession(payload.sessionId);

      // 2. 记录角色登出历史
      await this.recordCharacterLogout(payload.sub, payload.serverId, payload.characterId);

      // 3. 同步用户历史记录
      await this.userHistorySyncService.syncOnCharacterLogout(
        payload.sub,
        payload.serverId,
        payload.characterId,
        0 // TODO: 计算实际会话时长
      );

      // 4. 清理相关缓存
      await this.clearCharacterCache(payload.characterId);

      this.logger.log(`角色登出成功: ${payload.sub} <- ${payload.serverId}:${payload.characterId}`);
    } catch (error) {
      this.logger.error(`角色登出失败: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * 刷新角色Token (Refresh Character Token)
   * 延长当前角色会话的有效期
   */
  async refreshCharacterToken(currentToken: string): Promise<string> {
    this.logger.log('刷新角色Token请求');

    try {
      const payload = await this.jwtService.verifyCharacterToken(currentToken);

      // 1. 验证会话是否仍然有效
      const session = await this.characterSessionService.getSession(payload.sessionId);
      if (!session || !(session as any).isValid()) {
        throw new UnauthorizedException('会话已过期，无法刷新Token');
      }

      // 2. 延长会话时间
      await this.characterSessionService.extendSession(payload.sessionId, 4 * 3600); // 延长4小时

      // 3. 生成新的角色Token（符合设计文档规范）
      const newToken = this.jwtService.generateCharacterToken({
        sub: payload.sub,
        username: payload.username,
        email: payload.email,
        roles: payload.roles,
        permissions: payload.permissions,
        sessionId: payload.sessionId,
        deviceId: payload.deviceId,
        characterId: payload.characterId,
        serverId: payload.serverId,
        scope: 'character', // 符合设计文档规范
      });

      this.logger.log(`角色Token刷新成功: ${payload.characterId}`);
      return newToken;

    } catch (error) {
      this.logger.error(`刷新角色Token失败: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * 获取当前会话信息
   */
  async getCurrentSession(characterToken: string): Promise<CharacterSessionInfo> {
    this.logger.log('获取当前会话信息');

    try {
      const payload = await this.jwtService.verifyCharacterToken(characterToken);
      const session = await this.characterSessionService.getSession(payload.sessionId);

      if (!session || !(session as any).isValid()) {
        throw new UnauthorizedException('会话已过期');
      }

      return {
        sessionId: session.id,
        userId: session.userId,
        characterId: session.characterId,
        serverId: session.serverId,
        serverName: session.serverName,
        lastActivity: session.lastActivity,
        expiresAt: session.expiresAt,
      };
    } catch (error) {
      this.logger.error(`获取会话信息失败: ${error.message}`, error);
      throw error;
    }
  }







  /**
   * 私有方法：终止现有角色会话
   */
  private async terminateExistingCharacterSessions(userId: string): Promise<void> {
    try {
      await this.characterSessionService.terminateUserSessions(userId);
    } catch (error) {
      this.logger.warn(`终止现有会话失败: ${userId}`, error);
      // 不抛出错误，允许继续创建新会话
    }
  }

  /**
   * 私有方法：记录角色登录历史
   */
  private async recordCharacterLogin(userId: string, serverId: string, characterId: string): Promise<void> {
    try {
      const loginRecord = {
        userId,
        serverId,
        characterId,
        loginTime: new Date(),
        action: 'character_login',
      };

      // 记录到Redis（用于快速查询）
      const loginKey = `character:login:${userId}:${serverId}:${characterId}`;
      await this.redisService.set(loginKey, JSON.stringify(loginRecord), 24 * 3600, 'global'); // 24小时

      // 更新用户最后登录信息
      const lastLoginKey = `user:${userId}:last_character_login`;
      await this.redisService.set(lastLoginKey, JSON.stringify({
        serverId,
        characterId,
        loginTime: new Date(),
      }), 7 * 24 * 3600, 'global'); // 7天

      this.logger.debug(`角色登录历史记录完成: ${userId}@${serverId}:${characterId}`);
    } catch (error) {
      this.logger.warn(`记录角色登录历史失败: ${userId}`, error);
      // 不抛出错误，不影响主要登录流程
    }
  }

  /**
   * 私有方法：记录角色登出历史
   */
  private async recordCharacterLogout(userId: string, serverId: string, characterId: string): Promise<void> {
    try {
      const logoutRecord = {
        userId,
        serverId,
        characterId,
        logoutTime: new Date(),
        action: 'character_logout',
      };

      // 记录到Redis
      const logoutKey = `character:logout:${userId}:${serverId}:${characterId}`;
      await this.redisService.set(logoutKey, JSON.stringify(logoutRecord), 24 * 3600, 'global'); // 24小时

      // 清除登录记录
      const loginKey = `character:login:${userId}:${serverId}:${characterId}`;
      await this.redisService.del(loginKey, 'global');

      this.logger.debug(`角色登出历史记录完成: ${userId}@${serverId}:${characterId}`);
    } catch (error) {
      this.logger.warn(`记录角色登出历史失败: ${userId}`, error);
      // 不抛出错误，不影响主要登出流程
    }
  }

  /**
   * 私有方法：清理角色缓存
   */
  private async clearCharacterCache(characterId: string): Promise<void> {
    try {
      // 清理角色相关的缓存数据
      const cacheKeys = [
        `character:info:${characterId}`,
        `character:status:${characterId}`,
        `character:permissions:${characterId}`,
      ];

      const deletePromises = cacheKeys.map(key =>
        this.redisService.del(key, 'global')
      );

      await Promise.allSettled(deletePromises);
      this.logger.debug(`角色缓存清理完成: ${characterId}`);
    } catch (error) {
      this.logger.warn(`清理角色缓存失败: ${characterId}`, error);
      // 不抛出错误，不影响主要业务流程
    }
  }

}

// 类型定义
interface ServerInfo {
  id: string;
  name: string;
  status: 'active' | 'maintenance' | 'closed';
  openTime: Date;
  maxPlayers: number;
}

interface CharacterInfo {
  characterId: string;
  name: string;
  level: number;
  serverId: string;
  userId: string;
}

interface CharacterLoginResponse {
  characterToken: string;
  expiresIn: number;
  expiresAt: Date;
  character: CharacterInfo;
  server: ServerInfo;
  session: {
    id: string;
    expiresAt: Date;
  };
}

interface CharacterSessionInfo {
  sessionId: string;
  userId: string;
  characterId: string;
  serverId: string;
  serverName: string;
  lastActivity: Date;
  expiresAt: Date;
}
