import { 
  <PERSON>, 
  Get, 
  Post,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';

// 守卫和装饰器
import { JwtAuthGuard } from '../../../infrastructure/guards/jwt-auth.guard';
import { CurrentUser } from '../../../common/decorators/current-user.decorator';

// 服务
import { UserHistoryService } from '../services/user-history.service';
import { UserHistorySyncService } from '../services/user-history-sync.service';

// DTO
import { UserHistoryResponseDto } from '../dto/user-history-response.dto';
import { ServerHistoryResponseDto } from '../dto/server-history-response.dto';
import { CharacterSummaryResponseDto } from '../dto/character-summary-response.dto';
import { GlobalStatsResponseDto } from '../dto/global-stats-response.dto';

// 类型
import { User } from '../../users/entities/user.entity';

/**
 * 用户历史控制器
 * 提供用户历史记录查询、统计等HTTP接口
 */
@ApiTags('用户历史')
@Controller('user-history')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserHistoryController {
  private readonly logger = new Logger(UserHistoryController.name);

  constructor(
    private readonly userHistoryService: UserHistoryService,
    private readonly userHistorySyncService: UserHistorySyncService,
  ) {}

  /**
   * 获取用户完整历史记录
   * GET /api/auth/user-history
   */
  @Get()
  @ApiOperation({ 
    summary: '获取用户完整历史记录',
    description: '获取当前用户的完整历史记录，包括区服历史、角色摘要、全局统计等'
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: UserHistoryResponseDto
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getUserHistory(@CurrentUser() user: User): Promise<UserHistoryResponseDto> {
    this.logger.log(`获取用户历史记录: ${user.id}`);

    try {
      const userHistory = await this.userHistoryService.getUserHistory(user.id);
      
      return {
        success: true,
        message: '获取用户历史记录成功',
        data: userHistory.toJSON(),
      };

    } catch (error) {
      this.logger.error(`获取用户历史记录失败: ${user.id}`, error);
      throw error;
    }
  }

  /**
   * 获取用户区服历史
   * GET /api/auth/user-history/servers
   */
  @Get('servers')
  @ApiOperation({ 
    summary: '获取用户区服历史',
    description: '获取用户在各个区服的历史记录'
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: ServerHistoryResponseDto
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getUserServerHistory(@CurrentUser() user: User): Promise<ServerHistoryResponseDto> {
    this.logger.log(`获取用户区服历史: ${user.id}`);

    try {
      const serverHistory = await this.userHistoryService.getUserServerHistory(user.id);
      
      return {
        success: true,
        message: '获取用户区服历史成功',
        data: {
          servers: serverHistory,
          totalServers: serverHistory.length,
        },
      };

    } catch (error) {
      this.logger.error(`获取用户区服历史失败: ${user.id}`, error);
      throw error;
    }
  }

  /**
   * 获取指定区服的历史记录
   * GET /api/auth/user-history/servers/:serverId
   */
  @Get('servers/:serverId')
  @ApiOperation({ 
    summary: '获取指定区服的历史记录',
    description: '获取用户在指定区服的详细历史记录'
  })
  @ApiParam({ name: 'serverId', description: '区服ID' })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: Object
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 404, description: '区服历史不存在' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getUserServerHistoryById(
    @CurrentUser() user: User,
    @Param('serverId') serverId: string
  ): Promise<any> {
    this.logger.log(`获取用户区服历史: ${user.id}@${serverId}`);

    try {
      const serverHistory = await this.userHistoryService.getUserServerHistoryById(user.id, serverId);
      
      if (!serverHistory) {
        return {
          success: false,
          message: '该区服历史记录不存在',
          data: null,
        };
      }

      return {
        success: true,
        message: '获取区服历史记录成功',
        data: serverHistory,
      };

    } catch (error) {
      this.logger.error(`获取用户区服历史失败: ${user.id}@${serverId}`, error);
      throw error;
    }
  }

  /**
   * 获取用户角色摘要
   * GET /api/auth/user-history/characters
   */
  @Get('characters')
  @ApiOperation({ 
    summary: '获取用户角色摘要',
    description: '获取用户的所有角色摘要信息'
  })
  @ApiQuery({ name: 'serverId', required: false, description: '区服ID，不指定则返回所有区服的角色' })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: CharacterSummaryResponseDto
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getUserCharacters(
    @CurrentUser() user: User,
    @Query('serverId') serverId?: string
  ): Promise<CharacterSummaryResponseDto> {
    this.logger.log(`获取用户角色摘要: ${user.id}, serverId=${serverId || 'all'}`);

    try {
      const characters = await this.userHistoryService.getUserCharacters(user.id, serverId);
      
      return {
        success: true,
        message: '获取用户角色摘要成功',
        data: {
          characters,
          totalCharacters: characters.length,
          serverId,
        },
      };

    } catch (error) {
      this.logger.error(`获取用户角色摘要失败: ${user.id}`, error);
      throw error;
    }
  }

  /**
   * 获取用户全局统计
   * GET /api/auth/user-history/stats
   */
  @Get('stats')
  @ApiOperation({ 
    summary: '获取用户全局统计',
    description: '获取用户的全局统计数据'
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: GlobalStatsResponseDto
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getUserGlobalStats(@CurrentUser() user: User): Promise<GlobalStatsResponseDto> {
    this.logger.log(`获取用户全局统计: ${user.id}`);

    try {
      const globalStats = await this.userHistoryService.getUserGlobalStats(user.id);
      
      return {
        success: true,
        message: '获取用户全局统计成功',
        data: globalStats,
      };

    } catch (error) {
      this.logger.error(`获取用户全局统计失败: ${user.id}`, error);
      throw error;
    }
  }

  /**
   * 获取推荐区服
   * GET /api/auth/user-history/recommendations
   */
  @Get('recommendations')
  @ApiOperation({ 
    summary: '获取推荐区服',
    description: '基于用户历史记录获取推荐的区服列表'
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: Object
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getRecommendedServers(@CurrentUser() user: User): Promise<any> {
    this.logger.log(`获取推荐区服: ${user.id}`);

    try {
      // TODO: 这里需要从配置或其他服务获取可用区服列表
      const availableServers = [
        { id: 'server_001', name: '新手村', status: 'active', openTime: '2024-01-01' },
        { id: 'server_002', name: '勇者大陆', status: 'active', openTime: '2024-02-01' },
      ];

      const recommendedServers = await this.userHistoryService.getRecommendedServers(
        user.id, 
        availableServers
      );
      
      return {
        success: true,
        message: '获取推荐区服成功',
        data: {
          servers: recommendedServers,
          totalServers: recommendedServers.length,
        },
      };

    } catch (error) {
      this.logger.error(`获取推荐区服失败: ${user.id}`, error);
      throw error;
    }
  }

  /**
   * 手动同步用户历史数据
   * POST /api/auth/user-history/sync
   */
  @Post('sync')
  @ApiOperation({ 
    summary: '手动同步用户历史数据',
    description: '手动触发用户历史数据的同步更新'
  })
  @ApiResponse({ 
    status: 200, 
    description: '同步成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '用户历史数据同步成功' },
      },
    }
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async manualSync(@CurrentUser() user: User): Promise<any> {
    this.logger.log(`手动同步用户历史数据: ${user.id}`);

    try {
      await this.userHistorySyncService.manualSync(user.id);
      
      return {
        success: true,
        message: '用户历史数据同步成功',
      };

    } catch (error) {
      this.logger.error(`手动同步用户历史数据失败: ${user.id}`, error);
      throw error;
    }
  }

  /**
   * 获取同步状态
   * GET /api/auth/user-history/sync-status
   */
  @Get('sync-status')
  @ApiOperation({ 
    summary: '获取同步状态',
    description: '获取用户历史数据的同步状态信息'
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: Object
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getSyncStatus(): Promise<any> {
    this.logger.log('获取同步状态');

    try {
      const syncStatus = await this.userHistorySyncService.getSyncStatus();
      
      return {
        success: true,
        message: '获取同步状态成功',
        data: syncStatus,
      };

    } catch (error) {
      this.logger.error('获取同步状态失败', error);
      throw error;
    }
  }

  /**
   * 健康检查
   * GET /api/auth/user-history/health
   */
  @Get('health')
  @ApiOperation({ 
    summary: '健康检查',
    description: '检查用户历史服务的健康状态'
  })
  @ApiResponse({ 
    status: 200, 
    description: '服务正常',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', format: 'date-time' },
        service: { type: 'string', example: 'user-history' },
      },
    }
  })
  async healthCheck(): Promise<any> {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'user-history',
    };
  }
}
