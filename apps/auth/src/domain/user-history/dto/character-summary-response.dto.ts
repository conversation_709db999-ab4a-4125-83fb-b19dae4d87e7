import { ApiProperty } from '@nestjs/swagger';

/**
 * 角色摘要数据DTO
 */
export class CharacterSummaryDataDto {
  @ApiProperty({ description: '角色列表', type: Array })
  characters: any[];

  @ApiProperty({ description: '总角色数量', example: 3 })
  totalCharacters: number;

  @ApiProperty({ description: '区服ID（可选）', example: 'server_001', required: false })
  serverId?: string;
}

/**
 * 角色摘要响应DTO
 */
export class CharacterSummaryResponseDto {
  @ApiProperty({ description: '请求是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '获取用户角色摘要成功' })
  message: string;

  @ApiProperty({ description: '角色摘要数据', type: CharacterSummaryDataDto })
  data: CharacterSummaryDataDto;
}
