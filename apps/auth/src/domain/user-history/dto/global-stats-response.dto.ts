import { ApiProperty } from '@nestjs/swagger';

/**
 * 全局统计数据DTO
 */
export class GlobalStatsDataDto {
  @ApiProperty({ description: '总游戏时长(秒)', example: 86400 })
  totalPlayTime: number;

  @ApiProperty({ description: '总登录天数', example: 30 })
  totalLoginDays: number;

  @ApiProperty({ description: '总角色数', example: 5 })
  totalCharacters: number;

  @ApiProperty({ description: '游戏过的区服数', example: 3 })
  totalServersPlayed: number;

  @ApiProperty({ description: '首次游戏时间', example: '2024-01-01T00:00:00.000Z' })
  firstGameTime: Date;

  @ApiProperty({ description: '总充值金额', example: 99.99 })
  totalRecharge: number;

  @ApiProperty({ description: 'VIP等级', example: 3 })
  vipLevel: number;

  @ApiProperty({ description: '最后登录时间', example: '2024-01-15T10:30:00.000Z' })
  lastLoginTime: Date;
}

/**
 * 全局统计响应DTO
 */
export class GlobalStatsResponseDto {
  @ApiProperty({ description: '请求是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '获取用户全局统计成功' })
  message: string;

  @ApiProperty({ description: '全局统计数据', type: GlobalStatsDataDto })
  data: GlobalStatsDataDto;
}
