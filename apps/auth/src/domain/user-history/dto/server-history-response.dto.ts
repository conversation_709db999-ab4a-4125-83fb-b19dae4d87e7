import { ApiProperty } from '@nestjs/swagger';

/**
 * 区服历史数据DTO
 */
export class ServerHistoryDataDto {
  @ApiProperty({ description: '区服历史列表', type: Array })
  servers: any[];

  @ApiProperty({ description: '总区服数量', example: 5 })
  totalServers: number;
}

/**
 * 区服历史响应DTO
 */
export class ServerHistoryResponseDto {
  @ApiProperty({ description: '请求是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '获取用户区服历史成功' })
  message: string;

  @ApiProperty({ description: '区服历史数据', type: ServerHistoryDataDto })
  data: ServerHistoryDataDto;
}
