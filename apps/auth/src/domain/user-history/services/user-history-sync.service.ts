import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

// 服务
import { UserHistoryService } from './user-history.service';

// 实体
import { CharacterSummary } from '../entities/user-history.entity';

/**
 * 用户历史同步服务
 * 负责在角色数据变化时同步更新历史记录
 */
@Injectable()
export class UserHistorySyncService {
  private readonly logger = new Logger(UserHistorySyncService.name);

  constructor(
    private readonly userHistoryService: UserHistoryService,
  ) {}

  /**
   * 角色登录时同步历史
   */
  async syncOnCharacterLogin(
    userId: string,
    serverId: string,
    serverName: string,
    characterId: string,
    characterData: any
  ): Promise<void> {
    this.logger.log(`同步角色登录历史: ${userId}@${serverId}:${characterId}`);

    try {
      // 1. 更新最后登录信息
      await this.userHistoryService.updateLastLogin(userId, serverId, characterId);

      // 2. 更新区服历史
      await this.userHistoryService.updateServerHistory(userId, serverId, serverName);

      // 3. 更新角色摘要
      await this.userHistoryService.updateCharacterSummary(userId, serverId, characterId, {
        name: characterData.name,
        level: characterData.level,
        power: characterData.power,
        avatar: characterData.avatar,
        profession: characterData.profession,
        lastLoginTime: new Date(),
      });

      this.logger.log(`角色登录历史同步完成: ${userId}@${serverId}:${characterId}`);

    } catch (error) {
      this.logger.error(`同步角色登录历史失败: ${userId}@${serverId}:${characterId}`, error);
      // 不抛出错误，避免影响主要登录流程
    }
  }

  /**
   * 角色登出时同步历史
   */
  async syncOnCharacterLogout(
    userId: string,
    serverId: string,
    characterId: string,
    sessionDuration: number = 0
  ): Promise<void> {
    this.logger.log(`同步角色登出历史: ${userId}@${serverId}:${characterId}`);

    try {
      // 更新角色游戏时长
      await this.userHistoryService.updateCharacterSummary(userId, serverId, characterId, {
        totalPlayTime: sessionDuration, // 这里应该是累加，简化处理
        lastActiveAt: new Date(),
      });

      this.logger.log(`角色登出历史同步完成: ${userId}@${serverId}:${characterId}`);

    } catch (error) {
      this.logger.error(`同步角色登出历史失败: ${userId}@${serverId}:${characterId}`, error);
      // 不抛出错误，避免影响主要登出流程
    }
  }

  /**
   * 角色数据变化时同步历史
   */
  async syncOnCharacterUpdate(
    userId: string,
    serverId: string,
    characterId: string,
    updates: Partial<CharacterSummary>
  ): Promise<void> {
    // 异步更新，不影响主业务流程
    setImmediate(async () => {
      try {
        await this.userHistoryService.updateCharacterSummary(userId, serverId, characterId, updates);
        
        this.logger.debug(`角色数据同步完成: ${userId}@${serverId}:${characterId}`);
      } catch (error) {
        this.logger.warn(`同步角色数据失败: ${userId}@${serverId}:${characterId}`, error);
      }
    });
  }

  /**
   * 批量同步角色数据
   */
  async batchSyncCharacterData(syncData: Array<{
    userId: string;
    serverId: string;
    characterId: string;
    updates: Partial<CharacterSummary>;
  }>): Promise<void> {
    this.logger.log(`批量同步角色数据: ${syncData.length}条记录`);

    const batchSize = 10;
    for (let i = 0; i < syncData.length; i += batchSize) {
      const batch = syncData.slice(i, i + batchSize);
      
      const syncPromises = batch.map(data => 
        this.userHistoryService.updateCharacterSummary(
          data.userId, 
          data.serverId, 
          data.characterId, 
          data.updates
        ).catch(error => {
          this.logger.warn(`批量同步失败: ${data.userId}@${data.serverId}:${data.characterId}`, error);
        })
      );

      await Promise.allSettled(syncPromises);
      
      // 避免过于频繁的数据库操作
      if (i + batchSize < syncData.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    this.logger.log(`批量同步角色数据完成: ${syncData.length}条记录`);
  }

  /**
   * 定期数据一致性检查
   */
  @Cron('0 2 * * *') // 每天凌晨2点
  async performConsistencyCheck(): Promise<void> {
    this.logger.log('开始执行数据一致性检查');

    try {
      // TODO: 实现数据一致性检查逻辑
      // 1. 检查用户历史记录与实际用户数据的一致性
      // 2. 检查区服历史记录与实际区服数据的一致性
      // 3. 检查角色摘要与实际角色数据的一致性
      // 4. 修复发现的不一致数据

      const inconsistentUsers = await this.findInconsistentUsers();
      
      for (const userId of inconsistentUsers) {
        await this.repairUserHistory(userId);
      }

      this.logger.log(`数据一致性检查完成，修复了${inconsistentUsers.length}个用户的数据`);

    } catch (error) {
      this.logger.error('数据一致性检查失败', error);
    }
  }

  /**
   * 清理过期数据
   */
  @Cron('0 3 * * 0') // 每周日凌晨3点
  async cleanupExpiredData(): Promise<void> {
    this.logger.log('开始清理过期数据');

    try {
      // TODO: 实现过期数据清理逻辑
      // 1. 清理长时间未登录用户的详细历史记录
      // 2. 压缩历史数据，保留关键信息
      // 3. 清理已删除角色的摘要信息

      this.logger.log('过期数据清理完成');

    } catch (error) {
      this.logger.error('清理过期数据失败', error);
    }
  }

  /**
   * 生成用户历史报告
   */
  @Cron('0 1 * * 1') // 每周一凌晨1点
  async generateHistoryReport(): Promise<void> {
    this.logger.log('开始生成用户历史报告');

    try {
      // TODO: 实现历史报告生成逻辑
      // 1. 统计活跃用户数量
      // 2. 统计各区服用户分布
      // 3. 统计用户行为模式
      // 4. 生成推荐优化建议

      this.logger.log('用户历史报告生成完成');

    } catch (error) {
      this.logger.error('生成用户历史报告失败', error);
    }
  }

  /**
   * 私有方法：查找数据不一致的用户
   */
  private async findInconsistentUsers(): Promise<string[]> {
    // TODO: 实现不一致用户查找逻辑
    // 这里返回空数组作为占位符
    return [];
  }

  /**
   * 私有方法：修复用户历史数据
   */
  private async repairUserHistory(userId: string): Promise<void> {
    try {
      this.logger.log(`修复用户历史数据: ${userId}`);

      // TODO: 实现用户历史数据修复逻辑
      // 1. 从各个微服务获取用户的实际数据
      // 2. 对比历史记录中的数据
      // 3. 修复发现的不一致
      // 4. 更新历史记录

      this.logger.log(`用户历史数据修复完成: ${userId}`);

    } catch (error) {
      this.logger.error(`修复用户历史数据失败: ${userId}`, error);
    }
  }

  /**
   * 手动触发数据同步
   */
  async manualSync(userId: string): Promise<void> {
    this.logger.log(`手动触发数据同步: ${userId}`);

    try {
      // 重新构建用户的完整历史记录
      await this.repairUserHistory(userId);
      
      this.logger.log(`手动数据同步完成: ${userId}`);

    } catch (error) {
      this.logger.error(`手动数据同步失败: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 获取同步状态
   */
  async getSyncStatus(): Promise<{
    lastConsistencyCheck: Date;
    lastCleanup: Date;
    lastReport: Date;
    pendingSyncCount: number;
  }> {
    // TODO: 实现同步状态获取逻辑
    return {
      lastConsistencyCheck: new Date(),
      lastCleanup: new Date(),
      lastReport: new Date(),
      pendingSyncCount: 0,
    };
  }
}
