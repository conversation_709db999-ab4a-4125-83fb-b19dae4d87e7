/**
 * 安全Repository装饰器
 * 用于保护Repository方法免受恶意数据注入
 */

import { Logger } from '@nestjs/common';

/**
 * 安全字段白名单配置
 */
interface SecurityConfig {
  allowedFields: string[];
  requiredFields?: string[];
  maxDepth?: number;
}

/**
 * 安全Repository方法装饰器
 * 🛡️ 自动过滤和验证输入数据
 */
export function SecureRepository(config: SecurityConfig) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const logger = new Logger(`SecureRepository:${target.constructor.name}`);

    descriptor.value = async function (...args: any[]) {
      try {
        // 🛡️ 安全验证：检查输入参数
        const sanitizedArgs = args.map((arg, index) => {
          if (typeof arg === 'object' && arg !== null && index === 0) {
            return sanitizeData(arg, config, logger);
          }
          return arg;
        });

        // 调用原始方法
        return await method.apply(this, sanitizedArgs);
      } catch (error) {
        logger.error(`安全Repository方法执行失败: ${propertyName}`, error);
        throw error;
      }
    };
  };
}

/**
 * 🛡️ 数据清理函数
 */
function sanitizeData(data: any, config: SecurityConfig, logger: Logger): any {
  if (typeof data !== 'object' || data === null) {
    return data;
  }

  const sanitized: any = {};
  const { allowedFields, requiredFields = [], maxDepth = 3 } = config;

  // 检查必需字段
  for (const field of requiredFields) {
    if (data[field] === undefined) {
      logger.warn(`缺少必需字段: ${field}`);
      throw new Error(`缺少必需字段: ${field}`);
    }
  }

  // 白名单字段过滤
  for (const field of allowedFields) {
    if (data[field] !== undefined) {
      sanitized[field] = sanitizeValue(data[field], maxDepth - 1, logger);
    }
  }

  // 检测恶意字段
  const suspiciousFields = Object.keys(data).filter(key => 
    !allowedFields.includes(key) && isSuspiciousField(key)
  );

  if (suspiciousFields.length > 0) {
    logger.warn(`检测到可疑字段: ${suspiciousFields.join(', ')}`);
    // 在生产环境中可以选择抛出异常
    // throw new Error(`检测到可疑字段: ${suspiciousFields.join(', ')}`);
  }

  return sanitized;
}

/**
 * 🛡️ 递归清理值
 */
function sanitizeValue(value: any, depth: number, logger: Logger): any {
  if (depth <= 0) {
    return value;
  }

  if (typeof value === 'string') {
    return sanitizeString(value, logger);
  }

  if (Array.isArray(value)) {
    return value.map(item => sanitizeValue(item, depth - 1, logger));
  }

  if (typeof value === 'object' && value !== null) {
    const sanitized: any = {};
    for (const [key, val] of Object.entries(value)) {
      if (!isSuspiciousField(key)) {
        sanitized[key] = sanitizeValue(val, depth - 1, logger);
      }
    }
    return sanitized;
  }

  return value;
}

/**
 * 🛡️ 字符串安全检查
 */
function sanitizeString(str: string, logger: Logger): string {
  // 检测SQL注入模式
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
    /(--|\/\*|\*\/|;)/,
    /(\b(OR|AND)\b.*=.*)/i,
  ];

  // 检测XSS模式
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/i,
    /on\w+\s*=/i,
  ];

  // 检测NoSQL注入模式
  const noSqlPatterns = [
    /\$where/i,
    /\$regex/i,
    /\$ne/i,
    /\$gt/i,
    /\$lt/i,
  ];

  const allPatterns = [...sqlPatterns, ...xssPatterns, ...noSqlPatterns];

  for (const pattern of allPatterns) {
    if (pattern.test(str)) {
      logger.warn(`检测到潜在恶意字符串: ${str.substring(0, 50)}...`);
      throw new Error('检测到潜在的安全威胁');
    }
  }

  return str;
}

/**
 * 🛡️ 检测可疑字段名
 */
function isSuspiciousField(fieldName: string): boolean {
  const suspiciousPatterns = [
    /^\$/,           // MongoDB操作符
    /^_/,            // 私有字段
    /admin/i,        // 管理员相关
    /password/i,     // 密码相关
    /token/i,        // 令牌相关
    /secret/i,       // 密钥相关
    /permission/i,   // 权限相关
    /role/i,         // 角色相关
    /auth/i,         // 认证相关
  ];

  return suspiciousPatterns.some(pattern => pattern.test(fieldName));
}

/**
 * 预定义的安全配置
 */
export const SecurityConfigs = {
  FORMATION: {
    allowedFields: [
      'uid', 'characterId', 'serverId', 'teamFormations', 
      'currTeamFormationId', 'leagueTeamFormationId', 'warOfFaithTeamFormationId',
      'allTactics', 'allDefTactics', 'allFormations', 'fixId'
    ],
    requiredFields: ['characterId', 'serverId'],
    maxDepth: 3
  },
  
  TACTIC: {
    allowedFields: [
      'uid', 'characterId', 'serverId', 'tactics', 'formationTactics',
      'combinations', 'heroBonusCache', 'createTime', 'updateTime'
    ],
    requiredFields: ['characterId', 'serverId'],
    maxDepth: 3
  },
  
  INVENTORY: {
    allowedFields: [
      'uid', 'characterId', 'serverId', 'bag', 'itemUidToBookMarkId', 
      'createTime', 'updateTime'
    ],
    requiredFields: ['characterId', 'serverId'],
    maxDepth: 3
  },
  
  ITEM: {
    allowedFields: [
      'uid', 'characterId', 'serverId', 'item', 'resId2Uid', 'obtainTime'
    ],
    requiredFields: ['characterId', 'serverId'],
    maxDepth: 3
  }
};
