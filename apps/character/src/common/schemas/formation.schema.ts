/**
 * Formation Schema - 严格基于old项目teamFormations.js重新设计
 * 确保与old项目的数据结构100%一致
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import {SwapHerosDto} from "@character/common/dto/formation.dto";

// 阵容类型枚举 - 基于old项目commonEnum.FORMATION_TYPE
export enum FormationType {
  COMMON = 1,      // 普通阵容
  LEAGUE = 2,      // 联赛阵容
  WAR_OF_FAITH = 3, // 信仰之战阵容
  TOURNAMENT = 4,   // 锦标赛阵容
  FRIENDLY = 5,     // 友谊赛阵容
}

// 小队类型枚举 - 基于old项目TeamType字段
export enum TeamType {
  MAIN = 1,        // 主力队
  SUBSTITUTE = 2,  // 替补队
  RESERVE = 3,     // 预备队
}

// 教练子文档 - 基于old项目Trainers字段
@Schema({ _id: false })
export class FormationTrainer {
  @Prop({ default: '' })
  uid: string;         // 教练UID

  @Prop({ default: 0 })
  resId: number;       // 教练配置ID

  @Prop({ default: 1 })
  type: number;        // 教练类型

  @Prop({ default: 1 })
  level: number;       // 教练等级

  @Prop({ type: [Object], default: [] })
  tactics: any[];      // 教练战术
}

// 位置球员映射 - 严格基于old项目PositionToHerosObject结构
@Schema({ _id: false })
export class PositionToHerosObject {
  @Prop({ type: [String], default: [] })
  GK: string[];        // 门将位置的球员UID列表 (严格对应old项目GK)

  @Prop({ type: [String], default: [] })
  DL: string[];        // 左后卫位置的球员UID列表 (严格对应old项目DL)

  @Prop({ type: [String], default: [] })
  DC: string[];        // 中后卫位置的球员UID列表 (严格对应old项目DC)

  @Prop({ type: [String], default: [] })
  DR: string[];        // 右后卫位置的球员UID列表 (严格对应old项目DR)

  @Prop({ type: [String], default: [] })
  ML: string[];        // 左中场位置的球员UID列表 (严格对应old项目ML)

  @Prop({ type: [String], default: [] })
  MC: string[];        // 中中场位置的球员UID列表 (严格对应old项目MC)

  @Prop({ type: [String], default: [] })
  MR: string[];        // 右中场位置的球员UID列表 (严格对应old项目MR)

  @Prop({ type: [String], default: [] })
  WL: string[];        // 左边锋位置的球员UID列表 (严格对应old项目WL)

  @Prop({ type: [String], default: [] })
  ST: string[];        // 前锋位置的球员UID列表 (严格对应old项目ST)

  @Prop({ type: [String], default: [] })
  WR: string[];        // 右边锋位置的球员UID列表 (严格对应old项目WR)

  @Prop({ type: [String], default: [] })
  AM: string[];        // 前腰位置的球员UID列表 (严格对应old项目AM)

  @Prop({ type: [String], default: [] })
  DM: string[];        // 后腰位置的球员UID列表 (严格对应old项目DM)
}

// 单个阵容 - 严格基于old项目newTeamFormation方法
@Schema({ _id: false })
export class TeamFormation {
  @Prop({ required: true })
  uid: string;                    // 阵容UID - 对应old项目的Uid

  @Prop({ required: true })
  resId: number;                  // 配置阵型Id - 对应old项目的ResId

  @Prop({ default: '阵容一' })
  name: string;                   // 阵容名称 - 对应old项目的Name

  // 团队基础属性 - 严格对应old项目
  @Prop({ default: 0 })
  attack: number;                 // 球队进攻值

  @Prop({ default: 0 })
  defend: number;                 // 球队防守值

  @Prop({ default: 0 })
  actualStrength: number;         // 球队的实力

  @Prop({ default: 0 })
  isInitName: number;             // 是否是初始名字 0是 1不是

  @Prop({ default: 0 })
  isInitFormation: number;        // 是否初始阵容一 0不是 1是

  @Prop({ default: 0 })
  isLeague: number;               // 是否为联赛专用阵容 0为不是 1是为

  @Prop({ default: 1 })
  teamId: number;                 // 阵容Id 1,2,3,4 阵容1,2,3,4

  @Prop({ default: 1 })
  teamType: number;               // 小队类型 1主力队 2替补队 3预备队

  // 球员位置映射 - 严格对应old项目PositionToHerosObject
  @Prop({ type: PositionToHerosObject, default: () => ({
    GK: [], DL: [], DC: [], DR: [], ML: [], MC: [],
    MR: [], WL: [], ST: [], WR: [], AM: [], DM: []
  }) })
  positionToHerosObject: PositionToHerosObject;

  @Prop({ type: [String], default: [] })
  scenceUse: string[];            // 阵型使用场景标记

  @Prop({ default: 0 })
  inspireRate: number;            // 鼓舞加成比例,暂定为0

  @Prop({ default: 101 })
  useTactics: number;             // 当前阵容使用的战术

  @Prop({ default: 1101 })
  useDefTactics: number;          // 当前阵容使用防守的战术

  @Prop({ default: '' })
  freeKickHero: string;         // 指定的任意球球员

  @Prop({ default: '' })
  penaltiesHero: string;        // 指定的点球球员

  @Prop({ default: '' })
  cornerKickHero: string;       // 指定的角球球员

  @Prop({ type: [FormationTrainer], default: [] })
  trainers: FormationTrainer[];   // 教练

  @Prop({ default: FormationType.COMMON })
  type: number;                   // 阵容类型(用途)
}

// 主TeamFormations Schema - 严格基于old项目TeamFormations实体
@Schema({ 
  collection: 'teamFormations', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class TeamFormations {
  @Prop({ required: true, index: true })
  uid: string;                    // 玩家UID - 对应old项目的uid

  @Prop({ required: true, index: true })
  characterId: string;            // 角色ID (新增字段，用于微服务架构)

  @Prop({ required: true, index: true })
  serverId: string;               // 服务器ID (新增字段，用于微服务架构)

  // 所有阵容 - 对应old项目的allTeamFormations (Map转为Array)
  @Prop({ type: [TeamFormation], default: [] })
  teamFormations: TeamFormation[];

  @Prop({ default: '' })
  currTeamFormationId: string;    // 当前使用阵容 - 对应old项目

  @Prop({ default: '' })
  leagueTeamFormationId: string;  // 联赛阵容Id - 对应old项目

  @Prop({ default: '' })
  warOfFaithTeamFormationId: string; // 信仰之战阵容id - 对应old项目

  // 所有进攻战术 - 对应old项目的allTactics (Map转为Object)
  @Prop({ type: Object, default: {} })
  allTactics: Record<string, any>;

  // 所有防守战术 - 对应old项目的allDefTactics
  @Prop({ type: Object, default: {} })
  allDefTactics: Record<string, any>;

  // 所有阵型 - 对应old项目的allFormations
  @Prop({ type: [Object], default: [] })
  allFormations: any[];

  @Prop({ default: 0 })
  fixId: number;                  // 对应old项目的fixId
}

export const TeamFormationsSchema = SchemaFactory.createForClass(TeamFormations);

// 定义Document类型
export type TeamFormationsDocument = TeamFormations & Document;

// 创建索引
TeamFormationsSchema.index({ uid: 1 }, { unique: true });
TeamFormationsSchema.index({ characterId: 1 });
TeamFormationsSchema.index({ serverId: 1 });
TeamFormationsSchema.index({ currTeamFormationId: 1 });
