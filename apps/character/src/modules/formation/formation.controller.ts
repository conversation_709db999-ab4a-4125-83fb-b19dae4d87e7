/**
 * Formation Controller - 严格基于old项目API设计
 * 确保与old项目的API接口100%一致
 */

import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { FormationService } from './formation.service';
import { Cacheable, CacheEvict } from '@common/redis';
import {SwapHerosDto} from "@character/common/dto/formation.dto";

@Controller()
export class FormationController {
  private readonly logger = new Logger(FormationController.name);

  constructor(private readonly formationService: FormationService) {}

  /**
   * 获取角色阵容数据
   * 对应old项目: TeamFormations实体的toJSONforClient
   */
  @MessagePattern('formation.getFormations')
  @Cacheable({
    key: 'character:formations:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getCharacterFormations(@Payload() payload: { characterId: string; serverId?: string }) {
    this.logger.log(`获取角色阵容数据: ${payload.characterId}`);
    
    const formations = await this.formationService.getCharacterFormations(payload.characterId);
    if (!formations) {
      // 如果不存在，初始化阵容数据
      const newFormations = await this.formationService.initCharacterFormations(
        payload.characterId, 
        payload.serverId || 'server_001'
      );
      return { code: 0, data: newFormations };
    }

    return { code: 0, data: formations };
  }

  /**
   * 创建阵容
   * 对应old项目: newTeamFormation方法，优化API命名
   */
  @MessagePattern('formation.createFormation')
  @CacheEvict({
    key: 'character:formations:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createFormation(@Payload() payload: {
    characterId: string;
    resId: number;
    type?: number;
    serverId?: string;
  }) {
    this.logger.log(`创建阵容: ${payload.characterId}, 阵型ID: ${payload.resId}`);

    const formation = await this.formationService.createFormation(
      payload.characterId,
      payload.resId,
      payload.type
    );

    return { code: 0, data: formation };
  }

  /**
   * 添加球员到阵容位置
   * 对应old项目: addHeroInTeam方法，优化API命名
   */
  @MessagePattern('formation.addHeroToPosition')
  @CacheEvict({
    key: 'character:formations:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addHeroToPosition(@Payload() payload: {
    characterId: string;
    formationId: string;
    position: string;
    index: number;
    heroId: string;
    serverId?: string;
  }) {
    this.logger.log(`添加球员到阵容位置: ${JSON.stringify(payload)}`);

    const success = await this.formationService.addHeroToPosition(
      payload.characterId,
      payload.formationId,
      payload.position,
      payload.index,
      payload.heroId
    );

    return { code: success ? 0 : -1 };
  }

  /**
   * 从阵容位置移除球员
   * 对应old项目: deleteHeroFromTeam方法，优化API命名
   */
  @MessagePattern('formation.removeHeroFromPosition')
  @CacheEvict({
    key: 'character:formations:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async removeHeroFromPosition(@Payload() payload: {
    characterId: string;
    formationId: string;
    position: string;
    heroId: string;
    serverId?: string;
  }) {
    this.logger.log(`从阵容位置移除球员: ${JSON.stringify(payload)}`);

    const result = await this.formationService.removeHeroFromPosition(
      payload.characterId,
      payload.formationId,
      payload.position,
      payload.heroId
    );

    return { code: result };
  }

  /**
   * 设置当前激活阵容
   * 对应old项目: setCurrTeamFormationId方法，优化API命名
   */
  @MessagePattern('formation.setActiveFormation')
  @CacheEvict({
    key: 'character:formations:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async setActiveFormation(@Payload() payload: {
    characterId: string;
    formationId: string;
    serverId?: string;
  }) {
    this.logger.log(`设置当前激活阵容: ${payload.characterId}, 阵容: ${payload.formationId}`);

    const result = await this.formationService.setActiveFormation(
      payload.characterId,
      payload.formationId
    );

    return { code: result };
  }

  /**
   * 设置联赛专用阵容
   * 对应old项目: setLeagueTeamFormationId方法，优化API命名
   */
  @MessagePattern('formation.setLeagueFormation')
  @CacheEvict({
    key: 'character:formations:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async setLeagueFormation(@Payload() payload: {
    characterId: string;
    formationId: string;
    serverId?: string;
  }) {
    this.logger.log(`设置联赛专用阵容: ${payload.characterId}, 阵容: ${payload.formationId}`);

    const result = await this.formationService.setLeagueFormation(
      payload.characterId,
      payload.formationId
    );

    return { code: result };
  }

  /**
   * 设置信仰之战专用阵容
   * 对应old项目: setWarOfFaithTeamFormationId方法，优化API命名
   */
  @MessagePattern('formation.setWarOfFaithFormation')
  @CacheEvict({
    key: 'character:formations:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async setWarOfFaithFormation(@Payload() payload: {
    characterId: string;
    formationId: string;
    serverId?: string;
  }) {
    this.logger.log(`设置信仰之战专用阵容: ${payload.characterId}, 阵容: ${payload.formationId}`);

    const result = await this.formationService.setWarOfFaithFormation(
      payload.characterId,
      payload.formationId
    );

    return { code: result };
  }

  /**
   * 自动布阵
   * 对应old项目: autoFormation方法
   */
  @MessagePattern('formation.autoFormation')
  @CacheEvict({
    key: 'character:formations:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async autoFormation(@Payload() payload: {
    characterId: string;
    formationId: string;
    heroIds?: string[];
    serverId?: string;
  }) {
    this.logger.log(`自动布阵: ${payload.characterId}, 阵容: ${payload.formationId}`);

    // 如果没有提供球员ID，则获取角色的所有球员
    let heroIds = payload.heroIds;
    if (!heroIds || heroIds.length === 0) {
      // 调用Hero服务获取球员列表
      try {
        const heroesResponse = await this.formationService.getCharacterHeroes(payload.characterId);
        heroIds = heroesResponse.map(hero => hero.heroId);
      } catch (error) {
        this.logger.error('获取球员列表失败', error);
        return { code: -1, message: '获取球员列表失败' };
      }
    }

    const result = await this.formationService.autoFormation(
      payload.characterId,
      payload.formationId,
      heroIds
    );

    return result;
  }

  /**
   * 复制阵容
   * 对应old项目: copyTeamFormation方法，优化API命名
   */
  @MessagePattern('formation.copyFormation')
  @CacheEvict({
    key: 'character:formations:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async copyFormation(@Payload() payload: {
    characterId: string;
    sourceFormationId: string;
    serverId?: string;
  }) {
    this.logger.log(`复制阵容: ${payload.characterId}, 源阵容: ${payload.sourceFormationId}`);

    const result = await this.formationService.copyFormation(
      payload.characterId,
      payload.sourceFormationId
    );

    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 设置阵容战术
   * 对应old项目: setFormationTactics方法
   */
  @MessagePattern('formation.setFormationTactics')
  @CacheEvict({
    key: 'character:formations:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async setFormationTactics(@Payload() payload: { 
    characterId: string; 
    uid: string; 
    resId: number; 
    tacticsType: string;
    serverId?: string;
  }) {
    this.logger.log(`设置阵容战术: ${JSON.stringify(payload)}`);
    
    const result = await this.formationService.setFormationTactics(
      payload.characterId,
      payload.uid,
      payload.resId,
      payload.tacticsType
    );

    return { code: result };
  }

}
