/**
 * 角色服务健康检查控制器
 */

import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';

@ApiTags('健康检查')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @ApiOperation({ summary: '获取服务健康状态' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  async check() {
    return this.healthService.check();
  }

  @Get('ready')
  @ApiOperation({ summary: '检查服务是否就绪' })
  @ApiResponse({ status: 200, description: '服务就绪' })
  @ApiResponse({ status: 503, description: '服务未就绪' })
  async readiness() {
    return this.healthService.readiness();
  }

  @Get('live')
  @ApiOperation({ summary: '检查服务是否存活' })
  @ApiResponse({ status: 200, description: '服务存活' })
  @ApiResponse({ status: 503, description: '服务不存活' })
  async liveness() {
    return this.healthService.liveness();
  }
}
