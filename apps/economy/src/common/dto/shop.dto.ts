/**
 * 商店相关的数据传输对象
 */

import { IsString, IsNumber, IsOptional, IsBoolean, IsArray, IsEnum, Min, Max, Length, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ShopType, RefreshCycle } from '../schemas/shop.schema';

// 购买商品DTO
export class PurchaseGoodsDto {
  @ApiProperty({ description: '商品ID' })
  @IsNumber()
  goodsId: number;

  @ApiProperty({ description: '购买数量', minimum: 1 })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({ description: '商店类型' })
  @IsEnum(ShopType)
  shopType: ShopType;

  @ApiPropertyOptional({ description: '货币类型' })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({ description: '是否使用折扣' })
  @IsOptional()
  @IsBoolean()
  useDiscount?: boolean;
}

// 刷新商店DTO
export class RefreshShopDto {
  @ApiProperty({ description: '商店类型' })
  @IsEnum(ShopType)
  shopType: ShopType;

  @ApiPropertyOptional({ description: '刷新周期' })
  @IsOptional()
  @IsEnum(RefreshCycle)
  cycle?: RefreshCycle;

  @ApiPropertyOptional({ description: '是否强制刷新' })
  @IsOptional()
  @IsBoolean()
  forceRefresh?: boolean;
}

// 领取月卡奖励DTO
export class ClaimMonthCardDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '月卡类型' })
  @IsOptional()
  @IsNumber()
  cardType?: number;
}

// 购买月卡DTO
export class BuyMonthCardDto {
  @ApiProperty({ description: '月卡类型' })
  @IsNumber()
  cardType: number;

  @ApiProperty({ description: '购买天数' })
  @IsNumber()
  @Min(1)
  @Max(365)
  days: number;

  @ApiPropertyOptional({ description: '支付方式' })
  @IsOptional()
  @IsString()
  paymentMethod?: string;

  @ApiPropertyOptional({ description: '订单ID（支付验证用）' })
  @IsOptional()
  @IsString()
  orderId?: string;
}

// 商店信息响应DTO
export class ShopInfoDto {
  @ApiProperty({ description: '商店记录ID' })
  shopId: string;

  @ApiProperty({ description: '角色ID' })
  characterId: string;

  @ApiProperty({ description: '商店类型' })
  shopType: ShopType;

  @ApiProperty({ description: '每日刷新时间' })
  everyDayReTime: number;

  @ApiProperty({ description: '每周刷新时间' })
  weeklyReTime: number;

  @ApiProperty({ description: '每月刷新时间' })
  monthlyReTime: number;

  @ApiProperty({ description: '赛季刷新时间' })
  seasonReTime: number;

  @ApiProperty({ description: '每日限购记录' })
  everyDayBuy: any[];

  @ApiProperty({ description: '每周限购记录' })
  weeklyBuy: any[];

  @ApiProperty({ description: '每月限购记录' })
  monthlyBuy: any[];

  @ApiProperty({ description: '赛季限购记录' })
  seasonBuy: any[];

  @ApiProperty({ description: '购买历史' })
  purchaseHistory: any[];

  @ApiProperty({ description: '总消费金额' })
  totalSpent: number;

  @ApiProperty({ description: '总购买次数' })
  totalPurchases: number;

  @ApiProperty({ description: '最后购买时间' })
  lastPurchaseTime: number;

  @ApiProperty({ description: '是否需要每日刷新' })
  needsDailyRefresh: boolean;

  @ApiProperty({ description: '是否需要每周刷新' })
  needsWeeklyRefresh: boolean;

  @ApiProperty({ description: '是否需要每月刷新' })
  needsMonthlyRefresh: boolean;

  // 月卡相关
  @ApiProperty({ description: '月卡是否激活' })
  isMonthCardActive: boolean;

  @ApiProperty({ description: '是否可领取月卡奖励' })
  canClaimMonthCardReward: boolean;

  @ApiProperty({ description: '月卡剩余天数' })
  cardTime: number;

  @ApiProperty({ description: '今日是否已领取奖励' })
  isGetReward: number;
}

// 商店列表查询DTO
export class GetShopListDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '商店类型过滤' })
  @IsOptional()
  @IsEnum(ShopType)
  shopType?: ShopType;

  @ApiPropertyOptional({ description: '是否只显示有购买记录的商店' })
  @IsOptional()
  @IsBoolean()
  hasRecordsOnly?: boolean;

  @ApiPropertyOptional({ description: '排序字段' })
  @IsOptional()
  @IsString()
  sortBy?: 'lastPurchaseTime' | 'totalSpent' | 'totalPurchases';

  @ApiPropertyOptional({ description: '排序方向' })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';
}

// 购买历史查询DTO
export class GetPurchaseHistoryDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '商店类型过滤' })
  @IsOptional()
  @IsEnum(ShopType)
  shopType?: ShopType;

  @ApiPropertyOptional({ description: '商品ID过滤' })
  @IsOptional()
  @IsNumber()
  goodsId?: number;

  @ApiPropertyOptional({ description: '开始时间' })
  @IsOptional()
  @IsNumber()
  startTime?: number;

  @ApiPropertyOptional({ description: '结束时间' })
  @IsOptional()
  @IsNumber()
  endTime?: number;

  @ApiPropertyOptional({ description: '页码', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}

// 商店统计DTO
export class ShopStatsDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '统计时间范围（天）' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  days?: number;

  @ApiPropertyOptional({ description: '商店类型过滤' })
  @IsOptional()
  @IsEnum(ShopType)
  shopType?: ShopType;
}

// VIP商店特殊DTO
export class VipShopDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: 'VIP等级' })
  @IsNumber()
  @Min(0)
  @Max(15)
  vipLevel: number;
}

// 限时商店DTO
export class LimitShopDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '活动ID' })
  @IsOptional()
  @IsNumber()
  activityId?: number;

  @ApiPropertyOptional({ description: '限时商店类型' })
  @IsOptional()
  @IsString()
  limitType?: string;
}
