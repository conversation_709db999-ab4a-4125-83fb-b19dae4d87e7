/**
 * 兑换大厅Repository
 * 基于old项目exchangeHall.js数据访问逻辑
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Exchange, ExchangeDocument } from '../schemas/exchange.schema';

@Injectable()
export class ExchangeRepository {
  private readonly logger = new Logger(ExchangeRepository.name);

  constructor(
    @InjectModel(Exchange.name) private readonly exchangeModel: Model<ExchangeDocument>,
  ) {}

  /**
   * 根据玩家ID查找兑换记录
   */
  async findByPlayerId(uid: string): Promise<ExchangeDocument | null> {
    try {
      return await this.exchangeModel.findOne({ uid }).exec();
    } catch (error) {
      this.logger.error(`根据玩家ID查找兑换记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 创建兑换记录
   */
  async create(exchangeData: Partial<Exchange>): Promise<ExchangeDocument> {
    try {
      const exchange = new this.exchangeModel({
        ...exchangeData,
        exchangeList: [],
        exchangeNumList: [
          { type: 1, num: 1 },
          { type: 2, num: 1 },
          { type: 3, num: 1 },
        ],
        reExchangeHallTime: 0,
        totalCompounds: 0,
        totalDecomposes: 0,
        totalExchanges: 0,
        totalChipSpent: 0,
        totalChipGained: 0,
        lastUpdateTime: Date.now(),
      });

      const savedExchange = await exchange.save();
      this.logger.log(`兑换记录创建成功: ${savedExchange.uid}`);
      return savedExchange;
    } catch (error) {
      this.logger.error('创建兑换记录失败', error);
      throw error;
    }
  }

  /**
   * 获取或创建兑换记录
   */
  async getOrCreateExchange(uid: string, serverId: string): Promise<ExchangeDocument> {
    try {
      let exchange = await this.findByPlayerId(uid);
      
      if (!exchange) {
        exchange = await this.create({
          uid,
          serverId,
        });
        this.logger.log(`创建新兑换记录: ${uid}`);
      }
      
      return exchange;
    } catch (error) {
      this.logger.error('获取或创建兑换记录失败', error);
      throw error;
    }
  }

  /**
   * 更新兑换记录
   */
  async update(uid: string, updateData: Partial<Exchange>): Promise<ExchangeDocument | null> {
    try {
      const updatedExchange = await this.exchangeModel.findOneAndUpdate(
        { uid },
        { ...updateData, lastUpdateTime: Date.now() },
        { new: true }
      ).exec();

      if (updatedExchange) {
        this.logger.log(`兑换记录更新成功: ${uid}`);
      }

      return updatedExchange;
    } catch (error) {
      this.logger.error(`更新兑换记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 删除兑换记录
   */
  async delete(uid: string): Promise<boolean> {
    try {
      const result = await this.exchangeModel.deleteOne({ uid }).exec();
      if (result.deletedCount > 0) {
        this.logger.log(`兑换记录删除成功: ${uid}`);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`删除兑换记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 获取兑换统计
   */
  async getExchangeStats(): Promise<any> {
    try {
      const stats = await this.exchangeModel.aggregate([
        {
          $group: {
            _id: null,
            totalPlayers: { $sum: 1 },
            totalCompounds: { $sum: '$totalCompounds' },
            totalDecomposes: { $sum: '$totalDecomposes' },
            totalExchanges: { $sum: '$totalExchanges' },
            totalChipSpent: { $sum: '$totalChipSpent' },
            totalChipGained: { $sum: '$totalChipGained' },
            avgCompounds: { $avg: '$totalCompounds' },
            avgDecomposes: { $avg: '$totalDecomposes' },
            avgExchanges: { $avg: '$totalExchanges' },
          }
        }
      ]).exec();

      return stats[0] || {
        totalPlayers: 0,
        totalCompounds: 0,
        totalDecomposes: 0,
        totalExchanges: 0,
        totalChipSpent: 0,
        totalChipGained: 0,
        avgCompounds: 0,
        avgDecomposes: 0,
        avgExchanges: 0,
      };
    } catch (error) {
      this.logger.error('获取兑换统计失败', error);
      throw error;
    }
  }

  /**
   * 查找需要刷新的兑换大厅
   */
  async findNeedingRefresh(): Promise<ExchangeDocument[]> {
    try {
      const oneDayMs = 24 * 60 * 60 * 1000;
      const cutoffTime = Date.now() - oneDayMs;

      return await this.exchangeModel.find({
        reExchangeHallTime: { $lt: cutoffTime }
      }).exec();
    } catch (error) {
      this.logger.error('查找需要刷新的兑换大厅失败', error);
      throw error;
    }
  }

  /**
   * 批量更新兑换记录
   */
  async batchUpdate(updates: Array<{ uid: string; updateData: Partial<Exchange> }>): Promise<void> {
    try {
      const updatePromises = updates.map(({ uid, updateData }) =>
        this.exchangeModel.updateOne(
          { uid },
          { ...updateData, lastUpdateTime: Date.now() }
        ).exec()
      );

      await Promise.all(updatePromises);
      this.logger.log(`批量更新兑换记录: ${updates.length} 条记录`);
    } catch (error) {
      this.logger.error('批量更新兑换记录失败', error);
      throw error;
    }
  }
}
