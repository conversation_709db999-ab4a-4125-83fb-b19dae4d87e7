/**
 * 联赛转播Repository
 * 基于old项目relay.js数据访问逻辑
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Relay, RelayDocument } from '../schemas/relay.schema';

@Injectable()
export class RelayRepository {
  private readonly logger = new Logger(RelayRepository.name);

  constructor(
    @InjectModel(Relay.name) private readonly relayModel: Model<RelayDocument>,
  ) {}

  /**
   * 根据玩家ID查找转播记录
   */
  async findByPlayerId(uid: string): Promise<RelayDocument | null> {
    try {
      return await this.relayModel.findOne({ uid }).exec();
    } catch (error) {
      this.logger.error(`根据玩家ID查找转播记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 创建转播记录
   */
  async create(relayData: Partial<Relay>): Promise<RelayDocument> {
    try {
      const relay = new this.relayModel({
        ...relayData,
        isJoin: 0,
        integral: 0,
        totalIntegral: 0,
        joinTime: 0,
        expireTime: 0,
        awardList: [],
        totalAwardsReceived: 0,
        convertHistory: [],
        totalConvertCount: 0,
        totalIntegralSpent: 0,
        lastUpdateTime: Date.now(),
        seasonId: 0,
        weekId: 0,
      });

      const savedRelay = await relay.save();
      this.logger.log(`转播记录创建成功: ${savedRelay.uid}`);
      return savedRelay;
    } catch (error) {
      this.logger.error('创建转播记录失败', error);
      throw error;
    }
  }

  /**
   * 获取或创建转播记录
   */
  async getOrCreateRelay(uid: string, serverId: string): Promise<RelayDocument> {
    try {
      let relay = await this.findByPlayerId(uid);
      
      if (!relay) {
        relay = await this.create({
          uid,
          serverId,
        });
        this.logger.log(`创建新转播记录: ${uid}`);
      }
      
      return relay;
    } catch (error) {
      this.logger.error('获取或创建转播记录失败', error);
      throw error;
    }
  }

  /**
   * 更新转播记录
   */
  async update(uid: string, updateData: Partial<Relay>): Promise<RelayDocument | null> {
    try {
      const updatedRelay = await this.relayModel.findOneAndUpdate(
        { uid },
        { ...updateData, lastUpdateTime: Date.now() },
        { new: true }
      ).exec();

      if (updatedRelay) {
        this.logger.log(`转播记录更新成功: ${uid}`);
      }

      return updatedRelay;
    } catch (error) {
      this.logger.error(`更新转播记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 删除转播记录
   */
  async delete(uid: string): Promise<boolean> {
    try {
      const result = await this.relayModel.deleteOne({ uid }).exec();
      if (result.deletedCount > 0) {
        this.logger.log(`转播记录删除成功: ${uid}`);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`删除转播记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 查找已过期的转播记录
   */
  async findExpiredRelays(): Promise<RelayDocument[]> {
    try {
      const now = Date.now();
      return await this.relayModel.find({
        isJoin: 1,
        expireTime: { $lt: now }
      }).exec();
    } catch (error) {
      this.logger.error('查找已过期的转播记录失败', error);
      throw error;
    }
  }

  /**
   * 查找活跃的转播玩家
   */
  async findActiveRelayPlayers(): Promise<RelayDocument[]> {
    try {
      const now = Date.now();
      return await this.relayModel.find({
        isJoin: 1,
        expireTime: { $gt: now }
      }).exec();
    } catch (error) {
      this.logger.error('查找活跃的转播玩家失败', error);
      throw error;
    }
  }

  /**
   * 获取转播统计
   */
  async getRelayStats(): Promise<any> {
    try {
      const stats = await this.relayModel.aggregate([
        {
          $group: {
            _id: null,
            totalPlayers: { $sum: 1 },
            activePlayers: { 
              $sum: { 
                $cond: [
                  { $and: [
                    { $eq: ['$isJoin', 1] },
                    { $gt: ['$expireTime', Date.now()] }
                  ]},
                  1,
                  0
                ]
              }
            },
            totalIntegral: { $sum: '$totalIntegral' },
            totalAwardsReceived: { $sum: '$totalAwardsReceived' },
            totalConvertCount: { $sum: '$totalConvertCount' },
            totalIntegralSpent: { $sum: '$totalIntegralSpent' },
            avgIntegral: { $avg: '$integral' },
            avgAwards: { $avg: '$totalAwardsReceived' },
          }
        }
      ]).exec();

      return stats[0] || {
        totalPlayers: 0,
        activePlayers: 0,
        totalIntegral: 0,
        totalAwardsReceived: 0,
        totalConvertCount: 0,
        totalIntegralSpent: 0,
        avgIntegral: 0,
        avgAwards: 0,
      };
    } catch (error) {
      this.logger.error('获取转播统计失败', error);
      throw error;
    }
  }

  /**
   * 获取积分排行榜
   */
  async getIntegralRanking(limit: number = 100): Promise<RelayDocument[]> {
    try {
      const now = Date.now();
      return await this.relayModel.find({
        isJoin: 1,
        expireTime: { $gt: now }
      })
        .sort({ integral: -1, totalIntegral: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error('获取积分排行榜失败', error);
      throw error;
    }
  }

  /**
   * 批量更新转播记录
   */
  async batchUpdate(updates: Array<{ uid: string; updateData: Partial<Relay> }>): Promise<void> {
    try {
      const updatePromises = updates.map(({ uid, updateData }) =>
        this.relayModel.updateOne(
          { uid },
          { ...updateData, lastUpdateTime: Date.now() }
        ).exec()
      );

      await Promise.all(updatePromises);
      this.logger.log(`批量更新转播记录: ${updates.length} 条记录`);
    } catch (error) {
      this.logger.error('批量更新转播记录失败', error);
      throw error;
    }
  }

  /**
   * 清理过期的转播记录
   */
  async cleanupExpiredRelays(): Promise<number> {
    try {
      const expiredRelays = await this.findExpiredRelays();
      let cleanedCount = 0;

      for (const relay of expiredRelays) {
        // 重置转播状态
        relay.isJoin = 0;
        relay.integral = 0;
        relay.joinTime = 0;
        relay.expireTime = 0;
        relay.awardList = [];
        
        await relay.save();
        cleanedCount++;
      }

      this.logger.log(`清理过期转播记录: ${cleanedCount} 条记录`);
      return cleanedCount;
    } catch (error) {
      this.logger.error('清理过期转播记录失败', error);
      throw error;
    }
  }
}
