/**
 * 兑换大厅Schema
 * 基于old项目exchangeHall.js实体迁移
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 兑换物品记录子文档
@Schema({ _id: false })
export class ExchangeItemRecord {
  @Prop({ required: true })
  id: number;               // 物品ID

  @Prop({ required: true })
  itemId: number;           // 实际物品ID

  @Prop({ required: true })
  price: number;            // 价格

  @Prop({ default: 0 })
  isBuy: number;            // 是否已购买 0未购买 1已购买

  @Prop({ default: 0 })
  awardGroup: number;       // 奖励组

  @Prop({ default: 0 })
  rate: number;             // 权重

  @Prop({ default: 0 })
  rewardTeam: number;       // 奖励队伍类型
}

// 刷新次数记录子文档
@Schema({ _id: false })
export class RefreshRecord {
  @Prop({ required: true })
  type: number;             // 刷新类型

  @Prop({ default: 1 })
  num: number;              // 刷新次数
}

// 合成记录子文档
@Schema({ _id: false })
export class CompoundRecord {
  @Prop({ required: true })
  resId: number;            // 物品ID

  @Prop({ required: true })
  num: number;              // 合成数量

  @Prop({ required: true })
  compoundTime: number;     // 合成时间

  @Prop({ required: true })
  resultItemId: number;     // 合成结果物品ID

  @Prop({ required: true })
  resultNum: number;        // 合成结果数量
}

// 分解记录子文档
@Schema({ _id: false })
export class DecomposeRecord {
  @Prop({ required: true })
  resId: number;            // 物品ID

  @Prop({ required: true })
  num: number;              // 分解数量

  @Prop({ required: true })
  decomposeTime: number;    // 分解时间

  @Prop({ required: true })
  chipGained: number;       // 获得的碎片数量
}

// 主兑换大厅Schema（基于old项目ExchangeHall结构）
@Schema({ 
  collection: 'exchanges', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Exchange {
  @Prop({ required: true, unique: true })
  uid: string;              // 玩家ID

  @Prop({ required: true })
  serverId: string;         // 服务器ID

  // 兑换大厅相关（基于old项目ExchangeHall实体）
  @Prop({ type: [ExchangeItemRecord], default: [] })
  exchangeList: ExchangeItemRecord[];  // 兑换物品列表

  @Prop({ type: [RefreshRecord], default: [] })
  exchangeNumList: RefreshRecord[];    // 刷新次数记录

  @Prop({ default: 0 })
  reExchangeHallTime: number;          // 兑换大厅刷新时间

  // 合成分解记录
  @Prop({ type: [CompoundRecord], default: [] })
  compoundHistory: CompoundRecord[];   // 合成历史

  @Prop({ type: [DecomposeRecord], default: [] })
  decomposeHistory: DecomposeRecord[]; // 分解历史

  // 统计信息
  @Prop({ default: 0 })
  totalCompounds: number;   // 总合成次数

  @Prop({ default: 0 })
  totalDecomposes: number;  // 总分解次数

  @Prop({ default: 0 })
  totalExchanges: number;   // 总兑换次数

  @Prop({ default: 0 })
  totalChipSpent: number;   // 总消耗碎片

  @Prop({ default: 0 })
  totalChipGained: number;  // 总获得碎片

  @Prop({ default: 0 })
  lastUpdateTime: number;   // 最后更新时间
}

export type ExchangeDocument = Exchange & Document & ExchangeMethods;

export const ExchangeSchema = SchemaFactory.createForClass(Exchange);

// 定义方法接口
export interface ExchangeMethods {
  needsDailyRefresh(): boolean;
  canExchangeItem(itemId: number): boolean;
  addCompoundRecord(resId: number, num: number, resultItemId: number, resultNum: number): void;
  addDecomposeRecord(resId: number, num: number, chipGained: number): void;
  getRefreshNumByType(type: number): number;
  addRefreshNumByType(type: number): number;
  checkItemIsBuy(id: number): boolean;
  markItemAsBought(id: number): void;
}

// 添加实例方法
ExchangeSchema.methods.needsDailyRefresh = function() {
  const now = Date.now();
  const oneDayMs = 24 * 60 * 60 * 1000;
  return (now - this.reExchangeHallTime) >= oneDayMs;
};

ExchangeSchema.methods.canExchangeItem = function(itemId: number) {
  return !this.checkItemIsBuy(itemId);
};

ExchangeSchema.methods.addCompoundRecord = function(resId: number, num: number, resultItemId: number, resultNum: number) {
  this.compoundHistory.push({
    resId,
    num,
    compoundTime: Date.now(),
    resultItemId,
    resultNum,
  });
  
  this.totalCompounds++;
  this.lastUpdateTime = Date.now();
};

ExchangeSchema.methods.addDecomposeRecord = function(resId: number, num: number, chipGained: number) {
  this.decomposeHistory.push({
    resId,
    num,
    decomposeTime: Date.now(),
    chipGained,
  });
  
  this.totalDecomposes++;
  this.totalChipGained += chipGained;
  this.lastUpdateTime = Date.now();
};

ExchangeSchema.methods.getRefreshNumByType = function(type: number) {
  const record = this.exchangeNumList.find(item => item.type === type);
  return record ? record.num : 1;
};

ExchangeSchema.methods.addRefreshNumByType = function(type: number) {
  const record = this.exchangeNumList.find(item => item.type === type);
  if (record) {
    record.num++;
    return record.num;
  } else {
    this.exchangeNumList.push({ type, num: 2 });
    return 2;
  }
};

ExchangeSchema.methods.checkItemIsBuy = function(id: number) {
  const item = this.exchangeList.find(item => item.id === id);
  return item ? item.isBuy === 1 : false;
};

ExchangeSchema.methods.markItemAsBought = function(id: number) {
  const item = this.exchangeList.find(item => item.id === id);
  if (item) {
    item.isBuy = 1;
    this.totalExchanges++;
    this.lastUpdateTime = Date.now();
  }
};

// 添加中间件
ExchangeSchema.pre('save', function(next) {
  this.lastUpdateTime = Date.now();
  next();
});

// 添加静态方法
ExchangeSchema.statics.findByPlayer = function(uid: string) {
  return this.findOne({ uid });
};
