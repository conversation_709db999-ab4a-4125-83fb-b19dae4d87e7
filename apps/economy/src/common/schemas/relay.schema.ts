/**
 * 联赛转播Schema
 * 基于old项目relay.js实体迁移
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 转播奖励记录子文档
@Schema({ _id: false })
export class RelayAwardRecord {
  @Prop({ required: true })
  id: number;               // 奖励ID

  @Prop({ required: true })
  type: number;             // 奖励类型

  @Prop({ required: true })
  integral: number;         // 需要积分

  @Prop({ default: 0 })
  isReceive: number;        // 是否已领取 0未领取 1已领取

  @Prop({ type: Object })
  rewards: any;             // 奖励内容

  @Prop({ default: 0 })
  receiveTime: number;      // 领取时间
}

// 商城兑换记录子文档
@Schema({ _id: false })
export class ConvertibilityRecord {
  @Prop({ required: true })
  id: number;               // 兑换ID

  @Prop({ required: true })
  itemId: number;           // 物品ID

  @Prop({ required: true })
  price: number;            // 价格（积分）

  @Prop({ required: true })
  num: number;              // 兑换数量

  @Prop({ required: true })
  convertTime: number;      // 兑换时间
}

// 主联赛转播Schema（基于old项目Relay结构）
@Schema({ 
  collection: 'relays', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Relay {
  @Prop({ required: true, unique: true })
  uid: string;              // 玩家ID

  @Prop({ required: true })
  serverId: string;         // 服务器ID

  // 转播相关（基于old项目Relay实体）
  @Prop({ default: 0 })
  isJoin: number;           // 是否加入转播 0未加入 1已加入

  @Prop({ default: 0 })
  integral: number;         // 当前积分

  @Prop({ default: 0 })
  totalIntegral: number;    // 总积分

  @Prop({ default: 0 })
  joinTime: number;         // 加入时间

  @Prop({ default: 0 })
  expireTime: number;       // 过期时间

  // 奖励相关
  @Prop({ type: [RelayAwardRecord], default: [] })
  awardList: RelayAwardRecord[];  // 奖励列表

  @Prop({ default: 0 })
  totalAwardsReceived: number;    // 总领取奖励数

  // 商城兑换相关
  @Prop({ type: [ConvertibilityRecord], default: [] })
  convertHistory: ConvertibilityRecord[];  // 兑换历史

  @Prop({ default: 0 })
  totalConvertCount: number;      // 总兑换次数

  @Prop({ default: 0 })
  totalIntegralSpent: number;     // 总消耗积分

  // 统计信息
  @Prop({ default: 0 })
  lastUpdateTime: number;   // 最后更新时间

  @Prop({ default: 0 })
  seasonId: number;         // 赛季ID

  @Prop({ default: 0 })
  weekId: number;           // 周ID
}

export type RelayDocument = Relay & Document & RelayMethods;

export const RelaySchema = SchemaFactory.createForClass(Relay);

// 定义方法接口
export interface RelayMethods {
  isExpired(): boolean;
  canJoinRelay(): boolean;
  addIntegral(amount: number): void;
  canReceiveAward(awardId: number): boolean;
  receiveAward(awardId: number): boolean;
  canConvertItem(itemId: number, price: number): boolean;
  addConvertRecord(id: number, itemId: number, price: number, num: number): void;
  getAvailableAwards(): RelayAwardRecord[];
}

// 添加实例方法
RelaySchema.methods.isExpired = function() {
  return Date.now() > this.expireTime;
};

RelaySchema.methods.canJoinRelay = function() {
  return this.isJoin === 0 && !this.isExpired();
};

RelaySchema.methods.addIntegral = function(amount: number) {
  this.integral += amount;
  this.totalIntegral += amount;
  this.lastUpdateTime = Date.now();
};

RelaySchema.methods.canReceiveAward = function(awardId: number) {
  const award = this.awardList.find(a => a.id === awardId);
  if (!award) return false;
  
  return award.isReceive === 0 && this.integral >= award.integral;
};

RelaySchema.methods.receiveAward = function(awardId: number) {
  const award = this.awardList.find(a => a.id === awardId);
  if (!award || !this.canReceiveAward(awardId)) return false;
  
  award.isReceive = 1;
  award.receiveTime = Date.now();
  this.totalAwardsReceived++;
  this.lastUpdateTime = Date.now();
  
  return true;
};

RelaySchema.methods.canConvertItem = function(itemId: number, price: number) {
  return this.integral >= price;
};

RelaySchema.methods.addConvertRecord = function(id: number, itemId: number, price: number, num: number) {
  this.convertHistory.push({
    id,
    itemId,
    price,
    num,
    convertTime: Date.now(),
  });
  
  this.integral -= price;
  this.totalConvertCount++;
  this.totalIntegralSpent += price;
  this.lastUpdateTime = Date.now();
};

RelaySchema.methods.getAvailableAwards = function() {
  return this.awardList.filter(award => 
    award.isReceive === 0 && this.integral >= award.integral
  );
};

// 添加中间件
RelaySchema.pre('save', function(next) {
  this.lastUpdateTime = Date.now();
  next();
});

// 添加静态方法
RelaySchema.statics.findByPlayer = function(uid: string) {
  return this.findOne({ uid });
};
