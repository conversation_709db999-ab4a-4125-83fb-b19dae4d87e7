/**
 * 商店购买记录Schema
 * 基于old项目的Store实体迁移
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 商店类型枚举
export enum ShopType {
  NORMAL = 1,       // 普通商店
  VIP = 2,          // VIP商店
  LIMIT = 3,        // 限时商店
  SEASON = 4,       // 赛季商店
  EXCHANGE = 5,     // 兑换大厅
}

// 刷新周期枚举
export enum RefreshCycle {
  DAILY = 1,        // 每日刷新
  WEEKLY = 2,       // 每周刷新
  MONTHLY = 3,      // 每月刷新
  SEASON = 4,       // 赛季刷新
  NEVER = 5,        // 不刷新
}

// 购买记录子文档
@Schema({ _id: false })
export class PurchaseRecord {
  @Prop({ required: true })
  goodsId: number;          // 商品ID

  @Prop({ default: 0 })
  quantity: number;         // 购买数量

  @Prop({ default: 0 })
  totalCost: number;        // 总花费

  @Prop({ default: Date.now })
  purchaseTime: number;     // 购买时间

  @Prop({ default: '' })
  currency: string;         // 货币类型（gold、cash、energy等）
}

// 限购记录子文档
@Schema({ _id: false })
export class LimitRecord {
  @Prop({ required: true })
  goodsId: number;          // 商品ID

  @Prop({ default: 0 })
  purchasedCount: number;   // 已购买数量

  @Prop({ default: 0 })
  limitCount: number;       // 限购数量

  @Prop({ default: 0 })
  resetTime: number;        // 重置时间
}

// 主商店记录Schema
@Schema({ 
  collection: 'shops', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Shop {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  shopId: string;           // 商店记录ID

  @Prop({ required: true, index: true })
  characterId: string;      // 角色ID

  @Prop({ required: true, index: true })
  serverId: string;         // 区服ID

  @Prop({ required: true, enum: ShopType })
  shopType: ShopType;       // 商店类型

  // 刷新时间管理（基于old项目Store实体）
  @Prop({ default: Date.now })
  everyDayReTime: number;   // 每日刷新时间

  @Prop({ default: Date.now })
  weeklyReTime: number;     // 每周刷新时间

  @Prop({ default: Date.now })
  monthlyReTime: number;    // 每月刷新时间

  @Prop({ default: Date.now })
  seasonReTime: number;     // 赛季刷新时间

  // 购买记录（基于old项目Store实体）
  @Prop({ type: [LimitRecord], default: [] })
  everyDayBuy: LimitRecord[];   // 每日限购记录

  @Prop({ type: [LimitRecord], default: [] })
  weeklyBuy: LimitRecord[];     // 每周限购记录

  @Prop({ type: [LimitRecord], default: [] })
  monthlyBuy: LimitRecord[];    // 每月限购记录

  @Prop({ type: [LimitRecord], default: [] })
  seasonBuy: LimitRecord[];     // 赛季限购记录

  // 购买历史
  @Prop({ type: [PurchaseRecord], default: [] })
  purchaseHistory: PurchaseRecord[];  // 购买历史记录

  // VIP商店特有字段（基于old项目VipShop实体）
  @Prop({ type: Object, default: {} })
  goodsBuyList: Record<string, any>;  // VIP商品购买列表

  @Prop({ type: Object, default: {} })
  historyGoodBuyList: Record<string, any>;  // 历史购买统计

  // 月卡相关字段（基于old项目MonthCard实体）
  @Prop({ default: 0 })
  isGetReward: number;      // 当天是否已领取月卡奖励 0未领取 1已领取

  @Prop({ default: 0 })
  allDay: number;           // 月卡总天数

  @Prop({ default: 0 })
  cardTime: number;         // 月卡剩余天数

  @Prop({ default: 0 })
  refreshTime: number;      // 月卡刷新时间

  @Prop({ default: 0 })
  buyMonthCardTime: number; // 购买月卡时间

  @Prop({ default: 0 })
  buyMonthNum: number;      // 购买月卡次数

  @Prop({ type: [Object], default: [] })
  monthCardInfo: any[];     // 月卡信息

  @Prop({ default: 0 })
  isBuy: number;            // 是否购买月卡 0否 1是

  // 统计信息
  @Prop({ default: 0 })
  totalSpent: number;       // 总消费金额

  @Prop({ default: 0 })
  totalPurchases: number;   // 总购买次数

  @Prop({ default: 0 })
  lastPurchaseTime: number; // 最后购买时间

  // 虚拟字段：是否需要刷新
  get needsDailyRefresh(): boolean {
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;
    return (now - this.everyDayReTime) >= oneDayMs;
  }

  get needsWeeklyRefresh(): boolean {
    const now = Date.now();
    const oneWeekMs = 7 * 24 * 60 * 60 * 1000;
    return (now - this.weeklyReTime) >= oneWeekMs;
  }

  get needsMonthlyRefresh(): boolean {
    const now = Date.now();
    const oneMonthMs = 30 * 24 * 60 * 60 * 1000;
    return (now - this.monthlyReTime) >= oneMonthMs;
  }

  // 虚拟字段：月卡状态
  get isMonthCardActive(): boolean {
    return this.isBuy === 1 && this.cardTime > 0;
  }

  get canClaimMonthCardReward(): boolean {
    return this.isMonthCardActive && this.isGetReward === 0;
  }
}

export const ShopSchema = SchemaFactory.createForClass(Shop);

// 定义方法接口
export interface ShopMethods {
  canPurchase(goodsId: number, quantity: number, cycle: RefreshCycle): boolean;
  addPurchaseRecord(goodsId: number, quantity: number, cost: number, currency: string): void;
  refreshLimits(cycle: RefreshCycle): void;
  claimMonthCardReward(): any;
}

// 定义Document类型
export type ShopDocument = Shop & Document & ShopMethods;

// 创建索引
ShopSchema.index({ shopId: 1 }, { unique: true });
ShopSchema.index({ characterId: 1 });
ShopSchema.index({ serverId: 1 });
ShopSchema.index({ shopType: 1 });
ShopSchema.index({ characterId: 1, shopType: 1 });

// 添加虚拟字段
ShopSchema.virtual('needsDailyRefresh').get(function() {
  const now = Date.now();
  const oneDayMs = 24 * 60 * 60 * 1000;
  return (now - this.everyDayReTime) >= oneDayMs;
});

ShopSchema.virtual('needsWeeklyRefresh').get(function() {
  const now = Date.now();
  const oneWeekMs = 7 * 24 * 60 * 60 * 1000;
  return (now - this.weeklyReTime) >= oneWeekMs;
});

ShopSchema.virtual('needsMonthlyRefresh').get(function() {
  const now = Date.now();
  const oneMonthMs = 30 * 24 * 60 * 60 * 1000;
  return (now - this.monthlyReTime) >= oneMonthMs;
});

ShopSchema.virtual('isMonthCardActive').get(function() {
  return this.isBuy === 1 && this.cardTime > 0;
});

ShopSchema.virtual('canClaimMonthCardReward').get(function() {
  return this.isMonthCardActive && this.isGetReward === 0;
});

// 添加实例方法
ShopSchema.methods.canPurchase = function(goodsId: number, quantity: number, cycle: RefreshCycle) {
  let limitRecords: LimitRecord[] = [];
  
  switch (cycle) {
    case RefreshCycle.DAILY:
      limitRecords = this.everyDayBuy;
      break;
    case RefreshCycle.WEEKLY:
      limitRecords = this.weeklyBuy;
      break;
    case RefreshCycle.MONTHLY:
      limitRecords = this.monthlyBuy;
      break;
    case RefreshCycle.SEASON:
      limitRecords = this.seasonBuy;
      break;
    default:
      return true; // 无限制
  }
  
  const record = limitRecords.find(r => r.goodsId === goodsId);
  if (!record) return true;
  
  return (record.purchasedCount + quantity) <= record.limitCount;
};

ShopSchema.methods.addPurchaseRecord = function(goodsId: number, quantity: number, cost: number, currency: string) {
  this.purchaseHistory.push({
    goodsId,
    quantity,
    totalCost: cost,
    purchaseTime: Date.now(),
    currency,
  });
  
  this.totalSpent += cost;
  this.totalPurchases += quantity;
  this.lastPurchaseTime = Date.now();
};

ShopSchema.methods.refreshLimits = function(cycle: RefreshCycle) {
  const now = Date.now();
  
  switch (cycle) {
    case RefreshCycle.DAILY:
      this.everyDayBuy = [];
      this.everyDayReTime = now;
      break;
    case RefreshCycle.WEEKLY:
      this.weeklyBuy = [];
      this.weeklyReTime = now;
      break;
    case RefreshCycle.MONTHLY:
      this.monthlyBuy = [];
      this.monthlyReTime = now;
      break;
    case RefreshCycle.SEASON:
      this.seasonBuy = [];
      this.seasonReTime = now;
      break;
  }
};

ShopSchema.methods.claimMonthCardReward = function() {
  if (!this.canClaimMonthCardReward) {
    return null;
  }
  
  this.isGetReward = 1;
  this.refreshTime = Date.now();
  
  // TODO: 返回月卡奖励内容
  return {
    claimed: true,
    rewards: [], // 具体奖励内容
  };
};
