import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { PaymentService } from './payment.service';

@Controller()
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(private readonly paymentService: PaymentService) {}

  /**
   * 处理支付
   */
  @MessagePattern('payment.process')
  async processPayment(@Payload() payload: any) {
    this.logger.log('处理支付请求');
    const result = await this.paymentService.processPayment(payload);
    return {
      code: 0,
      message: '支付处理成功',
      data: result,
    };
  }

  /**
   * 验证支付
   */
  @MessagePattern('payment.verify')
  async verifyPayment(@Payload() payload: { transactionId: string }) {
    this.logger.log(`验证支付: ${payload.transactionId}`);
    const result = await this.paymentService.verifyPayment(payload.transactionId);
    return {
      code: 0,
      message: '验证成功',
      data: result,
    };
  }
}
