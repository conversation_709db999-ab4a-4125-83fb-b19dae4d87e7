import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  /**
   * 处理支付
   * 基于old项目: rechargeCreateOrder + payCallbackFromDqd逻辑
   *
   * 实现逻辑：
   * 1. 验证支付参数
   * 2. 创建支付订单
   * 3. 调用第三方支付接口
   * 4. 返回支付结果
   */
  async processPayment(paymentData: any): Promise<any> {
    this.logger.log('处理支付请求', paymentData);

    try {
      // 1. 验证支付参数
      const validationResult = await this.validatePaymentData(paymentData);
      if (!validationResult.valid) {
        return {
          success: false,
          error: validationResult.error,
          code: 'INVALID_PARAMS',
        };
      }

      // 2. 检查重复支付
      const duplicateCheck = await this.checkDuplicatePayment(paymentData);
      if (duplicateCheck.isDuplicate) {
        return {
          success: false,
          error: '重复支付请求',
          code: 'DUPLICATE_PAYMENT',
          existingOrderId: duplicateCheck.orderId,
        };
      }

      // 3. 创建支付订单（基于old项目insertNewOrder逻辑）
      const orderResult = await this.createPaymentOrder(paymentData);
      if (!orderResult.success) {
        return {
          success: false,
          error: '创建订单失败',
          code: 'CREATE_ORDER_FAILED',
        };
      }

      // 4. 获取充值配置
      const rechargeConfig = await this.getRechargeConfig(paymentData.rechargeId);
      if (!rechargeConfig) {
        return {
          success: false,
          error: '充值配置不存在',
          code: 'INVALID_RECHARGE_ID',
        };
      }

      // 5. 调用第三方支付接口（基于old项目的支付渠道逻辑）
      const paymentResult = await this.callThirdPartyPayment({
        orderId: orderResult.orderId,
        amount: rechargeConfig.rechargeAmount,
        playerId: paymentData.playerId,
        rechargeId: paymentData.rechargeId,
        channel: paymentData.channel || 'default',
      });

      // 6. 更新订单状态
      await this.updateOrderStatus(orderResult.orderId, 'PAYMENT_INITIATED');

      return {
        success: true,
        orderId: orderResult.orderId,
        transactionId: paymentResult.transactionId,
        paymentUrl: paymentResult.paymentUrl,
        amount: rechargeConfig.rechargeAmount,
        currency: rechargeConfig.currency || 'CNY',
      };
    } catch (error) {
      this.logger.error('处理支付失败', error);
      return {
        success: false,
        error: '支付处理异常',
        code: 'PAYMENT_ERROR',
      };
    }
  }

  /**
   * 验证支付
   * 基于old项目: payCallbackFromDqd + dealWithPaymentResult逻辑
   *
   * 实现逻辑：
   * 1. 验证签名
   * 2. 查询订单状态
   * 3. 防重复处理
   * 4. 发放充值奖励
   * 5. 更新订单状态
   */
  async verifyPayment(transactionId: string): Promise<any> {
    this.logger.log(`验证支付: ${transactionId}`);

    try {
      // 1. 查询订单信息
      const orderInfo = await this.findOrderByTransactionId(transactionId);
      if (!orderInfo) {
        return {
          verified: false,
          error: '订单不存在',
          code: 'ORDER_NOT_FOUND',
        };
      }

      // 2. 检查订单状态（基于old项目防重复充值逻辑）
      if (orderInfo.state === 'SEND_GOODS') {
        return {
          verified: true,
          status: 'already_processed',
          message: '订单已处理',
        };
      }

      // 3. 验证支付状态（调用第三方支付查询接口）
      const paymentStatus = await this.queryThirdPartyPaymentStatus(transactionId);
      if (!paymentStatus.success) {
        return {
          verified: false,
          error: '支付验证失败',
          code: 'PAYMENT_VERIFICATION_FAILED',
        };
      }

      // 4. 检查支付金额是否匹配
      const rechargeConfig = await this.getRechargeConfig(orderInfo.rechargeId);
      if (paymentStatus.amount !== rechargeConfig.rechargeAmount) {
        return {
          verified: false,
          error: '支付金额不匹配',
          code: 'AMOUNT_MISMATCH',
        };
      }

      // 5. 发放充值奖励（基于old项目dealWithPaymentResult逻辑）
      const rewardResult = await this.grantRechargeRewards({
        playerId: orderInfo.playerId,
        rechargeId: orderInfo.rechargeId,
        amount: paymentStatus.amount,
        orderId: orderInfo.orderId,
      });

      if (!rewardResult.success) {
        return {
          verified: false,
          error: '奖励发放失败',
          code: 'REWARD_GRANT_FAILED',
        };
      }

      // 6. 更新订单状态为已发货
      await this.updateOrderStatus(orderInfo.orderId, 'SEND_GOODS');

      // 7. 触发相关任务和统计
      await this.triggerRechargeRelatedTasks(orderInfo.playerId, orderInfo.rechargeId);

      return {
        verified: true,
        status: 'completed',
        orderId: orderInfo.orderId,
        playerId: orderInfo.playerId,
        amount: paymentStatus.amount,
        rewardGranted: rewardResult.rewards,
      };
    } catch (error) {
      this.logger.error('验证支付失败', error);
      return {
        verified: false,
        error: '支付验证异常',
        code: 'VERIFICATION_ERROR',
      };
    }
  }

  /**
   * 验证支付参数
   * 基于old项目: 参数验证逻辑
   */
  private async validatePaymentData(paymentData: any): Promise<{ valid: boolean; error?: string }> {
    try {
      // 检查必要参数
      if (!paymentData.playerId) {
        return { valid: false, error: '玩家ID不能为空' };
      }

      if (!paymentData.rechargeId) {
        return { valid: false, error: '充值ID不能为空' };
      }

      if (!paymentData.gid) {
        return { valid: false, error: '游戏服务器ID不能为空' };
      }

      // 验证充值ID是否存在
      const rechargeConfig = await this.getRechargeConfig(paymentData.rechargeId);
      if (!rechargeConfig) {
        return { valid: false, error: '无效的充值ID' };
      }

      // 验证玩家是否存在
      const playerExists = await this.checkPlayerExists(paymentData.playerId, paymentData.gid);
      if (!playerExists) {
        return { valid: false, error: '玩家不存在' };
      }

      return { valid: true };
    } catch (error) {
      this.logger.error('验证支付参数失败', error);
      return { valid: false, error: '参数验证异常' };
    }
  }

  /**
   * 检查重复支付
   * 基于old项目: 防重复支付逻辑
   */
  private async checkDuplicatePayment(paymentData: any): Promise<{ isDuplicate: boolean; orderId?: string }> {
    try {
      // TODO: 查询数据库检查是否有相同的未完成订单
      // const existingOrder = await this.paymentRepository.findPendingOrder({
      //   playerId: paymentData.playerId,
      //   rechargeId: paymentData.rechargeId,
      //   state: ['CREATE_ORDER', 'PAYMENT_INITIATED']
      // });

      // if (existingOrder) {
      //   return { isDuplicate: true, orderId: existingOrder.orderId };
      // }

      return { isDuplicate: false };
    } catch (error) {
      this.logger.error('检查重复支付失败', error);
      return { isDuplicate: false };
    }
  }

  /**
   * 创建支付订单
   * 基于old项目: insertNewOrder逻辑
   */
  private async createPaymentOrder(paymentData: any): Promise<{ success: boolean; orderId?: string }> {
    try {
      // 生成唯一订单ID（基于old项目utils.syncCreateUid()）
      const orderId = this.generateOrderId();

      // 获取充值配置
      const rechargeConfig = await this.getRechargeConfig(paymentData.rechargeId);

      // 创建订单数据
      const orderData = {
        orderId,
        time: Date.now(),
        playerId: paymentData.playerId,
        gid: paymentData.gid,
        rechargeId: paymentData.rechargeId,
        price: rechargeConfig.rechargeAmount,
        dqdOrderId: '', // 第三方订单ID，支付时填入
        state: 'CREATE_ORDER', // 基于old项目CommonEnum.RECHARGE_STATE.CREATE_ORDER
        channel: paymentData.channel || 'default',
        platform: paymentData.platform || 'default',
        createTime: new Date(),
      };

      // TODO: 保存到数据库
      // await this.paymentRepository.create(orderData);

      this.logger.log(`创建支付订单成功: ${orderId}`);
      return { success: true, orderId };
    } catch (error) {
      this.logger.error('创建支付订单失败', error);
      return { success: false };
    }
  }

  /**
   * 生成订单ID
   * 基于old项目: utils.syncCreateUid()逻辑
   */
  private generateOrderId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${timestamp}${random}`;
  }

  /**
   * 获取充值配置
   * 基于old项目: dataApi.allData.data["Recharge"]
   */
  private async getRechargeConfig(rechargeId: number): Promise<any> {
    try {
      // TODO: 从配置表获取充值配置
      // const config = await this.gameConfig.recharge?.get(rechargeId);
      // return config;

      // 暂时返回模拟数据
      return {
        rechargeAmount: 100,
        currency: 'CNY',
        goldReward: 1000,
        firstTimeBonus: 500,
      };
    } catch (error) {
      this.logger.error('获取充值配置失败', error);
      return null;
    }
  }

  /**
   * 调用第三方支付接口
   * 基于old项目: 第三方支付渠道逻辑
   */
  private async callThirdPartyPayment(paymentInfo: any): Promise<any> {
    try {
      // 调用第三方支付接口（基于old项目支付渠道逻辑）
      const paymentChannel = paymentInfo.channel || 'default';
      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 根据不同支付渠道构建支付参数
      const paymentParams = this.buildPaymentParams(paymentInfo, transactionId);

      // 根据渠道选择支付接口
      let paymentResult;
      switch (paymentChannel) {
        case 'alipay':
          paymentResult = await this.callAlipayInterface(paymentParams);
          break;
        case 'wechat':
          paymentResult = await this.callWechatInterface(paymentParams);
          break;
        case 'apple':
          paymentResult = await this.callApplePayInterface(paymentParams);
          break;
        default:
          // 默认支付渠道
          paymentResult = await this.callDefaultPayInterface(paymentParams);
          break;
      }

      if (paymentResult.success) {
        return {
          success: true,
          transactionId,
          paymentUrl: paymentResult.paymentUrl,
          status: 'pending',
          channel: paymentChannel,
        };
      } else {
        return {
          success: false,
          error: paymentResult.error,
          code: 'PAYMENT_INTERFACE_ERROR',
        };
      }
    } catch (error) {
      this.logger.error('调用第三方支付失败', error);
      return {
        success: false,
        error: '第三方支付调用失败',
      };
    }
  }

  /**
   * 查询第三方支付状态
   * 基于old项目: 第三方支付查询逻辑
   */
  private async queryThirdPartyPaymentStatus(transactionId: string): Promise<any> {
    try {
      // 调用第三方支付查询接口（基于old项目支付查询逻辑）
      // 从transactionId中提取支付渠道信息
      const paymentChannel = this.extractChannelFromTransactionId(transactionId);

      let queryResult;
      switch (paymentChannel) {
        case 'alipay':
          queryResult = await this.queryAlipayStatus(transactionId);
          break;
        case 'wechat':
          queryResult = await this.queryWechatStatus(transactionId);
          break;
        case 'apple':
          queryResult = await this.queryApplePayStatus(transactionId);
          break;
        default:
          queryResult = await this.queryDefaultPayStatus(transactionId);
          break;
      }

      if (queryResult.success) {
        return {
          success: true,
          status: queryResult.status, // 'pending', 'completed', 'failed'
          amount: queryResult.amount,
          transactionId,
          paidTime: queryResult.paidTime,
          channel: paymentChannel,
        };
      } else {
        return {
          success: false,
          error: queryResult.error,
          code: 'PAYMENT_QUERY_ERROR',
        };
      }
    } catch (error) {
      this.logger.error('查询第三方支付状态失败', error);
      return {
        success: false,
        error: '支付状态查询失败',
      };
    }
  }

  /**
   * 发放充值奖励
   * 基于old项目: dealWithPaymentResult + calcPaymentAddGold逻辑
   */
  private async grantRechargeRewards(rewardInfo: any): Promise<any> {
    try {
      // 1. 计算充值奖励（基于old项目calcPaymentAddGold）
      const rewardCalculation = await this.calculateRechargeRewards(rewardInfo);

      // 2. 发放金币奖励
      const goldResult = await this.grantGoldReward(rewardInfo.playerId, rewardCalculation.goldAmount);
      if (!goldResult.success) {
        return { success: false, error: '金币发放失败' };
      }

      // 3. 发放首充奖励（如果是首次充值）
      if (rewardCalculation.isFirstRecharge) {
        const firstRechargeResult = await this.grantFirstRechargeBonus(rewardInfo.playerId, rewardInfo.rechargeId);
        if (!firstRechargeResult.success) {
          this.logger.warn('首充奖励发放失败', firstRechargeResult.error);
        }
      }

      // 4. 触发充值相关任务
      await this.triggerRechargeRelatedTasks(rewardInfo.playerId, rewardInfo.rechargeId);

      return {
        success: true,
        rewards: {
          gold: rewardCalculation.goldAmount,
          firstRechargeBonus: rewardCalculation.isFirstRecharge,
          totalValue: rewardCalculation.totalValue,
        },
      };
    } catch (error) {
      this.logger.error('发放充值奖励失败', error);
      return {
        success: false,
        error: '奖励发放异常',
      };
    }
  }

  /**
   * 计算充值奖励
   * 基于old项目: calcPaymentAddGold逻辑
   */
  private async calculateRechargeRewards(rewardInfo: any): Promise<any> {
    try {
      const rechargeConfig = await this.getRechargeConfig(rewardInfo.rechargeId);
      const isFirstRecharge = await this.checkIsFirstRecharge(rewardInfo.playerId, rewardInfo.rechargeId);

      let goldAmount = rechargeConfig.goldReward;

      // 首充双倍奖励
      if (isFirstRecharge) {
        goldAmount += rechargeConfig.firstTimeBonus || rechargeConfig.goldReward;
      }

      return {
        goldAmount,
        isFirstRecharge,
        totalValue: goldAmount,
      };
    } catch (error) {
      this.logger.error('计算充值奖励失败', error);
      return {
        goldAmount: 0,
        isFirstRecharge: false,
        totalValue: 0,
      };
    }
  }

  /**
   * 检查是否首次充值
   * 基于old项目: player.isFirstRecharge逻辑
   */
  private async checkIsFirstRecharge(playerId: string, rechargeId: number): Promise<boolean> {
    try {
      // TODO: 调用Character服务检查首充状态
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.checkFirstRecharge',
      //   { playerId, rechargeId }
      // );
      // return result.data.isFirstRecharge;

      // 暂时返回true
      return true;
    } catch (error) {
      this.logger.error('检查首充状态失败', error);
      return false;
    }
  }

  /**
   * 发放金币奖励
   * 基于old项目: 金币发放逻辑
   */
  private async grantGoldReward(playerId: string, amount: number): Promise<any> {
    try {
      // TODO: 调用Character服务发放金币
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.addGold',
      //   { playerId, amount, reason: 'recharge' }
      // );
      // return result;

      this.logger.log(`发放金币奖励: ${playerId}, 数量: ${amount}`);
      return { success: true };
    } catch (error) {
      this.logger.error('发放金币奖励失败', error);
      return { success: false, error: '金币发放异常' };
    }
  }

  /**
   * 发放首充奖励
   * 基于old项目: 首充额外奖励逻辑
   */
  private async grantFirstRechargeBonus(playerId: string, rechargeId: number): Promise<any> {
    try {
      // TODO: 调用Character服务发放首充奖励
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.grantFirstRechargeBonus',
      //   { playerId, rechargeId }
      // );
      // return result;

      this.logger.log(`发放首充奖励: ${playerId}, 充值ID: ${rechargeId}`);
      return { success: true };
    } catch (error) {
      this.logger.error('发放首充奖励失败', error);
      return { success: false, error: '首充奖励发放异常' };
    }
  }

  /**
   * 触发充值相关任务
   * 基于old项目: 充值任务触发逻辑
   */
  private async triggerRechargeRelatedTasks(playerId: string, rechargeId: number): Promise<void> {
    try {
      // TODO: 调用Activity服务触发充值任务
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.ACTIVITY_SERVICE,
      //   'task.triggerRechargeTask',
      //   { playerId, rechargeId }
      // );

      this.logger.log(`触发充值任务: ${playerId}, 充值ID: ${rechargeId}`);
    } catch (error) {
      this.logger.error('触发充值任务失败', error);
    }
  }

  /**
   * 检查玩家是否存在
   * 基于old项目: 玩家验证逻辑
   */
  private async checkPlayerExists(playerId: string, gid: string): Promise<boolean> {
    try {
      // TODO: 调用Character服务检查玩家
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.checkPlayerExists',
      //   { playerId, gid }
      // );
      // return result.data.exists;

      // 暂时返回true
      return true;
    } catch (error) {
      this.logger.error('检查玩家存在失败', error);
      return false;
    }
  }

  /**
   * 根据交易ID查找订单
   * 基于old项目: findOneOrderByDqdOrderId逻辑
   */
  private async findOrderByTransactionId(transactionId: string): Promise<any> {
    try {
      // TODO: 查询数据库获取订单信息
      // const order = await this.paymentRepository.findByTransactionId(transactionId);
      // return order;

      // 暂时返回模拟数据
      return {
        orderId: `order_${Date.now()}`,
        playerId: 'player_123',
        rechargeId: 1,
        state: 'PAYMENT_INITIATED',
        price: 100,
      };
    } catch (error) {
      this.logger.error('查找订单失败', error);
      return null;
    }
  }

  /**
   * 更新订单状态
   * 基于old项目: updateOrderState逻辑
   */
  private async updateOrderStatus(orderId: string, state: string): Promise<void> {
    try {
      // TODO: 更新数据库订单状态
      // await this.paymentRepository.updateStatus(orderId, state);

      this.logger.log(`更新订单状态: ${orderId} -> ${state}`);
    } catch (error) {
      this.logger.error('更新订单状态失败', error);
    }
  }

  /**
   * 构建支付参数
   * 基于old项目: 支付参数构建逻辑
   */
  private buildPaymentParams(paymentInfo: any, transactionId: string): any {
    return {
      orderId: paymentInfo.orderId,
      transactionId,
      amount: paymentInfo.amount,
      currency: paymentInfo.currency || 'CNY',
      subject: paymentInfo.subject || '游戏充值',
      body: paymentInfo.body || `充值${paymentInfo.amount}元`,
      notifyUrl: `${process.env.PAYMENT_NOTIFY_URL}/payment/notify`,
      returnUrl: `${process.env.PAYMENT_RETURN_URL}/payment/return`,
      timestamp: Date.now(),
    };
  }

  /**
   * 调用支付宝支付接口
   * 基于old项目: 支付宝支付逻辑
   */
  private async callAlipayInterface(params: any): Promise<any> {
    try {
      // TODO: 实际调用支付宝API
      // const alipayResult = await alipaySDK.pageExecute('alipay.trade.page.pay', params);

      // 暂时返回模拟结果
      return {
        success: true,
        paymentUrl: `https://openapi.alipay.com/gateway.do?order=${params.orderId}`,
      };
    } catch (error) {
      this.logger.error('调用支付宝接口失败', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 调用微信支付接口
   * 基于old项目: 微信支付逻辑
   */
  private async callWechatInterface(params: any): Promise<any> {
    try {
      // TODO: 实际调用微信支付API
      // const wechatResult = await wechatPay.unifiedOrder(params);

      // 暂时返回模拟结果
      return {
        success: true,
        paymentUrl: `https://api.mch.weixin.qq.com/pay/unifiedorder?order=${params.orderId}`,
      };
    } catch (error) {
      this.logger.error('调用微信支付接口失败', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 调用苹果支付接口
   * 基于old项目: 苹果支付逻辑
   */
  private async callApplePayInterface(params: any): Promise<any> {
    try {
      // TODO: 实际调用苹果支付API
      // const appleResult = await applePay.verifyReceipt(params);

      // 暂时返回模拟结果
      return {
        success: true,
        paymentUrl: `https://buy.itunes.apple.com/verifyReceipt?order=${params.orderId}`,
      };
    } catch (error) {
      this.logger.error('调用苹果支付接口失败', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 调用默认支付接口
   * 基于old项目: 默认支付逻辑
   */
  private async callDefaultPayInterface(params: any): Promise<any> {
    try {
      // 默认支付接口逻辑
      return {
        success: true,
        paymentUrl: `https://pay.example.com/pay?order=${params.orderId}`,
      };
    } catch (error) {
      this.logger.error('调用默认支付接口失败', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 从交易ID中提取支付渠道
   * 基于old项目: 交易ID解析逻辑
   */
  private extractChannelFromTransactionId(transactionId: string): string {
    // 基于交易ID前缀判断支付渠道
    if (transactionId.startsWith('ali_')) return 'alipay';
    if (transactionId.startsWith('wx_')) return 'wechat';
    if (transactionId.startsWith('apple_')) return 'apple';
    return 'default';
  }

  /**
   * 查询支付宝支付状态
   * 基于old项目: 支付宝状态查询逻辑
   */
  private async queryAlipayStatus(transactionId: string): Promise<any> {
    try {
      // TODO: 实际调用支付宝查询API
      // const result = await alipaySDK.execute('alipay.trade.query', { out_trade_no: transactionId });

      // 暂时返回模拟结果
      return {
        success: true,
        status: 'completed',
        amount: 100,
        paidTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('查询支付宝状态失败', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 查询微信支付状态
   * 基于old项目: 微信支付状态查询逻辑
   */
  private async queryWechatStatus(transactionId: string): Promise<any> {
    try {
      // TODO: 实际调用微信支付查询API
      // const result = await wechatPay.orderQuery({ out_trade_no: transactionId });

      // 暂时返回模拟结果
      return {
        success: true,
        status: 'completed',
        amount: 100,
        paidTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('查询微信支付状态失败', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 查询苹果支付状态
   * 基于old项目: 苹果支付状态查询逻辑
   */
  private async queryApplePayStatus(transactionId: string): Promise<any> {
    try {
      // TODO: 实际调用苹果支付查询API
      // const result = await applePay.verifyReceipt({ receipt: transactionId });

      // 暂时返回模拟结果
      return {
        success: true,
        status: 'completed',
        amount: 100,
        paidTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('查询苹果支付状态失败', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 查询默认支付状态
   * 基于old项目: 默认支付状态查询逻辑
   */
  private async queryDefaultPayStatus(transactionId: string): Promise<any> {
    try {
      // 默认支付状态查询逻辑
      return {
        success: true,
        status: 'completed',
        amount: 100,
        paidTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('查询默认支付状态失败', error);
      return { success: false, error: error.message };
    }
  }
}
