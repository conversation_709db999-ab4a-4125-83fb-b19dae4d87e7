import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ShopService } from './shop.service';
import { 
  PurchaseGoodsDto, 
  RefreshShopDto, 
  ClaimMonthCardDto, 
  BuyMonthCardDto,
  GetShopListDto,
  GetPurchaseHistoryDto,
  ShopStatsDto,
  VipShopDto,
  LimitShopDto
} from '@economy/common/dto/shop.dto';
import { ShopType, RefreshCycle } from '@economy/common/schemas/shop.schema';
import { Cacheable, CacheEvict, CachePut } from '@common/redis';

@Controller()
export class ShopController {
  private readonly logger = new Logger(ShopController.name);

  constructor(private readonly shopService: ShopService) {}

  // ==================== 商店信息管理 ====================

  /**
   * 获取商店信息
   */
  @MessagePattern('shop.getInfo')
  @Cacheable({ 
    key: 'shop:info:#{payload.characterId}:#{payload.shopType}',
    dataType: 'server',
    ttl: 300
  })
  async getShopInfo(@Payload() payload: { characterId: string; serverId: string; shopType: ShopType }) {
    this.logger.log(`获取商店信息: ${payload.characterId}, 类型: ${payload.shopType}`);
    const shopInfo = await this.shopService.getShopInfo(payload.characterId, payload.serverId, payload.shopType);
    return {
      code: 0,
      message: '获取成功',
      data: shopInfo,
    };
  }

  /**
   * 获取商店列表
   */
  @MessagePattern('shop.getList')
  @Cacheable({ 
    key: 'shop:list:#{payload.characterId}',
    dataType: 'server',
    ttl: 300
  })
  async getShopList(@Payload() query: GetShopListDto) {
    this.logger.log(`获取商店列表: ${query.characterId}`);
    const shopList = await this.shopService.getShopList(query);
    return {
      code: 0,
      message: '获取成功',
      data: shopList,
    };
  }

  // ==================== 商品购买 ====================

  /**
   * 购买商品
   */
  @MessagePattern('shop.purchase')
  @CacheEvict({ 
    key: 'shop:info:#{payload.characterId}:#{payload.purchaseDto.shopType}',
    dataType: 'server'
  })
  async purchaseGoods(@Payload() payload: { 
    characterId: string; 
    serverId: string; 
    purchaseDto: PurchaseGoodsDto 
  }) {
    this.logger.log(`购买商品: ${payload.characterId}, 商品: ${payload.purchaseDto.goodsId}`);
    const result = await this.shopService.purchaseGoods(
      payload.characterId, 
      payload.serverId, 
      payload.purchaseDto
    );
    return {
      code: 0,
      message: '购买成功',
      data: result,
    };
  }

  /**
   * 刷新商店
   */
  @MessagePattern('shop.refresh')
  @CacheEvict({ 
    key: 'shop:info:#{payload.characterId}:#{payload.refreshDto.shopType}',
    dataType: 'server'
  })
  async refreshShop(@Payload() payload: { 
    characterId: string; 
    serverId: string; 
    refreshDto: RefreshShopDto 
  }) {
    this.logger.log(`刷新商店: ${payload.characterId}, 类型: ${payload.refreshDto.shopType}`);
    const shopInfo = await this.shopService.refreshShop(
      payload.characterId, 
      payload.serverId, 
      payload.refreshDto
    );
    return {
      code: 0,
      message: '刷新成功',
      data: shopInfo,
    };
  }

  // ==================== 月卡系统 ====================

  /**
   * 购买月卡
   */
  @MessagePattern('shop.buyMonthCard')
  @CacheEvict({ 
    key: 'shop:info:#{payload.characterId}:#{ShopType.NORMAL}',
    dataType: 'server'
  })
  async buyMonthCard(@Payload() payload: { 
    characterId: string; 
    serverId: string; 
    buyDto: BuyMonthCardDto 
  }) {
    this.logger.log(`购买月卡: ${payload.characterId}, 类型: ${payload.buyDto.cardType}`);
    const result = await this.shopService.buyMonthCard(
      payload.characterId, 
      payload.serverId, 
      payload.buyDto
    );
    return {
      code: 0,
      message: '购买成功',
      data: result,
    };
  }

  /**
   * 领取月卡奖励
   */
  @MessagePattern('shop.claimMonthCard')
  @CacheEvict({ 
    key: 'shop:info:#{payload.claimDto.characterId}:#{ShopType.NORMAL}',
    dataType: 'server'
  })
  async claimMonthCardReward(@Payload() payload: { claimDto: ClaimMonthCardDto }) {
    this.logger.log(`领取月卡奖励: ${payload.claimDto.characterId}`);
    const result = await this.shopService.claimMonthCardReward(payload.claimDto);
    return {
      code: 0,
      message: '领取成功',
      data: result,
    };
  }

  // ==================== VIP商店 ====================

  /**
   * 获取VIP商店信息
   */
  @MessagePattern('shop.getVipShop')
  @Cacheable({ 
    key: 'shop:vip:#{payload.characterId}:#{payload.vipLevel}',
    dataType: 'server',
    ttl: 600
  })
  async getVipShop(@Payload() payload: VipShopDto & { serverId: string }) {
    this.logger.log(`获取VIP商店: ${payload.characterId}, VIP等级: ${payload.vipLevel}`);
    const shopInfo = await this.shopService.getShopInfo(payload.characterId, payload.serverId, ShopType.VIP);
    return {
      code: 0,
      message: '获取成功',
      data: shopInfo,
    };
  }

  // ==================== 限时商店 ====================

  /**
   * 获取限时商店信息
   */
  @MessagePattern('shop.getLimitShop')
  @Cacheable({ 
    key: 'shop:limit:#{payload.characterId}:#{payload.activityId}',
    dataType: 'server',
    ttl: 180
  })
  async getLimitShop(@Payload() payload: LimitShopDto & { serverId: string }) {
    this.logger.log(`获取限时商店: ${payload.characterId}, 活动: ${payload.activityId}`);
    const shopInfo = await this.shopService.getShopInfo(payload.characterId, payload.serverId, ShopType.LIMIT);
    return {
      code: 0,
      message: '获取成功',
      data: shopInfo,
    };
  }

  // ==================== 购买历史和统计 ====================

  /**
   * 获取购买历史
   */
  @MessagePattern('shop.getPurchaseHistory')
  async getPurchaseHistory(@Payload() query: GetPurchaseHistoryDto) {
    this.logger.log(`获取购买历史: ${query.characterId}`);
    const history = await this.shopService.getPurchaseHistory(query);
    return {
      code: 0,
      message: '获取成功',
      data: history,
    };
  }

  /**
   * 获取商店统计
   */
  @MessagePattern('shop.getStats')
  @Cacheable({ 
    key: 'shop:stats:#{payload.characterId}:#{payload.days}',
    dataType: 'server',
    ttl: 1800
  })
  async getShopStats(@Payload() query: ShopStatsDto) {
    this.logger.log(`获取商店统计: ${query.characterId}`);
    const stats = await this.shopService.getShopStats(query);
    return {
      code: 0,
      message: '获取成功',
      data: stats,
    };
  }

  // ==================== 管理接口 ====================

  /**
   * 批量刷新商店（定时任务用）
   */
  @MessagePattern('shop.batchRefresh')
  async batchRefreshShops(@Payload() payload: { cycle: RefreshCycle }) {
    this.logger.log(`批量刷新商店: 周期 ${payload.cycle}`);
    const result = await this.shopService.batchRefreshShops(payload.cycle);
    return {
      code: 0,
      message: '刷新完成',
      data: result,
    };
  }
}
