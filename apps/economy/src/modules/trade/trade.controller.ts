import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { TradeService } from './trade.service';

@Controller()
export class TradeController {
  private readonly logger = new Logger(TradeController.name);

  constructor(private readonly tradeService: TradeService) {}

  /**
   * 创建交易
   */
  @MessagePattern('trade.create')
  async createTrade(@Payload() payload: any) {
    this.logger.log('创建交易');
    const result = await this.tradeService.createTrade(payload);
    return {
      code: 0,
      message: '交易创建成功',
      data: result,
    };
  }

  /**
   * 确认交易
   */
  @MessagePattern('trade.confirm')
  async confirmTrade(@Payload() payload: { tradeId: string }) {
    this.logger.log(`确认交易: ${payload.tradeId}`);
    const result = await this.tradeService.confirmTrade(payload.tradeId);
    return {
      code: 0,
      message: '交易确认成功',
      data: result,
    };
  }
}
