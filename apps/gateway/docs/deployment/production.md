# 生产部署指南

本指南详细介绍如何在生产环境中部署足球经理网关，包括高可用性配置、性能优化和安全加固。

## 🏗️ 部署架构

### 推荐的生产架构

```
                    ┌─────────────────┐
                    │   负载均衡器     │
                    │  (Nginx/HAProxy) │
                    └─────────────────┘
                             │
                ┌────────────┼────────────┐
                │            │            │
        ┌───────────┐ ┌───────────┐ ┌───────────┐
        │  网关实例1  │ │  网关实例2  │ │  网关实例3  │
        │   (主)     │ │   (备)     │ │   (备)     │
        └───────────┘ └───────────┘ └───────────┘
                │            │            │
                └────────────┼────────────┘
                             │
                ┌─────────────────────────────────┐
                │         共享服务层               │
                ├─────────────────────────────────┤
                │  Redis集群  │  MongoDB集群      │
                │  (缓存/会话) │  (持久化数据)      │
                └─────────────────────────────────┘
```

## 🐳 Docker 部署

### 1. Dockerfile 优化

```dockerfile
# 多阶段构建 Dockerfile
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./
COPY apps/gateway/package*.json ./apps/gateway/

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build:gateway

# 生产镜像
FROM node:18-alpine AS production

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S gateway -u 1001

# 设置工作目录
WORKDIR /app

# 复制构建产物
COPY --from=builder --chown=gateway:nodejs /app/dist ./dist
COPY --from=builder --chown=gateway:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=gateway:nodejs /app/package*.json ./

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 切换到非 root 用户
USER gateway

# 暴露端口
EXPOSE 3000 3001

# 启动命令
CMD ["node", "dist/apps/gateway/main.js"]
```

### 2. Docker Compose 生产配置

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # 网关服务
  gateway:
    image: football-manager/gateway:latest
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
      - NODE_ENV=production
      - GATEWAY_PORT=3000
      - REDIS_HOST=redis-cluster
      - MONGODB_URI=mongodb://mongo-cluster:27017/football-manager
    networks:
      - gateway-network
    depends_on:
      - redis-cluster
      - mongo-cluster
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx 负载均衡器
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - gateway
    networks:
      - gateway-network

  # Redis 集群
  redis-cluster:
    image: redis:7-alpine
    command: redis-server --appendonly yes --cluster-enabled yes
    volumes:
      - redis-data:/data
    networks:
      - gateway-network

  # MongoDB 集群
  mongo-cluster:
    image: mongo:6
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongo-data:/data/db
    networks:
      - gateway-network

networks:
  gateway-network:
    driver: overlay
    attachable: true

volumes:
  redis-data:
  mongo-data:
```

## ☸️ Kubernetes 部署

### 1. 网关部署配置

```yaml
# k8s/gateway-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway
  namespace: football-manager
  labels:
    app: gateway
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: gateway
  template:
    metadata:
      labels:
        app: gateway
        version: v1
    spec:
      serviceAccountName: gateway
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: gateway
        image: football-manager/gateway:v1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        - containerPort: 3001
          name: websocket
        env:
        - name: NODE_ENV
          value: "production"
        - name: GATEWAY_PORT
          value: "3000"
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: gateway-config
              key: redis-host
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: gateway-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: config
        configMap:
          name: gateway-config
      - name: logs
        emptyDir: {}
```

### 2. 服务配置

```yaml
# k8s/gateway-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: gateway-service
  namespace: football-manager
  labels:
    app: gateway
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  - port: 3001
    targetPort: 3001
    protocol: TCP
    name: websocket
  selector:
    app: gateway

---
apiVersion: v1
kind: Service
metadata:
  name: gateway-headless
  namespace: football-manager
  labels:
    app: gateway
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
  selector:
    app: gateway
```

### 3. Ingress 配置

```yaml
# k8s/gateway-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway-ingress
  namespace: football-manager
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.yourgame.com
    secretName: gateway-tls
  rules:
  - host: api.yourgame.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway-service
            port:
              number: 80
```

## 🔧 负载均衡配置

### Nginx 配置

```nginx
# nginx.conf
upstream gateway_backend {
    least_conn;
    server gateway-1:3000 max_fails=3 fail_timeout=30s;
    server gateway-2:3000 max_fails=3 fail_timeout=30s;
    server gateway-3:3000 max_fails=3 fail_timeout=30s;
    
    # 健康检查
    keepalive 32;
}

upstream websocket_backend {
    ip_hash; # WebSocket 需要会话粘性
    server gateway-1:3001 max_fails=3 fail_timeout=30s;
    server gateway-2:3001 max_fails=3 fail_timeout=30s;
    server gateway-3:3001 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name api.yourgame.com;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;

    # 限流
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # HTTP API
    location / {
        proxy_pass http://gateway_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # WebSocket
    location /socket.io/ {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 特定配置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_connect_timeout 5s;
    }

    # 健康检查
    location /health {
        access_log off;
        proxy_pass http://gateway_backend;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 📊 监控和日志

### 1. Prometheus 监控配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "gateway_rules.yml"

scrape_configs:
  - job_name: 'gateway'
    static_configs:
      - targets: ['gateway-1:3000', 'gateway-2:3000', 'gateway-3:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 2. 告警规则

```yaml
# gateway_rules.yml
groups:
- name: gateway
  rules:
  - alert: GatewayDown
    expr: up{job="gateway"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "网关实例下线"
      description: "网关实例 {{ $labels.instance }} 已下线超过1分钟"

  - alert: HighErrorRate
    expr: rate(gateway_http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "网关错误率过高"
      description: "网关错误率超过10%，当前值: {{ $value }}"

  - alert: HighLatency
    expr: histogram_quantile(0.95, rate(gateway_http_request_duration_seconds_bucket[5m])) > 0.5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "网关延迟过高"
      description: "网关P95延迟超过500ms，当前值: {{ $value }}s"
```

### 3. 日志聚合配置

```yaml
# filebeat.yml
filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
  - add_docker_metadata:
      host: "unix:///var/run/docker.sock"

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "gateway-logs-%{+yyyy.MM.dd}"

setup.template.name: "gateway"
setup.template.pattern: "gateway-*"
```

## 🔒 安全加固

### 1. 网络安全

```bash
# 防火墙规则
# 只允许必要的端口
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny incoming
ufw allow outgoing
ufw enable

# 限制 SSH 访问
echo "PermitRootLogin no" >> /etc/ssh/sshd_config
echo "PasswordAuthentication no" >> /etc/ssh/sshd_config
systemctl restart ssh
```

### 2. 容器安全

```dockerfile
# 安全的 Dockerfile 实践
FROM node:18-alpine

# 更新系统包
RUN apk update && apk upgrade && apk add --no-cache dumb-init

# 创建非特权用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S gateway -u 1001 -G nodejs

# 设置安全的文件权限
COPY --chown=gateway:nodejs . .

# 使用 dumb-init 作为 PID 1
ENTRYPOINT ["dumb-init", "--"]

# 以非特权用户运行
USER gateway
```

## 🚀 部署流程

### 1. 自动化部署脚本

```bash
#!/bin/bash
# deploy.sh

set -e

# 配置变量
IMAGE_TAG=${1:-latest}
NAMESPACE=football-manager
DEPLOYMENT_NAME=gateway

echo "开始部署网关 v${IMAGE_TAG}..."

# 构建镜像
echo "构建 Docker 镜像..."
docker build -t football-manager/gateway:${IMAGE_TAG} .

# 推送到镜像仓库
echo "推送镜像到仓库..."
docker push football-manager/gateway:${IMAGE_TAG}

# 更新 Kubernetes 部署
echo "更新 Kubernetes 部署..."
kubectl set image deployment/${DEPLOYMENT_NAME} \
  gateway=football-manager/gateway:${IMAGE_TAG} \
  -n ${NAMESPACE}

# 等待部署完成
echo "等待部署完成..."
kubectl rollout status deployment/${DEPLOYMENT_NAME} -n ${NAMESPACE}

# 验证部署
echo "验证部署状态..."
kubectl get pods -n ${NAMESPACE} -l app=gateway

echo "部署完成！"
```

### 2. 健康检查脚本

```bash
#!/bin/bash
# health-check.sh

GATEWAY_URL=${1:-http://localhost:3000}

echo "检查网关健康状态..."

# 检查 HTTP 健康
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${GATEWAY_URL}/health)
if [ $HTTP_STATUS -eq 200 ]; then
    echo "✅ HTTP 健康检查: 通过"
else
    echo "❌ HTTP 健康检查: 失败 (状态码: $HTTP_STATUS)"
    exit 1
fi

# 检查指标端点
METRICS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${GATEWAY_URL}/metrics)
if [ $METRICS_STATUS -eq 200 ]; then
    echo "✅ 指标端点: 正常"
else
    echo "⚠️  指标端点: 异常"
fi

# 检查 WebSocket
if command -v wscat &> /dev/null; then
    timeout 5 wscat -c ws://localhost:3001 --close &>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ WebSocket: 正常"
    else
        echo "❌ WebSocket: 异常"
    fi
fi

echo "健康检查完成！"
```

这个生产部署指南提供了完整的部署流程和最佳实践，确保网关在生产环境中的稳定运行。
