# 快速开始指南

在短短5分钟内让您的足球经理网关运行起来！本指南将引导您完成部署和测试网关的基本步骤。

## 前置条件

开始之前，请确保您已安装：

- **Node.js 18+**
- **Redis 6+** 正在运行
- **MongoDB 5+**（可选，用于会话存储）
- **Docker**（可选，用于容器化部署）

## 🚀 选项 1: 本地开发设置

### 步骤 1: 克隆和安装

```bash
# 克隆仓库
git clone https://github.com/your-org/football-manager-gateway.git
cd football-manager-gateway

# 安装依赖
npm install

# 复制环境配置
cp apps/gateway/.env.example apps/gateway/.env
```

### 步骤 2: 配置环境

编辑 `apps/gateway/.env`:

```env
# 基础配置
NODE_ENV=development
GATEWAY_PORT=3000

# Redis 配置（必需）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT 配置（必需）
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# 后端服务（更新为您的服务URL）
AUTH_SERVICE_URL=http://localhost:3001
CHARACTER_SERVICE_URL=http://localhost:3002
CLUB_SERVICE_URL=http://localhost:3003
```

### 步骤 3: 启动网关

```bash
# 开发模式（热重载）
npm run start:gateway:dev

# 或生产模式
npm run start:gateway:prod
```

### 步骤 4: 验证安装

```bash
# 检查健康端点
curl http://localhost:3000/health

# 预期响应：
{
  "status": "ok",
  "timestamp": "2023-12-01T10:00:00.000Z",
  "service": "gateway",
  "version": "1.0.0"
}
```

## 🐳 选项 2: Docker 部署

### 步骤 1: 快速 Docker 设置

```bash
# 克隆仓库
git clone https://github.com/your-org/football-manager-gateway.git
cd football-manager-gateway/apps/gateway

# 复制环境文件
cp .env.example .env

# 使用 Docker Compose 启动
docker-compose up -d
```

### 步骤 2: 验证 Docker 部署

```bash
# 检查运行的容器
docker-compose ps

# 检查网关日志
docker-compose logs gateway

# 测试健康端点
curl http://localhost:3000/health
```

## 🧪 测试网关

### 1. 健康检查

```bash
curl -X GET http://localhost:3000/health
```

### 2. 指标端点

```bash
curl -X GET http://localhost:3000/metrics
```

### 3. API 文档

访问 Swagger 文档：
```
http://localhost:3000/docs
```

### 4. 测试认证

```bash
# 测试 JWT 认证（替换为实际令牌）
curl -X GET http://localhost:3000/api/users/me \
  -H "Authorization: Bearer your-jwt-token-here"
```

### 5. 测试限流

```bash
# 发送多个请求测试限流
for i in {1..10}; do
  curl -X GET http://localhost:3000/api/test
  echo "请求 $i 完成"
done
```

### 6. 测试 WebSocket 连接

创建一个简单的 HTML 文件来测试 WebSocket：

```html
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket 测试</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
</head>
<body>
    <h1>WebSocket 测试</h1>
    <div id="status">连接中...</div>
    <div id="messages"></div>

    <script>
        const socket = io('http://localhost:3000');
        
        socket.on('connect', () => {
            document.getElementById('status').textContent = '已连接！';
            console.log('已连接到 WebSocket');
        });
        
        socket.on('connected', (data) => {
            console.log('服务器确认:', data);
            document.getElementById('messages').innerHTML += 
                '<p>服务器确认: ' + JSON.stringify(data) + '</p>';
        });
        
        socket.on('disconnect', () => {
            document.getElementById('status').textContent = '已断开连接';
            console.log('与 WebSocket 断开连接');
        });
    </script>
</body>
</html>
```

## 🎮 游戏特定快速测试

### 1. 模拟游戏服务

网关包含用于测试的模拟服务。在环境中启用它们：

```env
DEV_MOCK_SERVICES=true
```

### 2. 测试比赛事件

```bash
# 测试 WebSocket 比赛事件
curl -X POST http://localhost:3000/api/test/match-event \
  -H "Content-Type: application/json" \
  -d '{
    "matchId": "match_123",
    "event": "goal_scored",
    "data": {
      "playerId": "player_456",
      "minute": 23
    }
  }'
```

### 3. 测试用户通知

```bash
# 测试通知系统
curl -X POST http://localhost:3000/api/test/notification \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user_123",
    "type": "transfer_completed",
    "message": "您的转会报价已被接受！"
  }'
```

## 📊 监控设置

### 1. 查看指标

```bash
# 获取 Prometheus 指标
curl http://localhost:3000/metrics

# 获取网关统计信息
curl http://localhost:3000/api/admin/stats
```

### 2. 检查日志

```bash
# 查看应用日志
tail -f apps/gateway/logs/gateway.log

# 查看访问日志
tail -f apps/gateway/logs/access.log

# 查看错误日志
tail -f apps/gateway/logs/error.log
```

### 3. Redis 监控

```bash
# 连接到 Redis CLI
redis-cli

# 检查网关键
KEYS gateway:*

# 监控实时命令
MONITOR
```

## 🔧 常用配置

### 启用所有功能

```env
# 启用所有网关功能
FEATURE_WEBSOCKET_ENABLED=true
FEATURE_GRAPHQL_ENABLED=true
FEATURE_RATE_LIMITING_ENABLED=true
FEATURE_CACHING_ENABLED=true
FEATURE_CIRCUIT_BREAKER_ENABLED=true

# 启用监控
MONITORING_ENABLE_PROMETHEUS=true
MONITORING_ENABLE_JAEGER=false

# 启用安全功能
SECURITY_ENABLE_HELMET=true
SECURITY_ENABLE_COMPRESSION=true
```

### 性能调优

```env
# 开发优化
MAX_REQUEST_SIZE=10mb
KEEP_ALIVE_TIMEOUT=5000
HEADERS_TIMEOUT=60000

# 限流
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=1000

# 缓存
CACHE_ENABLED=true
CACHE_TTL=3600
```

## 🚨 故障排除

### 常见问题

1. **网关无法启动**
   ```bash
   # 检查端口是否被占用
   lsof -i :3000
   
   # 检查 Redis 连接
   redis-cli ping
   ```

2. **认证失败**
   ```bash
   # 验证 JWT 密钥是否设置
   echo $JWT_SECRET
   
   # 检查 Redis 中的认证数据
   redis-cli KEYS auth:*
   ```

3. **WebSocket 连接失败**
   ```bash
   # 检查 CORS 配置
   # 验证认证令牌
   # 检查防火墙设置
   ```

4. **限流不工作**
   ```bash
   # 检查 Redis 连接
   # 验证限流配置
   # 检查 Redis 键: redis-cli KEYS rate_limit:*
   ```

### 调试模式

启用调试日志：

```env
LOG_LEVEL=debug
DEBUG_AUTH=true
DEBUG_RATE_LIMIT=true
DEBUG_WEBSOCKET=true
```

### 健康检查脚本

创建一个简单的健康检查脚本：

```bash
#!/bin/bash
# health-check.sh

echo "检查网关健康状态..."

# 检查 HTTP 健康
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/health)
if [ $HTTP_STATUS -eq 200 ]; then
    echo "✅ HTTP 健康: 正常"
else
    echo "❌ HTTP 健康: 失败 ($HTTP_STATUS)"
fi

# 检查 Redis
REDIS_STATUS=$(redis-cli ping 2>/dev/null)
if [ "$REDIS_STATUS" = "PONG" ]; then
    echo "✅ Redis: 正常"
else
    echo "❌ Redis: 失败"
fi

# 检查 WebSocket（需要 wscat: npm install -g wscat）
if command -v wscat &> /dev/null; then
    timeout 5 wscat -c ws://localhost:3000 --close &>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ WebSocket: 正常"
    else
        echo "❌ WebSocket: 失败"
    fi
fi

echo "健康检查完成！"
```

## 🎯 下一步

现在您的网关已经运行，探索这些功能：

1. **[认证设置](../auth/jwt.md)** - 配置 JWT 认证
2. **[限流](../rate-limiting/overview.md)** - 设置限流规则
3. **[WebSocket 功能](../websocket/overview.md)** - 实现实时功能
4. **[监控](../monitoring/metrics.md)** - 设置全面监控
5. **[生产部署](../deployment/production.md)** - 部署到生产环境

## 📚 其他资源

- **[配置参考](../setup/configuration.md)** - 完整的配置选项
- **[API 文档](../api/http.md)** - 完整的 API 参考
- **[示例仓库](../../examples/)** - 代码示例和教程
- **[故障排除指南](../deployment/troubleshooting.md)** - 常见问题和解决方案

恭喜！您的足球经理网关现在已经启动并运行。您已经准备好构建令人惊叹的实时足球管理体验了！ ⚽🚀
