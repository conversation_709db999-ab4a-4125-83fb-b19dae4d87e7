# 中间件系统

网关的中间件系统提供了可插拔的请求处理管道，允许在请求处理的各个阶段插入自定义逻辑。本文档详细介绍中间件系统的设计、使用和扩展方法。

## 🔧 中间件架构

### 中间件管道

```
请求进入
    │
    ▼
┌─────────────────┐
│  安全中间件      │ ← CORS、Helmet、压缩
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  日志中间件      │ ← 请求日志记录
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  认证中间件      │ ← JWT、API Key验证
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  授权中间件      │ ← 权限检查
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  限流中间件      │ ← 流量控制
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  缓存中间件      │ ← 缓存检查
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  业务中间件      │ ← 自定义业务逻辑
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  路由处理器      │ ← 请求转发
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  响应中间件      │ ← 响应处理
└─────────────────┘
    │
    ▼
响应返回
```

### 中间件接口

```typescript
interface Middleware {
  name: string;                    // 中间件名称
  priority: number;                // 执行优先级
  enabled: boolean;                // 是否启用
  config?: any;                    // 配置参数
  
  // 请求处理
  onRequest?(req: Request, res: Response, next: NextFunction): Promise<void>;
  
  // 响应处理
  onResponse?(req: Request, res: Response, next: NextFunction): Promise<void>;
  
  // 错误处理
  onError?(error: Error, req: Request, res: Response, next: NextFunction): Promise<void>;
}
```

## 🛡️ 内置中间件

### 1. 安全中间件

#### CORS 中间件

```typescript
const corsMiddleware = {
  name: 'cors',
  priority: 100,
  config: {
    origin: [
      'https://game.example.com',
      'https://admin.example.com'
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-API-Key',
      'X-Request-ID'
    ],
    credentials: true,
    maxAge: 86400  // 预检请求缓存时间
  }
};
```

#### Helmet 安全头中间件

```typescript
const helmetMiddleware = {
  name: 'helmet',
  priority: 90,
  config: {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'", "wss:"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"]
      }
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    },
    noSniff: true,
    frameguard: { action: 'deny' },
    xssFilter: true
  }
};
```

#### 压缩中间件

```typescript
const compressionMiddleware = {
  name: 'compression',
  priority: 80,
  config: {
    threshold: 1024,     // 最小压缩大小
    level: 6,            // 压缩级别 (1-9)
    memLevel: 8,         // 内存级别 (1-9)
    filter: (req, res) => {
      // 不压缩已压缩的内容
      if (req.headers['x-no-compression']) {
        return false;
      }
      return compression.filter(req, res);
    }
  }
};
```

### 2. 认证中间件

#### JWT 认证中间件

```typescript
const jwtAuthMiddleware = {
  name: 'jwt-auth',
  priority: 70,
  config: {
    secret: process.env.JWT_SECRET,
    algorithms: ['HS256', 'RS256'],
    skipPaths: ['/health', '/metrics', '/docs'],
    extractToken: (req) => {
      // 从多个位置提取令牌
      return req.headers.authorization?.replace('Bearer ', '') ||
             req.headers['x-auth-token'] ||
             req.query.token ||
             req.cookies.token;
    }
  },
  
  async onRequest(req, res, next) {
    const token = this.config.extractToken(req);
    
    if (!token) {
      if (this.isSkipPath(req.path)) {
        return next();
      }
      return res.status(401).json({ error: '缺少认证令牌' });
    }
    
    try {
      const decoded = jwt.verify(token, this.config.secret);
      req.user = await this.getUserById(decoded.sub);
      req.authContext = {
        authenticated: true,
        user: req.user,
        token: decoded,
        authMethod: 'jwt'
      };
      next();
    } catch (error) {
      res.status(401).json({ error: '无效的认证令牌' });
    }
  }
};
```

#### API Key 认证中间件

```typescript
const apiKeyAuthMiddleware = {
  name: 'api-key-auth',
  priority: 65,
  config: {
    header: 'X-API-Key',
    queryParam: 'api_key',
    skipPaths: ['/health', '/metrics']
  },
  
  async onRequest(req, res, next) {
    const apiKey = req.headers[this.config.header.toLowerCase()] ||
                   req.query[this.config.queryParam];
    
    if (!apiKey) {
      if (this.isSkipPath(req.path)) {
        return next();
      }
      return res.status(401).json({ error: '缺少API密钥' });
    }
    
    try {
      const keyInfo = await this.validateApiKey(apiKey);
      req.apiKey = keyInfo;
      req.authContext = {
        authenticated: true,
        apiKey: keyInfo,
        authMethod: 'api-key'
      };
      next();
    } catch (error) {
      res.status(401).json({ error: '无效的API密钥' });
    }
  }
};
```

### 3. 限流中间件

```typescript
const rateLimitMiddleware = {
  name: 'rate-limit',
  priority: 60,
  config: {
    windowMs: 60000,     // 时间窗口
    max: 100,            // 最大请求数
    strategy: 'sliding-window',
    keyGenerator: (req) => {
      // 生成限流键
      if (req.user) {
        return `user:${req.user.id}`;
      }
      return `ip:${this.getClientIp(req)}`;
    },
    skipPaths: ['/health', '/metrics'],
    onLimitReached: (req, res) => {
      res.status(429).json({
        error: '请求过于频繁',
        retryAfter: Math.ceil(this.config.windowMs / 1000)
      });
    }
  },
  
  async onRequest(req, res, next) {
    if (this.isSkipPath(req.path)) {
      return next();
    }
    
    const key = this.config.keyGenerator(req);
    const result = await this.rateLimitService.checkRateLimit(key, this.config);
    
    // 设置限流头部
    res.setHeader('X-RateLimit-Limit', result.limit);
    res.setHeader('X-RateLimit-Remaining', result.remaining);
    res.setHeader('X-RateLimit-Reset', result.resetTime);
    
    if (!result.allowed) {
      return this.config.onLimitReached(req, res);
    }
    
    next();
  }
};
```

### 4. 缓存中间件

```typescript
const cacheMiddleware = {
  name: 'cache',
  priority: 50,
  config: {
    defaultTTL: 300,     // 默认缓存时间
    keyGenerator: (req) => {
      const user = req.user ? `user:${req.user.id}` : 'anonymous';
      return `cache:${req.method}:${req.path}:${user}`;
    },
    skipMethods: ['POST', 'PUT', 'DELETE', 'PATCH'],
    skipPaths: ['/api/live', '/api/realtime']
  },
  
  async onRequest(req, res, next) {
    if (this.shouldSkipCache(req)) {
      return next();
    }
    
    const cacheKey = this.config.keyGenerator(req);
    const cached = await this.cacheService.get(cacheKey);
    
    if (cached) {
      res.setHeader('X-Cache', 'HIT');
      res.setHeader('X-Cache-Date', cached.timestamp);
      return res.json(cached.data);
    }
    
    // 缓存未命中，继续处理
    res.setHeader('X-Cache', 'MISS');
    req.cacheKey = cacheKey;
    next();
  },
  
  async onResponse(req, res, next) {
    if (req.cacheKey && res.statusCode === 200) {
      const data = res.locals.responseData;
      await this.cacheService.set(req.cacheKey, data, this.config.defaultTTL);
    }
    next();
  }
};
```

## 🎮 游戏特定中间件

### 1. 游戏状态中间件

```typescript
const gameStateMiddleware = {
  name: 'game-state',
  priority: 40,
  config: {
    enabledPaths: ['/api/matches', '/api/clubs', '/api/players']
  },
  
  async onRequest(req, res, next) {
    if (!req.user || !this.isEnabledPath(req.path)) {
      return next();
    }
    
    try {
      // 获取用户游戏状态
      const gameState = await this.gameStateService.getUserState(req.user.id);
      req.gameState = gameState;
      
      // 设置游戏相关头部
      res.setHeader('X-Game-Season', gameState.currentSeason);
      res.setHeader('X-Game-Week', gameState.currentWeek);
      
      // 检查用户是否在比赛中
      if (gameState.inMatch) {
        res.setHeader('X-In-Match', 'true');
        res.setHeader('X-Match-ID', gameState.currentMatchId);
      }
      
      next();
    } catch (error) {
      this.logger.error('获取游戏状态失败:', error);
      next(); // 不阻塞请求
    }
  }
};
```

### 2. 比赛权限中间件

```typescript
const matchPermissionMiddleware = {
  name: 'match-permission',
  priority: 35,
  config: {
    paths: ['/api/matches/:id/*']
  },
  
  async onRequest(req, res, next) {
    if (!this.isMatchPath(req.path)) {
      return next();
    }
    
    const matchId = req.params.id;
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ error: '需要登录' });
    }
    
    try {
      // 检查用户是否有权限访问该比赛
      const hasPermission = await this.matchService.checkUserPermission(
        userId,
        matchId,
        req.method
      );
      
      if (!hasPermission) {
        return res.status(403).json({ error: '无权限访问该比赛' });
      }
      
      // 获取比赛信息
      const match = await this.matchService.getMatch(matchId);
      req.match = match;
      
      next();
    } catch (error) {
      res.status(500).json({ error: '权限检查失败' });
    }
  }
};
```

### 3. 俱乐部成员中间件

```typescript
const clubMemberMiddleware = {
  name: 'club-member',
  priority: 30,
  config: {
    paths: ['/api/clubs/:id/*'],
    requiredRoles: ['member', 'manager', 'owner']
  },
  
  async onRequest(req, res, next) {
    if (!this.isClubPath(req.path)) {
      return next();
    }
    
    const clubId = req.params.id;
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ error: '需要登录' });
    }
    
    try {
      // 检查用户是否是俱乐部成员
      const membership = await this.clubService.getUserMembership(userId, clubId);
      
      if (!membership) {
        return res.status(403).json({ error: '不是俱乐部成员' });
      }
      
      // 检查角色权限
      if (!this.config.requiredRoles.includes(membership.role)) {
        return res.status(403).json({ error: '权限不足' });
      }
      
      req.clubMembership = membership;
      next();
    } catch (error) {
      res.status(500).json({ error: '成员检查失败' });
    }
  }
};
```

## 🔧 自定义中间件

### 中间件开发模板

```typescript
class CustomMiddleware implements Middleware {
  name = 'custom-middleware';
  priority = 50;
  enabled = true;
  
  constructor(private config: any) {}
  
  async onRequest(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 请求前处理逻辑
      console.log(`处理请求: ${req.method} ${req.path}`);
      
      // 添加自定义头部
      req.headers['x-custom-header'] = 'custom-value';
      
      // 验证自定义逻辑
      if (!this.validateRequest(req)) {
        return res.status(400).json({ error: '请求验证失败' });
      }
      
      next();
    } catch (error) {
      next(error);
    }
  }
  
  async onResponse(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 响应后处理逻辑
      console.log(`响应状态: ${res.statusCode}`);
      
      // 添加响应头部
      res.setHeader('X-Custom-Response', 'processed');
      
      next();
    } catch (error) {
      next(error);
    }
  }
  
  async onError(error: Error, req: Request, res: Response, next: NextFunction): Promise<void> {
    // 错误处理逻辑
    console.error(`中间件错误: ${error.message}`);
    
    // 记录错误
    this.logError(error, req);
    
    next(error);
  }
  
  private validateRequest(req: Request): boolean {
    // 自定义验证逻辑
    return true;
  }
  
  private logError(error: Error, req: Request): void {
    // 错误日志记录
  }
}
```

### 中间件注册

```typescript
// 中间件管理器
class MiddlewareManager {
  private middlewares: Map<string, Middleware> = new Map();
  
  // 注册中间件
  register(middleware: Middleware): void {
    this.middlewares.set(middleware.name, middleware);
  }
  
  // 获取排序后的中间件列表
  getOrderedMiddlewares(): Middleware[] {
    return Array.from(this.middlewares.values())
      .filter(m => m.enabled)
      .sort((a, b) => b.priority - a.priority);
  }
  
  // 应用中间件到Express应用
  applyToApp(app: Express): void {
    const orderedMiddlewares = this.getOrderedMiddlewares();
    
    for (const middleware of orderedMiddlewares) {
      if (middleware.onRequest) {
        app.use(middleware.onRequest.bind(middleware));
      }
    }
  }
}

// 使用示例
const middlewareManager = new MiddlewareManager();

// 注册内置中间件
middlewareManager.register(corsMiddleware);
middlewareManager.register(helmetMiddleware);
middlewareManager.register(jwtAuthMiddleware);
middlewareManager.register(rateLimitMiddleware);

// 注册自定义中间件
middlewareManager.register(new CustomMiddleware(config));

// 应用到Express应用
middlewareManager.applyToApp(app);
```

## 📊 中间件监控

### 性能监控

```typescript
const performanceMiddleware = {
  name: 'performance',
  priority: 95,
  
  async onRequest(req, res, next) {
    req.startTime = Date.now();
    next();
  },
  
  async onResponse(req, res, next) {
    const duration = Date.now() - req.startTime;
    
    // 记录性能指标
    this.metricsService.recordMiddlewarePerformance(
      'total_request_duration',
      duration,
      { method: req.method, path: req.path }
    );
    
    // 设置响应时间头部
    res.setHeader('X-Response-Time', `${duration}ms`);
    
    next();
  }
};
```

### 错误监控

```typescript
const errorMonitoringMiddleware = {
  name: 'error-monitoring',
  priority: 10,
  
  async onError(error, req, res, next) {
    // 记录错误指标
    this.metricsService.recordError(error, {
      path: req.path,
      method: req.method,
      userAgent: req.get('user-agent'),
      ip: this.getClientIp(req)
    });
    
    // 发送错误到监控系统
    this.errorReporter.report(error, {
      request: req,
      user: req.user
    });
    
    next(error);
  }
};
```

这个中间件系统为足球经理网关提供了灵活、可扩展的请求处理能力，支持复杂的业务逻辑和安全要求。
