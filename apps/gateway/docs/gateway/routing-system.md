# 路由系统

网关的路由系统是整个架构的核心，负责将客户端请求智能地路由到相应的后端服务。本文档详细介绍路由系统的设计、配置和使用方法。

## 🎯 路由系统概述

### 核心功能

- **动态路由匹配**: 支持多种匹配模式和规则
- **负载均衡**: 多种负载均衡算法
- **健康检查**: 自动检测服务健康状态
- **故障转移**: 自动故障检测和恢复
- **路由缓存**: 提高路由匹配性能

### 路由处理流程

```
请求到达
    │
    ▼
┌─────────────────┐
│  系统路由检查    │ ← 检查是否为系统路由（健康检查、指标等）
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  路径预处理      │ ← 标准化路径格式
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  路由匹配        │ ← 查找匹配的路由规则
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  权限验证        │ ← 检查访问权限
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  服务选择        │ ← 负载均衡选择实例
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  请求转发        │ ← 转发到目标服务
└─────────────────┘
```

## 📋 路由配置

### 系统路由配置

系统路由是指网关内部的管理和监控路由，这些路由不会被代理到后端服务，而是由网关直接处理。

#### 环境变量配置

```bash
# 系统路由前缀配置（逗号分隔）
SYSTEM_ROUTE_PREFIXES=/health,/metrics,/auth,/api/docs,/api-json

# 静态资源路径配置（逗号分隔）
STATIC_RESOURCE_PATHS=/favicon.ico,/robots.txt,/sitemap.xml
```

#### 系统路由类型

| 路由前缀 | 功能 | 说明 |
|----------|------|------|
| `/health` | 健康检查 | 服务健康状态监控 |
| `/metrics` | 监控指标 | Prometheus 指标收集 |
| `/auth` | 认证服务 | JWT 认证和授权 |
| `/api/docs` | API 文档 | Swagger 文档界面 |
| `/api-json` | API 规范 | OpenAPI JSON 规范 |

#### 系统路由处理机制

1. **优先级**: 系统路由检查在所有代理路由之前执行
2. **跳过代理**: 匹配的系统路由直接跳过代理处理逻辑
3. **监控记录**: 自动记录系统路由跳过的监控指标
4. **配置化**: 支持通过环境变量动态配置系统路由

### 基础路由配置

```typescript
interface Route {
  id: string;                    // 路由唯一标识
  name: string;                  // 路由名称
  path: string;                  // 匹配路径
  method: string | string[];     // HTTP方法
  target: RouteTarget;           // 目标服务配置
  middleware?: string[];         // 中间件列表
  config: RouteConfig;           // 路由配置
  enabled: boolean;              // 是否启用
  priority: number;              // 路由优先级
}

interface RouteTarget {
  service: string;               // 目标服务名
  path: string;                  // 目标路径
  host?: string;                 // 目标主机
  port?: number;                 // 目标端口
  protocol: 'http' | 'https';   // 协议类型
  loadBalancer: LoadBalancerType; // 负载均衡算法
}
```

### 路由匹配模式

#### 1. 精确匹配

```typescript
{
  id: 'user-profile',
  path: '/api/users/profile',
  method: 'GET',
  target: {
    service: 'user-service',
    path: '/users/profile'
  }
}
```

#### 2. 参数匹配

```typescript
{
  id: 'user-detail',
  path: '/api/users/:id',
  method: ['GET', 'PUT', 'DELETE'],
  target: {
    service: 'user-service',
    path: '/users/:id'
  }
}
```

#### 3. 通配符匹配

```typescript
{
  id: 'user-all',
  path: '/api/users/*',
  method: '*',
  target: {
    service: 'user-service',
    path: '/users/*'
  }
}
```

#### 4. 正则表达式匹配

```typescript
{
  id: 'api-versioned',
  path: '/api/v(\\d+)/users/:id',
  method: 'GET',
  target: {
    service: 'user-service',
    path: '/v$1/users/:id'
  }
}
```

### 高级路由配置

#### 条件路由

```typescript
{
  id: 'premium-features',
  path: '/api/premium/*',
  method: '*',
  conditions: {
    headers: {
      'X-User-Type': 'premium'
    },
    query: {
      'version': '^2\\.'
    }
  },
  target: {
    service: 'premium-service',
    path: '/premium/*'
  }
}
```

#### 基于权重的路由

```typescript
{
  id: 'ab-testing',
  path: '/api/features/new',
  method: 'GET',
  targets: [
    {
      service: 'feature-service-v1',
      path: '/features/new',
      weight: 70  // 70% 流量
    },
    {
      service: 'feature-service-v2',
      path: '/features/new',
      weight: 30  // 30% 流量
    }
  ]
}
```

## ⚖️ 负载均衡

### 负载均衡算法

#### 1. 轮询 (Round Robin)

```typescript
{
  algorithm: 'round-robin',
  instances: [
    { url: 'http://service1:3000' },
    { url: 'http://service2:3000' },
    { url: 'http://service3:3000' }
  ]
}
```

#### 2. 加权轮询 (Weighted Round Robin)

```typescript
{
  algorithm: 'weighted-round-robin',
  instances: [
    { url: 'http://service1:3000', weight: 3 },
    { url: 'http://service2:3000', weight: 2 },
    { url: 'http://service3:3000', weight: 1 }
  ]
}
```

#### 3. 最少连接 (Least Connections)

```typescript
{
  algorithm: 'least-connections',
  instances: [
    { url: 'http://service1:3000', connections: 10 },
    { url: 'http://service2:3000', connections: 5 },
    { url: 'http://service3:3000', connections: 8 }
  ]
}
```

#### 4. 一致性哈希 (Consistent Hash)

```typescript
{
  algorithm: 'consistent-hash',
  hashKey: 'user_id',  // 哈希键
  instances: [
    { url: 'http://service1:3000', hash: 'node1' },
    { url: 'http://service2:3000', hash: 'node2' },
    { url: 'http://service3:3000', hash: 'node3' }
  ]
}
```

#### 5. IP 哈希 (IP Hash)

```typescript
{
  algorithm: 'ip-hash',
  instances: [
    { url: 'http://service1:3000' },
    { url: 'http://service2:3000' },
    { url: 'http://service3:3000' }
  ]
}
```

### 健康检查

```typescript
{
  healthCheck: {
    enabled: true,
    interval: 30000,        // 检查间隔 (毫秒)
    timeout: 5000,          // 超时时间
    retries: 3,             // 重试次数
    path: '/health',        // 健康检查路径
    method: 'GET',          // 检查方法
    expectedStatus: [200],  // 期望状态码
    expectedBody: 'OK',     // 期望响应体
    headers: {              // 自定义头部
      'User-Agent': 'Gateway-HealthCheck'
    }
  }
}
```

## 🔄 动态路由

### 路由热更新

```typescript
// 路由管理服务
class RouteManagerService {
  // 添加路由
  async addRoute(route: Route): Promise<void> {
    // 验证路由配置
    await this.validateRoute(route);
    
    // 添加到路由表
    this.routeTable.set(route.id, route);
    
    // 更新路由缓存
    this.updateRouteCache();
    
    // 发布路由变更事件
    this.eventEmitter.emit('route.added', route);
  }
  
  // 更新路由
  async updateRoute(routeId: string, updates: Partial<Route>): Promise<void> {
    const existingRoute = this.routeTable.get(routeId);
    if (!existingRoute) {
      throw new Error(`Route ${routeId} not found`);
    }
    
    const updatedRoute = { ...existingRoute, ...updates };
    await this.validateRoute(updatedRoute);
    
    this.routeTable.set(routeId, updatedRoute);
    this.updateRouteCache();
    
    this.eventEmitter.emit('route.updated', updatedRoute);
  }
  
  // 删除路由
  async removeRoute(routeId: string): Promise<void> {
    const route = this.routeTable.get(routeId);
    if (!route) {
      throw new Error(`Route ${routeId} not found`);
    }
    
    this.routeTable.delete(routeId);
    this.updateRouteCache();
    
    this.eventEmitter.emit('route.removed', route);
  }
}
```

### 路由版本管理

```typescript
{
  id: 'user-service-v2',
  path: '/api/v2/users/*',
  method: '*',
  target: {
    service: 'user-service-v2',
    path: '/users/*'
  },
  version: '2.0.0',
  migration: {
    from: 'user-service-v1',
    strategy: 'gradual',
    percentage: 10  // 逐步迁移10%流量
  }
}
```

## 🎮 游戏特定路由

### 比赛相关路由

```typescript
const matchRoutes = [
  {
    id: 'match-live',
    path: '/api/matches/:id/live',
    method: 'GET',
    target: {
      service: 'match-service',
      path: '/matches/:id/live'
    },
    config: {
      cache: { enabled: false },  // 实时数据不缓存
      rateLimit: { max: 100, window: '1m' }
    }
  },
  {
    id: 'match-events',
    path: '/api/matches/:id/events',
    method: 'GET',
    target: {
      service: 'match-service',
      path: '/matches/:id/events'
    },
    config: {
      cache: { enabled: true, ttl: 30 },  // 缓存30秒
      transform: {
        response: {
          removeFields: ['internal_id']
        }
      }
    }
  }
];
```

### 用户相关路由

```typescript
const userRoutes = [
  {
    id: 'user-profile',
    path: '/api/users/me',
    method: 'GET',
    target: {
      service: 'user-service',
      path: '/users/profile'
    },
    middleware: ['auth'],
    config: {
      cache: { enabled: true, ttl: 300 },
      transform: {
        request: {
          addHeaders: {
            'X-User-ID': '${user.id}'
          }
        }
      }
    }
  },
  {
    id: 'user-settings',
    path: '/api/users/settings',
    method: ['GET', 'PUT'],
    target: {
      service: 'user-service',
      path: '/users/settings'
    },
    middleware: ['auth', 'rate-limit'],
    config: {
      rateLimit: {
        max: 20,
        window: '1m',
        keyGenerator: 'user'
      }
    }
  }
];
```

## 🔧 路由中间件

### 内置中间件

```typescript
const middlewareConfig = {
  auth: {
    type: 'jwt',
    required: true,
    skipPaths: ['/health', '/metrics']
  },
  'rate-limit': {
    strategy: 'sliding-window',
    max: 100,
    window: '1m'
  },
  cors: {
    origin: ['https://game.example.com'],
    credentials: true
  },
  compression: {
    threshold: 1024,
    level: 6
  }
};
```

### 自定义中间件

```typescript
// 自定义游戏状态中间件
class GameStateMiddleware {
  async use(req: Request, res: Response, next: NextFunction) {
    const userId = req.user?.id;
    if (userId) {
      // 获取用户游戏状态
      const gameState = await this.gameStateService.getUserState(userId);
      req.gameState = gameState;
      
      // 检查用户是否在比赛中
      if (gameState.inMatch && req.path.includes('/matches/')) {
        req.headers['X-In-Match'] = 'true';
      }
    }
    
    next();
  }
}
```

## 📊 路由监控

### 路由指标

```typescript
const routeMetrics = {
  'route_requests_total': {
    type: 'counter',
    labels: ['route_id', 'method', 'status']
  },
  'route_duration_seconds': {
    type: 'histogram',
    labels: ['route_id', 'method']
  },
  'route_errors_total': {
    type: 'counter',
    labels: ['route_id', 'error_type']
  },
  'route_cache_hits_total': {
    type: 'counter',
    labels: ['route_id']
  },
  // 新增：系统路由跳过指标
  'gateway_system_route_skips_total': {
    type: 'counter',
    labels: ['path', 'route_type'],
    description: '系统路由跳过次数统计'
  }
};
```

### 系统路由监控

系统路由跳过指标提供了对网关内部路由处理的深入洞察：

```typescript
// 指标示例
gateway_system_route_skips_total{path="/health",route_type="prefix"} 1250
gateway_system_route_skips_total{path="/metrics",route_type="prefix"} 890
gateway_system_route_skips_total{path="/favicon.ico",route_type="static"} 45
```

**标签说明**:
- `path`: 被跳过的路径
- `route_type`: 路由类型（`prefix` 表示前缀匹配，`static` 表示静态资源）

**监控用途**:
- 分析系统路由的访问频率
- 识别异常的系统路由访问模式
- 优化系统路由配置

### 路由健康状态

```typescript
// 路由健康检查
class RouteHealthChecker {
  async checkRouteHealth(routeId: string): Promise<RouteHealth> {
    const route = this.routeTable.get(routeId);
    if (!route) {
      return { status: 'not_found' };
    }
    
    const instances = await this.getRouteInstances(route);
    const healthyInstances = instances.filter(i => i.healthy);
    
    return {
      status: healthyInstances.length > 0 ? 'healthy' : 'unhealthy',
      totalInstances: instances.length,
      healthyInstances: healthyInstances.length,
      lastCheck: new Date()
    };
  }
}
```

这个路由系统为足球经理网关提供了灵活、高性能的请求路由能力，支持复杂的游戏场景和高并发访问。

## 📋 实现状态

### 已完成功能
- [x] **路由接口定义** - 完整的 Route、ServiceTarget、RouteConfig 接口
- [x] **路由匹配算法** - 支持精确、参数、通配符、正则四种匹配模式
- [x] **动态路由管理** - RouteManagerService 提供完整的路由 CRUD 操作
- [x] **路由验证和冲突检测** - 自动验证路由配置并检测冲突
- [x] **路由缓存和性能优化** - Redis 缓存和事件驱动更新
- [x] **多HTTP方法支持** - 单个路由支持多个 HTTP 方法
- [x] **负载均衡集成** - 支持 5 种负载均衡算法
- [x] **中间件支持** - 路由级中间件配置
- [x] **监控和指标** - 完整的路由监控指标
- [x] **热更新** - 运行时动态更新路由配置

### 核心组件
- **RouteManagerService** - 路由管理核心服务
- **RouteMatcherService** - 路由匹配引擎
- **ProxyController** - HTTP 代理控制器
- **AuthGuard** - 认证守卫
- **RateLimitGuard** - 限流守卫
- **ProxyInterceptor** - 代理拦截器

### 相关文档
- [路由管理服务文档](../services/route-manager.md)
- [代理模块文档](../modules/proxy-module.md)
- [守卫和拦截器文档](../security/guards-interceptors.md)
- [指标监控文档](../modules/metrics-module.md)
