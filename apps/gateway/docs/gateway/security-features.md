# 安全特性

足球经理网关实现了多层安全防护体系，确保游戏数据和用户信息的安全。本文档详细介绍网关的安全特性、防护机制和最佳实践。

## 🛡️ 安全架构

### 多层防护体系

```
┌─────────────────────────────────────────────────────────────────┐
│                        网络层安全                                │
├─────────────────────────────────────────────────────────────────┤
│  防火墙  │  DDoS防护  │  IP白名单  │  地理位置限制  │  CDN防护   │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        传输层安全                                │
├─────────────────────────────────────────────────────────────────┤
│  TLS/SSL  │  证书管理  │  HSTS  │  证书透明度  │  密钥轮换      │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        应用层安全                                │
├─────────────────────────────────────────────────────────────────┤
│  认证授权  │  输入验证  │  输出编码  │  会话管理  │  CSRF防护     │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        数据层安全                                │
├─────────────────────────────────────────────────────────────────┤
│  数据加密  │  访问控制  │  审计日志  │  数据脱敏  │  备份加密     │
└─────────────────────────────────────────────────────────────────┘
```

## 🔐 认证与授权

### 1. 多因子认证 (MFA)

```typescript
// MFA 配置
interface MFAConfig {
  enabled: boolean;
  methods: MFAMethod[];
  gracePeriod: number;        // 宽限期
  backupCodes: number;        // 备用码数量
  enforceForRoles: string[];  // 强制启用的角色
}

enum MFAMethod {
  TOTP = 'totp',           // 时间基础一次性密码
  SMS = 'sms',             // 短信验证码
  EMAIL = 'email',         // 邮件验证码
  HARDWARE = 'hardware',   // 硬件令牌
  BIOMETRIC = 'biometric'  // 生物识别
}

// MFA 服务实现
class MFAService {
  async setupTOTP(userId: string): Promise<{ secret: string; qrCode: string }> {
    const secret = speakeasy.generateSecret({
      name: `足球经理 (${userId})`,
      issuer: '足球经理游戏'
    });
    
    // 保存密钥
    await this.characterService.saveMFASecret(userId, secret.base32);
    
    // 生成二维码
    const qrCode = await QRCode.toDataURL(secret.otpauth_url);
    
    return {
      secret: secret.base32,
      qrCode
    };
  }
  
  async verifyTOTP(userId: string, token: string): Promise<boolean> {
    const secret = await this.characterService.getMFASecret(userId);
    
    return speakeasy.totp.verify({
      secret,
      token,
      window: 2,  // 允许时间偏差
      encoding: 'base32'
    });
  }
  
  async sendSMSCode(userId: string, phoneNumber: string): Promise<void> {
    const code = this.generateVerificationCode();
    
    // 保存验证码
    await this.redisService.set(
      `mfa:sms:${userId}`,
      code,
      300 // 5分钟过期
    );
    
    // 发送短信
    await this.smsService.send(phoneNumber, `您的验证码是: ${code}`);
  }
  
  private generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
}
```

### 2. 基于角色的访问控制 (RBAC)

```typescript
// 权限模型
interface Permission {
  id: string;
  name: string;
  resource: string;    // 资源类型
  action: string;      // 操作类型
  conditions?: any;    // 条件限制
}

interface Role {
  id: string;
  name: string;
  permissions: Permission[];
  inherits?: string[]; // 继承的角色
}

// RBAC 服务
class RBACService {
  private roleHierarchy: Map<string, string[]> = new Map();
  
  async checkPermission(
    userId: string,
    resource: string,
    action: string,
    context?: any
  ): Promise<boolean> {
    const userRoles = await this.getUserRoles(userId);
    const allRoles = this.expandRoles(userRoles);
    
    for (const roleId of allRoles) {
      const role = await this.getRole(roleId);
      
      for (const permission of role.permissions) {
        if (this.matchesPermission(permission, resource, action, context)) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  private expandRoles(roles: string[]): string[] {
    const expanded = new Set<string>();
    
    const addRole = (roleId: string) => {
      if (expanded.has(roleId)) return;
      
      expanded.add(roleId);
      const inherits = this.roleHierarchy.get(roleId) || [];
      inherits.forEach(addRole);
    };
    
    roles.forEach(addRole);
    return Array.from(expanded);
  }
  
  private matchesPermission(
    permission: Permission,
    resource: string,
    action: string,
    context?: any
  ): boolean {
    // 资源匹配
    if (!this.matchesResource(permission.resource, resource)) {
      return false;
    }
    
    // 操作匹配
    if (!this.matchesAction(permission.action, action)) {
      return false;
    }
    
    // 条件匹配
    if (permission.conditions && !this.matchesConditions(permission.conditions, context)) {
      return false;
    }
    
    return true;
  }
}
```

## 🚫 攻击防护

### 1. DDoS 防护

```typescript
// DDoS 防护配置
interface DDoSConfig {
  enabled: boolean;
  burst: number;           // 突发请求数
  limit: number;           // 请求限制
  maxcount: number;        // 最大计数
  maxexpiry: number;       // 最大过期时间
  checkinterval: number;   // 检查间隔
  trustProxy: boolean;     // 信任代理
  includeUserAgent: boolean; // 包含用户代理
  errormessage: string;    // 错误消息
}

// DDoS 防护中间件
class DDoSProtectionMiddleware {
  private connections: Map<string, any> = new Map();
  
  constructor(private config: DDoSConfig) {}
  
  middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const key = this.generateKey(req);
      const now = Date.now();
      
      // 获取连接信息
      let connection = this.connections.get(key);
      if (!connection) {
        connection = {
          count: 0,
          expiry: now + this.config.maxexpiry,
          burst: this.config.burst
        };
        this.connections.set(key, connection);
      }
      
      // 检查是否过期
      if (now > connection.expiry) {
        connection.count = 0;
        connection.expiry = now + this.config.maxexpiry;
        connection.burst = this.config.burst;
      }
      
      // 增加计数
      connection.count++;
      
      // 检查突发限制
      if (connection.burst > 0) {
        connection.burst--;
        return next();
      }
      
      // 检查请求限制
      if (connection.count > this.config.limit) {
        this.logAttack(req, key, connection);
        return res.status(429).json({
          error: this.config.errormessage || 'Too many requests'
        });
      }
      
      next();
    };
  }
  
  private generateKey(req: Request): string {
    const ip = this.getClientIP(req);
    const userAgent = this.config.includeUserAgent ? req.get('user-agent') : '';
    return `${ip}:${userAgent}`;
  }
  
  private logAttack(req: Request, key: string, connection: any): void {
    this.logger.warn('DDoS attack detected', {
      key,
      count: connection.count,
      limit: this.config.limit,
      ip: this.getClientIP(req),
      userAgent: req.get('user-agent'),
      path: req.path
    });
  }
}
```

### 2. SQL 注入防护

```typescript
// SQL 注入检测器
class SQLInjectionDetector {
  private patterns: RegExp[] = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /('|(\\')|(;)|(--)|(\s)|(\/\*)|(\*\/))/i,
    /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT|ONLOAD|ONERROR|ONCLICK)\b)/i
  ];
  
  detectSQLInjection(input: string): boolean {
    if (!input || typeof input !== 'string') {
      return false;
    }
    
    const decoded = this.decodeInput(input);
    
    return this.patterns.some(pattern => pattern.test(decoded));
  }
  
  private decodeInput(input: string): string {
    try {
      // URL 解码
      let decoded = decodeURIComponent(input);
      
      // HTML 实体解码
      decoded = decoded.replace(/&lt;/g, '<')
                      .replace(/&gt;/g, '>')
                      .replace(/&amp;/g, '&')
                      .replace(/&quot;/g, '"')
                      .replace(/&#x27;/g, "'");
      
      return decoded;
    } catch {
      return input;
    }
  }
  
  sanitizeInput(input: string): string {
    if (!input || typeof input !== 'string') {
      return input;
    }
    
    // 移除危险字符
    return input.replace(/['"\\;--]/g, '')
                .replace(/\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b/gi, '')
                .trim();
  }
}
```

### 3. XSS 防护

```typescript
// XSS 防护服务
class XSSProtectionService {
  private dangerousPatterns: RegExp[] = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi
  ];
  
  sanitizeHTML(input: string): string {
    if (!input || typeof input !== 'string') {
      return input;
    }
    
    let sanitized = input;
    
    // 移除危险标签
    this.dangerousPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });
    
    // HTML 实体编码
    sanitized = sanitized.replace(/&/g, '&amp;')
                        .replace(/</g, '&lt;')
                        .replace(/>/g, '&gt;')
                        .replace(/"/g, '&quot;')
                        .replace(/'/g, '&#x27;')
                        .replace(/\//g, '&#x2F;');
    
    return sanitized;
  }
  
  validateInput(input: string): { isValid: boolean; threats: string[] } {
    const threats: string[] = [];
    
    this.dangerousPatterns.forEach((pattern, index) => {
      if (pattern.test(input)) {
        threats.push(`XSS Pattern ${index + 1} detected`);
      }
    });
    
    return {
      isValid: threats.length === 0,
      threats
    };
  }
}
```

## 🔒 数据保护

### 1. 数据加密

```typescript
// 数据加密服务
class DataEncryptionService {
  private algorithm = 'aes-256-gcm';
  private keyLength = 32;
  private ivLength = 16;
  private tagLength = 16;
  
  constructor(private masterKey: string) {}
  
  encrypt(data: string): { encrypted: string; iv: string; tag: string } {
    const key = crypto.scryptSync(this.masterKey, 'salt', this.keyLength);
    const iv = crypto.randomBytes(this.ivLength);
    
    const cipher = crypto.createCipher(this.algorithm, key, { iv });
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  }
  
  decrypt(encrypted: string, iv: string, tag: string): string {
    const key = crypto.scryptSync(this.masterKey, 'salt', this.keyLength);
    const decipher = crypto.createDecipher(
      this.algorithm,
      key,
      { iv: Buffer.from(iv, 'hex') }
    );
    
    decipher.setAuthTag(Buffer.from(tag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
  
  // 敏感字段加密
  encryptSensitiveFields(data: any, fields: string[]): any {
    const result = { ...data };
    
    fields.forEach(field => {
      if (result[field]) {
        const encrypted = this.encrypt(result[field]);
        result[field] = {
          encrypted: encrypted.encrypted,
          iv: encrypted.iv,
          tag: encrypted.tag,
          _encrypted: true
        };
      }
    });
    
    return result;
  }
  
  // 敏感字段解密
  decryptSensitiveFields(data: any, fields: string[]): any {
    const result = { ...data };
    
    fields.forEach(field => {
      if (result[field] && result[field]._encrypted) {
        const { encrypted, iv, tag } = result[field];
        result[field] = this.decrypt(encrypted, iv, tag);
      }
    });
    
    return result;
  }
}
```

### 2. 数据脱敏

```typescript
// 数据脱敏服务
class DataMaskingService {
  // 邮箱脱敏
  maskEmail(email: string): string {
    if (!email || !email.includes('@')) {
      return email;
    }
    
    const [username, domain] = email.split('@');
    const maskedUsername = username.length > 2 
      ? username.substring(0, 2) + '*'.repeat(username.length - 2)
      : username;
    
    return `${maskedUsername}@${domain}`;
  }
  
  // 手机号脱敏
  maskPhone(phone: string): string {
    if (!phone || phone.length < 7) {
      return phone;
    }
    
    return phone.substring(0, 3) + '*'.repeat(phone.length - 6) + phone.substring(phone.length - 3);
  }
  
  // 身份证号脱敏
  maskIdCard(idCard: string): string {
    if (!idCard || idCard.length < 8) {
      return idCard;
    }
    
    return idCard.substring(0, 4) + '*'.repeat(idCard.length - 8) + idCard.substring(idCard.length - 4);
  }
  
  // 银行卡号脱敏
  maskBankCard(cardNumber: string): string {
    if (!cardNumber || cardNumber.length < 8) {
      return cardNumber;
    }
    
    return cardNumber.substring(0, 4) + ' **** **** ' + cardNumber.substring(cardNumber.length - 4);
  }
  
  // 通用脱敏
  maskData(data: any, rules: MaskingRule[]): any {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    const result = { ...data };
    
    rules.forEach(rule => {
      if (result[rule.field]) {
        result[rule.field] = this.applyMaskingRule(result[rule.field], rule);
      }
    });
    
    return result;
  }
  
  private applyMaskingRule(value: string, rule: MaskingRule): string {
    switch (rule.type) {
      case 'email':
        return this.maskEmail(value);
      case 'phone':
        return this.maskPhone(value);
      case 'idcard':
        return this.maskIdCard(value);
      case 'bankcard':
        return this.maskBankCard(value);
      case 'custom':
        return rule.maskFunction ? rule.maskFunction(value) : value;
      default:
        return value;
    }
  }
}

interface MaskingRule {
  field: string;
  type: 'email' | 'phone' | 'idcard' | 'bankcard' | 'custom';
  maskFunction?: (value: string) => string;
}
```

## 📋 安全审计

### 1. 安全事件记录

```typescript
// 安全事件类型
enum SecurityEventType {
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',
  LOGOUT = 'logout',
  PASSWORD_CHANGE = 'password_change',
  PERMISSION_DENIED = 'permission_denied',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  DATA_ACCESS = 'data_access',
  ADMIN_ACTION = 'admin_action',
  SECURITY_VIOLATION = 'security_violation'
}

// 安全审计服务
class SecurityAuditService {
  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    const auditRecord = {
      id: this.generateEventId(),
      timestamp: new Date(),
      type: event.type,
      severity: event.severity,
      userId: event.userId,
      sessionId: event.sessionId,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      resource: event.resource,
      action: event.action,
      result: event.result,
      details: event.details,
      risk_score: this.calculateRiskScore(event)
    };
    
    // 存储到审计日志
    await this.auditRepository.save(auditRecord);
    
    // 实时告警检查
    if (auditRecord.risk_score > 80) {
      await this.alertService.sendSecurityAlert(auditRecord);
    }
    
    // 异常行为检测
    await this.detectAnomalousActivity(auditRecord);
  }
  
  private calculateRiskScore(event: SecurityEvent): number {
    let score = 0;
    
    // 基础风险评分
    switch (event.type) {
      case SecurityEventType.LOGIN_FAILURE:
        score += 20;
        break;
      case SecurityEventType.PERMISSION_DENIED:
        score += 30;
        break;
      case SecurityEventType.SECURITY_VIOLATION:
        score += 50;
        break;
      case SecurityEventType.SUSPICIOUS_ACTIVITY:
        score += 40;
        break;
    }
    
    // IP 地理位置风险
    if (event.ipAddress && this.isHighRiskLocation(event.ipAddress)) {
      score += 25;
    }
    
    // 时间风险（非工作时间）
    if (this.isOffHours(event.timestamp)) {
      score += 15;
    }
    
    // 频率风险
    const recentEvents = await this.getRecentEvents(event.userId, event.type, 300); // 5分钟内
    if (recentEvents.length > 5) {
      score += 30;
    }
    
    return Math.min(score, 100);
  }
  
  private async detectAnomalousActivity(event: SecurityEvent): Promise<void> {
    // 检测异常登录模式
    if (event.type === SecurityEventType.LOGIN_SUCCESS) {
      await this.detectAnomalousLogin(event);
    }
    
    // 检测异常访问模式
    if (event.type === SecurityEventType.DATA_ACCESS) {
      await this.detectAnomalousAccess(event);
    }
  }
}

interface SecurityEvent {
  type: SecurityEventType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent: string;
  resource?: string;
  action?: string;
  result: 'success' | 'failure';
  details?: any;
  timestamp?: Date;
}
```

这些安全特性为足球经理网关提供了全面的安全保护，确保游戏数据和用户信息的安全性。
