# 配置参考

足球经理网关提供了丰富的配置选项，支持多种部署场景和业务需求。本文档详细介绍所有配置参数和最佳实践。

## 📋 配置文件结构

### 主配置文件

```typescript
// apps/gateway/src/config/gateway.config.ts
export default () => ({
  // 基础配置
  app: {
    name: process.env.APP_NAME || 'football-manager-gateway',
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.GATEWAY_PORT) || 3000,
    host: process.env.GATEWAY_HOST || '0.0.0.0',
    timezone: process.env.TZ || 'UTC'
  },

  // 网关核心配置
  gateway: {
    // 路由配置
    routing: {
      enabled: true,
      caseSensitive: false,
      mergeParams: true,
      strict: false,
      defaultTimeout: 30000,
      maxRedirects: 5
    },

    // 中间件配置
    middleware: {
      order: [
        'security',
        'logging',
        'auth',
        'rate-limit',
        'cache',
        'business'
      ],
      enabled: {
        cors: true,
        helmet: true,
        compression: true,
        bodyParser: true
      }
    },

    // 代理配置
    proxy: {
      timeout: 30000,
      retries: 3,
      retryDelay: 1000,
      keepAlive: true,
      maxSockets: 50
    }
  }
});
```

## 🔐 安全配置

### JWT 认证配置

```typescript
// JWT 配置
jwt: {
  secret: process.env.JWT_SECRET,
  algorithm: process.env.JWT_ALGORITHM || 'HS256',
  expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  issuer: process.env.JWT_ISSUER || 'football-manager-gateway',
  audience: process.env.JWT_AUDIENCE || 'football-manager-app',
  
  // 高级选项
  clockTolerance: 60, // 时钟偏差容忍度（秒）
  ignoreExpiration: false,
  ignoreNotBefore: false,
  
  // 黑名单配置
  blacklist: {
    enabled: true,
    ttl: 86400, // 24小时
    keyPrefix: 'jwt:blacklist:'
  }
}
```

### CORS 配置

```typescript
// CORS 配置
cors: {
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'X-Request-ID',
    'X-User-Agent'
  ],
  exposedHeaders: [
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
    'X-Response-Time'
  ],
  credentials: true,
  maxAge: 86400, // 预检请求缓存时间
  optionsSuccessStatus: 200
}
```

### Helmet 安全头配置

```typescript
// Helmet 安全配置
helmet: {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "https:"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      childSrc: ["'none'"],
      workerSrc: ["'self'"],
      manifestSrc: ["'self'"]
    },
    reportOnly: process.env.NODE_ENV === 'development'
  },
  
  hsts: {
    maxAge: 31536000, // 1年
    includeSubDomains: true,
    preload: true
  },
  
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'same-origin' },
  
  // 隐藏技术栈信息
  hidePoweredBy: true
}
```

## 🚦 限流配置

### 基础限流配置

```typescript
// 限流配置
rateLimit: {
  // 全局默认配置
  global: {
    windowMs: 60000, // 1分钟
    max: 1000, // 1000请求/分钟
    strategy: 'sliding-window',
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    keyGenerator: 'ip' // ip | user | custom
  },

  // 端点特定配置
  endpoints: {
    '/api/auth/login': {
      windowMs: 300000, // 5分钟
      max: 5, // 5次尝试
      strategy: 'fixed-window',
      blockDuration: 900000 // 15分钟封禁
    },
    
    '/api/auth/register': {
      windowMs: 3600000, // 1小时
      max: 3, // 3次注册
      strategy: 'fixed-window'
    },
    
    '/api/matches/live': {
      windowMs: 60000,
      max: 200, // 高频访问
      strategy: 'token-bucket',
      capacity: 200,
      refillRate: 50,
      refillPeriod: 15000
    }
  },

  // 用户类型配置
  userTypes: {
    anonymous: { multiplier: 0.5 },
    basic: { multiplier: 1 },
    premium: { multiplier: 3 },
    vip: { multiplier: 5 },
    admin: { multiplier: 10 }
  },

  // Redis 配置
  redis: {
    keyPrefix: 'rate_limit:',
    ttl: 3600
  }
}
```

## 💾 缓存配置

### 多级缓存配置

```typescript
// 缓存配置
cache: {
  // L1 内存缓存
  memory: {
    enabled: true,
    maxSize: 100 * 1024 * 1024, // 100MB
    ttl: 300, // 5分钟
    algorithm: 'LRU',
    checkPeriod: 60 // 清理周期
  },

  // L2 Redis缓存
  redis: {
    enabled: true,
    ttl: 3600, // 1小时
    keyPrefix: 'cache:',
    compression: true,
    serialization: 'json'
  },

  // 缓存策略
  strategies: {
    'cache-aside': { default: true },
    'write-through': { enabled: false },
    'write-behind': { enabled: false, delay: 5000 }
  },

  // 端点缓存配置
  endpoints: {
    '/api/users/rankings': {
      ttl: 1800, // 30分钟
      tags: ['rankings', 'users'],
      varyBy: ['user_level', 'season']
    },
    
    '/api/matches/schedule': {
      ttl: 3600, // 1小时
      tags: ['matches', 'schedule'],
      varyBy: ['date', 'league']
    },
    
    '/api/players/market': {
      ttl: 300, // 5分钟
      tags: ['players', 'market'],
      varyBy: ['position', 'price_range']
    }
  }
}
```

## 🔌 WebSocket 配置

### WebSocket 服务配置

```typescript
// WebSocket 配置
websocket: {
  enabled: true,
  
  // 服务器配置
  server: {
    port: parseInt(process.env.WS_PORT) || 3001,
    path: '/socket.io',
    serveClient: false,
    pingTimeout: 60000,
    pingInterval: 25000,
    upgradeTimeout: 10000,
    maxHttpBufferSize: 1e6, // 1MB
    allowEIO3: false
  },

  // 传输配置
  transports: ['websocket', 'polling'],
  
  // CORS 配置
  cors: {
    origin: process.env.WS_CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true
  },

  // 适配器配置（Redis）
  adapter: {
    type: 'redis',
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_WS_DB) || 1
  },

  // 房间配置
  rooms: {
    maxMembers: 1000,
    autoCleanup: true,
    cleanupInterval: 300000, // 5分钟
    defaultRooms: ['lobby', 'announcements']
  },

  // 消息配置
  messages: {
    maxSize: 64 * 1024, // 64KB
    rateLimit: {
      points: 30, // 30条消息
      duration: 60 // 1分钟
    },
    history: {
      enabled: true,
      maxMessages: 100,
      ttl: 86400 // 24小时
    }
  }
}
```

## 📊 监控配置

### Prometheus 指标配置

```typescript
// 监控配置
monitoring: {
  // Prometheus 配置
  prometheus: {
    enabled: true,
    endpoint: '/metrics',
    defaultMetrics: true,
    prefix: 'gateway_',
    
    // 自定义指标
    customMetrics: {
      httpRequestDuration: {
        type: 'histogram',
        name: 'http_request_duration_seconds',
        help: 'HTTP request duration in seconds',
        labelNames: ['method', 'route', 'status_code'],
        buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
      },
      
      activeConnections: {
        type: 'gauge',
        name: 'websocket_active_connections',
        help: 'Number of active WebSocket connections',
        labelNames: ['room']
      }
    }
  },

  // 健康检查配置
  health: {
    enabled: true,
    endpoint: '/health',
    checks: {
      redis: { timeout: 5000 },
      database: { timeout: 5000 },
      services: { timeout: 3000 }
    }
  },

  // 分布式追踪配置
  tracing: {
    enabled: false,
    jaeger: {
      endpoint: process.env.JAEGER_ENDPOINT,
      serviceName: 'football-manager-gateway',
      sampler: {
        type: 'const',
        param: 1
      }
    }
  }
}
```

## 🗄️ 数据库配置

### Redis 配置

```typescript
// Redis 配置
redis: {
  // 主连接
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT) || 6379,
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB) || 0,
  
  // 连接选项
  connectTimeout: 10000,
  commandTimeout: 5000,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  
  // 连接池
  family: 4,
  maxMemoryPolicy: 'allkeys-lru',
  
  // 集群配置（可选）
  cluster: {
    enabled: false,
    nodes: [
      { host: 'redis-1', port: 6379 },
      { host: 'redis-2', port: 6379 },
      { host: 'redis-3', port: 6379 }
    ],
    options: {
      redisOptions: {
        password: process.env.REDIS_PASSWORD
      }
    }
  },

  // 哨兵配置（可选）
  sentinel: {
    enabled: false,
    sentinels: [
      { host: 'sentinel-1', port: 26379 },
      { host: 'sentinel-2', port: 26379 },
      { host: 'sentinel-3', port: 26379 }
    ],
    name: 'mymaster'
  }
}
```

### MongoDB 配置

```typescript
// MongoDB 配置
mongodb: {
  uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/football-manager',
  
  // 连接选项
  options: {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
    bufferMaxEntries: 0,
    bufferCommands: false,
    
    // 认证
    authSource: 'admin',
    
    // SSL/TLS
    ssl: process.env.MONGODB_SSL === 'true',
    sslValidate: process.env.MONGODB_SSL_VALIDATE === 'true',
    
    // 副本集
    replicaSet: process.env.MONGODB_REPLICA_SET,
    
    // 读写偏好
    readPreference: 'primary',
    writeConcern: {
      w: 'majority',
      j: true,
      wtimeout: 1000
    }
  }
}
```

## 🌐 服务发现配置

### 后端服务配置

```typescript
// 服务发现配置
services: {
  // 服务注册中心
  discovery: {
    type: 'static', // static | consul | eureka | kubernetes
    
    // 静态配置
    static: {
      'auth-service': [
        { url: 'http://auth-service-1:3001', weight: 1, healthy: true },
        { url: 'http://auth-service-2:3001', weight: 1, healthy: true }
      ],
      'user-service': [
        { url: 'http://user-service-1:3002', weight: 2, healthy: true },
        { url: 'http://user-service-2:3002', weight: 1, healthy: true }
      ],
      'match-service': [
        { url: 'http://match-service:3003', weight: 1, healthy: true }
      ]
    },
    
    // Consul 配置
    consul: {
      host: process.env.CONSUL_HOST || 'localhost',
      port: parseInt(process.env.CONSUL_PORT) || 8500,
      secure: false,
      defaults: {
        token: process.env.CONSUL_TOKEN
      }
    }
  },

  // 负载均衡配置
  loadBalancer: {
    algorithm: 'round-robin', // round-robin | weighted | least-connections | ip-hash
    healthCheck: {
      enabled: true,
      interval: 30000,
      timeout: 5000,
      retries: 3,
      path: '/health'
    }
  },

  // 熔断器配置
  circuitBreaker: {
    enabled: true,
    failureThreshold: 5,
    resetTimeout: 60000,
    monitoringPeriod: 10000,
    volumeThreshold: 10,
    errorThresholdPercentage: 50
  }
}
```

## 📝 日志配置

### 日志系统配置

```typescript
// 日志配置
logging: {
  level: process.env.LOG_LEVEL || 'info',
  format: process.env.LOG_FORMAT || 'json', // json | text
  
  // 输出配置
  transports: {
    console: {
      enabled: true,
      level: 'debug',
      colorize: process.env.NODE_ENV === 'development'
    },
    
    file: {
      enabled: true,
      level: 'info',
      filename: 'logs/gateway.log',
      maxSize: '20m',
      maxFiles: '14d',
      compress: true
    },
    
    error: {
      enabled: true,
      level: 'error',
      filename: 'logs/error.log',
      maxSize: '20m',
      maxFiles: '30d'
    }
  },

  // 结构化日志字段
  fields: {
    service: 'football-manager-gateway',
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  },

  // 敏感信息过滤
  redact: [
    'password',
    'token',
    'authorization',
    'cookie',
    'x-api-key'
  ]
}
```

## 🔧 环境变量

### 必需的环境变量

```bash
# 基础配置
NODE_ENV=production
GATEWAY_PORT=3000
GATEWAY_HOST=0.0.0.0

# 安全配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
CORS_ORIGIN=https://yourgame.com,https://admin.yourgame.com

# Redis 配置
REDIS_HOST=redis.yourdomain.com
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# MongoDB 配置
MONGODB_URI=mongodb://username:<EMAIL>:27017/football-manager

# 服务 URL
AUTH_SERVICE_URL=http://auth-service:3001
CHARACTER_SERVICE_URL=http://character-service:3002
CLUB_SERVICE_URL=http://club-service:3003
MATCH_SERVICE_URL=http://match-service:3004
PLAYER_SERVICE_URL=http://player-service:3005
```

### 可选的环境变量

```bash
# 监控配置
PROMETHEUS_ENABLED=true
JAEGER_ENDPOINT=http://jaeger:14268/api/traces

# 缓存配置
CACHE_MEMORY_MAX_SIZE=104857600
CACHE_REDIS_TTL=3600

# WebSocket 配置
WS_PORT=3001
WS_CORS_ORIGIN=https://yourgame.com

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json

# 限流配置
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=1000
```

这个配置参考提供了网关所有主要功能的详细配置选项，帮助您根据具体需求进行定制。
