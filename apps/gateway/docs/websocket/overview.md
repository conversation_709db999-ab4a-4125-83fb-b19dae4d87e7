# WebSocket Gateway Overview

足球经理网关提供了完整的 WebSocket 实现，支持实时通信功能，包括实时比赛更新、即时消息和实时通知。

## ✨ 核心特性

### 🔄 WebSocket 核心功能
- **实时双向通信**: 客户端与服务器之间的双向实时通信
- **房间管理**: 为不同场景创建和管理房间（比赛、聊天等）
- **智能消息路由**: 统一的消息路由到微服务架构
- **连接管理**: 自动连接处理、心跳检测和重连机制
- **JWT 认证**: 安全的 WebSocket 连接认证
- **智能限流**: WebSocket 专用的限流和防滥用保护
- **离线消息**: 用户离线时的消息存储和推送
- **多设备支持**: 同一用户多设备连接管理

### 🎮 游戏专用功能
- **实时比赛更新**: 实时比赛事件和比分更新
- **球员通知**: 转会、伤病等即时通知
- **聊天系统**: 比赛和俱乐部内的游戏聊天
- **用户状态**: 在线/离线状态跟踪
- **事件广播**: 系统级事件通知
- **微服务集成**: 与 6 个游戏微服务的完整集成

### 🛡️ 安全和性能
- **多层守卫**: 认证守卫 + 限流守卫
- **装饰器支持**: 灵活的限流和权限配置
- **滑动窗口限流**: 基于 Redis 的精确限流算法
- **权限控制**: 基于角色的访问控制
- **连接监控**: 实时连接统计和清理

## 🏗️ 架构设计

### 微服务集成架构

```
┌─────────────────────────────────────────────────────────────────────┐
│                        WebSocket Gateway                           │
├─────────────────────────────────────────────────────────────────────┤
│  连接管理器    │   房间管理器   │   消息路由器   │   会话管理器    │
├─────────────────────────────────────────────────────────────────────┤
│              认证守卫 (WsAuthGuard)                                │
├─────────────────────────────────────────────────────────────────────┤
│              限流守卫 (WsRateLimitGuard)                           │
├─────────────────────────────────────────────────────────────────────┤
│                        微服务客户端                                │
│  AUTH_SERVICE │ CHARACTER_SERVICE │ GAME_SERVICE │ CLUB_SERVICE │ ...   │
├─────────────────────────────────────────────────────────────────────┤
│                      Redis 传输层                                  │
│                  (微服务通信 & 缓存)                               │
├─────────────────────────────────────────────────────────────────────┤
│                     Socket.IO 服务器                               │
└─────────────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. **WebSocketGateway** - 主网关服务
- 统一的消息入口和路由
- 微服务消息转发
- 连接生命周期管理

#### 2. **SessionService** - 会话管理
- 用户会话创建和销毁
- 多设备连接支持
- Redis 会话持久化

#### 3. **WsAuthGuard** - 认证守卫
- JWT 令牌验证
- 用户信息注入
- 权限级别检查

#### 4. **WsRateLimitGuard** - 限流守卫
- 滑动窗口限流算法
- 用户级和IP级限流
- 自定义限流配置

## 🔄 连接生命周期

### 1. 连接建立

```typescript
// 客户端连接
const socket = io('http://localhost:3000', {
  auth: {
    token: 'jwt-token-here'
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000
});

// 服务端连接处理
@WSGateway({
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true,
  },
  namespace: '/',
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
})
export class WebSocketGateway {
  async handleConnection(client: AuthenticatedSocket) {
    // 自动认证客户端
    // 设置客户端上下文
    // 加入默认房间
    // 发送离线消息
  }
}
```

### 2. 增强的认证机制

```typescript
// 多来源 JWT 认证
private async authenticateSocket(socket: AuthenticatedSocket): Promise<void> {
  // 支持多种令牌来源
  const token = socket.handshake.auth?.token ||
                socket.handshake.query?.token ||
                this.extractTokenFromCookie(socket);

  if (!token) {
    socket.authenticated = false;
    return; // 允许匿名连接，但功能受限
  }

  try {
    const payload = this.jwtService.verify(token as string);

    socket.userId = payload.sub;
    socket.user = {
      id: payload.sub,
      username: payload.username,
      email: payload.email,
      roles: payload.roles || [],
    };
    socket.authenticated = true;

    // 创建会话
    await this.sessionService.createSession(payload.sub, socket.id);

    // 发送连接确认
    socket.emit('connected', {
      clientId: socket.id,
      userId: payload.sub,
      timestamp: new Date(),
      serverInfo: {
        version: '1.0.0',
        features: ['chat', 'match', 'notifications', 'microservices'],
      },
    });

  } catch (error) {
    throw new Error('Invalid authentication token');
  }
}
```

### 3. Room Management

```typescript
// Join room
@SubscribeMessage('join_room')
async handleJoinRoom(
  client: Socket,
  payload: { roomId: string; password?: string }
) {
  try {
    // Validate room access
    const canJoin = await this.validateRoomAccess(
      client.data.user,
      payload.roomId,
      payload.password
    );
    
    if (canJoin) {
      await client.join(payload.roomId);
      
      // Notify room members
      client.to(payload.roomId).emit('user_joined', {
        userId: client.data.user.id,
        username: client.data.user.username,
        timestamp: new Date(),
      });
      
      // Confirm join
      client.emit('joined_room', {
        roomId: payload.roomId,
        memberCount: await this.getRoomMemberCount(payload.roomId),
      });
    }
  } catch (error) {
    client.emit('error', { message: 'Failed to join room' });
  }
}
```

## Event Types

### System Events

```typescript
// Connection events
'connect'           // Client connected
'disconnect'        // Client disconnected
'error'            // Connection error
'reconnect'        // Client reconnected

// Authentication events
'connected'        // Authentication successful
'auth_error'       // Authentication failed
```

### Room Events

```typescript
// Room management
'join_room'        // Join a room
'leave_room'       // Leave a room
'joined_room'      // Successfully joined room
'left_room'        // Successfully left room
'user_joined'      // Another user joined room
'user_left'        // Another user left room

// Room information
'room_info'        // Get room information
'room_members'     // Get room member list
```

### Messaging Events

```typescript
// Message sending
'send_message'     // Send message to room or user
'message'          // Receive room message
'private_message'  // Receive private message
'message_sent'     // Message delivery confirmation

// Message management
'edit_message'     // Edit sent message
'delete_message'   // Delete sent message
'message_edited'   // Message was edited
'message_deleted'  // Message was deleted
```

### Game Events

```typescript
// Match events
'match_started'    // Match has started
'match_ended'      // Match has ended
'goal_scored'      // Goal was scored
'card_issued'      // Card was issued
'substitution'     // Player substitution

// Player events
'player_transferred'  // Player transfer
'player_injured'     // Player injury
'player_recovered'   // Player recovery

// Club events
'club_updated'     // Club information updated
'new_signing'      // New player signed

// System events
'notification'     // General notification
'system_message'   // System announcement
```

## Message Format

### Standard Message Structure

```typescript
interface WebSocketMessage {
  id: string;                    // Unique message ID
  type: string;                  // Message type
  timestamp: Date;               // Message timestamp
  senderId: string;              // Sender user ID
  senderName: string;            // Sender display name
  content: any;                  // Message content
  metadata?: Record<string, any>; // Additional metadata
}
```

### Room Message Example

```json
{
  "id": "msg_12345",
  "type": "room_message",
  "timestamp": "2023-12-01T10:00:00.000Z",
  "senderId": "user_123",
  "senderName": "John Doe",
  "roomId": "match_456",
  "content": {
    "text": "Great goal!",
    "messageType": "text"
  },
  "metadata": {
    "edited": false,
    "replyTo": null
  }
}
```

### Game Event Example

```json
{
  "id": "event_67890",
  "type": "goal_scored",
  "timestamp": "2023-12-01T10:15:30.000Z",
  "matchId": "match_456",
  "content": {
    "playerId": "player_789",
    "playerName": "Lionel Messi",
    "teamId": "team_123",
    "teamName": "Barcelona",
    "minute": 23,
    "assistId": "player_456",
    "assistName": "Xavi"
  },
  "metadata": {
    "homeScore": 1,
    "awayScore": 0
  }
}
```

## Room Types

### Match Rooms

```typescript
// Match room format: match_{matchId}
const matchRoomId = `match_${matchId}`;

// Match room features
- Live match commentary
- Real-time score updates
- Player statistics
- Fan chat
- Match events (goals, cards, substitutions)
```

### Club Rooms

```typescript
// Club room format: club_{clubId}
const clubRoomId = `club_${clubId}`;

// Club room features
- Club announcements
- Transfer news
- Member discussions
- Training updates
- Financial updates
```

### Private Rooms

```typescript
// Private room format: private_{userId1}_{userId2}
const privateRoomId = `private_${Math.min(userId1, userId2)}_${Math.max(userId1, userId2)}`;

// Private room features
- Direct messaging
- File sharing
- Voice/video calls (future)
- Message encryption
```

### System Rooms

```typescript
// System room format: system_{type}
const systemRoomId = `system_notifications`;

// System room features
- Global announcements
- Maintenance notifications
- System updates
- Emergency broadcasts
```

## Rate Limiting

### Connection Rate Limiting

```typescript
// Limit connections per IP
const connectionLimits = {
  windowMs: 60000,        // 1 minute
  max: 10,                // 10 connections per minute per IP
  skipSuccessfulRequests: true,
};

// Limit connections per user
const userConnectionLimits = {
  maxConcurrentConnections: 5,  // Max 5 concurrent connections per user
  maxConnectionsPerHour: 50,    // Max 50 connections per hour per user
};
```

### Message Rate Limiting

```typescript
// Message rate limiting
const messageLimits = {
  room: {
    windowMs: 60000,      // 1 minute
    max: 30,              // 30 messages per minute per room
  },
  private: {
    windowMs: 60000,      // 1 minute
    max: 60,              // 60 private messages per minute
  },
  global: {
    windowMs: 60000,      // 1 minute
    max: 100,             // 100 total messages per minute per user
  },
};
```

## Security Features

### Authentication

- JWT token validation on connection
- Token refresh mechanism
- Session management
- Multi-device support

### Authorization

- Room access control
- Permission-based messaging
- Admin privileges
- Moderation features

### Input Validation

```typescript
// Message validation
const validateMessage = (message: any) => {
  // Length validation
  if (message.text && message.text.length > 1000) {
    throw new Error('Message too long');
  }
  
  // Content filtering
  if (containsProfanity(message.text)) {
    throw new Error('Inappropriate content');
  }
  
  // Spam detection
  if (isSpam(message)) {
    throw new Error('Spam detected');
  }
  
  return true;
};
```

### Anti-Spam Measures

```typescript
// Duplicate message detection
const isDuplicateMessage = (userId: string, message: string) => {
  const recentMessages = getUserRecentMessages(userId);
  return recentMessages.includes(message);
};

// Rapid messaging detection
const isRapidMessaging = (userId: string) => {
  const messageCount = getUserMessageCount(userId, 10000); // Last 10 seconds
  return messageCount > 5;
};
```

## Monitoring and Analytics

### Connection Metrics

```typescript
// Track connection metrics
const connectionMetrics = {
  totalConnections: 0,
  activeConnections: 0,
  connectionsByRoom: new Map(),
  connectionsByUser: new Map(),
  averageConnectionDuration: 0,
};
```

### Message Metrics

```typescript
// Track message metrics
const messageMetrics = {
  totalMessages: 0,
  messagesByType: new Map(),
  messagesByRoom: new Map(),
  averageMessageLength: 0,
  messagesPerSecond: 0,
};
```

### Performance Monitoring

```typescript
// Monitor WebSocket performance
const performanceMetrics = {
  messageLatency: [],
  connectionLatency: [],
  memoryUsage: 0,
  cpuUsage: 0,
  errorRate: 0,
};
```

## Scaling and Clustering

### Redis Adapter

```typescript
// Configure Redis adapter for clustering
const redisAdapter = createAdapter({
  host: 'localhost',
  port: 6379,
  password: 'redis-password',
});

io.adapter(redisAdapter);
```

### Load Balancing

```typescript
// Sticky session configuration
const stickySession = {
  key: 'io',
  httpOnly: true,
  sameSite: true,
  maxAge: 86400000, // 24 hours
};
```

### Horizontal Scaling

- Multiple gateway instances
- Redis-based message distribution
- Session affinity
- Load balancer configuration

This WebSocket implementation provides a robust foundation for real-time features in your Football Manager game, ensuring scalability, security, and excellent user experience.
