/**
 * WebSocket Client Examples
 * 
 * This example demonstrates how to connect to and interact with the
 * WebSocket gateway from a client application.
 */

import { io, Socket } from 'socket.io-client';

/**
 * Example 1: Basic WebSocket Connection
 */
export class BasicWebSocketExample {
  private socket: Socket | null = null;
  
  async connect(url: string = 'http://localhost:3000', token?: string) {
    console.log('=== Basic WebSocket Connection Example ===');
    
    const options: any = {
      transports: ['websocket', 'polling'],
      timeout: 5000,
    };
    
    // Add authentication if token provided
    if (token) {
      options.auth = { token };
    }
    
    this.socket = io(url, options);
    
    // Connection events
    this.socket.on('connect', () => {
      console.log('✓ Connected to WebSocket server');
      console.log('Socket ID:', this.socket?.id);
    });
    
    this.socket.on('disconnect', (reason) => {
      console.log('✗ Disconnected from WebSocket server');
      console.log('Reason:', reason);
    });
    
    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error.message);
    });
    
    // Server confirmation
    this.socket.on('connected', (data) => {
      console.log('Server confirmation received:', data);
    });
    
    return new Promise<void>((resolve, reject) => {
      this.socket?.on('connect', () => resolve());
      this.socket?.on('connect_error', (error) => reject(error));
    });
  }
  
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
  
  getSocket(): Socket | null {
    return this.socket;
  }
}

/**
 * Example 2: Room Management
 */
export class RoomManagementExample {
  constructor(private socket: Socket) {}
  
  async joinRoom(roomId: string, password?: string) {
    console.log('\n=== Room Management Example ===');
    console.log(`Attempting to join room: ${roomId}`);
    
    return new Promise<void>((resolve, reject) => {
      // Listen for room events
      this.socket.on('joined_room', (data) => {
        console.log('✓ Successfully joined room:', data);
        resolve();
      });
      
      this.socket.on('error', (error) => {
        console.error('✗ Failed to join room:', error.message);
        reject(new Error(error.message));
      });
      
      // Send join request
      this.socket.emit('join_room', { roomId, password });
    });
  }
  
  async leaveRoom(roomId: string) {
    console.log(`Leaving room: ${roomId}`);
    
    return new Promise<void>((resolve) => {
      this.socket.on('left_room', (data) => {
        console.log('✓ Successfully left room:', data);
        resolve();
      });
      
      this.socket.emit('leave_room', { roomId });
    });
  }
  
  onUserJoined(callback: (data: any) => void) {
    this.socket.on('user_joined', callback);
  }
  
  onUserLeft(callback: (data: any) => void) {
    this.socket.on('user_left', callback);
  }
}

/**
 * Example 3: Messaging
 */
export class MessagingExample {
  constructor(private socket: Socket) {}
  
  sendRoomMessage(roomId: string, message: string, type: string = 'text') {
    console.log('\n=== Messaging Example ===');
    console.log(`Sending message to room ${roomId}: "${message}"`);
    
    this.socket.emit('send_message', {
      roomId,
      message,
      type,
    });
  }
  
  sendPrivateMessage(targetUserId: string, message: string, type: string = 'text') {
    console.log(`Sending private message to user ${targetUserId}: "${message}"`);
    
    this.socket.emit('send_message', {
      targetUserId,
      message,
      type,
    });
  }
  
  onMessage(callback: (data: any) => void) {
    this.socket.on('message', (data) => {
      console.log('📨 Message received:', data);
      callback(data);
    });
  }
  
  onPrivateMessage(callback: (data: any) => void) {
    this.socket.on('private_message', (data) => {
      console.log('📩 Private message received:', data);
      callback(data);
    });
  }
  
  onMessageSent(callback: (data: any) => void) {
    this.socket.on('message_sent', (data) => {
      console.log('✓ Message sent confirmation:', data);
      callback(data);
    });
  }
  
  onOfflineMessages(callback: (data: any) => void) {
    this.socket.on('offline_messages', (data) => {
      console.log('📬 Offline messages received:', data);
      callback(data);
    });
  }
}

/**
 * Example 4: Heartbeat and Connection Management
 */
export class HeartbeatExample {
  private heartbeatInterval: NodeJS.Timeout | null = null;
  
  constructor(private socket: Socket) {}
  
  startHeartbeat(interval: number = 30000) {
    console.log('\n=== Heartbeat Example ===');
    console.log(`Starting heartbeat with ${interval}ms interval`);
    
    this.socket.on('heartbeat_ack', (data) => {
      console.log('💓 Heartbeat acknowledged:', data);
    });
    
    this.heartbeatInterval = setInterval(() => {
      console.log('💓 Sending heartbeat...');
      this.socket.emit('heartbeat');
    }, interval);
  }
  
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
      console.log('💓 Heartbeat stopped');
    }
  }
}

/**
 * Example 5: Game-specific Events
 */
export class GameEventsExample {
  constructor(private socket: Socket) {}
  
  setupGameEventListeners() {
    console.log('\n=== Game Events Example ===');
    
    // User status events
    this.socket.on('user_status', (data) => {
      console.log('👤 User status update:', data);
    });
    
    // Match events
    this.socket.on('match_started', (data) => {
      console.log('⚽ Match started:', data);
    });
    
    this.socket.on('match_ended', (data) => {
      console.log('🏁 Match ended:', data);
    });
    
    this.socket.on('goal_scored', (data) => {
      console.log('⚽ Goal scored:', data);
    });
    
    // Player events
    this.socket.on('player_transferred', (data) => {
      console.log('🔄 Player transferred:', data);
    });
    
    this.socket.on('player_injured', (data) => {
      console.log('🏥 Player injured:', data);
    });
    
    // Club events
    this.socket.on('club_updated', (data) => {
      console.log('🏟️ Club updated:', data);
    });
    
    // Notification events
    this.socket.on('notification', (data) => {
      console.log('🔔 Notification:', data);
    });
  }
  
  // Send game actions
  sendGameAction(action: string, data: any) {
    console.log(`🎮 Sending game action: ${action}`, data);
    this.socket.emit('game_action', { action, data });
  }
}

/**
 * Complete WebSocket Client Example
 */
export class CompleteWebSocketClient {
  private basicExample: BasicWebSocketExample;
  private roomExample: RoomManagementExample | null = null;
  private messagingExample: MessagingExample | null = null;
  private heartbeatExample: HeartbeatExample | null = null;
  private gameExample: GameEventsExample | null = null;
  
  constructor() {
    this.basicExample = new BasicWebSocketExample();
  }
  
  async connect(url?: string, token?: string) {
    await this.basicExample.connect(url, token);
    
    const socket = this.basicExample.getSocket();
    if (socket) {
      this.roomExample = new RoomManagementExample(socket);
      this.messagingExample = new MessagingExample(socket);
      this.heartbeatExample = new HeartbeatExample(socket);
      this.gameExample = new GameEventsExample(socket);
      
      // Setup game event listeners
      this.gameExample.setupGameEventListeners();
    }
  }
  
  async runCompleteExample() {
    try {
      console.log('WebSocket Client Complete Example');
      console.log('=================================');
      
      // Connect with authentication
      await this.connect('http://localhost:3000', 'your-jwt-token-here');
      
      // Start heartbeat
      this.heartbeatExample?.startHeartbeat();
      
      // Join a room
      await this.roomExample?.joinRoom('match_123');
      
      // Setup message listeners
      this.messagingExample?.onMessage((data) => {
        console.log('Received room message:', data);
      });
      
      this.messagingExample?.onPrivateMessage((data) => {
        console.log('Received private message:', data);
      });
      
      // Send some messages
      this.messagingExample?.sendRoomMessage('match_123', 'Hello everyone!');
      
      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Send a game action
      this.gameExample?.sendGameAction('substitute_player', {
        matchId: 'match_123',
        playerOut: 'player_456',
        playerIn: 'player_789',
      });
      
      // Wait a bit more
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Leave room and disconnect
      await this.roomExample?.leaveRoom('match_123');
      this.heartbeatExample?.stopHeartbeat();
      this.basicExample.disconnect();
      
      console.log('\n✓ Complete WebSocket example finished successfully!');
      
    } catch (error) {
      console.error('WebSocket example failed:', error);
    }
  }
}

/**
 * Usage Examples
 */
export async function runWebSocketExamples() {
  console.log('WebSocket Client Examples');
  console.log('========================');
  
  // Example 1: Basic connection
  const basicExample = new BasicWebSocketExample();
  try {
    await basicExample.connect();
    console.log('Basic connection successful');
    basicExample.disconnect();
  } catch (error) {
    console.error('Basic connection failed:', error);
  }
  
  // Example 2: Complete client
  const completeClient = new CompleteWebSocketClient();
  await completeClient.runCompleteExample();
}

// Export all examples
export {
  BasicWebSocketExample,
  RoomManagementExample,
  MessagingExample,
  HeartbeatExample,
  GameEventsExample,
  CompleteWebSocketClient,
};
