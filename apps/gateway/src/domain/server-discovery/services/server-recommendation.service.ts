import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 核心服务
import { RedisService } from '@common/redis';
import { MICROSERVICE_NAMES } from '@shared/constants';

// 其他服务
import { ServerListService } from './server-list.service';
import { ServerStatusService } from './server-status.service';

// 类型定义
interface RecommendationContext {
  userId: string;
  userLevel?: number;
  preferredGameMode?: string;
  region?: string;
  deviceType?: 'mobile' | 'desktop';
  connectionQuality?: 'high' | 'medium' | 'low';
}

interface RecommendationResult {
  serverId: string;
  score: number;
  reasons: string[];
  category: 'best_match' | 'new_player' | 'returning_player' | 'high_performance';
  confidence: number;
}

interface RecommendationResponse {
  primary: RecommendationResult;
  alternatives: RecommendationResult[];
  explanation: string;
  timestamp: Date;
}

/**
 * 区服推荐服务
 * 基于多维度算法为用户推荐最适合的区服
 * 
 * 推荐算法核心要素：
 * 1. 用户历史行为：游戏时长、角色等级、充值记录等
 * 2. 社交关系：好友分布、公会归属等
 * 3. 区服特征：人数、活跃度、竞争激烈程度等
 * 4. 技术指标：延迟、稳定性、负载等
 * 5. 个人偏好：游戏模式、时间段、设备类型等
 */
@Injectable()
export class ServerRecommendationService {
  private readonly logger = new Logger(ServerRecommendationService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly serverListService: ServerListService,
    private readonly serverStatusService: ServerStatusService,
  ) {}

  /**
   * 为用户推荐区服
   * 主要入口方法，提供完整的推荐结果
   */
  async recommendServersForUser(context: RecommendationContext): Promise<RecommendationResponse> {
    this.logger.log(`为用户推荐区服: ${context.userId}`);

    try {
      // 1. 获取用户完整画像
      const userProfile = await this.getUserProfile(context.userId);
      
      // 2. 获取可用区服列表
      const serverList = await this.serverListService.getServerListForUser(context.userId);
      
      // 3. 计算推荐分数
      const recommendations = await Promise.all(
        serverList.servers.map(server => this.calculateRecommendationScore(server, userProfile, context))
      );

      // 4. 排序并分类
      recommendations.sort((a, b) => b.score - a.score);
      
      // 5. 选择主推荐和备选推荐
      const primary = recommendations[0];
      const alternatives = recommendations.slice(1, 4); // 最多3个备选

      // 6. 生成解释说明
      const explanation = this.generateExplanation(primary, userProfile);

      return {
        primary,
        alternatives,
        explanation,
        timestamp: new Date(),
      };

    } catch (error) {
      this.logger.error(`推荐区服失败: ${context.userId}`, error);
      throw error;
    }
  }

  /**
   * 获取新手推荐区服
   * 专门为新用户设计的推荐算法
   */
  async recommendForNewPlayer(context: RecommendationContext): Promise<RecommendationResult[]> {
    this.logger.log(`为新手推荐区服: ${context.userId}`);

    try {
      const serverList = await this.serverListService.getBaseServerList();
      
      const recommendations = await Promise.all(
        serverList.map(async (server) => {
          let score = 0;
          const reasons: string[] = [];

          // 新服优先 (30分)
          const daysSinceOpen = (Date.now() - server.openTime.getTime()) / (1000 * 60 * 60 * 24);
          if (daysSinceOpen <= 7) {
            score += 30;
            reasons.push('新开区服，与其他玩家同步起步');
          } else if (daysSinceOpen <= 30) {
            score += 20;
            reasons.push('较新区服，仍有追赶机会');
          }

          // 负载适中 (25分)
          const status = await this.serverStatusService.getServerStatus(server.id);
          if (status) {
            const loadRatio = status.currentPlayers / server.maxPlayers;
            if (loadRatio >= 0.2 && loadRatio <= 0.6) {
              score += 25;
              reasons.push('人数适中，既有活跃度又不拥挤');
            } else if (loadRatio < 0.2) {
              score += 15;
              reasons.push('人数较少，更容易获得关注');
            }
          }

          // 新手友好标签 (20分)
          if (server.tags?.includes('新手友好')) {
            score += 20;
            reasons.push('官方认证的新手友好区服');
          }

          // 区域匹配 (15分)
          if (server.region === context.region) {
            score += 15;
            reasons.push('就近区服，网络延迟更低');
          }

          // 稳定性 (10分)
          if (status?.status === 'online') {
            score += 10;
            reasons.push('区服运行稳定');
          }

          return {
            serverId: server.id,
            score,
            reasons,
            category: 'new_player' as const,
            confidence: Math.min(score / 100, 1),
          };
        })
      );

      return recommendations.sort((a, b) => b.score - a.score);

    } catch (error) {
      this.logger.error(`新手推荐失败: ${context.userId}`, error);
      return [];
    }
  }

  /**
   * 获取回流玩家推荐
   * 为长时间未登录的玩家提供推荐
   */
  async recommendForReturningPlayer(context: RecommendationContext): Promise<RecommendationResult[]> {
    this.logger.log(`为回流玩家推荐区服: ${context.userId}`);

    try {
      // 获取用户历史记录
      const userHistory = await this.getUserServerHistory(context.userId);
      const serverList = await this.serverListService.getBaseServerList();

      const recommendations = await Promise.all(
        serverList.map(async (server) => {
          let score = 0;
          const reasons: string[] = [];

          // 历史区服优先 (40分)
          const historyRecord = userHistory.find(h => h.serverId === server.id);
          if (historyRecord) {
            score += 40;
            reasons.push('您曾经游戏过的区服');
            
            // 角色等级加分
            if (historyRecord.maxLevel >= 30) {
              score += 15;
              reasons.push('您在此区服有高等级角色');
            }
          }

          // 活跃度适中 (25分)
          const status = await this.serverStatusService.getServerStatus(server.id);
          if (status) {
            const loadRatio = status.currentPlayers / server.maxPlayers;
            if (loadRatio >= 0.3 && loadRatio <= 0.8) {
              score += 25;
              reasons.push('区服活跃度高，容易找到队友');
            }
          }

          // 回流活动 (20分)
          // 这里可以检查是否有回流玩家专属活动
          if (this.hasReturningPlayerEvents(server.id)) {
            score += 20;
            reasons.push('有回流玩家专属活动');
          }

          // 好友分布 (15分)
          const friendsInServer = await this.getFriendsInServer(context.userId, server.id);
          if (friendsInServer > 0) {
            score += 15;
            reasons.push(`有${friendsInServer}位好友在此区服`);
          }

          return {
            serverId: server.id,
            score,
            reasons,
            category: 'returning_player' as const,
            confidence: Math.min(score / 100, 1),
          };
        })
      );

      return recommendations.sort((a, b) => b.score - a.score);

    } catch (error) {
      this.logger.error(`回流玩家推荐失败: ${context.userId}`, error);
      return [];
    }
  }

  /**
   * 计算单个区服的推荐分数
   */
  private async calculateRecommendationScore(
    server: any,
    userProfile: any,
    context: RecommendationContext
  ): Promise<RecommendationResult> {
    let score = 0;
    const reasons: string[] = [];
    let category: RecommendationResult['category'] = 'best_match';

    try {
      // 1. 历史行为分析 (30分)
      if (server.hasCharacter) {
        score += 30;
        reasons.push('您在此区服有角色');
        
        if (server.lastPlayTime) {
          const daysSinceLastPlay = (Date.now() - new Date(server.lastPlayTime).getTime()) / (1000 * 60 * 60 * 24);
          if (daysSinceLastPlay <= 7) {
            score += 15;
            reasons.push('最近游戏过');
          }
        }
      }

      // 2. 区服负载分析 (20分)
      if (server.loadRatio < 0.3) {
        score += 20;
        reasons.push('人数较少，体验更佳');
      } else if (server.loadRatio < 0.7) {
        score += 15;
        reasons.push('人数适中');
      } else if (server.loadRatio < 0.9) {
        score += 5;
        reasons.push('人数较多');
      }

      // 3. 新服加分 (15分)
      if (server.isNew) {
        score += 15;
        reasons.push('新开区服');
        category = 'new_player';
      }

      // 4. 性能指标 (15分)
      const status = await this.serverStatusService.getServerStatus(server.id);
      if (status) {
        if (status.responseTime < 50) {
          score += 15;
          reasons.push('响应速度快');
          category = 'high_performance';
        } else if (status.responseTime < 100) {
          score += 10;
          reasons.push('响应速度良好');
        }
      }

      // 5. 社交因素 (10分)
      const friendsCount = await this.getFriendsInServer(context.userId, server.id);
      if (friendsCount > 0) {
        score += 10;
        reasons.push(`有${friendsCount}位好友`);
      }

      // 6. 个人偏好匹配 (10分)
      if (this.matchesUserPreferences(server, userProfile, context)) {
        score += 10;
        reasons.push('符合您的游戏偏好');
      }

      return {
        serverId: server.id,
        score,
        reasons,
        category,
        confidence: Math.min(score / 100, 1),
      };

    } catch (error) {
      this.logger.error(`计算推荐分数失败: ${server.id}`, error);
      return {
        serverId: server.id,
        score: 0,
        reasons: ['计算失败'],
        category: 'best_match',
        confidence: 0,
      };
    }
  }

  /**
   * 获取用户画像
   */
  private async getUserProfile(userId: string): Promise<any> {
    try {
      // TODO: 实际实现时需要调用Auth服务
      // const response = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.AUTH_SERVICE,
      //   'user-history.getGlobalStats',
      //   { userId }
      // );

      // 暂时返回模拟数据
      return {
        totalPlayTime: 86400,
        totalCharacters: 2,
        vipLevel: 1,
      };
    } catch (error) {
      this.logger.warn(`获取用户画像失败: ${userId}`, error);
      return {};
    }
  }

  /**
   * 获取用户区服历史
   */
  private async getUserServerHistory(userId: string): Promise<any[]> {
    try {
      // TODO: 实际实现时需要调用Auth服务
      // const response = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.AUTH_SERVICE,
      //   'user-history.getServerHistory',
      //   { userId }
      // );

      // 暂时返回模拟数据
      return [
        {
          serverId: 'server_001',
          maxLevel: 25,
          lastPlayTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
        },
      ];
    } catch (error) {
      this.logger.warn(`获取用户历史失败: ${userId}`, error);
      return [];
    }
  }

  /**
   * 检查是否有回流玩家活动
   */
  private hasReturningPlayerEvents(serverId: string): boolean {
    // 这里应该检查实际的活动数据
    // 暂时返回随机结果
    return Math.random() > 0.7;
  }

  /**
   * 获取好友在指定区服的数量
   */
  private async getFriendsInServer(userId: string, serverId: string): Promise<number> {
    try {
      // 这里应该调用社交服务获取好友分布
      // 暂时返回随机数
      return Math.floor(Math.random() * 5);
    } catch (error) {
      return 0;
    }
  }

  /**
   * 检查是否匹配用户偏好
   */
  private matchesUserPreferences(server: any, userProfile: any, context: RecommendationContext): boolean {
    // 这里可以实现复杂的偏好匹配逻辑
    // 暂时简化处理
    return server.region === context.region;
  }

  /**
   * 生成推荐解释
   */
  private generateExplanation(recommendation: RecommendationResult, userProfile: any): string {
    const reasons = recommendation.reasons.slice(0, 3); // 最多显示3个原因
    
    let explanation = `我们为您推荐此区服，因为：${reasons.join('、')}`;
    
    if (recommendation.confidence > 0.8) {
      explanation += '。这是我们的强烈推荐！';
    } else if (recommendation.confidence > 0.6) {
      explanation += '。这是一个不错的选择。';
    } else {
      explanation += '。您也可以考虑其他备选区服。';
    }

    return explanation;
  }
}
