import {Module} from '@nestjs/common';
import {PassportModule} from '@nestjs/passport';
import {ConfigModule} from '@nestjs/config';

import {UserController} from './user.controller';
import {UserService} from './user.service';
import {JwtStrategy} from './strategies/jwt.strategy';
import {ApiKeyStrategy} from './strategies/api-key.strategy';
import {AuthGuard} from './guards/auth.guard';
import {RolesGuard} from './guards/roles.guard';
import {PermissionsGuard} from './guards/permissions.guard';

// 使用共享模块
import {JwtSharedModule} from '../../infrastructure/jwt/jwt-shared.module';

/**
 * 用户模块
 *
 * 提供完整的用户管理和认证功能，包括：
 * - 用户认证（JWT、API Key）
 * - 用户信息管理
 * - 角色权限控制
 * - 会话管理
 * - 密码管理
 * - 安全审计
 */
@Module({
  imports: [
    ConfigModule,

    // Passport 模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // 使用共享模块，避免重复配置
    JwtSharedModule,
  ],
  controllers: [UserController],
  providers: [
    UserService,
    JwtStrategy,
    ApiKeyStrategy,
    AuthGuard,
    RolesGuard,
    PermissionsGuard,
  ],
  exports: [
    UserService,
    AuthGuard,
    RolesGuard,
    PermissionsGuard,
    JwtSharedModule,  // 导出共享的 JWT 模块
    PassportModule,
  ],
})
export class UserModule {}
