import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';
import { MicroserviceClientService } from '@common/microservice-kit';

import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import {MICROSERVICE_NAMES} from "@libs/shared";

/**
 * 认证服务接口
 */
export interface JwtPayload {
  sub: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  iat?: number;
  exp?: number;
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  user: {
    id: string;
    username: string;
    email: string;
    roles: string[];
  };
}

/**
 * 用户服务
 *
 * 处理用户相关的业务逻辑，包括：
 * - 用户登录验证
 * - 用户注册流程
 * - 密码管理（修改、重置）
 * - 令牌刷新和会话管理
 * - 用户信息管理
 * - 用户状态管理
 *
 * 注意：此服务专注于用户业务流程，实际的令牌验证由核心层的 AuthService 处理
 */
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);
  private readonly TOKEN_BLACKLIST_PREFIX = 'auth:blacklist:';
  private readonly REFRESH_TOKEN_PREFIX = 'auth:refresh:';

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    // 使用统一的微服务客户端
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 用户登录 - 完全通过Auth服务处理
   */
  async login(loginDto: LoginDto): Promise<LoginResponse> {
    try {
      // 调用Auth服务进行完整的登录处理（包括Token生成和会话管理）
      const authResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'auth.login',
        {
          ...loginDto,
          // 传递客户端信息给Auth服务进行会话管理
          deviceInfo: {
            userAgent: loginDto.userAgent,
            ipAddress: loginDto.ipAddress,
            fingerprint: loginDto.deviceFingerprint,
          }
        }
      );

      if (!authResult.success) {
        throw new UnauthorizedException(authResult.message || 'Invalid credentials');
      }

      // Gateway只做数据转换和缓存，不生成Token
      const response = {
        accessToken: authResult.accessToken,
        refreshToken: authResult.refreshToken,
        expiresIn: authResult.expiresIn,
        expiresAt: authResult.expiresAt,
        tokenType: authResult.tokenType || 'Bearer',
        user: {
          id: authResult.user.id,
          username: authResult.user.username,
          email: authResult.user.email,
          roles: authResult.user.roles || [],
        },
      };

      // 缓存用户信息以提高性能（不缓存Token）
      await this.cacheUserInfo(authResult.user.id, authResult.user);

      this.logger.log(`用户登录成功: ${authResult.user.username}`);
      return response;

    } catch (error) {
      this.logger.error(`登录失败: ${error.message}`);
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('认证失败');
    }
  }

  /**
   * 网关层独立的Token生成逻辑
   */
  private async generateGatewayTokens(user: any): Promise<LoginResponse> {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles || [],
      permissions: user.permissions || [],
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = await this.generateRefreshToken(user.id);

    // 存储到网关层的Redis缓存
    await this.storeRefreshToken(user.id, refreshToken);

    this.logger.log(`User ${user.username} logged in successfully`);

    return {
      accessToken,
      refreshToken,
      expiresIn: this.getTokenExpirationTime(),
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        roles: user.roles || [],
      },
    };
  }

  /**
   * 刷新访问令牌 - 使用RPC调用获取用户信息
   */
  async refreshToken(refreshToken: string): Promise<Omit<LoginResponse, 'user'>> {
    try {
      // 验证刷新令牌
      const userId = await this.validateRefreshToken(refreshToken);
      if (!userId) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // 使用统一的微服务客户端获取用户信息
      const userResult = await this.microserviceClient.call(
        'auth',
        'user.findById',
        { id: userId }
      );

      if (!userResult.success) {
        throw new UnauthorizedException('User not found');
      }

      // 生成新的Token对
      return this.generateNewTokens(userResult.user, refreshToken);
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`);
      throw new UnauthorizedException('Token refresh failed');
    }
  }

  /**
   * 生成新的Token对
   */
  private async generateNewTokens(user: any, oldRefreshToken: string): Promise<Omit<LoginResponse, 'user'>> {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles || [],
      permissions: user.permissions || [],
    };

    const accessToken = this.jwtService.sign(payload);
    const newRefreshToken = await this.generateRefreshToken(user.id);

    // 原子性更新：删除旧Token，存储新Token
    await Promise.all([
      this.removeRefreshToken(user.id, oldRefreshToken),
      this.storeRefreshToken(user.id, newRefreshToken),
    ]);

    this.logger.log(`Token refreshed for user: ${user.username}`);

    return {
      accessToken,
      refreshToken: newRefreshToken,
      expiresIn: this.getTokenExpirationTime(),
    };
  }

  /**
   * 用户登出
   */
  async logout(userId: string, token: string): Promise<void> {
    try {
      // 将令牌加入黑名单
      await this.addTokenToBlacklist(token);

      // 删除用户的所有刷新令牌
      await this.removeAllRefreshTokens(userId);

      this.logger.log(`User ${userId} logged out successfully`);
    } catch (error) {
      this.logger.error(`Logout failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证 JWT 令牌
   */
  async verifyToken(token: string): Promise<JwtPayload> {
    try {
      // 检查令牌是否在黑名单中
      const isBlacklisted = await this.isTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new UnauthorizedException('Token is blacklisted');
      }

      // 验证令牌
      const payload = this.jwtService.verify(token) as JwtPayload;
      return payload;
    } catch (error) {
      this.logger.error(`Token verification failed: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * 获取当前用户信息 - 使用RPC调用
   */
  async getCurrentUser(userId: string): Promise<any> {
    try {
      // 使用统一的微服务客户端
      const userResult = await this.microserviceClient.call(
        'auth',
        'user.findById',
        { id: userId }
      );

      if (!userResult.success) {
        throw new UnauthorizedException('User not found');
      }

      return userResult.user;
    } catch (error) {
      this.logger.error(`Get current user failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 修改密码 - 使用RPC调用
   */
  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    try {
      // 使用统一的微服务客户端
      const result = await this.microserviceClient.call(
        'auth',
        'auth.changePassword',
        {
          userId,
          ...changePasswordDto,
        }
      );

      if (!result.success) {
        throw new UnauthorizedException('Password change failed');
      }

      this.logger.log(`Password changed for user: ${userId}`);
    } catch (error) {
      this.logger.error(`Change password failed: ${error.message}`);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 生成刷新令牌
   */
  private async generateRefreshToken(userId: string): Promise<string> {
    const token = `refresh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return token;
  }

  /**
   * 存储刷新令牌
   */
  private async storeRefreshToken(userId: string, refreshToken: string): Promise<void> {
    const key = `${this.REFRESH_TOKEN_PREFIX}${userId}:${refreshToken}`;
    const ttl = 30 * 24 * 60 * 60; // 30天
    await this.redisService.set(key, userId, ttl);
  }

  /**
   * 验证刷新令牌 - 使用RedisService接口
   */
  private async validateRefreshToken(refreshToken: string): Promise<string | null> {
    try {
      // ✅ 使用RedisService的keys方法而不是直接操作ioredis
      const pattern = `${this.REFRESH_TOKEN_PREFIX}*:${refreshToken}`;
      const keys = await this.redisService.keys(pattern);
      if (keys.length === 0) {
        return null;
      }

      const userId = await this.redisService.get<string>(keys[0]);
      return userId;
    } catch (error) {
      this.logger.error(`Failed to validate refresh token: ${error.message}`);
      return null;
    }
  }

  /**
   * 删除刷新令牌
   */
  private async removeRefreshToken(userId: string, refreshToken: string): Promise<void> {
    const key = `${this.REFRESH_TOKEN_PREFIX}${userId}:${refreshToken}`;
    await this.redisService.del(key);
  }

  /**
   * 删除用户的所有刷新令牌 - 使用RedisService接口
   */
  private async removeAllRefreshTokens(userId: string): Promise<void> {
    try {
      // 使用RedisService的deletePattern方法批量删除
      const pattern = `${this.REFRESH_TOKEN_PREFIX}${userId}:*`;
      const deletedCount = await this.redisService.deletePattern(pattern);
      this.logger.log(`Removed ${deletedCount} refresh tokens for user: ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to remove all refresh tokens: ${error.message}`);
    }
  }

  /**
   * 将令牌加入黑名单
   */
  private async addTokenToBlacklist(token: string): Promise<void> {
    const key = `${this.TOKEN_BLACKLIST_PREFIX}${token}`;
    const ttl = this.getTokenExpirationTime();
    await this.redisService.set(key, '1', ttl);
  }

  /**
   * 检查令牌是否在黑名单中
   */
  private async isTokenBlacklisted(token: string): Promise<boolean> {
    const key = `${this.TOKEN_BLACKLIST_PREFIX}${token}`;
    const result = await this.redisService.get(key);
    return result !== null;
  }

  /**
   * 获取令牌过期时间（秒）
   */
  private getTokenExpirationTime(): number {
    const expiresIn = this.configService.get<string>('gateway.security.jwtExpiresIn', '24h');
    
    // 简单解析时间字符串
    if (expiresIn.endsWith('h')) {
      return parseInt(expiresIn) * 60 * 60;
    } else if (expiresIn.endsWith('m')) {
      return parseInt(expiresIn) * 60;
    } else if (expiresIn.endsWith('s')) {
      return parseInt(expiresIn);
    } else {
      return 24 * 60 * 60; // 默认24小时
    }
  }
}
