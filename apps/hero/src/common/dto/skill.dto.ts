/**
 * 技能相关的数据传输对象
 */

import { IsString, IsNumber, IsOptional, IsBoolean, IsArray, IsEnum, Min, Max, Length, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { SkillType, SkillPosition } from '../types';
import { SkillActiveStatus } from '../schemas/skill.schema';

// 学习技能DTO
export class LearnSkillDto {
  @ApiProperty({ description: '球员ID' })
  @IsString()
  heroId: string;

  @ApiProperty({ description: '技能配置ID' })
  @IsNumber()
  configId: number;

  @ApiPropertyOptional({ description: '获得来源' })
  @IsOptional()
  @IsString()
  obtainSource?: string;

  @ApiPropertyOptional({ description: '获得费用' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  obtainCost?: number;
}

// 升级技能DTO
export class UpgradeSkillDto {
  @ApiProperty({ description: '球员技能实例ID' })
  @IsString()
  skillId: string;

  @ApiProperty({ description: '目标等级' })
  @IsNumber()
  @Min(1)
  targetLevel: number;

  @ApiPropertyOptional({ description: '是否使用道具' })
  @IsOptional()
  @IsBoolean()
  useItems?: boolean;

  @ApiPropertyOptional({ description: '使用的道具列表' })
  @IsOptional()
  @IsArray()
  items?: Array<{
    itemId: number;
    quantity: number;
  }>;
}

// 激活技能DTO
export class ActivateSkillDto {
  @ApiProperty({ description: '球员技能实例ID' })
  @IsString()
  skillId: string;

  @ApiProperty({ description: '技能槽位置' })
  @IsNumber()
  @Min(1)
  @Max(3)
  slotPosition: number;
}

// 球员技能信息响应DTO
export class SkillInfoDto {
  @ApiProperty({ description: '球员技能实例ID' })
  skillId: string;

  @ApiProperty({ description: '球员ID' })
  heroId: string;

  @ApiProperty({ description: '技能配置ID' })
  configId: number;

  @ApiProperty({ description: '当前等级' })
  level: number;

  @ApiProperty({ description: '当前经验值' })
  experience: number;

  @ApiProperty({ description: '激活状态' })
  activeStatus: SkillActiveStatus;

  @ApiProperty({ description: '技能槽位置' })
  slotPosition: number;

  @ApiProperty({ description: '使用次数' })
  usageCount: number;

  @ApiProperty({ description: '最后使用时间' })
  lastUsedTime: number;

  @ApiProperty({ description: '升级次数' })
  upgradeCount: number;

  @ApiProperty({ description: '总升级费用' })
  totalUpgradeCost: number;

  @ApiProperty({ description: '获得时间' })
  obtainTime: number;

  @ApiProperty({ description: '获得来源' })
  obtainSource: string;

  @ApiProperty({ description: '是否锁定' })
  isLocked: boolean;

  @ApiProperty({ description: '是否已激活' })
  isActive: boolean;

  @ApiProperty({ description: '是否已装备' })
  isEquipped: boolean;

  @ApiProperty({ description: '是否可升级' })
  canUpgrade: boolean;

  @ApiProperty({ description: '下一级所需经验' })
  nextLevelExp: number;

  @ApiProperty({ description: '升级进度百分比' })
  upgradeProgress: number;
}

// 球员技能列表查询DTO
export class GetSkillListDto {
  @ApiProperty({ description: '球员ID' })
  @IsString()
  heroId: string;

  @ApiPropertyOptional({ description: '激活状态过滤' })
  @IsOptional()
  @IsEnum(SkillActiveStatus)
  activeStatus?: SkillActiveStatus;

  @ApiPropertyOptional({ description: '是否只显示已装备技能' })
  @IsOptional()
  @IsBoolean()
  equippedOnly?: boolean;

  @ApiPropertyOptional({ description: '是否包含技能配置信息' })
  @IsOptional()
  @IsBoolean()
  includeConfig?: boolean;
}

// 技能配置查询DTO
export class GetSkillConfigListDto {
  @ApiPropertyOptional({ description: '技能类型过滤' })
  @IsOptional()
  @IsEnum(SkillType)
  type?: SkillType;

  @ApiPropertyOptional({ description: '适用位置过滤' })
  @IsOptional()
  @IsEnum(SkillPosition)
  position?: SkillPosition;

  @ApiPropertyOptional({ description: '技能等级过滤 (S, A, B, C)' })
  @IsOptional()
  @IsString()
  skillRank?: string;

  @ApiPropertyOptional({ description: '技能类型过滤 (typeA字段)' })
  @IsOptional()
  @IsNumber()
  skillType?: number;

  @ApiPropertyOptional({ description: '最小星级值' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  minStarValue?: number;

  @ApiPropertyOptional({ description: '最大星级值' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  maxStarValue?: number;

  @ApiPropertyOptional({ description: '稀有度过滤' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(5)
  rarity?: number;

  @ApiPropertyOptional({ description: '名称搜索' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '是否只显示可用技能' })
  @IsOptional()
  @IsBoolean()
  availableOnly?: boolean;

  @ApiPropertyOptional({ description: '排序字段' })
  @IsOptional()
  @IsString()
  sortBy?: 'skillId' | 'name' | 'type' | 'rarity' | 'unlockLevel';

  @ApiPropertyOptional({ description: '排序方向' })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @ApiPropertyOptional({ description: '页码', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}
