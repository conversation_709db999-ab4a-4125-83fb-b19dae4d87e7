/**
 * 球员技能实例Schema
 * 管理球员拥有的技能实例，包括等级、激活状态等
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 技能激活状态枚举
export enum SkillActiveStatus {
  INACTIVE = 0,   // 未激活
  ACTIVE = 1,     // 已激活
  LOCKED = 2,     // 已锁定
}

// 主球员技能实例Schema
@Schema({ 
  collection: 'skills', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Skill {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  skillId: string;      // 球员技能实例ID

  @Prop({ required: true, index: true })
  heroId: string;       // 球员ID

  @Prop({ required: true, index: true })
  characterId: string;  // 角色ID

  @Prop({ required: true, index: true })
  serverId: string;     // 区服ID

  @Prop({ required: true, index: true })
  configId: number;     // 技能配置ID

  // 技能状态
  @Prop({ default: 1, min: 1 })
  level: number;        // 当前等级

  @Prop({ default: 0 })
  experience: number;   // 当前经验值

  @Prop({ default: SkillActiveStatus.INACTIVE, enum: SkillActiveStatus })
  activeStatus: SkillActiveStatus; // 激活状态

  @Prop({ default: 0 })
  slotPosition: number; // 技能槽位置（0表示未装备）

  // 使用统计
  @Prop({ default: 0 })
  usageCount: number;   // 使用次数

  @Prop({ default: 0 })
  lastUsedTime: number; // 最后使用时间

  @Prop({ default: 0 })
  totalDamage: number;  // 总伤害

  @Prop({ default: 0 })
  totalHealing: number; // 总治疗

  // 升级历史
  @Prop({ default: 0 })
  upgradeCount: number; // 升级次数

  @Prop({ default: 0 })
  totalUpgradeCost: number; // 总升级费用

  @Prop({ default: 0 })
  lastUpgradeTime: number; // 最后升级时间

  // 获得信息
  @Prop({ default: 0 })
  obtainTime: number;   // 获得时间

  @Prop({ default: '' })
  obtainSource: string; // 获得来源（抽卡、任务、商店等）

  @Prop({ default: 0 })
  obtainCost: number;   // 获得费用

  // 锁定信息
  @Prop({ default: false })
  isLocked: boolean;    // 是否锁定

  @Prop({ default: 0 })
  lockTime: number;     // 锁定时间

  @Prop({ default: '' })
  lockReason: string;   // 锁定原因

  // 虚拟字段：是否已激活
  get isActive(): boolean {
    return this.activeStatus === SkillActiveStatus.ACTIVE;
  }

  // 虚拟字段：是否已装备
  get isEquipped(): boolean {
    return this.slotPosition > 0;
  }

  // 虚拟字段：是否可升级
  get canUpgrade(): boolean {
    return !this.isLocked && this.activeStatus !== SkillActiveStatus.LOCKED;
  }

  // 虚拟字段：下一级所需经验
  get nextLevelExp(): number {
    return Math.pow(this.level, 2) * 100;
  }

  // 虚拟字段：升级进度百分比
  get upgradeProgress(): number {
    const nextExp = this.nextLevelExp;
    return nextExp > 0 ? Math.min((this.experience / nextExp) * 100, 100) : 100;
  }
}

export const SkillSchema = SchemaFactory.createForClass(Skill);

// 定义Document类型
export type SkillDocument = Skill & Document;

// 创建索引
SkillSchema.index({ skillId: 1 }, { unique: true });
SkillSchema.index({ heroId: 1 });
SkillSchema.index({ characterId: 1 });
SkillSchema.index({ serverId: 1 });
SkillSchema.index({ configId: 1 });
SkillSchema.index({ heroId: 1, configId: 1 }, { unique: true }); // 球员不能拥有重复技能
SkillSchema.index({ activeStatus: 1 });
SkillSchema.index({ slotPosition: 1 });
SkillSchema.index({ level: 1 });
SkillSchema.index({ obtainTime: 1 });

// 添加虚拟字段
SkillSchema.virtual('isActive').get(function() {
  return this.activeStatus === SkillActiveStatus.ACTIVE;
});

SkillSchema.virtual('isEquipped').get(function() {
  return this.slotPosition > 0;
});

SkillSchema.virtual('canUpgrade').get(function() {
  return !this.isLocked && this.activeStatus !== SkillActiveStatus.LOCKED;
});

SkillSchema.virtual('nextLevelExp').get(function() {
  return Math.pow(this.level, 2) * 100;
});

SkillSchema.virtual('upgradeProgress').get(function() {
  const nextExp = this.nextLevelExp;
  return nextExp > 0 ? Math.min((this.experience / nextExp) * 100, 100) : 100;
});

// 添加实例方法
SkillSchema.methods.addExperience = function(exp: number) {
  this.experience += exp;
  
  // 检查是否可以升级
  while (this.experience >= this.nextLevelExp && this.level < 10) { // 假设最大等级为10
    this.experience -= this.nextLevelExp;
    this.level += 1;
    this.upgradeCount += 1;
    this.lastUpgradeTime = Date.now();
  }
  
  return this.level;
};

SkillSchema.methods.activate = function(slotPosition?: number) {
  this.activeStatus = SkillActiveStatus.ACTIVE;
  if (slotPosition) {
    this.slotPosition = slotPosition;
  }
  return this;
};

SkillSchema.methods.deactivate = function() {
  this.activeStatus = SkillActiveStatus.INACTIVE;
  this.slotPosition = 0;
  return this;
};

SkillSchema.methods.lock = function(reason: string = '') {
  this.isLocked = true;
  this.lockTime = Date.now();
  this.lockReason = reason;
  this.activeStatus = SkillActiveStatus.LOCKED;
  return this;
};

SkillSchema.methods.unlock = function() {
  this.isLocked = false;
  this.lockTime = 0;
  this.lockReason = '';
  if (this.activeStatus === SkillActiveStatus.LOCKED) {
    this.activeStatus = SkillActiveStatus.INACTIVE;
  }
  return this;
};

SkillSchema.methods.use = function(damage: number = 0, healing: number = 0) {
  this.usageCount += 1;
  this.lastUsedTime = Date.now();
  this.totalDamage += damage;
  this.totalHealing += healing;
  return this;
};

SkillSchema.methods.getEffectiveLevel = function() {
  // 考虑装备、buff等因素的有效等级
  return this.level;
};

SkillSchema.methods.calculateUpgradeCost = function(targetLevel: number) {
  let totalCost = 0;
  for (let level = this.level + 1; level <= targetLevel; level++) {
    // 基础升级费用公式
    totalCost += level * 1000;
  }
  return totalCost;
};
