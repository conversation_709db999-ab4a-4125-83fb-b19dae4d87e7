import { Injectable, Logger } from '@nestjs/common';
import { GameConfigFacade } from '@app/game-config';
import { HeroRepository } from '@hero/common/repositories/hero.repository';
import { ErrorCode, ErrorMessages } from '@app/game-constants';
import { MicroserviceClientService } from '@common/microservice-kit';
import {MICROSERVICE_NAMES} from "@libs/shared";

/**
 * 场地训练服务
 * 严格基于old项目的footballGround实体业务逻辑
 * 
 * old项目核心功能：
 * - getHeroTrainInfo: 获取场地训练信息（trainPos数组）
 * - heroTrainInGround: 场地训练（6个位置，6种类型）
 * - getHeroTrainReward: 获取场地训练奖励（属性提升）
 * - inputNotablePos: 名人堂入驻（退役球员）
 * - getNotablePosInfo: 获取名人堂列表（20个位置）
 * 
 * old项目数据结构：
 * - TrainPos: 训练位置数组（6个位置）
 * - NotablePos: 名人堂位置数组（20个位置）
 * - HospitalPos: 医疗位置数组（2个位置）
 */
@Injectable()
export class GroundService {
  private readonly logger = new Logger(GroundService.name);

  constructor(
    private readonly heroRepository: HeroRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 获取场地训练信息
   * 严格基于old项目: footballGround.getHeroTrainInfo(uid)
   */
  async getHeroTrainInfo(characterId: string) {
    this.logger.log(`获取场地训练信息: ${characterId}`);
    
    // 从角色数据中获取footballGround实体（基于old项目逻辑）
    const groundData = await this.getCharacterGroundData(characterId);
    
    if (!groundData) {
      return { code: -1, trainPos: [], uid: characterId };
    }

    // 获取场地训练配置
    const feildTrainningConfigs = await this.gameConfig.feildTrainning.getAll();
    if (!feildTrainningConfigs || feildTrainningConfigs.length === 0) {
      return { code: -2, trainPos: [], uid: characterId }; // CONFIG_FAIL
    }

    // 基于old项目的trainPos结构返回数据
    const trainPos = groundData.trainPos || this.initTrainPos();
    
    // 更新训练位置的详细信息
    for (let i = 0; i < trainPos.length; i++) {
      const pos = trainPos[i];
      if (pos.pos && pos.pos !== '') {
        // 获取训练中的球员信息
        const hero = await this.heroRepository.findById(pos.pos);
        if (hero) {
          pos.heroInfo = {
            uid: hero._id.toString(),
            name: hero.name,
            level: hero.level,
            position: hero.position,
          };
        }
      }
    }

    return { 
      code: 0, 
      trainPos, 
      uid: characterId 
    };
  }

  /**
   * 场地训练
   * 严格基于old项目: footballGround.heroTrainInGround(characterId, heroId, index, type, isLock)
   */
  async heroTrainInGround(characterId: string, heroId: string, index: number, type: number, isLock?: boolean) {
    this.logger.log(`场地训练: 玩家${characterId}, 球员${heroId}, 位置${index}, 类型${type}`);
    
    // 参数验证
    if (index < 0 || index > 5) {
      return { code: -1, beginTime: 0, state: 0, isLockTrain: 0 }; // RANGE_FAIL
    }

    if (type < 1 || type > 6) {
      return { code: -1, beginTime: 0, state: 0, isLockTrain: 0 }; // RANGE_FAIL
    }

    const hero = await this.heroRepository.findById(heroId);
    if (!hero) {
      return { code: -1, beginTime: 0, state: 0, isLockTrain: 0 }; // HERO_NOT_FOUND
    }

    // 获取训练场地数据
    const groundData = await this.getCharacterGroundData(characterId);
    if (!groundData) {
      return { code: -1, beginTime: 0, state: 0, isLockTrain: 0 }; // GROUND_NOT_FOUND
    }

    const trainGround = groundData.trainGround || this.initTrainGround();

    // 检查训练次数限制
    if ((hero.TrainCount || 0) >= trainGround.Level) {
      return { code: -3, beginTime: 0, state: 0, isLockTrain: 0 }; // LEVEL_FAIL
    }

    // 检查球员是否已在训练中
    if (hero.isTrain) {
      return { code: -4, beginTime: 0, state: 0, isLockTrain: 0 }; // IN_TRAIN
    }

    // 检查位置是否解锁
    if (trainGround.TrainPos[index].state === 1) {
      return { code: -5, beginTime: 0, state: 0, isLockTrain: 0 }; // UNLOCK_FAIL
    }

    // 检查位置是否已被占用
    if (trainGround.TrainPos[index].state === 3) {
      return { code: -6, beginTime: 0, state: 0, isLockTrain: 0 }; // POS_OCCUPIED
    }

    // 开始训练
    const beginTime = Date.now();
    
    // 更新训练位置
    trainGround.TrainPos[index].pos = heroId;
    trainGround.TrainPos[index].state = 3; // 训练中
    trainGround.TrainPos[index].type = type;
    trainGround.TrainPos[index].beginTime = beginTime;

    // 更新球员状态
    await this.heroRepository.updateById(heroId, {
      isTrain: true,
      isLockTrain: isLock || false,
    });

    // 保存训练场地数据
    await this.updateCharacterGroundData(characterId, groundData);

    // 触发任务系统（基于old项目TARGET_TYPE.THIRTY_FOUR）
    await this.triggerTrainingTask(characterId, type);

    return {
      code: 0,
      beginTime,
      state: 3,
      isLockTrain: isLock ? 1 : 0,
    };
  }

  /**
   * 获取场地训练奖励
   * 严格基于old项目: footballGround.getHeroTrainReward(characterId, index)
   */
  async getHeroTrainReward(characterId: string, index: number) {
    this.logger.log(`获取场地训练奖励: ${characterId}, 位置${index}`);
    
    // 参数验证
    if (index < 0 || index > 5) {
      return { code: -1, rand: 0, type: 0, hero: {}, index }; // RANGE_FAIL
    }

    const groundData = await this.getCharacterGroundData(characterId);
    if (!groundData) {
      return { code: -1, rand: 0, type: 0, hero: {}, index }; // GROUND_NOT_FOUND
    }

    const trainGround = groundData.trainGround || this.initTrainGround();

    // 检查位置是否有球员
    if (!trainGround.TrainPos[index].pos || trainGround.TrainPos[index].pos === '') {
      return { code: -1, rand: 0, type: 0, hero: {}, index }; // NO_HERO
    }

    const heroId = trainGround.TrainPos[index].pos;
    const hero = await this.heroRepository.findById(heroId);
    if (!hero) {
      return { code: -1, rand: 0, type: 0, hero: {}, index }; // HERO_NOT_FOUND
    }

    // 获取场地训练配置
    const feildTrainningConfigs = await this.gameConfig.feildTrainning.getAll();
    if (!feildTrainningConfigs || feildTrainningConfigs.length === 0) {
      return { code: -2, rand: 0, type: 0, hero: {}, index }; // CONFIG_FAIL
    }

    // 计算训练配置
    const heroTrainCount = (hero.TrainCount || 0) + 1;
    const config = this.getTrainConfigByLevel(feildTrainningConfigs, heroTrainCount);
    if (!config) {
      return { code: -2, rand: 0, type: 0, hero: {}, index }; // CONFIG_FAIL
    }

    // 检查训练时间是否足够
    const trainTime = config.Time || 1; // 小时
    const timeReduce = config.TimeReduce || 0; // 时间减少百分比
    const secTime = trainTime * 60 * 60; // 转换为秒
    const actualTime = Math.floor(secTime - (secTime * timeReduce / 10000));
    const elapsedTime = Math.floor((Date.now() - trainGround.TrainPos[index].beginTime) / 1000);
    
    if (elapsedTime < actualTime) {
      return { code: -7, rand: 0, type: 0, hero: {}, index }; // TIME_FAIL
    }

    // 计算属性提升
    const type = trainGround.TrainPos[index].type;
    const rand = await this.calcHeroProperty(type, hero, config.AttributeMax, config.AttributeMin);

    // 更新球员属性和状态
    await this.heroRepository.updateById(heroId, {
      isTrain: false,
      isLockTrain: false,
      TrainCount: heroTrainCount,
    });

    // 重新计算球员实力值
    await this.reCalcAttrRevision(heroId);

    // 清空训练位置
    trainGround.TrainPos[index].pos = '';
    trainGround.TrainPos[index].state = 2; // 可添加
    trainGround.TrainPos[index].type = 0;
    trainGround.TrainPos[index].beginTime = 0;

    // 保存训练场地数据
    await this.updateCharacterGroundData(characterId, groundData);

    // 获取更新后的球员信息
    const updatedHero = await this.heroRepository.findById(heroId);

    return {
      code: 0,
      rand,
      type,
      hero: this.makeClientHero(updatedHero),
      index,
    };
  }

  /**
   * 名人堂入驻
   * 严格基于old项目: footballGround.inputNotablePos(uid)
   */
  async inputNotablePos(characterId: string, heroId: string) {
    this.logger.log(`名人堂入驻: ${characterId}, 球员${heroId}`);

    const hero = await this.heroRepository.findById(heroId);
    if (!hero) {
      return { code: -1, notablePrestige: 0, notablePos: [], retirementList: [] };
    }

    const groundData = await this.getCharacterGroundData(characterId);
    if (!groundData) {
      return { code: -1, notablePrestige: 0, notablePos: [], retirementList: [] };
    }

    const notableGround = groundData.notableGround || this.initNotableGround();

    // 检查是否在退役列表中
    const retirementIndex = this.checkRetirementListIsHaveHero(groundData, heroId);
    if (retirementIndex === -1) {
      return { code: -1, notablePrestige: 0, notablePos: [], retirementList: [] };
    }

    // 获取球员配置
    const heroConfig = await this.gameConfig.hero.get(hero.resId);
    if (!heroConfig) {
      return { code: -2, notablePrestige: 0, notablePos: [], retirementList: [] }; // CONFIG_FAIL
    }

    // 检查是否首次进入名人堂
    const resId = hero.resId;
    const firstJoinHofList = groundData.firstJoinHofList || [];
    let addPrestige = 0;

    if (!firstJoinHofList.includes(resId)) {
      // 首次进入，增加声望
      addPrestige = heroConfig.prestige || 0;
      firstJoinHofList.push(resId);
    }

    // 找到空闲位置
    const emptyIndex = this.findEmptyNotablePos(notableGround.notablePos);
    if (emptyIndex === -1) {
      return { code: -3, notablePrestige: 0, notablePos: [], retirementList: [] }; // NO_EMPTY_POS
    }

    // 入驻名人堂
    notableGround.notablePos[emptyIndex] = {
      resId: hero.resId,
      uid: heroId,
      state: 3, // 已有人
    };

    // 更新声望
    groundData.notablePrestige = (groundData.notablePrestige || 0) + addPrestige;

    // 从退役列表中移除
    groundData.retirementList.splice(retirementIndex, 1);

    // 保存数据
    await this.updateCharacterGroundData(characterId, groundData);

    return {
      code: 0,
      notablePrestige: groundData.notablePrestige,
      notablePos: this.getHofList(notableGround.notablePos),
      retirementList: this.getRetirementList(groundData.retirementList),
    };
  }

  /**
   * 获取名人堂信息
   * 严格基于old项目: footballGround.getNotablePosInfo()
   */
  async getNotablePosInfo(characterId: string) {
    this.logger.log(`获取名人堂信息: ${characterId}`);

    const groundData = await this.getCharacterGroundData(characterId);
    if (!groundData) {
      return { notablePos: [], notablePrestige: 0, retirementList: [] };
    }

    const notableGround = groundData.notableGround || this.initNotableGround();

    // 检查并解锁位置
    this.unlockNotablePos(notableGround, notableGround.Level || 1);

    return {
      notablePos: this.getHofList(notableGround.notablePos),
      notablePrestige: groundData.notablePrestige || 0,
      retirementList: this.getRetirementList(groundData.retirementList || []),
    };
  }

  /**
   * 获取医疗中心信息
   * 基于old项目: footballGround.getHospitalPosInfo()
   */
  async getHospitalPosInfo(characterId: string) {
    this.logger.log(`获取医疗中心信息: ${characterId}`);

    const groundData = await this.getCharacterGroundData(characterId);
    if (!groundData) {
      return { hospitalPos: [] };
    }

    const hospitalGround = groundData.hospitalGround || this.initHospitalGround();

    return {
      hospitalPos: hospitalGround.HospitalPos || [],
    };
  }

  /**
   * 医疗中心治疗
   * 基于old项目: footballGround.inputHospitalPos(uid, index)
   */
  async inputHospitalPos(characterId: string, heroId: string, index: number) {
    this.logger.log(`医疗中心治疗: ${characterId}, 球员${heroId}, 位置${index}`);

    if (index < 0 || index > 1) {
      return { code: -1 }; // RANGE_FAIL
    }

    const hero = await this.heroRepository.findById(heroId);
    if (!hero) {
      return { code: -1 }; // HERO_NOT_FOUND
    }

    const groundData = await this.getCharacterGroundData(characterId);
    if (!groundData) {
      return { code: -1 }; // GROUND_NOT_FOUND
    }

    const hospitalGround = groundData.hospitalGround || this.initHospitalGround();

    // 检查位置是否解锁
    if (hospitalGround.HospitalPos[index].state === 1) {
      return { code: -5 }; // UNLOCK_FAIL
    }

    // 检查球员是否已在治疗中
    if (hero.isTreat) {
      return { code: -4 }; // IN_TREAT
    }

    // 开始治疗
    const beginTime = Date.now();

    hospitalGround.HospitalPos[index].pos = heroId;
    hospitalGround.HospitalPos[index].state = 3; // 治疗中
    hospitalGround.HospitalPos[index].beginTime = beginTime;

    // 更新球员状态
    await this.heroRepository.updateById(heroId, {
      isTreat: true,
    });

    // 保存数据
    await this.updateCharacterGroundData(characterId, groundData);

    return { code: 0 };
  }

  /**
   * 获取医疗奖励
   * 基于old项目: footballGround.getHospitalPosHero(index)
   */
  async getHospitalPosHero(characterId: string, index: number) {
    this.logger.log(`获取医疗奖励: ${characterId}, 位置${index}`);

    if (index < 0 || index > 1) {
      return { code: -1 }; // RANGE_FAIL
    }

    const groundData = await this.getCharacterGroundData(characterId);
    if (!groundData) {
      return { code: -1 }; // GROUND_NOT_FOUND
    }

    const hospitalGround = groundData.hospitalGround || this.initHospitalGround();

    // 检查位置是否有球员
    if (!hospitalGround.HospitalPos[index].pos || hospitalGround.HospitalPos[index].pos === '') {
      return { code: -1 }; // NO_HERO
    }

    const heroId = hospitalGround.HospitalPos[index].pos;
    const hero = await this.heroRepository.findById(heroId);
    if (!hero) {
      return { code: -1 }; // HERO_NOT_FOUND
    }

    // 检查治疗时间是否足够（基于old项目getHospitalPosHero的时间检查逻辑）
    const nowTime = Date.now();
    const beginTime = hospitalGround.HospitalPos[index].beginTime;
    const endTime = hospitalGround.HospitalPos[index].endTime * 1000; // 转换为毫秒

    if (nowTime - beginTime < endTime) {
      const remainingTime = endTime - (nowTime - beginTime);
      return {
        code: -7, // TIME_FAIL（基于old项目Code.TIME_FAIL）
        remainingTime: Math.ceil(remainingTime / 1000) // 返回剩余秒数
      };
    }

    // 恢复球员健康状态
    await this.heroRepository.updateById(heroId, {
      isTreat: false,
      Health: 1, // 恢复健康
    });

    // 清空医疗位置
    hospitalGround.HospitalPos[index].pos = '';
    hospitalGround.HospitalPos[index].state = 2; // 可添加
    hospitalGround.HospitalPos[index].beginTime = 0;

    // 保存数据
    await this.updateCharacterGroundData(characterId, groundData);

    // 更新退役球员列表（基于old项目updateRetirementHero逻辑）
    await this.updateRetirementHeroList(characterId);

    return { code: 0 };
  }

  // ==================== old项目核心辅助方法 ====================

  /**
   * 获取角色的场地数据
   * 基于old项目: footballGround实体
   */
  private async getCharacterGroundData(characterId: string): Promise<any> {
    try {
      // 调用Character服务获取footballGround实体数据（基于old项目footballGround实体结构）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        { characterId }
      );

      if (result && result.code === 0 && result.data && result.data.footballGround) {
        // 返回完整的footballGround实体数据
        const footballGround = result.data.footballGround;

        // 确保数据结构完整性（基于old项目footballGround构造函数）
        return {
          uid: footballGround.uid || characterId,
          adminGround: footballGround.adminGround || new Map(),
          mainGround: footballGround.mainGround || new Map(),
          trainGround: footballGround.trainGround || new Map(),
          transferGround: footballGround.transferGround || new Map(),
          hospitalGround: footballGround.hospitalGround || new Map(),
          notableGround: footballGround.notableGround || new Map(),
          prestige: footballGround.prestige || 0,
          ballFan: footballGround.ballFan || 0,
          trainCount: footballGround.trainCount || 0,
          isOpen: footballGround.isOpen || 0,
          ballHandbook: footballGround.ballHandbook || [],
          ballHandbookActual: footballGround.ballHandbookActual || 0,
          ballComHandbook: footballGround.ballComHandbook || [],
          ballComHandbookActual: footballGround.ballComHandbookActual || 0,
          ballComCount: footballGround.ballComCount || 0,
          retirementList: footballGround.retirementList || [],
          notablePrestige: footballGround.notablePrestige || 0,
          firstJoinHofList: footballGround.firstJoinHofList || [],
        };
      }

      // 基于old项目结构的完整场地数据
      const groundData = {
        uid: characterId,
        adminGround: this.initDefaultGround('admin'),
        mainGround: this.initDefaultGround('main'),
        trainGround: this.initTrainGround(),
        transferGround: this.initDefaultGround('transfer'),
        hospitalGround: this.initHospitalGround(),
        notableGround: this.initNotableGround(),
        prestige: 0, // 声望值
        ballFan: 0, // 球迷数
        trainCount: 0, // 训练点数
        isOpen: 0, // 0未开放 1已开放
        isGetBallFansRewrd: 0, // 是否已领取球迷奖励
        getBallFansRewrdTime: 0, // 领取球迷奖励时间
        ballHandbook: [], // 球员图鉴
        ballHandbookActual: 0, // 球员实力
        ballComHandbook: [], // 组合图鉴
        ballComHandbookActual: 0, // 组合总实力
        ballComCount: 0, // 组合图鉴激活个数
        retirementList: [], // 退役列表
        notablePrestige: 0, // 名人堂声望
        firstJoinHofList: [], // 首次加入名人堂列表
        lastUpdateTime: new Date(),
        version: 1,
      };

      // 基于old项目：检查场地开放条件
      return this.checkGroundOpenStatus(groundData, characterId);
    } catch (error) {
      this.logger.error('获取场地数据失败', error);
      return null;
    }
  }

  /**
   * 检查场地开放状态
   * 基于old项目: 场地开放条件检查逻辑
   */
  private async checkGroundOpenStatus(groundData: any, characterId: string): Promise<any> {
    try {
      // TODO: 调用Character服务获取角色等级
      // const characterInfo = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.getInfo',
      //   { characterId }
      // );
      //
      // const characterLevel = characterInfo.data.level || 1;

      // 暂时使用模拟等级
      const characterLevel = Math.floor(Math.random() * 50) + 1;

      // 基于old项目：获取场地开放等级配置
      const openLevel = await this.getGroundOpenLevel();

      // 检查是否达到开放等级
      if (characterLevel >= openLevel) {
        groundData.isOpen = 1; // 已开放
      } else {
        groundData.isOpen = 0; // 未开放
      }

      return groundData;
    } catch (error) {
      this.logger.error('检查场地开放状态失败', error);
      return groundData;
    }
  }

  /**
   * 获取场地开放等级
   * 基于old项目: SystemParam配置表
   */
  private async getGroundOpenLevel(): Promise<number> {
    try {
      // TODO: 从配置表获取场地开放等级
      // const config = await this.gameConfig.systemParam?.get('GROUND_OPEN_LEVEL');
      // return config ? parseInt(config.value) : 10;

      // 暂时返回默认开放等级
      return 10;
    } catch (error) {
      this.logger.error('获取场地开放等级失败', error);
      return 10;
    }
  }

  /**
   * 更新角色的场地数据
   */
  private async updateCharacterGroundData(characterId: string, groundData: any): Promise<void> {
    try {
      this.logger.log(`更新场地数据: ${characterId}`);

      // 调用Character服务更新footballGround实体数据（基于old项目footballGround保存逻辑）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.updateFootballGround',
        {
          characterId,
          footballGround: {
            ...groundData,
            lastUpdateTime: new Date(), // 更新时间戳
            version: (groundData.version || 0) + 1 // 版本号递增
          }
        }
      );

      if (!result || result.code !== 0) {
        this.logger.error(`更新场地数据失败: ${characterId}`, result);
        throw new Error(`更新场地数据失败: ${result?.message || '未知错误'}`);
      }

      this.logger.debug(`场地数据更新成功: ${characterId}`);
    } catch (error) {
      this.logger.error('更新场地数据失败', error);
      throw error;
    }
  }



  /**
   * 初始化训练位置
   */
  private initTrainPos(): any[] {
    const trainPos = [];
    for (let i = 0; i < 6; i++) {
      trainPos.push({
        pos: '', // 球员ID
        state: i < 2 ? 2 : 1, // 状态：1锁住，2可添加，3训练中
        type: 0, // 训练类型：1素质，2防守，3射术，4技巧，5定位球，6守门
        beginTime: 0, // 开始时间
      });
    }
    return trainPos;
  }

  /**
   * 初始化训练场地
   */
  private initTrainGround(): any {
    return {
      Level: 1,
      TrainPos: this.initTrainPos(),
    };
  }

  /**
   * 初始化默认场地
   * 基于old项目: 各种场地的默认初始化
   */
  private initDefaultGround(type: string): any {
    switch (type) {
      case 'admin': // 行政楼
        return {
          Level: 1,
          IsUpgrade: 0,
          UpgradeBeginTime: 0,
          UpgradeEndTime: 0,
        };
      case 'main': // 主球场
        return {
          Level: 1,
          IsUpgrade: 0,
          UpgradeBeginTime: 0,
          UpgradeEndTime: 0,
          Capacity: 1000, // 容量
        };
      case 'transfer': // 转会中心
        return {
          Level: 1,
          IsUpgrade: 0,
          UpgradeBeginTime: 0,
          UpgradeEndTime: 0,
          TransferSlots: 5, // 转会位数
        };
      default:
        return {
          Level: 1,
          IsUpgrade: 0,
          UpgradeBeginTime: 0,
          UpgradeEndTime: 0,
        };
    }
  }

  /**
   * 初始化名人堂
   */
  private initNotableGround(): any {
    const notablePos = [];
    for (let i = 0; i < 20; i++) {
      notablePos.push({
        resId: 0,
        uid: '',
        state: i < 2 ? 2 : 1, // 状态：1锁住，2可添加，3已有人
      });
    }

    return {
      Level: 1,
      notablePos,
    };
  }

  /**
   * 初始化医疗中心
   */
  private initHospitalGround(): any {
    const hospitalPos = [];
    for (let i = 0; i < 2; i++) {
      hospitalPos.push({
        pos: '', // 球员ID
        state: 2, // 状态：1锁住，2可添加，3治疗中
        beginTime: 0, // 开始时间
      });
    }

    return {
      Level: 1,
      HospitalPos: hospitalPos,
    };
  }

  /**
   * 根据等级获取训练配置
   */
  private getTrainConfigByLevel(configs: any[], level: number): any {
    return configs.find(config => config.Level === level);
  }

  /**
   * 计算球员属性提升
   * 基于old项目: calcHeroProperty方法的完整实现
   */
  private async calcHeroProperty(type: number, hero: any, maxValue: number, minValue: number): Promise<number> {
    try {
      // 基于old项目：使用calc.randRange计算随机属性提升值
      const rand = Math.floor(Math.random() * (maxValue - minValue + 1)) + minValue;

      // 基于old项目：根据训练类型更新对应属性
      // 1素质，2防守，3射术，4技巧，5定位球，6守门
      const attributeMap = {
        1: 'speed',      // 素质 -> 速度
        2: 'defending',  // 防守
        3: 'shooting',   // 射术
        4: 'dribbling',  // 技巧
        5: 'passing',    // 定位球 -> 传球
        6: 'goalkeeping' // 守门
      };

      const attributeName = attributeMap[type];
      if (attributeName && hero.attributes) {
        const currentValue = hero.attributes[attributeName] || 0;

        // 基于old项目：应用数据浮动（calc.floatingNum逻辑）
        const floatingRand = this.applyFloatingNum(rand);
        const newValue = Math.min(currentValue + floatingRand, 100); // 最大100

        // 更新球员属性（基于old项目逻辑）
        await this.updateHeroAttribute(hero._id, attributeName, newValue);

        this.logger.debug(`训练属性更新: ${attributeName} ${currentValue} -> ${newValue} (+${floatingRand})`);

        return floatingRand;
      }

      return rand;
    } catch (error) {
      this.logger.error('计算球员属性提升失败', error);
      return 0;
    }
  }

  /**
   * 应用数据浮动
   * 基于old项目: calc.floatingNum方法
   */
  private applyFloatingNum(num: number): number {
    // 基于old项目：5%上浮，10%下浮
    const top = 0.05;
    const bottom = -0.1;
    const randomFloat = (Math.random() * (top - bottom) + bottom);
    return Math.floor(num * (1 + randomFloat));
  }

  /**
   * 更新球员属性
   * 基于old项目: 属性更新逻辑
   */
  private async updateHeroAttribute(heroId: string, attributeName: string, newValue: number): Promise<void> {
    try {
      await this.heroRepository.updateById(heroId, {
        [`attributes.${attributeName}`]: newValue
      });
    } catch (error) {
      this.logger.error('更新球员属性失败', error);
    }
  }

  /**
   * 重新计算球员实力值
   * 基于old项目: reCalcAttrRevision
   */
  private async reCalcAttrRevision(heroId: string): Promise<void> {
    this.logger.log(`重新计算球员实力值: ${heroId}`);

    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        this.logger.error('球员不存在，无法重新计算实力值', heroId);
        return;
      }

      // 获取球员基础属性
      const attributes = hero.attributes || {};

      // 计算总实力值（基于old项目的实力计算公式）
      const totalPower = this.calculateTotalPower(attributes);

      // 计算评分（基于old项目的评分算法）
      const rating = this.calculateRating(attributes, hero.level);

      // 更新球员实力数据
      await this.heroRepository.updateById(heroId, {
        totalPower,
        rating,
        lastCalculateTime: new Date(),
      });

      this.logger.debug(`球员实力重新计算完成: ${heroId}, 实力: ${totalPower}, 评分: ${rating}`);
    } catch (error) {
      this.logger.error('重新计算球员实力值失败', error);
    }
  }

  /**
   * 计算球员总实力
   * 基于old项目的实力计算公式
   */
  private calculateTotalPower(attributes: any): number {
    const {
      speed = 0,
      shooting = 0,
      passing = 0,
      defending = 0,
      dribbling = 0,
      physicality = 0,
      goalkeeping = 0,
    } = attributes;

    // old项目实力计算公式（权重分配）
    const totalPower = Math.floor(
      speed * 0.15 +
      shooting * 0.20 +
      passing * 0.15 +
      defending * 0.15 +
      dribbling * 0.15 +
      physicality * 0.15 +
      goalkeeping * 0.05
    );

    return Math.max(totalPower, 1); // 最低实力为1
  }

  /**
   * 计算球员评分
   * 基于old项目的评分算法
   */
  private calculateRating(attributes: any, level: number): number {
    const totalPower = this.calculateTotalPower(attributes);
    const levelBonus = level * 2; // 等级加成
    const rating = Math.floor((totalPower + levelBonus) / 10);

    return Math.min(Math.max(rating, 1), 100); // 评分范围1-100
  }

  /**
   * 生成客户端球员数据
   * 基于old项目: makeClientHero
   */
  private makeClientHero(hero: any): any {
    if (!hero) return {};

    return {
      uid: hero._id.toString(),
      name: hero.name,
      level: hero.level,
      position: hero.position,
      attributes: hero.attributes,
      Health: hero.Health || 1,
      TrainCount: hero.TrainCount || 0,
      isTrain: hero.isTrain || false,
      isTreat: hero.isTreat || false,
    };
  }

  /**
   * 检查退役列表中是否有指定球员
   * 基于old项目: checkRetirementListIsHaveHero
   */
  private checkRetirementListIsHaveHero(groundData: any, heroId: string): number {
    const retirementList = groundData.retirementList || [];
    return retirementList.findIndex(hero => hero.uid === heroId);
  }

  /**
   * 找到空闲的名人堂位置
   */
  private findEmptyNotablePos(notablePos: any[]): number {
    return notablePos.findIndex(pos => pos.state === 2); // 可添加状态
  }

  /**
   * 解锁名人堂位置
   * 基于old项目: unlockNotablePos
   */
  private unlockNotablePos(notableGround: any, level: number): void {
    // 根据等级解锁对应数量的位置
    const unlockCount = Math.min(level * 2, 20); // 每级解锁2个位置，最多20个

    for (let i = 0; i < unlockCount; i++) {
      if (notableGround.notablePos[i].state === 1) {
        notableGround.notablePos[i].state = 2; // 解锁为可添加状态
      }
    }
  }

  /**
   * 获取名人堂列表
   * 基于old项目: getHofList
   */
  private async getHofList(notablePos: any[]): Promise<any[]> {
    const hofList = [];

    for (const pos of notablePos) {
      if (pos.state === 3 && pos.uid) {
        // 获取球员信息
        const hero = await this.heroRepository.findById(pos.uid);
        if (hero) {
          const heroConfig = await this.gameConfig.hero.get(hero.resId);
          hofList.push({
            uid: pos.uid,
            resId: pos.resId,
            name: hero.name,
            position: heroConfig?.position1 || 'Unknown',
            level: hero.level,
            state: pos.state,
          });
        }
      } else {
        hofList.push({
          uid: '',
          resId: 0,
          name: '',
          position: '',
          level: 0,
          state: pos.state,
        });
      }
    }

    return hofList;
  }

  /**
   * 获取退役列表
   * 基于old项目: getRetirementList
   */
  private async getRetirementList(retirementList: any[]): Promise<any[]> {
    const formattedList = [];

    for (const retiredHero of retirementList) {
      const hero = await this.heroRepository.findById(retiredHero.uid);
      if (hero) {
        const heroConfig = await this.gameConfig.hero.get(hero.resId);
        formattedList.push({
          uid: retiredHero.uid,
          name: hero.name,
          position: heroConfig?.position1 || 'Unknown',
          level: hero.level,
          retireTime: retiredHero.retireTime,
        });
      }
    }

    return formattedList;
  }

  /**
   * 获取球迷信息
   * 基于old项目: footballGround.getBallFans() 和 matchService.getFansRank()
   * 用于商业赛系统获取对手的球迷数量、排名和球场开放状态
   */
  async getFansInfo(characterId: string) {
    this.logger.log(`获取球迷信息: ${characterId}`);

    try {
      // 获取角色的球场数据
      const groundData = await this.getCharacterGroundData(characterId);

      if (!groundData) {
        // 如果没有球场数据，返回默认值
        return {
          ballFanCount: 0,
          fanRank: 0,
          isGroundOpen: false,
        };
      }

      // 计算球迷数量（基于old项目的initBallFan逻辑）
      let ballFanCount = 0;

      // 获取各种球场建筑的球迷贡献
      const groundTypes = ['adminGround', 'mainGround', 'trainGround', 'transferGround', 'hospitalGround', 'notableGround'];

      for (const groundType of groundTypes) {
        const ground = groundData[groundType];
        if (ground && ground.length > 0) {
          const building = ground[0]; // 取第一个建筑
          let addFans = 0;

          // 主球场1级的特殊处理
          if (building.type === 2 && building.level === 1) {
            // 从系统参数获取主球场基础球迷数
            // TODO: 需要找到正确的系统参数ID，暂时使用默认值
            addFans = 100; // 默认100
          } else if (building.level > 1) {
            // 从Field配置表获取球迷数
            // 构造配置ID：类型_等级
            const configId = parseInt(`${building.type}${building.level - 1}`);
            const fieldConfig = await this.gameConfig.field.get(configId);
            addFans = fieldConfig?.fansShow || 0;
          }

          ballFanCount += addFans;
        }
      }

      // 确保球迷数不为负数
      if (ballFanCount < 0) {
        ballFanCount = 0;
      }

      // 检查球场是否开放（基于角色等级）
      let isGroundOpen = false;
      try {
        // 从Character服务获取角色信息
        const characterInfo = await this.microserviceClient.call(
          'character',
          'character.getInfo',
          { characterId, serverId: 'server_001' }
        );

        if (characterInfo && characterInfo.data) {
          const characterLevel = characterInfo.data.level;

          // 从系统参数获取球场开放等级要求
          const openLevelParam = await this.gameConfig.systemParam.get(1104); // openLevel参数ID
          const requiredLevel = openLevelParam?.parameter || 3; // 默认3级开放（行政楼开放等级）

          // 判断角色等级是否达到球场开放要求
          isGroundOpen = characterLevel >= requiredLevel;

          this.logger.log(`球场开放状态检查: ${characterId}, 角色等级: ${characterLevel}, 要求等级: ${requiredLevel}, 开放状态: ${isGroundOpen}`);
        } else {
          this.logger.warn(`获取角色信息失败: ${characterId}`);
          isGroundOpen = false;
        }
      } catch (error) {
        this.logger.warn(`检查球场开放状态失败: ${characterId}`, error);
        isGroundOpen = false;
      }

      // 获取球迷排名（基于old项目排名系统）
      const fanRank = await this.getFanRanking(characterId, ballFanCount);

      return {
        ballFanCount,
        fanRank,
        isGroundOpen,
      };
    } catch (error) {
      this.logger.error(`获取球迷信息失败: ${characterId}`, error);
      return {
        ballFanCount: 0,
        fanRank: 0,
        isGroundOpen: false,
      };
    }
  }

  /**
   * 触发训练任务
   * 基于old项目: TARGET_TYPE.THIRTY_FOUR训练任务触发
   */
  private async triggerTrainingTask(characterId: string, trainType: number): Promise<void> {
    try {
      // 调用Activity服务触发训练任务（基于old项目TARGET_TYPE.THIRTY_FOUR）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        'task.triggerTask',
        {
          characterId,
          triggerType: 'TRAINING', // TARGET_TYPE.THIRTY_FOUR
          arg1: trainType, // 训练类型：1素质，2防守，3射术，4技巧，5定位球，6守门
          arg2: 1, // 训练次数
        }
      );

      if (result && result.code === 0) {
        this.logger.debug(`训练任务触发成功: ${characterId}, 训练类型: ${trainType}`);
      }
    } catch (error) {
      this.logger.error('触发训练任务失败', error);
    }
  }

  /**
   * 更新退役球员列表
   * 基于old项目: updateRetirementHero逻辑
   */
  private async updateRetirementHeroList(characterId: string): Promise<void> {
    try {
      // 获取当前场地数据
      const groundData = await this.getCharacterGroundData(characterId);
      if (!groundData) {
        return;
      }

      // 获取所有退役球员（基于old项目退役条件检查）
      const allHeroes = await this.heroRepository.findByCharacterId(characterId);
      const retiredHeroes = allHeroes.filter(hero => hero.isRetired === true);

      // 更新退役列表（基于old项目retirementList结构）
      const retirementList = retiredHeroes.map(hero => ({
        uid: hero._id,
        resId: hero.resId,
        name: hero.name,
        quality: hero.quality,
        retirementTime: hero.retirementTime || Date.now(),
        lifeNum: hero.lifeNum || 1,
      }));

      // 更新场地数据中的退役列表
      groundData.retirementList = retirementList;
      await this.updateCharacterGroundData(characterId, groundData);

      this.logger.debug(`退役球员列表更新完成: ${characterId}, 退役球员数: ${retirementList.length}`);
    } catch (error) {
      this.logger.error('更新退役球员列表失败', error);
    }
  }

  /**
   * 获取球迷排名
   * 基于old项目: 球迷排名系统
   */
  private async getFanRanking(characterId: string, ballFanCount: number): Promise<number> {
    try {
      // TODO: 调用排名服务获取球迷排名
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.RANKING_SERVICE,
      //   'ranking.getFanRanking',
      //   { characterId, ballFanCount }
      // );
      // return result.data.rank || 0;

      // 暂时返回模拟排名（基于球迷数量简单计算）
      if (ballFanCount >= 100000) return Math.floor(Math.random() * 100) + 1;
      if (ballFanCount >= 50000) return Math.floor(Math.random() * 500) + 100;
      if (ballFanCount >= 10000) return Math.floor(Math.random() * 2000) + 500;
      return Math.floor(Math.random() * 10000) + 2000;
    } catch (error) {
      this.logger.error('获取球迷排名失败', error);
      return 0;
    }
  }
}
