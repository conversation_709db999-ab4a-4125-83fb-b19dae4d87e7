import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { HeroService } from './hero.service';
import {
  CreateHeroDto,
  UpdateHeroDto,
  EvolveHeroDto,
  LevelUpHeroDto,
  UpgradeSkillDto,
  MarketOperationDto,
  GetHeroListDto
} from '@hero/common/dto/hero.dto';
import { HeroPosition } from '@app/game-constants';
import { Cacheable, CacheEvict, CachePut } from '@common/redis';

@Controller()
export class HeroController {
  private readonly logger = new Logger(HeroController.name);

  constructor(private readonly heroService: HeroService) {}

  // ==================== 球员配置管理 ====================

  /**
   * 获取球员配置信息
   */
  @MessagePattern('hero.getConfig')
  @Cacheable({
    key: 'hero:config:#{payload.heroId}',
    dataType: 'global',
    ttl: 3600
  })
  async getHeroConfig(@Payload() payload: { heroId: number }) {
    this.logger.log(`获取球员配置信息: ${payload.heroId}`);
    const config = await this.heroService.getHeroConfig(payload.heroId);
    return {
      code: 0,
      message: '获取成功',
      data: config,
    };
  }

  /**
   * 根据位置获取球员配置
   */
  @MessagePattern('hero.getConfigByPosition')
  @Cacheable({
    key: 'hero:config:position:#{payload.position}:#{payload.limit}',
    dataType: 'global',
    ttl: 1800
  })
  async getHeroConfigsByPosition(@Payload() payload: { position: HeroPosition; limit?: number }) {
    this.logger.log(`根据位置获取球员配置: ${payload.position}`);
    const configs = await this.heroService.getHeroConfigsByPosition(payload.position, payload.limit);
    return {
      code: 0,
      message: '获取成功',
      data: configs,
    };
  }

  // ==================== 球员状态管理 ====================

  /**
   * 设置球员治疗状态
   */
  @MessagePattern('hero.setTreatStatus')
  async setHeroTreatStatus(@Payload() payload: { heroId: string; isTreat: boolean }) {
    this.logger.log(`设置球员治疗状态: ${payload.heroId}, 治疗中: ${payload.isTreat}`);
    const hero = await this.heroService.setHeroTreatStatus(payload.heroId, payload.isTreat);
    return {
      code: 0,
      message: '设置成功',
      data: hero,
    };
  }



  /**
   * 更新球员疲劳值
   */
  @MessagePattern('hero.updateFatigue')
  async updateHeroFatigue(@Payload() payload: { heroId: string; fatigueChange: number }) {
    this.logger.log(`更新球员疲劳值: ${payload.heroId}, 变化: ${payload.fatigueChange}`);
    const hero = await this.heroService.updateHeroFatigue(payload.heroId, payload.fatigueChange);
    return {
      code: 0,
      message: '更新成功',
      data: hero,
    };
  }



  // ==================== 球员实例管理 ====================

  /**
   * 创建新球员
   */
  @MessagePattern('hero.create')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createHero(@Payload() createDto: CreateHeroDto) {
    this.logger.log(`创建球员请求: ${JSON.stringify(createDto)}`);
    const hero = await this.heroService.createHero(createDto);
    return {
      code: 0,
      message: '球员创建成功',
      data: hero,
    };
  }

  /**
   * 批量获取球员信息
   */
  @MessagePattern('hero.getBatch')
  async getBatchHeroes(@Payload() payload: { heroIds: string[]; serverId?: string }) {
    this.logger.log(`批量获取球员信息: ${payload.heroIds.length}个球员`);
    const heroes = await this.heroService.getBatchHeroes(payload.heroIds);
    return {
      code: 0,
      message: '获取成功',
      data: heroes,
    };
  }

  /**
   * 获取球员信息
   */
  @MessagePattern('hero.getInfo')
  @Cacheable({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getHeroInfo(@Payload() payload: { heroId: string; serverId?: string }) {
    this.logger.log(`获取球员信息: ${payload.heroId}`);
    const hero = await this.heroService.getHeroInfo(payload.heroId);
    return {
      code: 0,
      message: '获取成功',
      data: hero,
    };
  }

  /**
   * 更新球员信息
   */
  @MessagePattern('hero.update')
  @CachePut({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async updateHero(@Payload() payload: { heroId: string; updateDto: UpdateHeroDto; serverId?: string }) {
    this.logger.log(`更新球员信息: ${payload.heroId}`);
    const hero = await this.heroService.updateHero(payload.heroId, payload.updateDto);
    return {
      code: 0,
      message: '更新成功',
      data: hero,
    };
  }

  /**
   * 获取球员列表
   */
  @MessagePattern('hero.getList')
  @Cacheable({
    key: 'character:heroes:#{payload.characterId}:#{payload.page}:#{payload.limit}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getHeroList(@Payload() query: GetHeroListDto) {
    this.logger.log(`获取球员列表: ${JSON.stringify(query)}`);
    const result = await this.heroService.getHeroList(query);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }



  /**
   * 球员升级
   */
  @MessagePattern('hero.levelUp')
  @CacheEvict({
    key: 'hero:info:#{payload.levelUpDto.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async levelUpHero(@Payload() payload: { levelUpDto: LevelUpHeroDto; serverId?: string }) {
    this.logger.log(`球员升级: ${payload.levelUpDto.heroId}`);
    // TODO: 实现球员升级逻辑
    return {
      code: 0,
      message: '升级成功',
      data: {
        leveledUp: true,
        heroId: payload.levelUpDto.heroId,
        oldLevel: 1,
        newLevel: 2,
        attributeIncrease: {
          speed: 1,
          shooting: 1,
          passing: 1,
          defending: 1,
          dribbling: 1,
          physicality: 1,
          goalkeeping: 1,
        },
        expCost: 100,
        remainingExp: 0,
      },
    };
  }

  /**
   * 技能升级
   */
  @MessagePattern('hero.skill.upgrade')
  @CacheEvict({
    key: 'hero:info:#{payload.skillDto.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async upgradeSkill(@Payload() payload: { skillDto: UpgradeSkillDto; serverId?: string }) {
    this.logger.log(`技能升级: ${payload.skillDto.heroId}, 技能: ${payload.skillDto.skillId}`);
    // TODO: 实现技能升级逻辑
    return {
      code: 0,
      message: '技能升级成功',
      data: {
        heroId: payload.skillDto.heroId,
        skillId: payload.skillDto.skillId,
        oldLevel: 1,
        newLevel: 2,
        cost: 1000,
      },
    };
  }

  /**
   * 市场操作
   */
  @MessagePattern('hero.market.operation')
  @CacheEvict({
    key: 'hero:info:#{payload.marketDto.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async marketOperation(@Payload() payload: { marketDto: MarketOperationDto; serverId?: string }) {
    this.logger.log(`市场操作: ${payload.marketDto.heroId}, 操作: ${payload.marketDto.operation}`);
    // TODO: 实现市场操作逻辑
    return {
      code: 0,
      message: '操作成功',
      data: {
        heroId: payload.marketDto.heroId,
        operation: payload.marketDto.operation,
        price: payload.marketDto.price,
        success: true,
      },
    };
  }

  /**
   * 获取阵容中的球员
   */
  @MessagePattern('hero.getFormation')
  @Cacheable({
    key: 'character:formation:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getFormationHeroes(@Payload() payload: { characterId: string; serverId?: string }) {
    this.logger.log(`获取阵容球员: ${payload.characterId}`);
    // TODO: 实现获取阵容球员逻辑
    return {
      code: 0,
      message: '获取成功',
      data: {
        characterId: payload.characterId,
        heroes: [],
        formation: '4-4-2',
      },
    };
  }

  /**
   * 获取市场球员
   */
  @MessagePattern('hero.getMarket')
  @Cacheable({
    key: 'server:market:heroes:#{payload.page}:#{payload.limit}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getMarketHeroes(@Payload() payload: {
    page?: number;
    limit?: number;
    serverId?: string
  }) {
    this.logger.log(`获取市场球员: 页码${payload.page || 1}`);
    // TODO: 实现获取市场球员逻辑
    return {
      code: 0,
      message: '获取成功',
      data: {
        list: [],
        total: 0,
        page: payload.page || 1,
        limit: payload.limit || 20,
      },
    };
  }

  /**
   * 获取球员统计
   * 基于old项目: 统计角色的球员数据
   */
  @MessagePattern('hero.getStats')
  @Cacheable({
    key: 'character:hero:stats:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getHeroStats(@Payload() payload: { characterId: string; serverId?: string }) {
    this.logger.log(`获取球员统计: ${payload.characterId}`);

    try {
      const stats = await this.heroService.getCharacterHeroStats(payload.characterId);

      return {
        code: 0,
        message: '获取成功',
        data: stats,
      };
    } catch (error) {
      this.logger.error('获取球员统计失败', error);
      return {
        code: -1,
        message: '获取球员统计失败',
        data: {
          totalHeroes: 0,
          averageLevel: 0,
          maxLevel: 0,
          totalMarketValue: 0,
          heroesInFormation: 0,
          heroesOnMarket: 0,
        },
      };
    }
  }

  // ==================== 升星系统接口 ====================

  /**
   * 获取升星需求
   */
  @MessagePattern('hero.evolution.requirements')
  @Cacheable({
    key: 'hero:evolution:requirements:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getEvolutionRequirements(@Payload() payload: { heroId: string; serverId?: string }) {
    this.logger.log(`获取升星需求: ${payload.heroId}`);
    const requirements = await this.heroService.getEvolutionRequirements(payload.heroId);
    return {
      code: 0,
      message: '获取成功',
      data: requirements,
    };
  }

  /**
   * 球员升星
   */
  @MessagePattern('hero.evolve')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async evolveHero(@Payload() evolveDto: EvolveHeroDto) {
    this.logger.log(`球员升星: ${evolveDto.heroId}`);
    const result = await this.heroService.evolveHero(evolveDto);
    return {
      code: 0,
      message: result.isEvolutionSuccess ? '升星成功' : '升星失败',
      data: result,
    };
  }

  // ==================== 状态管理接口 ====================

  /**
   * 获取球员状态
   */
  @MessagePattern('hero.status.get')
  @Cacheable({
    key: 'hero:status:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60
  })
  async getHeroStatus(@Payload() payload: { heroId: string; serverId?: string }) {
    this.logger.log(`获取球员状态: ${payload.heroId}`);
    const status = await this.heroService.getHeroStatus(payload.heroId);
    return {
      code: 0,
      message: '获取成功',
      data: status,
    };
  }

  /**
   * 治疗球员
   */
  @MessagePattern('hero.treat')
  @CacheEvict({
    key: 'hero:status:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async treatHero(@Payload() payload: { heroId: string; treatmentType?: string; serverId?: string }) {
    this.logger.log(`治疗球员: ${payload.heroId}, 类型: ${payload.treatmentType}`);
    const result = await this.heroService.treatHero(payload.heroId, payload.treatmentType as any);
    return {
      code: 0,
      message: '治疗开始',
      data: result,
    };
  }

  /**
   * 球员续约
   */
  @MessagePattern('hero.contract.renew')
  @CacheEvict({
    key: 'hero:status:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async renewHeroContract(@Payload() payload: { heroId: string; contractDays: number; serverId?: string }) {
    this.logger.log(`球员续约: ${payload.heroId}, 天数: ${payload.contractDays}`);
    const result = await this.heroService.renewHeroContract(payload.heroId, payload.contractDays);
    return {
      code: 0,
      message: '续约成功',
      data: result,
    };
  }

  /**
   * 恢复球员疲劳
   */
  @MessagePattern('hero.fatigue.recover')
  @CacheEvict({
    key: 'hero:status:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async recoverHeroFatigue(@Payload() payload: { heroId: string; recoveryValue?: number; serverId?: string }) {
    this.logger.log(`恢复球员疲劳: ${payload.heroId}, 恢复值: ${payload.recoveryValue}`);
    await this.heroService.recoverHeroFatigue(payload.heroId, payload.recoveryValue);
    return {
      code: 0,
      message: '疲劳恢复成功',
      data: null,
    };
  }

  // ==================== 突破系统接口 ====================

  /**
   * 获取突破信息
   */
  @MessagePattern('hero.breakthrough.info')
  @Cacheable({
    key: 'hero:breakthrough:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getBreakthroughInfo(@Payload() payload: { heroId: string; serverId?: string }) {
    this.logger.log(`获取突破信息: ${payload.heroId}`);
    const info = await this.heroService.getBreakthroughInfo(payload.heroId);
    return {
      code: 0,
      message: '获取成功',
      data: info,
    };
  }

  /**
   * 球员突破
   */
  @MessagePattern('hero.breakthrough')
  @CacheEvict({
    key: 'hero:breakthrough:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async breakthroughHero(@Payload() payload: { heroId: string; serverId?: string }) {
    this.logger.log(`球员突破: ${payload.heroId}`);
    const result = await this.heroService.breakthroughHero(payload.heroId);
    return {
      code: 0,
      message: '突破成功',
      data: result,
    };
  }

  /**
   * 撤销突破
   */
  @MessagePattern('hero.breakthrough.revert')
  @CacheEvict({
    key: 'hero:breakthrough:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async revertBreakthrough(@Payload() payload: { heroId: string; serverId?: string }) {
    this.logger.log(`撤销突破: ${payload.heroId}`);
    const result = await this.heroService.revertBreakthrough(payload.heroId);
    return {
      code: 0,
      message: '撤销成功',
      data: result,
    };
  }

  // ==================== 属性计算接口 ====================

  /**
   * 重新计算球员属性
   */
  @MessagePattern('hero.attributes.recalculate')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async recalculateHeroAttributes(@Payload() payload: { heroId: string; serverId?: string }) {
    this.logger.log(`重新计算球员属性: ${payload.heroId}`);
    await this.heroService.reCalcHeroAttributes(payload.heroId);
    return {
      code: 0,
      message: '属性重新计算成功',
    };
  }

  /**
   * 批量重新计算角色所有球员属性
   */
  @MessagePattern('hero.attributes.recalculateAll')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async recalculateAllHeroAttributes(@Payload() payload: { characterId: string; serverId?: string }) {
    this.logger.log(`批量重新计算球员属性: ${payload.characterId}`);
    await this.heroService.reCalcAllHeroAttributes(payload.characterId);
    return {
      code: 0,
      message: '所有球员属性重新计算成功',
    };
  }

  // ==================== 生涯管理接口 ====================

  /**
   * 球员续约
   */
  @MessagePattern('hero.career.renew')
  @CacheEvict({
    key: 'hero:status:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async renewHeroCareer(@Payload() payload: { heroId: string; days?: number; serverId?: string }) {
    this.logger.log(`球员续约: ${payload.heroId}, 天数: ${payload.days || 365}`);
    const result = await this.heroService.addHeroCareerDays(payload.heroId, payload.days);
    return {
      code: 0,
      message: '续约成功',
      data: result,
    };
  }

  /**
   * 获取球员生涯信息
   */
  @MessagePattern('hero.career.info')
  @Cacheable({
    key: 'hero:career:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getHeroCareerInfo(@Payload() payload: { heroId: string; serverId?: string }) {
    this.logger.log(`获取球员生涯信息: ${payload.heroId}`);
    const careerInfo = await this.heroService.getHeroCareerInfo(payload.heroId);
    return {
      code: 0,
      message: '获取成功',
      data: careerInfo,
    };
  }

  /**
   * 检查合约到期
   */
  @MessagePattern('hero.career.checkExpiration')
  @Cacheable({
    key: 'character:contract:expiration:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800 // 30分钟
  })
  async checkContractExpiration(@Payload() payload: { characterId: string; serverId?: string }) {
    this.logger.log(`检查合约到期: ${payload.characterId}`);
    const expirationInfo = await this.heroService.checkContractExpiration(payload.characterId);
    return {
      code: 0,
      message: '检查完成',
      data: expirationInfo,
    };
  }

  /**
   * 处理球员退役
   */
  @MessagePattern('hero.career.processRetirement')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async processRetirement(@Payload() payload: { characterId: string; serverId?: string }) {
    this.logger.log(`处理球员退役: ${payload.characterId}`);
    const retiredHeroes = await this.heroService.checkAndProcessRetirement(payload.characterId);
    return {
      code: 0,
      message: '退役处理完成',
      data: {
        retiredHeroes,
        retiredCount: retiredHeroes.length,
      },
    };
  }

  // ==================== 注意：球探、养成、生涯等功能已迁移到独立模块 ====================
  //
  // 这些功能现在由以下独立模块处理：
  // - ScoutModule: 球探系统相关功能
  // - CultivationModule: 球员养成系统功能
  // - CareerModule: 生涯管理系统功能
  // - TrainingModule: 训练系统功能
  //
  // hero.controller.ts 现在只处理基础的球员CRUD操作
}
