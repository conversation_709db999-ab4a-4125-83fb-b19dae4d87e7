import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Hero相关组件
import { HeroController } from './hero.controller';
import { HeroService } from './hero.service';
import { HeroRepository } from '@hero/common/repositories/hero.repository';
import { Hero, HeroSchema } from '@hero/common/schemas/hero.schema';
import { MicroserviceKitModule } from '@common/microservice-kit';

/**
 * Hero基础模块
 * 处理球员的基础CRUD操作
 *
 * 注意：
 * - GameConfigModule已在app.module.ts中全局注册，无需重复导入
 * - MongooseModule.forFeature()用于注册特定Schema，需要在使用的模块中注册
 */
@Module({
  imports: [
    // 注册Hero Schema（必需）
    MongooseModule.forFeature([
      { name: Hero.name, schema: HeroSchema },
    ]),
    // 导入微服务通信模块
    MicroserviceKitModule,
  ],

  controllers: [HeroController],

  providers: [
    HeroService,
    HeroRepository,
  ],

  exports: [
    HeroService,
    HeroRepository,
  ],
})
export class HeroModule {}
