import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { HeroDefinition, GameConfigFacade } from '@app/game-config';
import { HeroDocument } from '@hero/common/schemas/hero.schema';
import { HeroRepository } from '@hero/common/repositories/hero.repository';
import { HeroQuality } from '../../common/types';
import { HeroPosition } from '@app/game-constants';
import { MicroserviceClientService } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';
import {
  CreateHeroDto,
  UpdateHeroDto,
  TrainHeroDto,
  EvolveHeroDto,
  LevelUpHeroDto,
  UpgradeSkillDto,
  MarketOperationDto,
  HeroInfoDto,
  TrainResultDto,
  EvolveResultDto,
  LevelUpResultDto,
  GetHeroListDto
} from '@hero/common/dto/hero.dto';
import { ErrorCode, ErrorMessages } from '@app/game-constants';
import { GAME_CONSTANTS } from '@app/game-constants';

@Injectable()
export class HeroService {
  private readonly logger = new Logger(HeroService.name);

  constructor(
    private readonly heroRepository: HeroRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  // ==================== 球员配置管理 ====================

  /**
   * 获取球员配置信息
   * 修复：使用GameConfigFacade获取静态配置，而不是通过Repository
   */
  async getHeroConfig(heroId: number): Promise<any> {
    try {
      const heroDefinition = await this.gameConfig.hero.get(heroId);
      if (!heroDefinition) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      return this.toHeroConfigDto(heroDefinition);
    } catch (error) {
      this.logger.error('获取球员配置信息失败', error);
      throw error;
    }
  }

  /**
   * 根据位置获取球员配置
   * 修复：使用GameConfigFacade的filter方法进行高效筛选
   */
  async getHeroConfigsByPosition(position: HeroPosition, limit: number = 50): Promise<any[]> {
    try {
      // 使用GameConfigFacade的filter方法根据位置筛选
      // 注意：HeroDefinition中位置字段是position1和position2
      const heroDefinitions = await this.gameConfig.hero.filter(hero =>
        hero.position1 === position || hero.position2 === position
      );

      // 应用数量限制并按评分排序
      const limitedHeroes = heroDefinitions
        .sort((a, b) => b.rating - a.rating) // 按评分降序排序
        .slice(0, limit);

      return limitedHeroes.map(heroDef => this.toHeroConfigDto(heroDef));
    } catch (error) {
      this.logger.error('根据位置获取球员配置失败', error);
      throw error;
    }
  }

  // ==================== 球员实例管理 ====================

  /**
   * 创建新球员
   * 修复：使用GameConfigFacade获取静态配置，而不是通过Repository
   */
  async createHero(createDto: CreateHeroDto): Promise<HeroDocument> {
    try {
      // 检查球员配置是否存在
      const heroDefinition = await this.gameConfig.hero.get(createDto.resId);
      if (!heroDefinition) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      // 生成球员ID
      const heroId = this.generateHeroId();

      // 根据配置表ID和品质初始化属性
      const initialAttributes = this.generateInitialAttributes(createDto.resId, createDto.quality);

      // 创建球员数据
      const heroData = {
        heroId,
        characterId: createDto.characterId,
        serverId: createDto.serverId,
        resId: createDto.resId,
        name: createDto.name,
        position: createDto.position,
        quality: createDto.quality,
        level: createDto.level || 1,
        nationality: createDto.nationality || '',
        club: createDto.club || '',
        attributes: initialAttributes,
        baseAttributes: { ...initialAttributes },
        obtainTime: Date.now(),
        obtainType: createDto.obtainType || 1,
        energy: 100,
        morale: 100,
        marketValue: this.calculateMarketValue(createDto.quality, createDto.level || 1),
      };

      const savedHero = await this.heroRepository.create(heroData);

      this.logger.log(`球员创建成功: ${heroId}, 角色: ${createDto.characterId}`);
      return savedHero;
    } catch (error) {
      this.logger.error('创建球员失败', error);
      throw error;
    }
  }

  /**
   * 批量获取球员信息
   */
   async getBatchHeroes(heroIds: string[]): Promise<HeroInfoDto[]> {
    try {
      const heroes = await this.heroRepository.findByIds(heroIds);

      // 过滤掉null/undefined的球员，只返回存在的球员信息
      const validHeroes = heroes.filter(hero => hero != null);

      if (validHeroes.length === 0) {
        this.logger.warn(`批量获取球员信息：请求${heroIds.length}个球员，但没有找到任何有效球员`);
        return [];
      }

      if (validHeroes.length < heroIds.length) {
        const foundIds = validHeroes.map(hero => hero.heroId);
        const missingIds = heroIds.filter(id => !foundIds.includes(id));
        this.logger.warn(`批量获取球员信息：请求${heroIds.length}个球员，找到${validHeroes.length}个，缺失球员ID: ${missingIds.join(', ')}`);
      }

      return validHeroes.map(hero => this.toHeroInfoDto(hero));
    } catch (error) {
      this.logger.error('批量获取球员信息失败', error);
      throw error;
    }
  }

  /**
   * 获取球员信息
   */
  async getHeroInfo(heroId: string): Promise<HeroInfoDto> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      return this.toHeroInfoDto(hero);
    } catch (error) {
      this.logger.error('获取球员信息失败', error);
      throw error;
    }
  }

  /**
   * 更新球员信息
   */
  async updateHero(heroId: string, updateDto: UpdateHeroDto): Promise<HeroDocument> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      const updatedHero = await this.heroRepository.update(heroId, updateDto);

      this.logger.log(`球员信息更新成功: ${heroId}`);
      return updatedHero;
    } catch (error) {
      this.logger.error('更新球员信息失败', error);
      throw error;
    }
  }

  /**
   * 获取球员列表
   */
  async getHeroList(query: GetHeroListDto): Promise<any> {
    try {
      const result = await this.heroRepository.findWithPagination(query, query.characterId);

      return {
        list: result.data.map(hero => this.toHeroInfoDto(hero)),
        total: result.total,
        page: result.page,
        limit: result.limit,
        pages: result.pages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    } catch (error) {
      this.logger.error('获取球员列表失败', error);
      throw error;
    }
  }

  /**
   * 设置球员治疗状态
   */
  async setHeroTreatStatus(heroId: string, isTreat: boolean): Promise<HeroDocument> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      hero.isTreat = isTreat;
      const updatedHero = await hero.save();

      this.logger.log(`球员治疗状态更新: ${heroId}, 治疗中: ${isTreat}`);
      return updatedHero;
    } catch (error) {
      this.logger.error('设置球员治疗状态失败', error);
      throw error;
    }
  }

  /**
   * 设置球员训练状态
   */
  async setHeroTrainStatus(heroId: string, isTrain: boolean, isLockTrain?: boolean): Promise<HeroDocument> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      hero.isTrain = isTrain;
      if (isLockTrain !== undefined) {
        hero.isLockTrain = isLockTrain;
      }
      const updatedHero = await hero.save();

      this.logger.log(`球员训练状态更新: ${heroId}, 训练中: ${isTrain}, 锁定: ${isLockTrain}`);
      return updatedHero;
    } catch (error) {
      this.logger.error('设置球员训练状态失败', error);
      throw error;
    }
  }

  /**
   * 更新球员疲劳值
   */
  async updateHeroFatigue(heroId: string, fatigueChange: number): Promise<HeroDocument> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      // 更新疲劳值，确保在0-100范围内
      hero.fatigue = Math.max(0, Math.min(100, hero.fatigue + fatigueChange));
      hero.reTimeFatigue = Date.now();

      const updatedHero = await hero.save();

      this.logger.log(`球员疲劳值更新: ${heroId}, 变化: ${fatigueChange}, 当前: ${hero.fatigue}`);
      return updatedHero;
    } catch (error) {
      this.logger.error('更新球员疲劳值失败', error);
      throw error;
    }
  }

  /**
   * 球员突破（潜能系统）
   */
  async breakthroughHero(heroId: string): Promise<any> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      const currentBreakthroughCount = hero.breakthrough.length;

      // 检查是否可以突破（最多10次）
      if (currentBreakthroughCount >= 10) {
        throw new BadRequestException({
          code: ErrorCode.HERO_BREAKTHROUGH_LIMIT,
          message: ErrorMessages[ErrorCode.HERO_BREAKTHROUGH_LIMIT],
        });
      }

      // 计算突破费用
      const breakthroughCost = this.calculateBreakthroughCost(currentBreakthroughCount);

      // 检查金币是否足够（基于old项目逻辑）
      const goldCheckResult = await this.checkCharacterGold(hero.characterId, breakthroughCost);
      if (!goldCheckResult.sufficient) {
        return {
          code: -1,
          message: '金币不足',
          required: breakthroughCost,
          current: goldCheckResult.current,
        };
      }

      // 扣除金币
      const deductResult = await this.deductCharacterGold(hero.characterId, breakthroughCost);
      if (!deductResult) {
        return {
          code: -1,
          message: '金币扣除失败',
        };
      }

      // 根据球员品质和突破次数计算突破值（1-7）
      const breakthroughValue = this.calculateBreakthroughValue(hero.quality, currentBreakthroughCount + 1);

      // 计算新的潜能值
      const oldPotential = this.calculateCurrentPotential(hero.breakthrough);
      const newPotential = oldPotential + breakthroughValue;

      // 更新突破记录
      const newBreakthrough = [...hero.breakthrough, breakthroughValue];

      // 更新潜能上限
      const newOldBreakOut = Math.max(hero.oldBreakOut, 30 + newPotential);

      // 计算属性上限提升
      const attributeLimits = this.calculateAttributeLimits(hero.resId, newPotential);

      // 更新球员数据
      await this.heroRepository.update(heroId, {
        breakthrough: newBreakthrough,
        oldBreakOut: newOldBreakOut,
      });

      // 重新计算属性（如果球员在阵容中，需要更新阵容属性）
      // TODO: 通知Character服务更新阵容属性

      this.logger.log(`球员突破成功: ${heroId}, 突破值: ${breakthroughValue}, 新潜能: ${newPotential}`);

      return {
        success: true,
        heroId,
        breakthroughValue,
        oldPotential,
        newPotential,
        breakthroughCount: newBreakthrough.length,
        cost: breakthroughCost,
        attributeLimits,
        canBreakthroughAgain: newBreakthrough.length < 10,
      };
    } catch (error) {
      this.logger.error('球员突破失败', error);
      throw error;
    }
  }

  /**
   * 撤销球员突破
   */
  async revertBreakthrough(heroId: string): Promise<any> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      // 检查是否有突破记录
      if (hero.breakthrough.length === 0) {
        throw new BadRequestException({
          code: ErrorCode.NO_BREAKTHROUGH_TO_REVERT,
          message: ErrorMessages[ErrorCode.NO_BREAKTHROUGH_TO_REVERT],
        });
      }

      // 检查是否有非完美突破（值不为7）
      const nonPerfectIndex = hero.breakthrough.findIndex(value => value !== 7);
      if (nonPerfectIndex === -1) {
        throw new BadRequestException({
          code: ErrorCode.ALL_BREAKTHROUGH_PERFECT,
          message: ErrorMessages[ErrorCode.ALL_BREAKTHROUGH_PERFECT],
        });
      }

      // 计算撤销费用
      const revertCost = this.calculateRevertCost();

      // TODO: 检查金币是否足够（需要与Character服务通信）

      // 移除最后一次突破
      const revertedValue = hero.breakthrough.pop();
      const newBreakthrough = [...hero.breakthrough];

      // 重新计算潜能值
      const newPotential = this.calculateCurrentPotential(newBreakthrough);
      const newOldBreakOut = Math.max(30, 30 + newPotential);

      // 更新球员数据
      await this.heroRepository.update(heroId, {
        breakthrough: newBreakthrough,
        oldBreakOut: newOldBreakOut,
      });

      this.logger.log(`球员突破撤销成功: ${heroId}, 撤销值: ${revertedValue}, 新潜能: ${newPotential}`);

      return {
        success: true,
        heroId,
        revertedValue,
        newPotential,
        breakthroughCount: newBreakthrough.length,
        cost: revertCost,
      };
    } catch (error) {
      this.logger.error('撤销球员突破失败', error);
      throw error;
    }
  }

  /**
   * 训练球员（阶段性培养系统）
   */
  async trainHero(trainDto: TrainHeroDto): Promise<TrainResultDto> {
    try {
      const hero = await this.heroRepository.findById(trainDto.heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      // 检查是否可以训练
      if (!hero.canTrain) {
        throw new BadRequestException({
          code: ErrorCode.HERO_TRAINING_COOLDOWN,
          message: ErrorMessages[ErrorCode.HERO_TRAINING_COOLDOWN],
        });
      }

      // 检查球员是否在训练中或治疗中
      if (hero.isTrain || hero.isTreat) {
        throw new BadRequestException({
          code: ErrorCode.HERO_IN_TRAINING,
          message: ErrorMessages[ErrorCode.HERO_IN_TRAINING],
        });
      }

      const count = trainDto.count || 1;
      const trainingTime = Date.now();

      // 根据训练类型执行不同的训练逻辑
      let trainingResult;
      switch (trainDto.trainingType) {
        case 1: // 初级训练
        case 2: // 中级训练
        case 3: // 高级训练
          trainingResult = await this.executeGeneralTraining(hero, trainDto, count);
          break;
        case 4: // 定向训练
          trainingResult = await this.executeTargetedTraining(hero, trainDto, count);
          break;
        default:
          throw new BadRequestException({
            code: ErrorCode.INVALID_PARAMETER,
            message: ErrorMessages[ErrorCode.INVALID_PARAMETER],
          });
      }

      // 更新球员训练数据
      const updateData = {
        'training.trainingCount': hero.training.trainingCount + count,
        'training.lastTrainingTime': trainingTime,
        'training.trainingCooldown': this.calculateTrainingCooldown(trainDto.trainingType),
        ...trainingResult.updateData,
      };

      await this.heroRepository.update(trainDto.heroId, updateData);

      this.logger.log(`球员训练完成: ${trainDto.heroId}, 类型: ${trainDto.trainingType}, 次数: ${count}`);

      return {
        success: true,
        heroId: trainDto.heroId,
        trainingType: trainDto.trainingType,
        trainingMethod: trainDto.trainingMethod,
        attributeChanges: trainingResult.attributeChanges,
        goldCost: trainingResult.goldCost,
        itemCost: trainingResult.itemCost,
        trainCount: count,
        currentStage: trainingResult.currentStage,
        reachedStageLimit: trainingResult.reachedStageLimit,
        nextTrainTime: trainingTime + this.calculateTrainingCooldown(trainDto.trainingType),
        trainingTime,
      };
    } catch (error) {
      this.logger.error('训练球员失败', error);
      throw error;
    }
  }

  /**
   * 球员升星
   */
  async evolveHero(evolveDto: EvolveHeroDto): Promise<EvolveResultDto> {
    try {
      const hero = await this.heroRepository.findById(evolveDto.heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      const oldStar = hero.evolution.star;

      // 检查是否可以升星
      if (!hero.canEvolve) {
        throw new BadRequestException({
          code: ErrorCode.HERO_CANNOT_EVOLVE,
          message: ErrorMessages[ErrorCode.HERO_CANNOT_EVOLVE],
        });
      }

      // 检查是否已达到最高星级
      if (oldStar >= 9) {
        throw new BadRequestException({
          code: ErrorCode.HERO_MAX_STAR,
          message: ErrorMessages[ErrorCode.HERO_MAX_STAR],
        });
      }

      // 验证材料球员数量
      const requiredMaterialCount = hero.evolutionMaterialCount;
      if (evolveDto.materialHeroIds.length < requiredMaterialCount && !evolveDto.useUniversalCard) {
        throw new BadRequestException({
          code: ErrorCode.INSUFFICIENT_MATERIAL,
          message: ErrorMessages[ErrorCode.INSUFFICIENT_MATERIAL],
        });
      }

      // 验证材料球员
      const materialHeroes = await Promise.all(
        evolveDto.materialHeroIds.map(id => this.heroRepository.findById(id))
      );

      for (const materialHero of materialHeroes) {
        if (!materialHero || materialHero.isLocked || materialHero.isInFormation) {
          throw new BadRequestException({
            code: ErrorCode.INVALID_MATERIAL_HERO,
            message: ErrorMessages[ErrorCode.INVALID_MATERIAL_HERO],
          });
        }
      }

      // 计算升星成功率
      const successRate = hero.evolutionSuccessRate;

      // 计算消耗的金币
      const goldCost = this.calculateEvolutionGoldCost(oldStar, evolveDto.useProtection);

      // TODO: 检查金币是否足够（需要与Character服务通信）

      // 执行升星逻辑
      let isEvolutionSuccess = false;
      let newStar = oldStar;

      if (evolveDto.useProtection) {
        // 使用保护道具，必定成功
        isEvolutionSuccess = true;
        newStar = oldStar + 1;
      } else {
        // 概率升星
        const random = Math.random() * 100;
        if (random <= successRate) {
          isEvolutionSuccess = true;
          newStar = oldStar + 1;
        } else {
          // 升星失败，可能降星
          const degradeRate = this.calculateDegradeRate(oldStar);
          const degradeRandom = Math.random() * 100;
          if (degradeRandom <= degradeRate && oldStar > 0) {
            newStar = Math.max(0, oldStar - 1);
          } else {
            newStar = oldStar; // 保持原星级
          }
        }
      }

      const evolutionTime = Date.now();

      // 计算属性加成
      const attributeBonus = this.calculateEvolutionAttributeBonus(hero, newStar);

      // 更新球员数据
      const updateData = {
        'evolution.star': newStar,
        'evolution.evolutionCount': hero.evolution.evolutionCount + 1,
        'evolution.lastEvolutionTime': evolutionTime,
        'evolution.consumedHeroes': [...hero.evolution.consumedHeroes, ...evolveDto.materialHeroIds],
        'evolution.isMaxStar': newStar >= 9,
      };

      // 应用属性加成
      Object.keys(attributeBonus).forEach(attr => {
        updateData[`attributes.${attr}`] = hero.attributes[attr] + attributeBonus[attr];
      });

      await this.heroRepository.update(evolveDto.heroId, updateData);

      // 删除材料球员
      await Promise.all(
        evolveDto.materialHeroIds.map(id => this.heroRepository.softDelete(id))
      );

      this.logger.log(`球员升星完成: ${evolveDto.heroId}, ${oldStar} -> ${newStar}, 成功: ${isEvolutionSuccess}`);

      return {
        success: true,
        heroId: evolveDto.heroId,
        oldStar,
        newStar,
        isEvolutionSuccess,
        successRate,
        attributeBonus,
        consumedHeroes: evolveDto.materialHeroIds,
        consumedItems: evolveDto.items || [],
        consumedGold: goldCost,
        newOverallRating: this.calculateOverallRating(hero.position as HeroPosition, {
          ...hero.attributes,
          ...Object.keys(attributeBonus).reduce((acc, key) => {
            acc[key] = hero.attributes[key] + attributeBonus[key];
            return acc;
          }, {})
        }),
        evolutionTime,
      };
    } catch (error) {
      this.logger.error('球员升星失败', error);
      throw error;
    }
  }

  /**
   * 生成球员ID
   */
  private generateHeroId(): string {
    return `hero_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 计算球员位置适应性
   * 基于old项目的calcHeroMatchRate方法实现
   */
  private calculatePositionAdaptability(hero: HeroDocument): any {
    // 位置映射表（基于old项目的TEAM_FORMATION_CONFIG_POSITION_TO_ID）
    const positionMapping = {
      'GK': 1, 'DC': 2, 'DL': 3, 'DR': 4, 'DM': 5, 'MC': 6,
      'ML': 7, 'MR': 8, 'AM': 9, 'ST': 10, 'WL': 11, 'WR': 12
    };

    // 基础适应性配置表（基于old项目的PositionMatch.json）
    const positionMatchConfig = {
      1: { GK: 1.0, DC: 0.3, DL: 0.3, DR: 0.3, DM: 0.3, MC: 0.3, ML: 0.3, MR: 0.3, AM: 0.3, ST: 0.3, WL: 0.3, WR: 0.3 }, // 门将
      2: { GK: 0.3, DC: 1.0, DL: 0.8, DR: 0.8, DM: 0.85, MC: 0.7, ML: 0.5, MR: 0.5, AM: 0.3, ST: 0.3, WL: 0.3, WR: 0.3 }, // 中后卫
      3: { GK: 0.3, DC: 0.8, DL: 1.0, DR: 0.8, DM: 0.6, MC: 0.6, ML: 0.95, MR: 0.5, AM: 0.4, ST: 0.3, WL: 0.9, WR: 0.3 }, // 左后卫
      4: { GK: 0.3, DC: 0.8, DL: 0.8, DR: 1.0, DM: 0.6, MC: 0.6, ML: 0.5, MR: 0.95, AM: 0.4, ST: 0.3, WL: 0.3, WR: 0.9 }, // 右后卫
      5: { GK: 0.3, DC: 0.9, DL: 0.6, DR: 0.6, DM: 1.0, MC: 0.9, ML: 0.7, MR: 0.7, AM: 0.95, ST: 0.4, WL: 0.4, WR: 0.4 }, // 后腰
      6: { GK: 0.3, DC: 0.8, DL: 0.6, DR: 0.6, DM: 0.9, MC: 1.0, ML: 0.8, MR: 0.8, AM: 0.9, ST: 0.5, WL: 0.5, WR: 0.5 }, // 中前卫
      7: { GK: 0.3, DC: 0.3, DL: 0.8, DR: 0.3, DM: 0.7, MC: 0.7, ML: 1.0, MR: 0.8, AM: 0.5, ST: 0.4, WL: 1.0, WR: 0.5 }, // 左前卫
      8: { GK: 0.3, DC: 0.3, DL: 0.3, DR: 0.8, DM: 0.7, MC: 0.7, ML: 0.8, MR: 1.0, AM: 0.5, ST: 0.4, WL: 0.5, WR: 1.0 }, // 右前卫
      9: { GK: 0.3, DC: 0.4, DL: 0.4, DR: 0.4, DM: 1.0, MC: 1.0, ML: 0.8, MR: 0.8, AM: 1.0, ST: 0.95, WL: 0.8, WR: 0.8 }, // 前腰
      10: { GK: 0.3, DC: 0.3, DL: 0.3, DR: 0.3, DM: 0.3, MC: 0.3, ML: 0.3, MR: 0.3, AM: 0.8, ST: 1.0, WL: 0.9, WR: 0.9 }, // 中锋
      11: { GK: 0.3, DC: 0.3, DL: 0.7, DR: 0.3, DM: 0.4, MC: 0.5, ML: 0.85, MR: 0.4, AM: 0.5, ST: 1.0, WL: 1.0, WR: 0.95 }, // 左边锋
      12: { GK: 0.3, DC: 0.3, DL: 0.3, DR: 0.7, DM: 0.4, MC: 0.5, ML: 0.4, MR: 0.85, AM: 0.5, ST: 1.0, WL: 0.95, WR: 1.0 } // 右边锋
    };

    // 获取球员主位置对应的ID
    const heroPositionId = positionMapping[hero.position] || 10; // 默认为中锋
    const adaptabilityConfig = positionMatchConfig[heroPositionId];

    if (!adaptabilityConfig) {
      this.logger.warn(`未找到位置${hero.position}(ID:${heroPositionId})的适应性配置，使用默认值`);
      // 返回默认适应性（所有位置50%）- 使用标准HeroPosition格式
      return {
        GK: 50, DC: 50, DL: 50, DR: 50, DM: 50, MC: 50,
        ML: 50, MR: 50, AM: 50, ST: 50, WL: 50, WR: 50
      };
    }

    // 转换为百分制并返回（字段名必须与Character服务Schema一致 - 大写格式）
    return {
      GK: Math.round(adaptabilityConfig.GK * 100),   // 门将 (严格对应old项目)
      DL: Math.round(adaptabilityConfig.DL * 100),   // 左后卫 (严格对应old项目)
      DC: Math.round(adaptabilityConfig.DC * 100),   // 中后卫 (严格对应old项目)
      DR: Math.round(adaptabilityConfig.DR * 100),   // 右后卫 (严格对应old项目)
      DM: Math.round(adaptabilityConfig.DM * 100),   // 后腰 (严格对应old项目)
      MC: Math.round(adaptabilityConfig.MC * 100),   // 中前卫 (严格对应old项目)
      ML: Math.round(adaptabilityConfig.ML * 100),   // 左前卫 (严格对应old项目)
      MR: Math.round(adaptabilityConfig.MR * 100),   // 右前卫 (严格对应old项目)
      AM: Math.round(adaptabilityConfig.AM * 100),   // 前腰 (严格对应old项目)
      ST: Math.round(adaptabilityConfig.ST * 100),   // 中锋 (严格对应old项目)
      WL: Math.round(adaptabilityConfig.WL * 100),   // 左边锋 (严格对应old项目)
      WR: Math.round(adaptabilityConfig.WR * 100)    // 右边锋 (严格对应old项目)
    };
  }

  /**
   * 根据配置表ID和品质生成初始属性
   */
  private generateInitialAttributes(resId: number, quality: HeroQuality): any {
    // 基础属性值根据品质决定
    const baseValue = {
      [HeroQuality.WHITE]: 30,
      [HeroQuality.GREEN]: 40,
      [HeroQuality.BLUE]: 50,
      [HeroQuality.PURPLE]: 60,
      [HeroQuality.ORANGE]: 70,
      [HeroQuality.RED]: 80,
    }[quality] || 30;

    // 随机浮动 ±5
    const randomVariation = () => baseValue + Math.floor(Math.random() * 11) - 5;

    return {
      speed: randomVariation(),
      shooting: randomVariation(),
      passing: randomVariation(),
      defending: randomVariation(),
      dribbling: randomVariation(),
      physicality: randomVariation(),
      goalkeeping: randomVariation(),
    };
  }

  /**
   * 计算市场价值
   */
  private calculateMarketValue(quality: HeroQuality, level: number): number {
    const baseValue = {
      [HeroQuality.WHITE]: 1000,
      [HeroQuality.GREEN]: 5000,
      [HeroQuality.BLUE]: 20000,
      [HeroQuality.PURPLE]: 50000,
      [HeroQuality.ORANGE]: 100000,
      [HeroQuality.RED]: 200000,
    }[quality] || 1000;

    return baseValue * (1 + level * 0.1);
  }

  /**
   * 计算属性提升
   */
  private calculateAttributeIncrease(quality: HeroQuality, count: number): number {
    const baseIncrease = {
      [HeroQuality.WHITE]: 1,
      [HeroQuality.GREEN]: 1,
      [HeroQuality.BLUE]: 2,
      [HeroQuality.PURPLE]: 2,
      [HeroQuality.ORANGE]: 3,
      [HeroQuality.RED]: 3,
    }[quality] || 1;

    return baseIncrease * count;
  }

  /**
   * 计算升星属性加成
   */
  private calculateEvolutionBonus(quality: HeroQuality, star: number): any {
    const bonus = star * 2; // 每星+2属性

    return {
      speed: bonus,
      shooting: bonus,
      passing: bonus,
      defending: bonus,
      dribbling: bonus,
      physicality: bonus,
      goalkeeping: bonus,
    };
  }

  /**
   * 计算总评分
   */
  private calculateOverallRating(position: HeroPosition, attributes: any): number {
    // 简化的评分计算
    const total = Object.values(attributes).reduce((sum: number, value: any) => {
      return sum + (Number(value) || 0);
    }, 0) as number;
    return Math.round(total / 7);
  }

  /**
   * 转换为球员配置DTO
   * 现在使用GameConfigFacade返回的数据而不是废弃的HeroDefinitionDocument
   */
  private toHeroConfigDto(heroDefinition: any): any {
    return {
      heroId: heroDefinition.heroId,
      modeId: heroDefinition.modeId,
      name: heroDefinition.name,
      cnName: heroDefinition.cnName,
      enName: heroDefinition.enName,
      position: heroDefinition.position,
      quality: heroDefinition.quality,
      rating: heroDefinition.rating,
      avatar: heroDefinition.avatar,
      faceIcon: heroDefinition.faceIcon,
      nationality: heroDefinition.nationality,
      club: heroDefinition.club,
      age: heroDefinition.age,
      height: heroDefinition.height,
      weight: heroDefinition.weight,
      baseAttributes: heroDefinition.baseAttributes,
      growthConfig: heroDefinition.growthConfig,
      defaultSkills: heroDefinition.defaultSkills,
      learnableSkills: heroDefinition.learnableSkills,
      obtainConfig: heroDefinition.obtainConfig,
      isActive: heroDefinition.isActive,
      isLimited: heroDefinition.isLimited,
      limitEndTime: heroDefinition.limitEndTime,
      isExpired: heroDefinition.isExpired,
      isAvailable: heroDefinition.isAvailable,
      isGoalkeeper: heroDefinition.isGoalkeeper,
      totalAttributes: heroDefinition.totalAttributes,
    };
  }

  /**
   * 转换为DTO
   */
  private toHeroInfoDto(hero: HeroDocument): HeroInfoDto {
    // 计算球员位置适应性
    const adaptability = this.calculatePositionAdaptability(hero);

    return {
      heroId: hero.heroId,
      uid: hero.heroId, // 添加uid字段，与Character服务兼容
      characterId: hero.characterId,
      serverId: hero.serverId,
      resId: hero.resId,
      name: hero.name,
      position: hero.position,
      quality: hero.quality,
      level: hero.level,
      exp: hero.exp,
      avatar: hero.avatar,
      nationality: hero.nationality,
      club: hero.club,
      attributes: hero.attributes,
      skills: hero.skills,
      evolution: hero.evolution,
      overallRating: hero.overallRating,
      isLocked: hero.isLocked,
      isInFormation: hero.isInFormation,
      energy: hero.energy,
      morale: hero.morale,
      equipments: hero.equipments,
      contractDays: hero.contractDays,
      salary: hero.salary,
      marketValue: hero.marketValue,
      isOnMarket: hero.isOnMarket,
      adaptability, // 添加适应性字段
      stats: {
        matchesPlayed: hero.matchesPlayed,
        goals: hero.goals,
        assists: hero.assists,
        yellowCards: hero.yellowCards,
        redCards: hero.redCards,
      },
      obtainTime: hero.obtainTime,
      canTrain: hero.canTrain,
      canEvolve: hero.canEvolve,
    };
  }

  // ==================== 升星系统辅助方法 ====================

  /**
   * 计算升星金币消耗
   */
  private calculateEvolutionGoldCost(currentStar: number, useProtection: boolean): number {
    // 基础消耗随星级递增
    const baseCost = 1000 * Math.pow(2, currentStar);

    // 使用保护道具需要额外消耗
    const protectionMultiplier = useProtection ? 2 : 1;

    return baseCost * protectionMultiplier;
  }

  /**
   * 计算降星概率
   */
  private calculateDegradeRate(currentStar: number): number {
    // 星级越高，失败时降星概率越高
    if (currentStar <= 3) return 0; // 3星以下不降星
    if (currentStar <= 6) return 20; // 4-6星失败时20%概率降星
    return 30; // 7-9星失败时30%概率降星
  }

  /**
   * 计算升星属性加成
   */
  private calculateEvolutionAttributeBonus(hero: any, newStar: number): any {
    const oldStar = hero.evolution.star;
    const starDiff = newStar - oldStar;

    if (starDiff === 0) {
      // 星级没有变化，无属性加成
      return {
        speed: 0, shooting: 0, passing: 0, defending: 0,
        dribbling: 0, physicality: 0, goalkeeping: 0
      };
    }

    // 每升一星，所有属性增加基础值的4%
    const bonusRate = 0.04 * starDiff;

    return {
      speed: Math.round(hero.attributes.speed * bonusRate),
      shooting: Math.round(hero.attributes.shooting * bonusRate),
      passing: Math.round(hero.attributes.passing * bonusRate),
      defending: Math.round(hero.attributes.defending * bonusRate),
      dribbling: Math.round(hero.attributes.dribbling * bonusRate),
      physicality: Math.round(hero.attributes.physicality * bonusRate),
      goalkeeping: Math.round(hero.attributes.goalkeeping * bonusRate),
    };
  }

  /**
   * 获取升星所需材料信息
   */
  async getEvolutionRequirements(heroId: string): Promise<any> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      const currentStar = hero.evolution.star;

      return {
        heroId,
        currentStar,
        maxStar: 9,
        canEvolve: hero.canEvolve,
        successRate: hero.evolutionSuccessRate,
        requiredMaterialCount: hero.evolutionMaterialCount,
        goldCost: this.calculateEvolutionGoldCost(currentStar, false),
        goldCostWithProtection: this.calculateEvolutionGoldCost(currentStar, true),
        degradeRate: this.calculateDegradeRate(currentStar),
        nextStarBonus: this.calculateEvolutionAttributeBonus(hero, currentStar + 1),
      };
    } catch (error) {
      this.logger.error('获取升星需求失败', error);
      throw error;
    }
  }

  // ==================== 训练系统辅助方法 ====================

  /**
   * 执行通用训练（初级、中级、高级）
   */
  private async executeGeneralTraining(hero: any, trainDto: any, count: number): Promise<any> {
    const attributeChanges = {};
    const updateData = {};
    let goldCost = 0;
    let itemCost = [];
    let currentStage = 1;
    let reachedStageLimit = false;

    // 根据训练类型确定影响的属性
    const affectedAttributes = this.getAffectedAttributesByTrainingType(trainDto.trainingType);

    for (const attribute of affectedAttributes) {
      const stageInfo = hero.training[`${attribute}Stage`];
      const currentAttributeValue = hero.attributes[attribute];

      // 计算训练效果
      const trainingEffect = this.calculateTrainingEffect(
        trainDto.trainingType,
        stageInfo.stage,
        hero.quality,
        count
      );

      // 计算消耗
      const cost = this.calculateTrainingCost(trainDto.trainingType, trainDto.trainingMethod, stageInfo.stage, count);
      goldCost += cost.gold;
      itemCost = itemCost.concat(cost.items);

      // 更新属性值
      const newAttributeValue = Math.min(currentAttributeValue + trainingEffect.attributeIncrease, 100);

      attributeChanges[attribute] = {
        oldValue: currentAttributeValue,
        newValue: newAttributeValue,
        increase: trainingEffect.attributeIncrease,
      };

      // 更新阶段进度
      const newStageProgress = stageInfo.stageProgress + trainingEffect.stageProgress;
      const newStage = this.calculateNewStage(stageInfo.stage, newStageProgress);

      updateData[`attributes.${attribute}`] = newAttributeValue;
      updateData[`training.${attribute}Stage.stage`] = newStage.stage;
      updateData[`training.${attribute}Stage.stageProgress`] = newStage.progress;
      updateData[`training.${attribute}Stage.totalTrainingCount`] = stageInfo.totalTrainingCount + count;

      currentStage = Math.max(currentStage, newStage.stage);
      if (newStage.stage >= 9) {
        reachedStageLimit = true;
      }
    }

    return {
      attributeChanges,
      updateData,
      goldCost,
      itemCost,
      currentStage,
      reachedStageLimit,
    };
  }

  /**
   * 执行定向训练
   */
  private async executeTargetedTraining(hero: any, trainDto: any, count: number): Promise<any> {
    const attributeChanges = {};
    const updateData = {};
    let goldCost = 0;
    let itemCost = [];
    let currentStage = 1;
    let reachedStageLimit = false;

    // 定向训练只影响指定的属性
    const targetAttributes = trainDto.targetAttributes || ['speed']; // 默认训练速度

    for (const attribute of targetAttributes) {
      const stageInfo = hero.training[`${attribute}Stage`];
      const currentAttributeValue = hero.attributes[attribute];

      // 定向训练效果更强
      const trainingEffect = this.calculateTargetedTrainingEffect(
        stageInfo.stage,
        hero.quality,
        count
      );

      // 定向训练消耗更高
      const cost = this.calculateTargetedTrainingCost(trainDto.trainingMethod, stageInfo.stage, count);
      goldCost += cost.gold;
      itemCost = itemCost.concat(cost.items);

      // 更新属性值
      const newAttributeValue = Math.min(currentAttributeValue + trainingEffect.attributeIncrease, 100);

      attributeChanges[attribute] = {
        oldValue: currentAttributeValue,
        newValue: newAttributeValue,
        increase: trainingEffect.attributeIncrease,
      };

      // 更新阶段进度
      const newStageProgress = stageInfo.stageProgress + trainingEffect.stageProgress;
      const newStage = this.calculateNewStage(stageInfo.stage, newStageProgress);

      updateData[`attributes.${attribute}`] = newAttributeValue;
      updateData[`training.${attribute}Stage.stage`] = newStage.stage;
      updateData[`training.${attribute}Stage.stageProgress`] = newStage.progress;
      updateData[`training.${attribute}Stage.totalTrainingCount`] = stageInfo.totalTrainingCount + count;

      currentStage = Math.max(currentStage, newStage.stage);
      if (newStage.stage >= 9) {
        reachedStageLimit = true;
      }
    }

    return {
      attributeChanges,
      updateData,
      goldCost,
      itemCost,
      currentStage,
      reachedStageLimit,
    };
  }

  /**
   * 根据训练类型获取影响的属性
   */
  private getAffectedAttributesByTrainingType(trainingType: number): string[] {
    switch (trainingType) {
      case 1: // 初级训练 - 全属性小幅提升
        return ['speed', 'shooting', 'passing', 'defending', 'dribbling', 'physicality'];
      case 2: // 中级训练 - 主要属性中幅提升
        return ['speed', 'shooting', 'passing', 'defending'];
      case 3: // 高级训练 - 核心属性大幅提升
        return ['shooting', 'passing'];
      default:
        return ['speed'];
    }
  }

  /**
   * 计算训练效果
   */
  private calculateTrainingEffect(trainingType: number, stage: number, quality: number, count: number): any {
    // 基础效果根据训练类型和阶段计算
    const baseEffect = this.getBaseTrainingEffect(trainingType, stage);

    // 品质加成
    const qualityMultiplier = 1 + (quality - 1) * 0.1;

    // 次数加成
    const countMultiplier = count;

    const attributeIncrease = Math.round(baseEffect.attribute * qualityMultiplier * countMultiplier);
    const stageProgress = Math.round(baseEffect.progress * countMultiplier);

    return {
      attributeIncrease: Math.min(attributeIncrease, 10), // 单次最多增加10点
      stageProgress: Math.min(stageProgress, 100), // 单次最多增加100进度
    };
  }

  /**
   * 获取基础训练效果
   */
  private getBaseTrainingEffect(trainingType: number, stage: number): any {
    const stageMultiplier = Math.max(1, 10 - stage); // 阶段越高，效果递减

    switch (trainingType) {
      case 1: // 初级训练
        return {
          attribute: 1 * stageMultiplier,
          progress: 10 * stageMultiplier,
        };
      case 2: // 中级训练
        return {
          attribute: 2 * stageMultiplier,
          progress: 15 * stageMultiplier,
        };
      case 3: // 高级训练
        return {
          attribute: 3 * stageMultiplier,
          progress: 20 * stageMultiplier,
        };
      default:
        return { attribute: 1, progress: 10 };
    }
  }

  /**
   * 计算定向训练效果
   */
  private calculateTargetedTrainingEffect(stage: number, quality: number, count: number): any {
    // 定向训练效果是高级训练的1.5倍
    const baseEffect = this.getBaseTrainingEffect(3, stage);
    const qualityMultiplier = 1 + (quality - 1) * 0.1;
    const countMultiplier = count;
    const targetedMultiplier = 1.5;

    const attributeIncrease = Math.round(baseEffect.attribute * qualityMultiplier * countMultiplier * targetedMultiplier);
    const stageProgress = Math.round(baseEffect.progress * countMultiplier * targetedMultiplier);

    return {
      attributeIncrease: Math.min(attributeIncrease, 15), // 定向训练单次最多增加15点
      stageProgress: Math.min(stageProgress, 150), // 定向训练单次最多增加150进度
    };
  }

  /**
   * 计算训练消耗
   */
  private calculateTrainingCost(trainingType: number, trainingMethod: number, stage: number, count: number): any {
    const baseCost = this.getBaseTrainingCost(trainingType, stage);
    const countMultiplier = count;
    const stageMultiplier = Math.pow(1.2, stage - 1); // 阶段越高，消耗越大

    if (trainingMethod === 1) { // 使用金币
      return {
        gold: Math.round(baseCost.gold * countMultiplier * stageMultiplier),
        items: [],
      };
    } else { // 使用道具
      return {
        gold: 0,
        items: baseCost.items.map(item => ({
          ...item,
          quantity: item.quantity * countMultiplier,
        })),
      };
    }
  }

  /**
   * 获取基础训练消耗
   */
  private getBaseTrainingCost(trainingType: number, stage: number): any {
    switch (trainingType) {
      case 1: // 初级训练
        return {
          gold: 1000,
          items: [{ itemId: 'training_item_1', quantity: 1 }],
        };
      case 2: // 中级训练
        return {
          gold: 2000,
          items: [{ itemId: 'training_item_2', quantity: 1 }],
        };
      case 3: // 高级训练
        return {
          gold: 5000,
          items: [{ itemId: 'training_item_3', quantity: 1 }],
        };
      default:
        return { gold: 1000, items: [] };
    }
  }

  /**
   * 计算定向训练消耗
   */
  private calculateTargetedTrainingCost(trainingMethod: number, stage: number, count: number): any {
    const baseCost = this.getBaseTrainingCost(3, stage); // 基于高级训练
    const countMultiplier = count;
    const stageMultiplier = Math.pow(1.3, stage - 1); // 定向训练消耗更高
    const targetedMultiplier = 2; // 定向训练消耗是高级训练的2倍

    if (trainingMethod === 1) { // 使用金币
      return {
        gold: Math.round(baseCost.gold * countMultiplier * stageMultiplier * targetedMultiplier),
        items: [],
      };
    } else { // 使用道具
      return {
        gold: 0,
        items: [{ itemId: 'targeted_training_item', quantity: count }],
      };
    }
  }

  /**
   * 计算新的阶段和进度
   */
  private calculateNewStage(currentStage: number, newProgress: number): any {
    let stage = currentStage;
    let progress = newProgress;

    // 每100进度升一阶段
    while (progress >= 100 && stage < 9) {
      progress -= 100;
      stage++;
    }

    // 达到最高阶段时，进度不再增加
    if (stage >= 9) {
      progress = 0;
    }

    return { stage, progress };
  }

  /**
   * 计算训练冷却时间
   */
  private calculateTrainingCooldown(trainingType: number): number {
    switch (trainingType) {
      case 1: // 初级训练 - 10分钟
        return 10 * 60 * 1000;
      case 2: // 中级训练 - 20分钟
        return 20 * 60 * 1000;
      case 3: // 高级训练 - 30分钟
        return 30 * 60 * 1000;
      case 4: // 定向训练 - 60分钟
        return 60 * 60 * 1000;
      default:
        return 30 * 60 * 1000;
    }
  }

  // ==================== 球员状态管理系统 ====================

  /**
   * 增加球员疲劳值
   */
  async addHeroFatigue(heroId: string, fatigueValue: number): Promise<void> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      const newFatigue = Math.min(hero.fatigue + fatigueValue, 100);
      const newFatigueRatio = this.calculateFatigueRatio(newFatigue);

      await this.heroRepository.update(heroId, {
        fatigue: newFatigue,
        fatigueRatio: newFatigueRatio,
      });

      this.logger.log(`球员疲劳增加: ${heroId}, 疲劳值: ${hero.fatigue} -> ${newFatigue}`);
    } catch (error) {
      this.logger.error('增加球员疲劳失败', error);
      throw error;
    }
  }

  /**
   * 恢复球员疲劳值
   */
  async recoverHeroFatigue(heroId: string, recoveryValue?: number): Promise<void> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      // 如果没有指定恢复值，则完全恢复
      const recovery = recoveryValue !== undefined ? recoveryValue : hero.fatigue;
      const newFatigue = Math.max(hero.fatigue - recovery, 0);
      const newFatigueRatio = this.calculateFatigueRatio(newFatigue);

      await this.heroRepository.update(heroId, {
        fatigue: newFatigue,
        fatigueRatio: newFatigueRatio,
        reTimeFatigue: newFatigue > 0 ? Date.now() + (newFatigue * 60 * 1000) : 0, // 每点疲劳需要1分钟恢复
      });

      this.logger.log(`球员疲劳恢复: ${heroId}, 疲劳值: ${hero.fatigue} -> ${newFatigue}`);
    } catch (error) {
      this.logger.error('恢复球员疲劳失败', error);
      throw error;
    }
  }

  /**
   * 治疗球员
   */
  async treatHero(heroId: string, treatmentType: 'quick' | 'normal' | 'full' = 'normal'): Promise<any> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      if (hero.isTreat) {
        throw new BadRequestException({
          code: ErrorCode.HERO_IN_TREATMENT,
          message: ErrorMessages[ErrorCode.HERO_IN_TREATMENT],
        });
      }

      const treatmentInfo = this.calculateTreatmentInfo(treatmentType, hero.fatigue);

      await this.heroRepository.update(heroId, {
        isTreat: true,
        fatigue: 0,
        fatigueRatio: 1.0,
        reTimeFatigue: Date.now() + treatmentInfo.duration,
      });

      // 设置治疗完成的定时任务
      setTimeout(async () => {
        await this.heroRepository.update(heroId, {
          isTreat: false,
          reTimeFatigue: 0,
        });
        this.logger.log(`球员治疗完成: ${heroId}`);
      }, treatmentInfo.duration);

      this.logger.log(`球员开始治疗: ${heroId}, 类型: ${treatmentType}, 持续时间: ${treatmentInfo.duration}ms`);

      return {
        heroId,
        treatmentType,
        duration: treatmentInfo.duration,
        cost: treatmentInfo.cost,
        completionTime: Date.now() + treatmentInfo.duration,
      };
    } catch (error) {
      this.logger.error('治疗球员失败', error);
      throw error;
    }
  }

  /**
   * 续约球员
   */
  async renewHeroContract(heroId: string, contractDays: number): Promise<any> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      // 检查是否可以续约
      if (hero.contractDays > 30) {
        throw new BadRequestException({
          code: ErrorCode.CONTRACT_NOT_EXPIRED,
          message: ErrorMessages[ErrorCode.CONTRACT_NOT_EXPIRED],
        });
      }

      const renewalCost = this.calculateRenewalCost(hero.level, hero.quality, contractDays);
      const newContractDays = hero.contractDays + contractDays;
      const newLifeNum = hero.lifeNum + 1;

      await this.heroRepository.update(heroId, {
        contractDays: newContractDays,
        lifeNum: newLifeNum,
        treatyReTime: Date.now(),
      });

      this.logger.log(`球员续约成功: ${heroId}, 新合约天数: ${newContractDays}, 生涯次数: ${newLifeNum}`);

      return {
        heroId,
        oldContractDays: hero.contractDays,
        newContractDays,
        renewalDays: contractDays,
        cost: renewalCost,
        lifeNum: newLifeNum,
      };
    } catch (error) {
      this.logger.error('球员续约失败', error);
      throw error;
    }
  }

  /**
   * 球员退役检查
   */
  async checkHeroRetirement(heroId: string): Promise<boolean> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        return false;
      }

      // 退役条件：合约到期且生涯次数达到上限
      const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
      const shouldRetire = hero.contractDays <= 0 && hero.lifeNum >= maxLifeNum;

      if (shouldRetire) {
        await this.heroRepository.update(heroId, {
          isRetired: true,
          retirementTime: Date.now(),
        });
        this.logger.log(`球员退役: ${heroId}, 生涯次数: ${hero.lifeNum}`);
      }

      return shouldRetire;
    } catch (error) {
      this.logger.error('检查球员退役失败', error);
      return false;
    }
  }

  // ==================== 状态管理辅助方法 ====================

  /**
   * 计算疲劳衰减系数
   */
  private calculateFatigueRatio(fatigue: number): number {
    // 疲劳值越高，属性衰减越严重
    // 0疲劳 = 1.0倍属性，100疲劳 = 0.5倍属性
    return Math.max(0.5, 1.0 - (fatigue * 0.005));
  }

  /**
   * 计算治疗信息
   */
  private calculateTreatmentInfo(treatmentType: string, fatigue: number): any {
    switch (treatmentType) {
      case 'quick': // 快速治疗 - 5分钟，费用高
        return {
          duration: 5 * 60 * 1000,
          cost: { gold: 5000, items: [] },
        };
      case 'normal': // 普通治疗 - 根据疲劳值计算时间
        return {
          duration: fatigue * 60 * 1000, // 每点疲劳1分钟
          cost: { gold: fatigue * 100, items: [] },
        };
      case 'full': // 完全治疗 - 立即恢复，费用最高
        return {
          duration: 0,
          cost: { gold: 10000, items: [{ itemId: 'healing_potion', quantity: 1 }] },
        };
      default:
        return {
          duration: fatigue * 60 * 1000,
          cost: { gold: fatigue * 100, items: [] },
        };
    }
  }

  /**
   * 计算续约费用
   */
  private calculateRenewalCost(level: number, quality: number, contractDays: number): any {
    const baseCost = level * quality * 100;
    const daysCost = contractDays * 50;
    const totalGold = baseCost + daysCost;

    return {
      gold: totalGold,
      items: [],
    };
  }

  /**
   * 计算最大生涯次数
   */
  private calculateMaxLifeNum(quality: number): number {
    // 品质越高，可续约次数越多
    switch (quality) {
      case 1: return 3;  // 白色品质最多3次
      case 2: return 4;  // 绿色品质最多4次
      case 3: return 5;  // 蓝色品质最多5次
      case 4: return 6;  // 紫色品质最多6次
      case 5: return 8;  // 橙色品质最多8次
      case 6: return 10; // 红色品质最多10次
      default: return 3;
    }
  }

  /**
   * 获取球员状态信息
   */
  async getHeroStatus(heroId: string): Promise<any> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      const currentTime = Date.now();
      const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
      const canRenew = hero.contractDays <= 30 && hero.lifeNum < maxLifeNum;
      const willRetire = hero.contractDays <= 0 && hero.lifeNum >= maxLifeNum;

      return {
        heroId,
        fatigue: hero.fatigue,
        fatigueRatio: hero.fatigueRatio,
        isTreat: hero.isTreat,
        isTrain: hero.isTrain,
        isLocked: hero.isLocked,
        isInFormation: hero.isInFormation,
        contractDays: hero.contractDays,
        lifeNum: hero.lifeNum,
        maxLifeNum,
        canRenew,
        willRetire,
        treatmentTimeRemaining: hero.isTreat ? Math.max(0, hero.reTimeFatigue - currentTime) : 0,
        fatigueRecoveryTime: hero.fatigue > 0 ? hero.reTimeFatigue : 0,
        energy: hero.energy,
        morale: hero.morale,
        battleNum: hero.battleNum,
        marketValue: hero.marketValue,
        isOnMarket: hero.isOnMarket,
      };
    } catch (error) {
      this.logger.error('获取球员状态失败', error);
      throw error;
    }
  }

  // ==================== 突破系统辅助方法 ====================

  /**
   * 计算当前潜能值
   */
  private calculateCurrentPotential(breakthrough: number[]): number {
    return breakthrough.reduce((sum, value) => sum + value, 0);
  }

  /**
   * 计算突破费用
   */
  private calculateBreakthroughCost(currentCount: number): number {
    // 基础费用随突破次数递增
    return 10000 * Math.pow(1.5, currentCount);
  }

  /**
   * 计算撤销费用
   */
  private calculateRevertCost(): number {
    // 撤销费用固定
    return 50000;
  }

  /**
   * 计算突破值（基于品质和权重）
   */
  private calculateBreakthroughValue(quality: number, breakthroughLayer: number): number {
    // 根据品质确定权重ID
    const weightId = this.getBreakthroughWeightId(quality);

    // 根据权重和层数计算突破值
    return this.calculateWeightedBreakthroughValue(weightId, breakthroughLayer);
  }

  /**
   * 根据品质获取突破权重ID
   */
  private getBreakthroughWeightId(quality: number): number {
    // 品质越高，获得高突破值的概率越大
    switch (quality) {
      case 1: return 1; // 白色品质
      case 2: return 2; // 绿色品质
      case 3: return 3; // 蓝色品质
      case 4: return 4; // 紫色品质
      case 5: return 5; // 橙色品质
      case 6: return 6; // 红色品质
      default: return 1;
    }
  }

  /**
   * 根据权重计算突破值
   */
  private calculateWeightedBreakthroughValue(weightId: number, layer: number): number {
    // 模拟old项目中的权重配置
    const weightConfigs = {
      1: { // 白色品质权重
        1: [10, 15, 20, 25, 15, 10, 5], // 1-7的权重
        2: [15, 20, 25, 20, 10, 7, 3],
        3: [20, 25, 25, 15, 8, 5, 2],
      },
      2: { // 绿色品质权重
        1: [8, 12, 18, 25, 20, 12, 5],
        2: [10, 15, 20, 25, 18, 10, 2],
        3: [15, 20, 25, 20, 12, 6, 2],
      },
      3: { // 蓝色品质权重
        1: [5, 10, 15, 25, 25, 15, 5],
        2: [8, 12, 18, 25, 22, 12, 3],
        3: [10, 15, 20, 25, 20, 8, 2],
      },
      4: { // 紫色品质权重
        1: [3, 8, 12, 22, 28, 20, 7],
        2: [5, 10, 15, 25, 25, 15, 5],
        3: [8, 12, 18, 25, 22, 12, 3],
      },
      5: { // 橙色品质权重
        1: [2, 5, 10, 20, 30, 25, 8],
        2: [3, 8, 12, 22, 28, 20, 7],
        3: [5, 10, 15, 25, 25, 15, 5],
      },
      6: { // 红色品质权重
        1: [1, 3, 8, 18, 32, 28, 10],
        2: [2, 5, 10, 20, 30, 25, 8],
        3: [3, 8, 12, 22, 28, 20, 7],
      },
    };

    const layerWeights = weightConfigs[weightId]?.[Math.min(layer, 3)] || weightConfigs[1][1];

    // 根据权重随机选择突破值
    const totalWeight = layerWeights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;

    for (let i = 0; i < layerWeights.length; i++) {
      random -= layerWeights[i];
      if (random <= 0) {
        return i + 1; // 返回1-7的值
      }
    }

    return 1; // 默认返回1
  }

  /**
   * 计算属性上限
   */
  private calculateAttributeLimits(resId: number, potential: number): any {
    // 基于潜能值计算属性上限提升
    // 潜能值越高，属性上限越高
    const potentialMultiplier = potential / 10; // 每10点潜能提升1倍

    return {
      speed: Math.round(80 + potentialMultiplier * 20),
      shooting: Math.round(80 + potentialMultiplier * 20),
      passing: Math.round(80 + potentialMultiplier * 20),
      defending: Math.round(80 + potentialMultiplier * 20),
      dribbling: Math.round(80 + potentialMultiplier * 20),
      physicality: Math.round(80 + potentialMultiplier * 20),
      goalkeeping: Math.round(80 + potentialMultiplier * 20),
    };
  }

  /**
   * 获取突破信息
   */
  async getBreakthroughInfo(heroId: string): Promise<any> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      const currentPotential = this.calculateCurrentPotential(hero.breakthrough);
      const canBreakthrough = hero.breakthrough.length < 10;
      const canRevert = hero.breakthrough.length > 0 && hero.breakthrough.some(value => value !== 7);

      return {
        heroId,
        breakthroughCount: hero.breakthrough.length,
        breakthroughHistory: hero.breakthrough,
        currentPotential,
        maxPotential: 70, // 10次 * 7点
        oldBreakOut: hero.oldBreakOut,
        canBreakthrough,
        canRevert,
        breakthroughCost: canBreakthrough ? this.calculateBreakthroughCost(hero.breakthrough.length) : 0,
        revertCost: canRevert ? this.calculateRevertCost() : 0,
        attributeLimits: this.calculateAttributeLimits(hero.resId, currentPotential),
      };
    } catch (error) {
      this.logger.error('获取突破信息失败', error);
      throw error;
    }
  }

  // ==================== 属性重新计算系统 ====================

  /**
   * 重新计算球员所有属性（核心方法）
   * 对应old项目中的reCalcAttrRevision方法
   */
  async reCalcHeroAttributes(heroId: string): Promise<void> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      // 1. 计算图鉴加成（教练加成）
      const coachBonus = this.calculateCoachBonus(hero);

      // 2. 获取球员所在阵容信息
      const formationInfo = await this.getHeroFormationInfo(heroId);

      // 3. 计算阵容相关加成
      let formationBonus = { attributes: {}, tactics: {}, trainer: {} };
      if (formationInfo.isInMainFormation) {
        formationBonus = await this.calculateFormationBonus(heroId, formationInfo.teamUid);
      }

      // 4. 计算信仰技能加成
      const beliefSkillBonus = this.calculateBeliefSkillBonus(hero);

      // 5. 计算一级属性（基础属性）
      const primaryAttributes = this.calculatePrimaryAttributes(hero, {
        coach: coachBonus,
        formation: formationBonus.attributes,
        beliefSkill: beliefSkillBonus,
      });

      // 6. 计算二级属性（综合属性）
      const secondaryAttributes = this.calculateSecondaryAttributes(hero, primaryAttributes);

      // 7. 更新球员属性
      await this.heroRepository.update(heroId, {
        'attributes.speed': primaryAttributes.speed,
        'attributes.shooting': primaryAttributes.shooting,
        'attributes.passing': primaryAttributes.passing,
        'attributes.defending': primaryAttributes.defending,
        'attributes.dribbling': primaryAttributes.dribbling,
        'attributes.physicality': primaryAttributes.physicality,
        'attributes.goalkeeping': primaryAttributes.goalkeeping,
        'secondaryAttributes': secondaryAttributes,
        'lastAttributeCalculation': Date.now(),
      });

      this.logger.log(`球员属性重新计算完成: ${heroId}`);
    } catch (error) {
      this.logger.error('重新计算球员属性失败', error);
      throw error;
    }
  }

  /**
   * 批量重新计算所有球员属性
   */
  async reCalcAllHeroAttributes(characterId: string): Promise<void> {
    try {
      const heroes = await this.heroRepository.findByCharacterId(characterId);

      for (const hero of heroes) {
        await this.reCalcHeroAttributes(hero.heroId);
      }

      this.logger.log(`角色所有球员属性重新计算完成: ${characterId}, 共${heroes.length}个球员`);
    } catch (error) {
      this.logger.error('批量重新计算球员属性失败', error);
      throw error;
    }
  }

  /**
   * 获取角色球员统计信息
   * 基于old项目: 统计角色的球员数据
   */
  async getCharacterHeroStats(characterId: string): Promise<any> {
    try {
      this.logger.log(`获取角色球员统计: ${characterId}`);

      // 获取角色所有球员
      const heroes = await this.heroRepository.findByCharacterId(characterId);

      if (heroes.length === 0) {
        return {
          totalHeroes: 0,
          averageLevel: 0,
          maxLevel: 0,
          totalMarketValue: 0,
          heroesInFormation: 0,
          heroesOnMarket: 0,
          averageRating: 0,
          totalPower: 0,
        };
      }

      // 计算统计数据
      const totalHeroes = heroes.length;
      const totalLevel = heroes.reduce((sum, hero) => sum + (hero.level || 1), 0);
      const averageLevel = Math.round(totalLevel / totalHeroes);
      const maxLevel = Math.max(...heroes.map(hero => hero.level || 1));

      // 计算总市场价值
      const totalMarketValue = heroes.reduce((sum, hero) => sum + (hero.marketValue || 0), 0);

      // 计算平均评分
      const totalRating = heroes.reduce((sum, hero) => sum + (hero.averageRating || 0), 0);
      const averageRating = totalHeroes > 0 ? Math.round(totalRating / totalHeroes) : 0;

      // 计算总实力
      const totalPower = heroes.reduce((sum, hero) => sum + (hero.totalPower || 0), 0);

      // TODO: 统计阵容中的球员数量（需要调用Character服务）
      const heroesInFormation = 0;

      // TODO: 统计市场上的球员数量（需要调用相关服务）
      const heroesOnMarket = 0;

      const stats = {
        totalHeroes,
        averageLevel,
        maxLevel,
        totalMarketValue,
        heroesInFormation,
        heroesOnMarket,
        averageRating,
        totalPower,
      };

      this.logger.debug(`角色球员统计完成: ${characterId}`, stats);
      return stats;
    } catch (error) {
      this.logger.error('获取角色球员统计失败', error);
      throw error;
    }
  }

  /**
   * 计算教练图鉴加成
   * 基于old项目: calcCoachAttr + checkHeroHandbookAttr逻辑
   *
   * 实现逻辑：
   * 1. 获取角色的图鉴收集数据
   * 2. 计算单个球员图鉴加成
   * 3. 计算组合图鉴加成
   * 4. 累加所有图鉴加成
   */
  private async calculateCoachBonus(hero: any): Promise<any> {
    try {
      // 1. 获取角色的图鉴收集数据
      const handbookData = await this.getCharacterHandbookData(hero.characterId);
      if (!handbookData) {
        return this.getEmptyAttributeBonus();
      }

      let totalBonus = this.getEmptyAttributeBonus();

      // 2. 计算单个球员图鉴加成（基于old项目PlayerHandbook配置）
      if (handbookData.playerHandbook && handbookData.playerHandbook.length > 0) {
        for (const resId of handbookData.playerHandbook) {
          const playerConfig = await this.gameConfig.playerHandbook?.get(resId);
          if (playerConfig) {
            // 基于old项目：直接累加各属性值
            totalBonus.speed += playerConfig.speed || 0;
            totalBonus.shooting += playerConfig.finishing || 0;
            totalBonus.passing += playerConfig.passing || 0;
            totalBonus.defending += playerConfig.standingTackle || 0;
            totalBonus.dribbling += playerConfig.dribbling || 0;
            totalBonus.physicality += playerConfig.strength || 0;
            totalBonus.goalkeeping += playerConfig.save || 0;
          }
        }
      }

      // 3. 计算组合图鉴加成（基于old项目CombinationHandbook配置）
      if (handbookData.combinationHandbook && handbookData.combinationHandbook.length > 0) {
        for (const comboId of handbookData.combinationHandbook) {
          const comboConfig = await this.gameConfig.combinationHandbook?.get(comboId);
          if (comboConfig) {
            // 基于old项目：组合图鉴提供额外加成
            totalBonus.speed += comboConfig.speed || 0;
            totalBonus.shooting += comboConfig.finishing || 0;
            totalBonus.passing += comboConfig.passing || 0;
            totalBonus.defending += comboConfig.standingTackle || 0;
            totalBonus.dribbling += comboConfig.dribbling || 0;
            totalBonus.physicality += comboConfig.strength || 0;
            totalBonus.goalkeeping += comboConfig.save || 0;
          }
        }
      }

      this.logger.debug(`教练图鉴加成计算完成: ${hero._id}`, totalBonus);
      return totalBonus;
    } catch (error) {
      this.logger.error('计算教练图鉴加成失败', error);
      return this.getEmptyAttributeBonus();
    }
  }

  /**
   * 获取球员阵容信息
   * 基于old项目: getHeroInMainTeamUid
   */
  private async getHeroFormationInfo(heroId: string): Promise<any> {
    try {
      // 调用Character服务获取球员阵容信息（基于old项目getHeroInMainTeamUid）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'formation.getHeroInMainTeamUid',
        { heroId }
      );

      if (!result || result.code !== 0) {
        this.logger.debug(`球员不在主力阵容中: ${heroId}`);
        return {
          isInMainFormation: false,
          teamUid: null,
          position: null,
        };
      }

      const formationData = result.data || {};
      return {
        isInMainFormation: !!formationData.teamUid,
        teamUid: formationData.teamUid,
        position: formationData.position,
      };
    } catch (error) {
      this.logger.error('获取球员阵容信息失败', error);
      return {
        isInMainFormation: false,
        teamUid: null,
        position: null,
      };
    }
  }

  /**
   * 计算阵容相关加成
   * 基于old项目: calcHeroTrainerAttr + calcHeroTacticsAttr + calcTrainerSkillAttr逻辑
   *
   * 实现逻辑：
   * 1. 计算教练属性加成
   * 2. 计算战术加成
   * 3. 计算教练技能加成
   * 4. 返回综合加成结果
   */
  private async calculateFormationBonus(heroId: string, teamUid: string): Promise<any> {
    try {
      // 1. 计算教练属性加成（基于old项目calcHeroTrainerAttr）
      const trainerBonus = await this.calculateTrainerAttributeBonus(heroId, teamUid);

      // 2. 计算战术加成（基于old项目calcHeroTacticsAttr）
      const tacticsBonus = await this.calculateTacticsBonus(heroId, teamUid);

      // 3. 计算教练技能加成（基于old项目calcTrainerSkillAttr）
      const trainerSkillBonus = await this.calculateTrainerSkillBonus(heroId, teamUid);

      return {
        attributes: {
          speed: trainerBonus.speed + trainerSkillBonus.speed,
          shooting: trainerBonus.shooting + trainerSkillBonus.shooting,
          passing: trainerBonus.passing + trainerSkillBonus.passing,
          defending: trainerBonus.defending + trainerSkillBonus.defending,
          dribbling: trainerBonus.dribbling + trainerSkillBonus.dribbling,
          physicality: trainerBonus.physicality + trainerSkillBonus.physicality,
          goalkeeping: trainerBonus.goalkeeping + trainerSkillBonus.goalkeeping,
        },
        tactics: {
          attack: tacticsBonus.attack,
          defend: tacticsBonus.defend,
        },
        trainer: trainerBonus,
      };
    } catch (error) {
      this.logger.error('计算阵容相关加成失败', error);
      return {
        attributes: this.getEmptyAttributeBonus(),
        tactics: { attack: 0, defend: 0 },
        trainer: this.getEmptyAttributeBonus(),
      };
    }
  }

  /**
   * 计算信仰技能加成
   * 基于old项目: reCalcBeliefSkillAttr逻辑
   *
   * 实现逻辑：
   * 1. 获取角色的信仰技能列表
   * 2. 检查每个技能是否解锁和激活
   * 3. 检查球员是否享有该技能加成
   * 4. 累加所有有效的技能加成
   */
  private async calculateBeliefSkillBonus(hero: any): Promise<any> {
    try {
      // 1. 获取角色的信仰技能数据
      const beliefSkillData = await this.getCharacterBeliefSkillData(hero.characterId);
      if (!beliefSkillData || !beliefSkillData.skillList) {
        return this.getEmptyAttributeBonus();
      }

      let totalBonus = this.getEmptyAttributeBonus();

      // 2. 遍历所有信仰技能
      for (const skill of beliefSkillData.skillList) {
        // 检查技能是否激活（status === 1）
        if (skill.status !== 1) {
          continue;
        }

        // 3. 检查球员是否享有该技能加成
        const hasSkillBonus = await this.checkHeroHasBeliefSkillBonus(hero, skill.skillId);
        if (!hasSkillBonus) {
          continue;
        }

        // 4. 获取技能配置并计算加成
        const skillConfig = await this.gameConfig.beliefSkill?.get(skill.skillId);
        if (skillConfig) {
          // 基于old项目：技能等级影响加成值
          const skillLevel = skill.level || 1;
          const bonusMultiplier = skillLevel; // 简化处理，实际可能有更复杂的公式

          // 累加技能加成（基于old项目的属性映射）
          totalBonus.speed += (skillConfig.speed || 0) * bonusMultiplier;
          totalBonus.shooting += (skillConfig.finishing || 0) * bonusMultiplier;
          totalBonus.passing += (skillConfig.passing || 0) * bonusMultiplier;
          totalBonus.defending += (skillConfig.standingTackle || 0) * bonusMultiplier;
          totalBonus.dribbling += (skillConfig.dribbling || 0) * bonusMultiplier;
          totalBonus.physicality += (skillConfig.strength || 0) * bonusMultiplier;
          totalBonus.goalkeeping += (skillConfig.save || 0) * bonusMultiplier;
        }
      }

      this.logger.debug(`信仰技能加成计算完成: ${hero._id}`, totalBonus);
      return totalBonus;
    } catch (error) {
      this.logger.error('计算信仰技能加成失败', error);
      return this.getEmptyAttributeBonus();
    }
  }

  /**
   * 获取角色图鉴收集数据
   * 基于old项目: player.footballGround.handbookData
   */
  private async getCharacterHandbookData(characterId: string): Promise<any> {
    try {
      // 调用Character服务获取图鉴数据（基于old项目player.footballGround.getBallHandbook）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        { characterId }
      );

      if (!result || result.code !== 0 || !result.data) {
        this.logger.warn(`获取角色信息失败: ${characterId}`);
        return {
          playerHandbook: [],
          combinationHandbook: [],
        };
      }

      // 从角色数据中提取图鉴信息
      const footballGroundData = result.data.footballGround || {};

      return {
        playerHandbook: footballGroundData.ballHandbook || [],        // 已收集的球员图鉴
        combinationHandbook: footballGroundData.ballComHandbook || [], // 已激活的组合图鉴
        ballActual: footballGroundData.ballActual || 0,               // 球员实力加成
        ballComActual: footballGroundData.ballComActual || 0,         // 组合实力加成
      };
    } catch (error) {
      this.logger.error('获取角色图鉴数据失败', error);
      return {
        playerHandbook: [],
        combinationHandbook: [],
        ballActual: 0,
        ballComActual: 0,
      };
    }
  }

  /**
   * 获取角色信仰技能数据
   * 基于old项目: player.beliefSkill.skillList
   */
  private async getCharacterBeliefSkillData(characterId: string): Promise<any> {
    try {
      // TODO: 调用Character服务获取信仰技能数据
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.getBeliefSkillData',
      //   { characterId }
      // );
      // return result.data;

      // 暂时返回模拟数据
      return {
        skillList: [
          { skillId: 1001, status: 1, level: 2 },
          { skillId: 1002, status: 1, level: 1 },
          { skillId: 1003, status: 0, level: 0 },
        ],
      };
    } catch (error) {
      this.logger.error('获取角色信仰技能数据失败', error);
      return null;
    }
  }

  /**
   * 检查球员是否享有信仰技能加成
   * 基于old项目: checkHeroIsHaveBeliefSkillAttr逻辑
   *
   * 实现逻辑：
   * 1. 获取技能配置中的位置要求
   * 2. 获取球员配置中的位置信息
   * 3. 检查位置是否匹配
   */
  private async checkHeroHasBeliefSkillBonus(hero: any, skillId: number): Promise<boolean> {
    try {
      // 1. 获取信仰技能配置
      const skillConfig = await this.gameConfig.beliefSkill?.get(skillId);
      if (!skillConfig) {
        return false;
      }

      // 2. 获取球员配置
      const heroConfig = await this.getHeroDefinitionConfig(hero.resId);
      if (!heroConfig) {
        return false;
      }

      // 3. 检查位置匹配（基于old项目逻辑）
      const heroPosition = heroConfig.position1; // 球员主位置

      // 检查技能的AddPosition1-AddPosition12字段
      for (let i = 1; i <= 12; i++) {
        const addPositionField = `addPosition${i}`;
        const skillPosition = skillConfig[addPositionField];

        if (heroPosition === skillPosition) {
          return true;
        }
      }

      return false;
    } catch (error) {
      this.logger.error('检查球员信仰技能加成失败', error);
      return false;
    }
  }

  /**
   * 获取球员配置
   * 基于old项目: Footballer或FootballerPve配置表
   */
  private async getHeroDefinitionConfig(resId: number): Promise<any> {
    try {
      // 基于old项目逻辑：resId/10000 < 9 使用Hero，否则使用HeroPve
      const bead = Math.floor(resId / 10000);

      if (bead < 9) {
        return await this.gameConfig.hero?.get(resId);
      } else {
        return await this.gameConfig.heroPve?.get(resId);
      }
    } catch (error) {
      this.logger.error('获取球员配置失败', error);
      return null;
    }
  }

  /**
   * 计算教练属性加成
   * 基于old项目: calcHeroTrainerAttr逻辑
   */
  private async calculateTrainerAttributeBonus(heroId: string, teamUid: string): Promise<any> {
    try {
      // 调用Character服务获取阵容教练数据（基于old项目calcHeroTrainerAttr）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'formation.calcHeroTrainerAttr',
        { heroId, teamUid }
      );

      if (!result || result.code !== 0 || !result.data) {
        this.logger.debug(`获取教练属性加成失败: ${heroId}, ${teamUid}`);
        return this.getEmptyAttributeBonus();
      }

      // 转换old项目属性格式到新格式
      return this.convertOldAttributesToNew(result.data);
    } catch (error) {
      this.logger.error('计算教练属性加成失败', error);
      return this.getEmptyAttributeBonus();
    }
  }

  /**
   * 计算战术加成
   * 基于old项目: calcHeroTacticsAttr逻辑
   */
  private async calculateTacticsBonus(heroId: string, teamUid: string): Promise<any> {
    try {
      // 调用Character服务获取战术加成（基于old项目calcHeroTacticsAttr）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'formation.calcHeroTacticsAttr',
        { heroId, teamUid }
      );

      if (!result || result.code !== 0 || !result.data) {
        this.logger.debug(`获取战术加成失败: ${heroId}, ${teamUid}`);
        return { attack: 0, defend: 0 };
      }

      return {
        attack: result.data.attack || 0,
        defend: result.data.defend || 0,
      };
    } catch (error) {
      this.logger.error('计算战术加成失败', error);
      return { attack: 0, defend: 0 };
    }
  }

  /**
   * 计算教练技能加成
   * 基于old项目: calcTrainerSkillAttr逻辑
   */
  private async calculateTrainerSkillBonus(heroId: string, teamUid: string): Promise<any> {
    try {
      // 调用Character服务获取教练技能加成（基于old项目calcTrainerSkillAttr）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'formation.calcTrainerSkillAttr',
        { heroId, teamUid }
      );

      if (!result || result.code !== 0 || !result.data) {
        this.logger.debug(`获取教练技能加成失败: ${heroId}, ${teamUid}`);
        return this.getEmptyAttributeBonus();
      }

      // 转换old项目属性格式到新格式
      return this.convertOldAttributesToNew(result.data);
    } catch (error) {
      this.logger.error('计算教练技能加成失败', error);
      return this.getEmptyAttributeBonus();
    }
  }

  /**
   * 获取空的属性加成对象
   */
  private getEmptyAttributeBonus(): any {
    return {
      speed: 0,
      shooting: 0,
      passing: 0,
      defending: 0,
      dribbling: 0,
      physicality: 0,
      goalkeeping: 0,
    };
  }

  /**
   * 计算一级属性（基础属性）
   */
  private calculatePrimaryAttributes(hero: any, bonuses: any): any {
    const baseAttributes = hero.attributes;
    const starBonus = this.calculateStarAttributeBonus(hero);
    const trainingBonus = this.calculateTrainingAttributeBonus(hero);
    const breakthroughBonus = this.calculateBreakthroughAttributeBonus(hero);

    const result = {};
    const attributeKeys = ['speed', 'shooting', 'passing', 'defending', 'dribbling', 'physicality', 'goalkeeping'];

    for (const attr of attributeKeys) {
      result[attr] = Math.round(
        baseAttributes[attr] +
        starBonus[attr] +
        trainingBonus[attr] +
        breakthroughBonus[attr] +
        bonuses.coach[attr] +
        bonuses.formation[attr] +
        bonuses.beliefSkill[attr]
      );
    }

    return result;
  }

  /**
   * 计算二级属性（综合属性）
   */
  private calculateSecondaryAttributes(hero: any, primaryAttributes: any): any {
    // 根据位置计算综合评分
    const position = hero.position;
    let overallRating = 0;

    switch (position) {
      case 1: // 守门员
        overallRating = Math.round(
          primaryAttributes.goalkeeping * 0.6 +
          primaryAttributes.defending * 0.2 +
          primaryAttributes.physicality * 0.2
        );
        break;
      case 2:
      case 3:
      case 4:
      case 5: // 后卫
        overallRating = Math.round(
          primaryAttributes.defending * 0.4 +
          primaryAttributes.physicality * 0.3 +
          primaryAttributes.passing * 0.2 +
          primaryAttributes.speed * 0.1
        );
        break;
      case 6:
      case 7:
      case 8:
      case 9: // 中场
        overallRating = Math.round(
          primaryAttributes.passing * 0.3 +
          primaryAttributes.dribbling * 0.25 +
          primaryAttributes.speed * 0.2 +
          primaryAttributes.shooting * 0.15 +
          primaryAttributes.defending * 0.1
        );
        break;
      case 10:
      case 11: // 前锋
        overallRating = Math.round(
          primaryAttributes.shooting * 0.4 +
          primaryAttributes.dribbling * 0.3 +
          primaryAttributes.speed * 0.2 +
          primaryAttributes.physicality * 0.1
        );
        break;
      default:
        overallRating = Math.round(
          (primaryAttributes.speed +
           primaryAttributes.shooting +
           primaryAttributes.passing +
           primaryAttributes.defending +
           primaryAttributes.dribbling +
           primaryAttributes.physicality) / 6
        );
    }

    return {
      overallRating,
      attackRating: Math.round((primaryAttributes.shooting + primaryAttributes.dribbling + primaryAttributes.speed) / 3),
      defenseRating: Math.round((primaryAttributes.defending + primaryAttributes.physicality) / 2),
      midFieldRating: Math.round((primaryAttributes.passing + primaryAttributes.dribbling) / 2),
    };
  }

  /**
   * 计算升星属性加成
   */
  private calculateStarAttributeBonus(hero: any): any {
    const starLevel = hero.evolution?.star || 0;
    const baseAttributes = hero.attributes;
    const bonusRate = starLevel * 0.04; // 每星级4%加成

    return {
      speed: Math.round(baseAttributes.speed * bonusRate),
      shooting: Math.round(baseAttributes.shooting * bonusRate),
      passing: Math.round(baseAttributes.passing * bonusRate),
      defending: Math.round(baseAttributes.defending * bonusRate),
      dribbling: Math.round(baseAttributes.dribbling * bonusRate),
      physicality: Math.round(baseAttributes.physicality * bonusRate),
      goalkeeping: Math.round(baseAttributes.goalkeeping * bonusRate),
    };
  }

  /**
   * 计算训练属性加成
   */
  private calculateTrainingAttributeBonus(hero: any): any {
    // 基于训练阶段计算属性加成
    const training = hero.training;
    if (!training) {
      return { speed: 0, shooting: 0, passing: 0, defending: 0, dribbling: 0, physicality: 0, goalkeeping: 0 };
    }

    return {
      speed: this.calculateAttributeTrainingBonus(training.speedStage),
      shooting: this.calculateAttributeTrainingBonus(training.shootingStage),
      passing: this.calculateAttributeTrainingBonus(training.passingStage),
      defending: this.calculateAttributeTrainingBonus(training.defendingStage),
      dribbling: this.calculateAttributeTrainingBonus(training.dribblingStage),
      physicality: this.calculateAttributeTrainingBonus(training.physicalityStage),
      goalkeeping: 0, // 守门员属性通常不通过训练提升
    };
  }

  /**
   * 计算单个属性的训练加成
   */
  private calculateAttributeTrainingBonus(stageInfo: any): number {
    if (!stageInfo) return 0;

    const stage = stageInfo.stage || 1;
    const progress = stageInfo.stageProgress || 0;

    // 每个阶段提供固定加成，进度提供额外加成
    const stageBonus = (stage - 1) * 5; // 每阶段5点加成
    const progressBonus = Math.round(progress * 0.05); // 进度转换为加成

    return stageBonus + progressBonus;
  }

  /**
   * 计算突破属性加成
   */
  private calculateBreakthroughAttributeBonus(hero: any): any {
    const breakthrough = hero.breakthrough || [];
    const totalPotential = breakthrough.reduce((sum, value) => sum + value, 0);

    // 每点潜能提供0.5点属性加成
    const bonusPerAttribute = Math.round(totalPotential * 0.5);

    return {
      speed: bonusPerAttribute,
      shooting: bonusPerAttribute,
      passing: bonusPerAttribute,
      defending: bonusPerAttribute,
      dribbling: bonusPerAttribute,
      physicality: bonusPerAttribute,
      goalkeeping: bonusPerAttribute,
    };
  }

  // ==================== 球员生涯管理系统 ====================

  /**
   * 添加球员生涯天数（续约）
   * 对应old项目中的addHeroLeftDay方法
   */
  async addHeroCareerDays(heroId: string, days: number = 365): Promise<any> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      // 检查球员是否已退役
      if (hero.isRetired) {
        throw new BadRequestException({
          code: ErrorCode.HERO_ALREADY_RETIRED,
          message: ErrorMessages[ErrorCode.HERO_ALREADY_RETIRED],
        });
      }

      // 检查生涯次数限制（根据品质决定）
      const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
      if (hero.lifeNum >= maxLifeNum) {
        throw new BadRequestException({
          code: ErrorCode.HERO_MAX_CAREER,
          message: ErrorMessages[ErrorCode.HERO_MAX_CAREER],
        });
      }

      // 计算续约费用
      const renewalCost = this.calculateCareerRenewalCost(hero.level, hero.quality, days);

      // TODO: 检查金币是否足够（需要与Character服务通信）

      // 更新球员数据
      const newContractDays = hero.contractDays + days;
      const newLifeNum = hero.lifeNum + 1;

      await this.heroRepository.update(heroId, {
        contractDays: newContractDays,
        lifeNum: newLifeNum,
        treatyReTime: Date.now(),
      });

      this.logger.log(`球员续约成功: ${heroId}, 新合约天数: ${newContractDays}, 生涯次数: ${newLifeNum}`);

      return {
        heroId,
        oldContractDays: hero.contractDays,
        newContractDays,
        addedDays: days,
        lifeNum: newLifeNum,
        maxLifeNum,
        cost: renewalCost,
        renewalTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('球员续约失败', error);
      throw error;
    }
  }

  /**
   * 计算续约费用
   */
  private calculateCareerRenewalCost(level: number, quality: number, days: number): any {
    // 基础费用根据等级和品质计算
    const baseCost = level * quality * 100;

    // 天数费用
    const daysCost = Math.round(days * 2);

    // 总费用
    const totalGold = baseCost + daysCost;

    return {
      gold: totalGold,
      items: [],
    };
  }

  /**
   * 检查并处理球员退役
   */
  async checkAndProcessRetirement(characterId: string): Promise<string[]> {
    try {
      const heroes = await this.heroRepository.findByCharacterId(characterId);
      const retiredHeroes = [];

      for (const hero of heroes) {
        if (this.shouldRetire(hero)) {
          await this.processHeroRetirement(hero.heroId);
          retiredHeroes.push(hero.heroId);
        }
      }

      if (retiredHeroes.length > 0) {
        this.logger.log(`处理球员退役: ${characterId}, 退役球员: ${retiredHeroes.join(', ')}`);
      }

      return retiredHeroes;
    } catch (error) {
      this.logger.error('检查球员退役失败', error);
      throw error;
    }
  }

  /**
   * 判断球员是否应该退役
   */
  private shouldRetire(hero: any): boolean {
    // 退役条件：合约到期且生涯次数达到上限
    const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
    return hero.contractDays <= 0 && hero.lifeNum >= maxLifeNum && !hero.isRetired;
  }

  /**
   * 处理球员退役
   */
  private async processHeroRetirement(heroId: string): Promise<void> {
    try {
      await this.heroRepository.update(heroId, {
        isRetired: true,
        retirementTime: Date.now(),
        isInFormation: false, // 退役球员自动离开阵容
        isLocked: false, // 解除锁定
      });

      // 通知Character服务更新阵容（基于old项目退役逻辑）
      await this.notifyFormationUpdate(heroId);

      // 触发退役相关任务和成就（基于old项目TARGET_TYPE）
      await this.triggerRetirementTasks(heroId);

      this.logger.log(`球员退役处理完成: ${heroId}`);
    } catch (error) {
      this.logger.error('处理球员退役失败', error);
      throw error;
    }
  }

  /**
   * 获取球员生涯信息
   */
  async getHeroCareerInfo(heroId: string): Promise<any> {
    try {
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        throw new NotFoundException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
      const canRenew = hero.contractDays <= 30 && hero.lifeNum < maxLifeNum && !hero.isRetired;
      const willRetire = hero.contractDays <= 0 && hero.lifeNum >= maxLifeNum;
      const daysUntilRetirement = hero.contractDays;

      return {
        heroId,
        contractDays: hero.contractDays,
        lifeNum: hero.lifeNum,
        maxLifeNum,
        canRenew,
        willRetire,
        isRetired: hero.isRetired,
        retirementTime: hero.retirementTime,
        daysUntilRetirement,
        renewalCost: canRenew ? this.calculateCareerRenewalCost(hero.level, hero.quality, 365) : null,
        careerStats: {
          totalMatches: hero.battleNum || 0,
          totalGoals: hero.goals || 0,
          totalAssists: hero.assists || 0,
          averageRating: hero.averageRating || 0,
        },
      };
    } catch (error) {
      this.logger.error('获取球员生涯信息失败', error);
      throw error;
    }
  }

  /**
   * 批量检查合约到期
   */
  async checkContractExpiration(characterId: string): Promise<any> {
    try {
      const heroes = await this.heroRepository.findByCharacterId(characterId);

      const expiringHeroes = heroes.filter(hero =>
        hero.contractDays <= 7 && hero.contractDays > 0 && !hero.isRetired
      );

      const expiredHeroes = heroes.filter(hero =>
        hero.contractDays <= 0 && !hero.isRetired
      );

      const retirementCandidates = expiredHeroes.filter(hero => {
        const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
        return hero.lifeNum >= maxLifeNum;
      });

      return {
        expiringHeroes: expiringHeroes.map(hero => ({
          heroId: hero.heroId,
          name: hero.name,
          contractDays: hero.contractDays,
          lifeNum: hero.lifeNum,
          maxLifeNum: this.calculateMaxLifeNum(hero.quality),
        })),
        expiredHeroes: expiredHeroes.map(hero => ({
          heroId: hero.heroId,
          name: hero.name,
          lifeNum: hero.lifeNum,
          maxLifeNum: this.calculateMaxLifeNum(hero.quality),
          canRenew: hero.lifeNum < this.calculateMaxLifeNum(hero.quality),
        })),
        retirementCandidates: retirementCandidates.map(hero => ({
          heroId: hero.heroId,
          name: hero.name,
          lifeNum: hero.lifeNum,
        })),
        summary: {
          totalHeroes: heroes.length,
          expiringCount: expiringHeroes.length,
          expiredCount: expiredHeroes.length,
          retirementCount: retirementCandidates.length,
        },
      };
    } catch (error) {
      this.logger.error('检查合约到期失败', error);
      throw error;
    }
  }

  // ==================== 疲劳恢复定时任务系统 ====================

  /**
   * 处理所有球员的疲劳恢复（定时任务）
   * 对应old项目中的calcHeroFatigueReTime方法
   */
  async processFatigueRecovery(): Promise<void> {
    try {
      // 获取所有有疲劳值的球员
      const fatigueHeroes = await this.heroRepository.findHeroesWithFatigue();

      if (fatigueHeroes.length === 0) {
        return;
      }

      const currentTime = Date.now();
      const recoveryUpdates = [];

      for (const hero of fatigueHeroes) {
        if (hero.fatigue <= 0 || hero.reTimeFatigue <= 0) {
          continue;
        }

        // 计算时间差（小时）
        const hoursPassed = Math.floor((currentTime - hero.reTimeFatigue) / (60 * 60 * 1000));

        if (hoursPassed <= 0) {
          continue;
        }

        // 计算疲劳恢复
        const recoveryRate = this.calculateFatigueRecoveryRate(hero);
        const fatigueRecovery = Math.floor(hoursPassed * recoveryRate);

        if (fatigueRecovery > 0) {
          const newFatigue = Math.max(0, hero.fatigue - fatigueRecovery);
          const newFatigueRatio = this.calculateFatigueRatio(newFatigue);

          recoveryUpdates.push({
            heroId: hero.heroId,
            oldFatigue: hero.fatigue,
            newFatigue,
            newFatigueRatio,
            reTimeFatigue: newFatigue > 0 ? currentTime : 0,
          });
        }
      }

      // 批量更新疲劳值
      if (recoveryUpdates.length > 0) {
        await this.batchUpdateFatigueRecovery(recoveryUpdates);
        this.logger.log(`疲劳恢复处理完成: ${recoveryUpdates.length}个球员`);
      }
    } catch (error) {
      this.logger.error('处理疲劳恢复失败', error);
    }
  }

  /**
   * 计算疲劳恢复速率
   */
  private calculateFatigueRecoveryRate(hero: any): number {
    // 基础恢复速率：每小时恢复1点疲劳
    let baseRate = 1;

    // 品质加成：品质越高恢复越快
    const qualityBonus = hero.quality * 0.1;

    // 等级加成：等级越高恢复越快
    const levelBonus = hero.level * 0.01;

    // 治疗状态加成
    const treatmentBonus = hero.isTreat ? 2 : 0;

    return baseRate + qualityBonus + levelBonus + treatmentBonus;
  }

  /**
   * 批量更新疲劳恢复
   */
  private async batchUpdateFatigueRecovery(updates: any[]): Promise<void> {
    try {
      const updatePromises = updates.map(update =>
        this.heroRepository.update(update.heroId, {
          fatigue: update.newFatigue,
          fatigueRatio: update.newFatigueRatio,
          reTimeFatigue: update.reTimeFatigue,
        })
      );

      await Promise.all(updatePromises);
    } catch (error) {
      this.logger.error('批量更新疲劳恢复失败', error);
      throw error;
    }
  }

  /**
   * 处理合约天数递减（每日任务）
   */
  async processContractDayDecrement(): Promise<void> {
    try {
      // 获取所有未退役的球员
      const activeHeroes = await this.heroRepository.findActiveHeroes();

      const contractUpdates = [];
      const retirementCandidates = [];

      for (const hero of activeHeroes) {
        if (hero.contractDays > 0) {
          const newContractDays = hero.contractDays - 1;
          contractUpdates.push({
            heroId: hero.heroId,
            contractDays: newContractDays,
          });

          // 检查是否需要退役
          if (newContractDays <= 0) {
            const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
            if (hero.lifeNum >= maxLifeNum) {
              retirementCandidates.push(hero.heroId);
            }
          }
        }
      }

      // 批量更新合约天数
      if (contractUpdates.length > 0) {
        await this.batchUpdateContractDays(contractUpdates);
      }

      // 处理退役
      if (retirementCandidates.length > 0) {
        for (const heroId of retirementCandidates) {
          await this.processHeroRetirement(heroId);
        }
      }

      this.logger.log(`合约天数处理完成: ${contractUpdates.length}个球员, 退役: ${retirementCandidates.length}个球员`);
    } catch (error) {
      this.logger.error('处理合约天数递减失败', error);
    }
  }

  /**
   * 批量更新合约天数
   */
  private async batchUpdateContractDays(updates: any[]): Promise<void> {
    try {
      const updatePromises = updates.map(update =>
        this.heroRepository.update(update.heroId, {
          contractDays: update.contractDays,
        })
      );

      await Promise.all(updatePromises);
    } catch (error) {
      this.logger.error('批量更新合约天数失败', error);
      throw error;
    }
  }

  /**
   * 获取疲劳恢复统计
   */
  async getFatigueRecoveryStats(): Promise<any> {
    try {
      const fatigueHeroes = await this.heroRepository.findHeroesWithFatigue();

      const stats = {
        totalFatigueHeroes: fatigueHeroes.length,
        highFatigueHeroes: fatigueHeroes.filter(h => h.fatigue >= 80).length,
        mediumFatigueHeroes: fatigueHeroes.filter(h => h.fatigue >= 40 && h.fatigue < 80).length,
        lowFatigueHeroes: fatigueHeroes.filter(h => h.fatigue > 0 && h.fatigue < 40).length,
        inTreatmentHeroes: fatigueHeroes.filter(h => h.isTreat).length,
        averageFatigue: fatigueHeroes.length > 0 ?
          Math.round(fatigueHeroes.reduce((sum, h) => sum + h.fatigue, 0) / fatigueHeroes.length) : 0,
      };

      return stats;
    } catch (error) {
      this.logger.error('获取疲劳恢复统计失败', error);
      throw error;
    }
  }

  /**
   * 检查角色金币
   * 基于old项目: 金币检查逻辑
   */
  private async checkCharacterGold(characterId: string, requiredAmount: number): Promise<any> {
    try {
      // 调用Character服务检查金币（基于old项目checkResourceIsEnough）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        { characterId }
      );

      if (!result || result.code !== 0 || !result.data) {
        this.logger.error(`获取角色信息失败: ${characterId}`);
        return {
          sufficient: false,
          current: 0,
        };
      }

      const currentGold = result.data.gold || 0;
      return {
        sufficient: currentGold >= requiredAmount,
        current: currentGold,
      };
    } catch (error) {
      this.logger.error('检查角色金币失败', error);
      return {
        sufficient: false,
        current: 0,
      };
    }
  }

  /**
   * 扣除角色金币
   * 基于old项目: 金币扣除逻辑
   */
  private async deductCharacterGold(characterId: string, amount: number): Promise<boolean> {
    try {
      // 调用Character服务扣除金币（基于old项目deductMoney）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.currency.subtract',
        {
          characterId,
          currencyDto: {
            currencyType: 'gold',
            amount: amount,
            reason: 'hero_breakthrough'
          }
        }
      );

      if (result && result.code === 0) {
        this.logger.debug(`扣除角色金币成功: ${characterId}, 数量: ${amount}`);
        return true;
      } else {
        this.logger.error(`扣除角色金币失败: ${characterId}, 数量: ${amount}, 错误: ${result?.message}`);
        return false;
      }
    } catch (error) {
      this.logger.error('扣除角色金币失败', error);
      return false;
    }
  }

  /**
   * 通知阵容更新
   * 基于old项目: 退役球员需要从阵容中移除
   */
  private async notifyFormationUpdate(heroId: string): Promise<void> {
    try {
      // 获取球员信息
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        this.logger.error(`球员不存在: ${heroId}`);
        return;
      }

      // 调用Character服务更新阵容
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'formation.removeHeroFromAllFormations',
        { characterId: hero.characterId, heroId }
      );

      if (result && result.code === 0) {
        this.logger.log(`球员已从阵容中移除: ${heroId}`);
      } else {
        this.logger.warn(`移除球员失败: ${result?.message || '未知错误'}`);
      }
    } catch (error) {
      this.logger.error('通知阵容更新失败', error);
    }
  }

  /**
   * 触发退役任务
   * 基于old项目: TARGET_TYPE退役任务触发
   */
  private async triggerRetirementTasks(heroId: string): Promise<void> {
    try {
      // 获取退役球员信息
      const hero = await this.heroRepository.findById(heroId);
      if (!hero) {
        this.logger.error(`退役球员不存在: ${heroId}`);
        return;
      }

      // 调用Activity服务触发退役任务
      const taskResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        'task.triggerTask',
        {
          characterId: hero.characterId,
          triggerType: 'HERO_RETIREMENT',
          arg1: hero.quality,
          arg2: hero.lifeNum,
          heroId
        }
      );

      if (taskResult && taskResult.code === 0) {
        this.logger.log(`退役任务触发成功: ${heroId}`);
      }

      // 调用Activity服务触发退役成就
      const achievementResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        'achievement.triggerAchievement',
        {
          characterId: hero.characterId,
          achievementType: 'HERO_RETIREMENT',
          heroQuality: hero.quality,
          heroId
        }
      );

      if (achievementResult && achievementResult.code === 0) {
        this.logger.log(`退役成就触发成功: ${heroId}`);
      }
    } catch (error) {
      this.logger.error('触发退役任务失败', error);
    }
  }

  /**
   * 转换old项目属性格式到新格式
   * 基于old项目: 属性名称映射
   */
  private convertOldAttributesToNew(oldAttributes: any): any {
    return {
      speed: oldAttributes.speed || oldAttributes.Speed || 0,
      shooting: oldAttributes.shooting || oldAttributes.Shooting || 0,
      passing: oldAttributes.passing || oldAttributes.Passing || 0,
      defending: oldAttributes.defending || oldAttributes.Defending || 0,
      dribbling: oldAttributes.dribbling || oldAttributes.Dribbling || 0,
      physicality: oldAttributes.physicality || oldAttributes.Physicality || 0,
      goalkeeping: oldAttributes.goalkeeping || oldAttributes.Goalkeeping || 0,
    };
  }
}
