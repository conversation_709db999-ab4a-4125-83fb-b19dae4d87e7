import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { SkillService } from './skill.service';
import {
  LearnSkillDto,
  UpgradeSkillDto,
  ActivateSkillDto,
  GetSkillListDto,
  GetSkillConfigListDto
} from '@hero/common/dto/skill.dto';
import { SkillPosition } from '../../common/types';
import { Cacheable, CacheEvict } from '@common/redis';

@Controller()
export class SkillController {
  private readonly logger = new Logger(SkillController.name);

  constructor(private readonly skillService: SkillService) {}

  // ==================== 技能配置管理 ====================

  /**
   * 获取技能配置信息
   */
  @MessagePattern('skill.getConfig')
  @Cacheable({
    key: 'skill:config:#{payload.skillId}',
    dataType: 'global',
    ttl: 3600
  })
  async getSkillConfig(@Payload() payload: { skillId: number }) {
    this.logger.log(`获取技能配置信息: ${payload.skillId}`);
    const config = await this.skillService.getSkillConfig(payload.skillId);
    return {
      code: 0,
      message: '获取成功',
      data: config,
    };
  }

  /**
   * 获取技能配置列表
   */
  @MessagePattern('skill.getConfigList')
  @Cacheable({
    key: 'skill:config:list:#{payload.page}:#{payload.limit}:#{payload.type}:#{payload.position}',
    dataType: 'global',
    ttl: 1800
  })
  async getSkillConfigList(@Payload() query: GetSkillConfigListDto) {
    this.logger.log(`获取技能配置列表: ${JSON.stringify(query)}`);
    const result = await this.skillService.getSkillConfigList(query);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 根据位置获取技能配置
   */
  @MessagePattern('skill.getConfigByPosition')
  @Cacheable({
    key: 'skill:config:position:#{payload.position}:#{payload.limit}',
    dataType: 'global',
    ttl: 1800
  })
  async getSkillConfigsByPosition(@Payload() payload: { position: SkillPosition; limit?: number }) {
    this.logger.log(`根据位置获取技能配置: ${payload.position}`);
    const configs = await this.skillService.getSkillConfigsByPosition(payload.position, payload.limit);
    return {
      code: 0,
      message: '获取成功',
      data: configs,
    };
  }

  // ==================== 球员技能管理 ====================

  /**
   * 球员学习技能
   */
  @MessagePattern('skill.learn')
  @CacheEvict({ 
    key: 'hero:skills:#{payload.learnDto.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async learnSkill(@Payload() payload: { learnDto: LearnSkillDto; serverId?: string }) {
    this.logger.log(`球员学习技能: ${JSON.stringify(payload.learnDto)}`);
    const skill = await this.skillService.learnSkill(payload.learnDto);
    return {
      code: 0,
      message: '技能学习成功',
      data: skill,
    };
  }

  /**
   * 升级球员技能
   */
  @MessagePattern('skill.upgrade')
  @CacheEvict({ 
    key: 'hero:skill:#{payload.upgradeDto.skillId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async upgradeSkill(@Payload() payload: { upgradeDto: UpgradeSkillDto; serverId?: string }) {
    this.logger.log(`升级球员技能: ${JSON.stringify(payload.upgradeDto)}`);
    const skill = await this.skillService.upgradeSkill(payload.upgradeDto);
    return {
      code: 0,
      message: '技能升级成功',
      data: skill,
    };
  }

  /**
   * 激活球员技能
   */
  @MessagePattern('skill.activate')
  @CacheEvict({ 
    key: 'hero:skills:active:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async activateSkill(@Payload() payload: { 
    activateDto: ActivateSkillDto; 
    heroId: string;
    serverId?: string 
  }) {
    this.logger.log(`激活球员技能: ${JSON.stringify(payload.activateDto)}`);
    const skill = await this.skillService.activateSkill(payload.activateDto);
    return {
      code: 0,
      message: '技能激活成功',
      data: skill,
    };
  }

  /**
   * 取消激活球员技能
   */
  @MessagePattern('skill.deactivate')
  @CacheEvict({ 
    key: 'hero:skills:active:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deactivateSkill(@Payload() payload: { 
    skillId: string; 
    heroId: string;
    serverId?: string 
  }) {
    this.logger.log(`取消激活球员技能: ${payload.skillId}`);
    const skill = await this.skillService.deactivateSkill(payload.skillId);
    return {
      code: 0,
      message: '技能取消激活成功',
      data: skill,
    };
  }

  /**
   * 获取球员技能列表
   */
  @MessagePattern('skill.getList')
  @Cacheable({ 
    key: 'hero:skills:#{payload.heroId}:#{payload.activeStatus}:#{payload.equippedOnly}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getSkillList(@Payload() query: GetSkillListDto & { serverId?: string }) {
    this.logger.log(`获取球员技能列表: ${JSON.stringify(query)}`);
    const skills = await this.skillService.getSkillList(query);
    return {
      code: 0,
      message: '获取成功',
      data: skills,
    };
  }

  /**
   * 获取球员已激活的技能
   */
  @MessagePattern('skill.getActive')
  @Cacheable({ 
    key: 'hero:skills:active:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getActiveSkills(@Payload() payload: { heroId: string; serverId?: string }) {
    this.logger.log(`获取球员已激活技能: ${payload.heroId}`);
    const skills = await this.skillService.getActiveSkills(payload.heroId);
    return {
      code: 0,
      message: '获取成功',
      data: skills,
    };
  }

  /**
   * 重置球员技能
   */
  @MessagePattern('skill.reset')
  @CacheEvict({ 
    key: 'hero:skills:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async resetSkills(@Payload() payload: { 
    heroId: string; 
    resetType: 'all' | 'level' | 'activation';
    serverId?: string 
  }) {
    this.logger.log(`重置球员技能: ${payload.heroId}, 类型: ${payload.resetType}`);
    // TODO: 实现重置球员技能逻辑
    return {
      code: 0,
      message: '技能重置成功',
      data: {
        heroId: payload.heroId,
        resetType: payload.resetType,
        resetTime: Date.now(),
      },
    };
  }

  /**
   * 批量操作球员技能
   */
  @MessagePattern('skill.batchOperation')
  @CacheEvict({ 
    key: 'hero:skills:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchOperateSkills(@Payload() payload: { 
    heroId: string;
    operation: 'activate' | 'deactivate' | 'upgrade' | 'delete';
    skillIds: string[];
    serverId?: string;
  }) {
    this.logger.log(`批量操作球员技能: ${payload.operation}, 数量: ${payload.skillIds.length}`);
    // TODO: 实现批量操作球员技能逻辑
    return {
      code: 0,
      message: '批量操作完成',
      data: {
        operation: payload.operation,
        processedCount: payload.skillIds.length,
        successCount: payload.skillIds.length,
        failedCount: 0,
      },
    };
  }

  /**
   * 获取技能统计
   */
  @MessagePattern('skill.getStats')
  @Cacheable({ 
    key: 'hero:skill:stats:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getSkillStats(@Payload() payload: { heroId: string; serverId?: string }) {
    this.logger.log(`获取技能统计: ${payload.heroId}`);
    const stats = await this.skillService.getSkillStats(payload.heroId);
    return {
      code: 0,
      message: '获取成功',
      data: stats,
    };
  }

  /**
   * 使用技能
   */
  @MessagePattern('skill.use')
  async useSkill(@Payload() payload: { 
    skillId: string; 
    heroId: string;
    targetId?: string;
    serverId?: string;
  }) {
    this.logger.log(`使用技能: ${payload.skillId}, 球员: ${payload.heroId}`);
    // TODO: 实现使用技能逻辑
    return {
      code: 0,
      message: '技能使用成功',
      data: {
        skillId: payload.skillId,
        heroId: payload.heroId,
        damage: 0,
        healing: 0,
        effects: [],
      },
    };
  }
}
