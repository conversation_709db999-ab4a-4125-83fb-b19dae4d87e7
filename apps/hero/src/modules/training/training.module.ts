import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TrainingController } from './training.controller';
import { TrainingService } from './training.service';
import { HeroRepository } from '@hero/common/repositories/hero.repository';
import { Hero, HeroSchema } from '@hero/common/schemas/hero.schema';

/**
 * 球员训练模块
 * 处理球员训练、特训等功能
 * 
 * 注意：
 * - GameConfigModule已在app.module.ts中全局注册，无需重复导入
 * - MongooseModule.forFeature()用于注册特定Schema，需要在使用的模块中注册
 */
@Module({
  imports: [
    // 注册Hero Schema（HeroRepository需要）
    MongooseModule.forFeature([
      { name: Hero.name, schema: HeroSchema },
    ]),
  ],
  controllers: [TrainingController],
  providers: [
    TrainingService,
    HeroRepository, // 共享的Hero数据访问层
  ],
  exports: [TrainingService],
})
export class TrainingModule {}
