/**
 * 阵型配置工具类
 * 迁移自libs/game-config/src/formation/formation-definition.schema.ts中的业务逻辑
 * 
 * 严格遵循原则：只使用接口中实际存在的字段，基于真实的配置数据
 */
export class FormationConfigUtils {

  /**
   * 根据角色获取位置
   * 迁移自：FormationDefinitionSchema.methods.getPositionByRole
   */
  static getPositionByRole(formationConfig: any, role: string): any | null {
    const positions = formationConfig.positions || [];
    return positions.find((pos: any) => pos.role === role) || null;
  }

  /**
   * 根据角色获取多个位置
   * 迁移自：FormationDefinitionSchema.methods.getPositionsByRole
   */
  static getPositionsByRole(formationConfig: any, role: string): any[] {
    const positions = formationConfig.positions || [];
    return positions.filter((pos: any) => pos.role === role);
  }

  /**
   * 检查阵型是否可以解锁
   * 迁移自：FormationDefinitionSchema.methods.canUnlock
   */
  static canUnlock(
    formationConfig: any, 
    level: number, 
    vip: number, 
    achievements: number[]
  ): boolean {
    const condition = formationConfig.unlockCondition || {};
    
    // 检查等级要求
    const minLevel = condition.minLevel || 1;
    if (level < minLevel) return false;
    
    // 检查VIP要求
    const requiredVip = condition.requiredVip || 0;
    if (vip < requiredVip) return false;
    
    // 检查成就要求
    const requiredAchievements = condition.requiredAchievements || [];
    for (const requiredAchievement of requiredAchievements) {
      if (!achievements.includes(requiredAchievement)) return false;
    }
    
    return true;
  }

  /**
   * 计算阵型化学反应
   * 迁移自：FormationDefinitionSchema.methods.calculateChemistry
   */
  static calculateChemistry(formationConfig: any, heros: any[]): number {
    let chemistry = 0;
    const positions = formationConfig.positions || [];
    const bonus = formationConfig.bonus || {};
    
    for (const hero of heros) {
      const position = positions.find((pos: any) => pos.position === hero.position);
      if (position && hero.preferredPosition === position.role) {
        chemistry += 10; // 完美匹配
      } else if (position) {
        chemistry += 5;  // 部分匹配
      }
    }
    
    const chemistryBonus = bonus.chemistry || 0;
    return Math.min(100, chemistry + chemistryBonus);
  }

  /**
   * 检查阵型是否可用
   * 迁移自：FormationDefinitionSchema.virtual('isAvailable')
   */
  static isAvailable(formationConfig: any): boolean {
    return formationConfig.isActive !== false; // 默认为true
  }

  /**
   * 计算阵型总评分
   * 迁移自：FormationDefinitionSchema.virtual('totalRating')
   */
  static calculateTotalRating(formationConfig: any): number {
    const offensiveRating = formationConfig.offensiveRating || 0;
    const defensiveRating = formationConfig.defensiveRating || 0;
    const balanceRating = formationConfig.balanceRating || 0;
    
    return Math.floor((offensiveRating + defensiveRating + balanceRating) / 3);
  }

  /**
   * 获取阵型位置数量
   * 迁移自：FormationDefinitionSchema.virtual('positionCount')
   */
  static getPositionCount(formationConfig: any): number {
    const positions = formationConfig.positions || [];
    return positions.length;
  }

  /**
   * 获取阵型类型名称
   */
  static getFormationTypeName(formationType: string): string {
    const typeMap: Record<string, string> = {
      '4-4-2': '四四二',
      '4-3-3': '四三三',
      '3-5-2': '三五二',
      '4-5-1': '四五一',
      '5-3-2': '五三二',
      '4-2-3-1': '四二三一',
      '3-4-3': '三四三',
      '5-4-1': '五四一',
    };
    
    return typeMap[formationType] || formationType;
  }

  /**
   * 检查阵型是否为进攻型
   */
  static isOffensiveFormation(formationConfig: any): boolean {
    const offensiveRating = formationConfig.offensiveRating || 0;
    const defensiveRating = formationConfig.defensiveRating || 0;
    
    return offensiveRating > defensiveRating;
  }

  /**
   * 检查阵型是否为防守型
   */
  static isDefensiveFormation(formationConfig: any): boolean {
    const offensiveRating = formationConfig.offensiveRating || 0;
    const defensiveRating = formationConfig.defensiveRating || 0;
    
    return defensiveRating > offensiveRating;
  }

  /**
   * 检查阵型是否为平衡型
   */
  static isBalancedFormation(formationConfig: any): boolean {
    const offensiveRating = formationConfig.offensiveRating || 0;
    const defensiveRating = formationConfig.defensiveRating || 0;
    
    return Math.abs(offensiveRating - defensiveRating) <= 5;
  }

  /**
   * 获取阵型风格描述
   */
  static getFormationStyle(formationConfig: any): string {
    if (this.isOffensiveFormation(formationConfig)) {
      return '进攻型';
    } else if (this.isDefensiveFormation(formationConfig)) {
      return '防守型';
    } else {
      return '平衡型';
    }
  }

  /**
   * 计算阵型适合的战术强度
   */
  static getRecommendedTactics(formationConfig: any): any {
    const tactics = formationConfig.tactics || {};
    
    return {
      attackIntensity: tactics.attackIntensity || 50,
      defenseIntensity: tactics.defenseIntensity || 50,
      passingStyle: tactics.passingStyle || 50,
      tempo: tactics.tempo || 50,
      width: tactics.width || 50,
      pressing: tactics.pressing || 50,
    };
  }

  /**
   * 获取阵型位置坐标
   */
  static getPositionCoordinates(formationConfig: any): any[] {
    const positions = formationConfig.positions || [];
    return positions.map((pos: any) => ({
      position: pos.position,
      x: pos.x,
      y: pos.y,
      role: pos.role,
      description: pos.description || '',
    }));
  }

  /**
   * 检查球员是否适合阵型位置
   */
  static isHeroSuitableForPosition(
    formationConfig: any,
    positionNumber: number,
    heroPosition: string
  ): boolean {
    const positions = formationConfig.positions || [];
    const formationPosition = positions.find((pos: any) => pos.position === positionNumber);
    
    if (!formationPosition) return false;
    
    // 完全匹配
    if (formationPosition.role === heroPosition) return true;
    
    // TODO: 基于实际配置实现位置兼容性检查
    // 例如：CB可以打CDM，CAM可以打CM等
    
    return false;
  }

  /**
   * 计算阵型解锁费用
   */
  static getUnlockCost(formationConfig: any): number {
    const condition = formationConfig.unlockCondition || {};
    return condition.unlockCost || 0;
  }

  /**
   * 获取阵型解锁条件描述
   */
  static getUnlockDescription(formationConfig: any): string {
    const condition = formationConfig.unlockCondition || {};
    const descriptions = [];
    
    if (condition.minLevel > 1) {
      descriptions.push(`等级${condition.minLevel}级`);
    }
    
    if (condition.requiredVip > 0) {
      descriptions.push(`VIP${condition.requiredVip}`);
    }
    
    if (condition.unlockCost > 0) {
      descriptions.push(`${condition.unlockCost}金币`);
    }
    
    if (condition.description) {
      descriptions.push(condition.description);
    }
    
    return descriptions.length > 0 ? descriptions.join('，') : '无特殊要求';
  }

  /**
   * 获取阵型属性加成
   */
  static getFormationBonus(formationConfig: any): any {
    const bonus = formationConfig.bonus || {};
    
    return {
      attack: bonus.attack || 0,
      midfield: bonus.midfield || 0,
      defense: bonus.defense || 0,
      chemistry: bonus.chemistry || 0,
      morale: bonus.morale || 0,
    };
  }
}
