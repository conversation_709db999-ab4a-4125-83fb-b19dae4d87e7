/**
 * Hero微服务工具类统一导出
 * 
 * 这些工具类迁移自libs/game-config中各个definition.schema.ts文件的业务逻辑
 * 严格遵循原则：只使用接口中实际存在的字段，基于真实的配置数据
 */

// 球员配置工具类
export { HeroConfigUtils } from './hero-config.utils';

// 技能配置工具类
export { SkillConfigUtils } from './skill-config.utils';

// 阵型配置工具类
export { FormationConfigUtils } from './formation-config.utils';

/**
 * 工具类使用说明：
 * 
 * 1. HeroConfigUtils - 球员配置相关工具
 *    - calculateAttributeAtLevel() - 计算指定等级的属性值
 *    - calculateExpRequired() - 计算升级所需经验
 *    - canLearnSkill() - 检查是否可以学习技能
 *    - isExpired() - 检查是否已过期
 *    - isAvailable() - 检查是否可用
 *    - isGoalkeeper() - 检查是否为门将
 *    - calculateTotalAttributes() - 计算总属性值
 *    - calculateMarketValue() - 计算市场价值
 * 
 * 2. SkillConfigUtils - 技能配置相关工具
 *    - getEffectsAtLevel() - 获取指定等级的技能效果
 *    - getUpgradeCost() - 计算升级费用
 *    - canUpgradeTo() - 检查是否可以升级
 *    - isCompatibleWithPosition() - 检查位置兼容性
 *    - calculateEffectValue() - 计算效果数值
 * 
 * 3. FormationConfigUtils - 阵型配置相关工具
 *    - getPositionByRole() - 根据角色获取位置
 *    - canUnlock() - 检查是否可以解锁
 *    - calculateChemistry() - 计算化学反应
 *    - calculateTotalRating() - 计算总评分
 * 
 * 使用示例：
 * 
 * ```typescript
 * import { HeroConfigUtils, SkillConfigUtils } from '@hero/utils';
 * 
 * // 计算球员属性
 * const attack = HeroConfigUtils.calculateAttributeAtLevel(heroConfig, 'attack', 50);
 * 
 * // 检查技能升级
 * const canUpgrade = SkillConfigUtils.canUpgradeTo(skillConfig, 5, heroLevel);
 * ```
 */
