/**
 * 技能配置工具类
 * 迁移自libs/game-config/src/skill/skill-definition.schema.ts中的业务逻辑
 * 
 * 严格遵循原则：只使用接口中实际存在的字段，基于真实的配置数据
 */
export class SkillConfigUtils {

  /**
   * 获取指定等级的技能效果
   * 迁移自：SkillDefinitionSchema.methods.getEffectsAtLevel
   */
  static getEffectsAtLevel(skillConfig: any, level: number): any[] {
    // TODO: 需要根据实际的SkillDefinition接口字段调整
    const upgrades = skillConfig.upgrades || [];
    const upgrade = upgrades.find((u: any) => u.level === level);
    return upgrade ? upgrade.effects : [];
  }

  /**
   * 计算技能升级费用
   * 迁移自：SkillDefinitionSchema.methods.getUpgradeCost
   */
  static getUpgradeCost(skillConfig: any, fromLevel: number, toLevel: number): number {
    let totalCost = 0;
    const upgrades = skillConfig.upgrades || [];
    
    for (let level = fromLevel + 1; level <= toLevel; level++) {
      const upgrade = upgrades.find((u: any) => u.level === level);
      if (upgrade) {
        totalCost += upgrade.cost || 0;
      }
    }
    return totalCost;
  }

  /**
   * 检查是否可以升级到指定等级
   * 迁移自：SkillDefinitionSchema.methods.canUpgradeTo
   */
  static canUpgradeTo(skillConfig: any, targetLevel: number, heroLevel: number): boolean {
    const maxLevel = skillConfig.maxLevel || 1;
    if (targetLevel > maxLevel) return false;

    const upgrades = skillConfig.upgrades || [];
    const upgrade = upgrades.find((u: any) => u.level === targetLevel);
    if (!upgrade) return false;

    const requiredHeroLevel = upgrade.requiredHeroLevel || 0;
    return heroLevel >= requiredHeroLevel;
  }

  /**
   * 检查技能是否与位置兼容
   * 迁移自：SkillDefinitionSchema.methods.isCompatibleWithPosition
   */
  static isCompatibleWithPosition(skillConfig: any, position: number): boolean {
    const skillPosition = skillConfig.position;
    
    // 0表示全位置技能
    if (skillPosition === 0) return true;
    
    // 直接位置匹配
    if (skillPosition === position) return true;
    
    // TODO: 基于实际配置实现位置兼容性检查
    // 例如：后卫技能可能适用于所有后卫位置
    
    return false;
  }

  /**
   * 计算指定等级和效果类型的效果数值
   * 迁移自：SkillDefinitionSchema.methods.calculateEffectValue
   */
  static calculateEffectValue(skillConfig: any, level: number, effectType: string): number {
    const effects = this.getEffectsAtLevel(skillConfig, level);
    const effect = effects.find((e: any) => e.type === effectType);
    return effect ? (effect.value || 0) : 0;
  }

  /**
   * 获取技能类型名称
   */
  static getSkillTypeName(skillType: number | string): string {
    const typeMap: Record<string | number, string> = {
      1: '被动技能',
      2: '主动技能',
      3: '触发技能',
      4: '光环技能',
      'passive': '被动技能',
      'active': '主动技能',
      'trigger': '触发技能',
      'aura': '光环技能',
    };
    
    return typeMap[skillType] || '未知类型';
  }

  /**
   * 获取技能位置名称
   */
  static getSkillPositionName(position: number | string): string {
    const positionMap: Record<string | number, string> = {
      0: '全位置',
      1: '门将',
      2: '后卫',
      3: '中场',
      4: '前锋',
      'all': '全位置',
      'gk': '门将',
      'def': '后卫',
      'mid': '中场',
      'fwd': '前锋',
    };
    
    return positionMap[position] || '未知位置';
  }

  /**
   * 检查技能是否为被动技能
   */
  static isPassiveSkill(skillConfig: any): boolean {
    return skillConfig.type === 1 || skillConfig.type === 'passive';
  }

  /**
   * 检查技能是否为主动技能
   */
  static isActiveSkill(skillConfig: any): boolean {
    return skillConfig.type === 2 || skillConfig.type === 'active';
  }

  /**
   * 检查技能是否为触发技能
   */
  static isTriggerSkill(skillConfig: any): boolean {
    return skillConfig.type === 3 || skillConfig.type === 'trigger';
  }

  /**
   * 检查技能是否为光环技能
   */
  static isAuraSkill(skillConfig: any): boolean {
    return skillConfig.type === 4 || skillConfig.type === 'aura';
  }

  /**
   * 计算技能稀有度星级
   */
  static getSkillRarity(skillConfig: any): number {
    return Math.max(1, Math.min(5, skillConfig.rarity || 1));
  }

  /**
   * 获取技能稀有度名称
   */
  static getSkillRarityName(rarity: number): string {
    const rarityMap: Record<number, string> = {
      1: '一星',
      2: '二星',
      3: '三星',
      4: '四星',
      5: '五星',
    };
    
    return rarityMap[rarity] || '一星';
  }

  /**
   * 检查技能升级所需道具
   */
  static getRequiredItems(skillConfig: any, targetLevel: number): number[] {
    const upgrades = skillConfig.upgrades || [];
    const upgrade = upgrades.find((u: any) => u.level === targetLevel);
    return upgrade ? (upgrade.requiredItems || []) : [];
  }

  /**
   * 计算技能效果持续时间
   */
  static getEffectDuration(skillConfig: any, level: number, effectType: string): number {
    const effects = this.getEffectsAtLevel(skillConfig, level);
    const effect = effects.find((e: any) => e.type === effectType);
    return effect ? (effect.duration || 0) : 0;
  }

  /**
   * 计算技能效果触发概率
   */
  static getEffectProbability(skillConfig: any, level: number, effectType: string): number {
    const effects = this.getEffectsAtLevel(skillConfig, level);
    const effect = effects.find((e: any) => e.type === effectType);
    return effect ? (effect.probability || 1) : 1;
  }

  /**
   * 检查技能效果触发条件
   */
  static getEffectCondition(skillConfig: any, level: number, effectType: string): string {
    const effects = this.getEffectsAtLevel(skillConfig, level);
    const effect = effects.find((e: any) => e.type === effectType);
    return effect ? (effect.condition || '') : '';
  }

  /**
   * 获取技能在指定等级的所有效果描述
   */
  static getSkillDescription(skillConfig: any, level: number): string {
    const effects = this.getEffectsAtLevel(skillConfig, level);
    
    if (effects.length === 0) {
      return skillConfig.description || '无效果描述';
    }
    
    const descriptions = effects
      .filter((effect: any) => effect.description)
      .map((effect: any) => effect.description);
    
    return descriptions.length > 0 ? descriptions.join('；') : skillConfig.description || '无效果描述';
  }
}
