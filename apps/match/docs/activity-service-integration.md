# Activity & Economy服务集成指南

## 概述

Activity服务和Economy服务已开发完成但尚未测试，为避免Match服务中的超时错误，相关调用已暂时注释。本文档说明如何在这些服务测试完成后重新开启集成。

## 当前状态

### ✅ 已完成
- Activity服务代码开发完成
- Economy服务代码开发完成
- Match服务中的Activity和Economy调用已识别并注释
- 微服务依赖配置已更新

### ❌ 待完成
- Activity服务功能测试
- Economy服务功能测试
- 服务间集成测试
- Match服务中相关调用的重新开启

## 被注释的调用位置

### 1. League服务 - 联赛PVE战斗任务触发
**文件**: `apps/match/src/modules/league/league.service.ts`
**行数**: 504-521
**功能**: 联赛战斗胜利后触发金牌任务（taskType: 39）
**影响**: 联赛战斗功能正常，但不会更新任务进度

### 2. Tournament服务 - 世界杯参加任务触发
**文件**: `apps/match/src/modules/tournament/tournament.service.ts`
**行数**: 160-177
**功能**: 参加世界杯后触发任务（taskType: 31）
**影响**: 世界杯参加功能正常，但不会更新任务进度

### 3. League服务 - 购买联赛次数Economy调用
**文件**: `apps/match/src/modules/league/league.service.ts`
**行数**: 720-730
**功能**: 购买联赛次数时调用Economy服务扣除费用
**影响**: 购买联赛次数功能正常，但不会实际扣除资源

### 4. 微服务依赖配置
**文件**: `apps/match/src/app.module.ts`
**行数**: 81-94
**功能**: Activity和Economy服务的微服务客户端注册
**影响**: Match服务不会尝试连接这些服务

## 开启步骤

### 第一步：启动Activity服务
```bash
# 在项目根目录执行
npm run start:activity
```

### 第二步：验证Activity服务健康状态
```bash
# 检查服务是否正常启动
curl http://localhost:3006/api/health

# 预期响应
{
  "status": "ok",
  "timestamp": "2025-07-21T12:00:00.000Z",
  "uptime": 123.456
}
```

### 第三步：测试Activity服务核心接口
```bash
# 测试任务触发接口（通过网关）
# 需要先获取有效的认证token
curl -X POST http://localhost:3000/api/activity/task/trigger \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "characterId": "test_character_id",
    "taskType": 39,
    "arg1": "test_value"
  }'
```

### 第四步：重新开启Match服务中的Activity调用

#### 4.1 恢复微服务依赖配置
在 `apps/match/src/app.module.ts` 中：
```typescript
// 取消注释这一行
MICROSERVICE_NAMES.ACTIVITY_SERVICE,    // 需要调用活动服务（任务进度更新）
```

#### 4.2 恢复League服务中的任务触发
在 `apps/match/src/modules/league/league.service.ts` 中：
```typescript
// 取消注释并恢复原始代码
await this.microserviceClient.call(
  MICROSERVICE_NAMES.ACTIVITY_SERVICE,
  'task.triggerTask',
  {
    characterId,
    taskType: 39, // THIRTY_NINE 金牌任务
  }
);
```

#### 4.3 恢复Tournament服务中的任务触发
在 `apps/match/src/modules/tournament/tournament.service.ts` 中：
```typescript
// 取消注释并恢复原始代码
await this.microserviceClient.call(
  MICROSERVICE_NAMES.ACTIVITY_SERVICE,
  'task.triggerTask',
  {
    characterId: dto.characterId,
    taskType: 31, // THIRTY_ONE 参加世界杯任务
  }
);
```

### 第五步：重启Match服务
```bash
# 重启Match服务以应用配置变更
# 如果使用npm run start:match，需要停止并重新启动
```

### 第六步：验证集成功能
1. 测试联赛PVE战斗功能
2. 验证任务进度是否正确更新
3. 测试世界杯参加功能
4. 检查相关任务是否被触发

## 注意事项

### 服务启动顺序
确保按以下顺序启动服务：
1. Gateway
2. Auth
3. Character
4. Hero
5. Activity ⭐ **新增**
6. Match

### 错误处理
如果Activity服务调用失败，Match服务应该：
- 记录错误日志但不影响主要功能
- 考虑添加重试机制
- 提供降级方案（如任务进度延迟更新）

### 监控要点
- Activity服务的响应时间
- 任务触发的成功率
- 微服务间通信的稳定性

## 相关接口文档

### Activity服务主要接口
- `activity.task.trigger` - 触发任务进度更新
- `activity.task.getList` - 获取任务列表
- `activity.task.complete` - 完成任务

### 任务类型说明
- `taskType: 31` - 参加世界杯任务
- `taskType: 39` - 金牌任务（联赛相关）

## 测试建议

1. **单元测试**: 确保Activity服务的核心逻辑正确
2. **集成测试**: 验证与Match服务的通信正常
3. **端到端测试**: 完整的游戏流程测试
4. **性能测试**: 确保任务触发不影响游戏体验

## 联系信息

如有问题，请联系：
- 后端开发团队
- 微服务架构负责人
