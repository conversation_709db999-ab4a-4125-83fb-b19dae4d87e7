import { Injectable, Logger } from '@nestjs/common';
import { TournamentRepository } from '../../common/repositories/tournament.repository';
import { GameConfigFacade, TeamDefinition } from '@app/game-config';
import { MicroserviceClientService } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { HeroPosition } from '@app/game-constants';
import { 
  GetWorldCupInfoDto,
  JoinWorldCupDto,
  WorldCupBattleDto,
  BuyWorldCupTimesDto,
  GetWorldCupRewardDto,
  JoinRegionalCupDto,
  RegionalCupBattleDto,
  WorldCupInfoResponseDto,
  JoinWorldCupResponseDto,
  WorldCupBattleResponseDto,
  RegionalCupInfoResponseDto
} from '../../common/dto/tournament.dto';
import { Tournament, WorldCupData, RegionalCupData, TournamentBattleRecord } from '../../common/schemas/tournament.schema';
import { BattleService } from '../battle/battle.service';
import { BattleDataService } from '../../common/services/battle-data.service';

/**
 * 锦标赛系统服务
 * 严格基于old项目worldCup.js、middleEastCup.js等的业务逻辑实现
 * 
 * 核心功能：
 * - 世界杯锦标赛系统
 * - 区域杯赛系统（中东杯、海湾杯、MLS）
 * - 锦标赛报名和赛程管理
 * - 淘汰赛制和奖励发放
 * - 每日重置机制
 */
@Injectable()
export class TournamentService {
  private readonly logger = new Logger(TournamentService.name);

  constructor(
    private readonly tournamentRepository: TournamentRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly microserviceClient: MicroserviceClientService,
    private readonly battleService: BattleService,
    private readonly battleDataService: BattleDataService,
  ) {}

  /**
   * 获取世界杯信息
   * 基于old项目: WorldCup.prototype.getWorldCupInfo
   */
  async getWorldCupInfo(dto: GetWorldCupInfoDto): Promise<WorldCupInfoResponseDto> {
    this.logger.log(`获取世界杯信息: ${dto.characterId}`);

    try {
      // 查找现有锦标赛数据
      let tournament = await this.tournamentRepository.findByUid(dto.characterId);
      
      // 如果没有数据，初始化锦标赛数据
      if (!tournament) {
        tournament = await this.initTournamentData(dto.characterId);
      } else {
        // 检查跨天重置
        await this.checkDailyReset(tournament);
      }

      if (!tournament || !tournament.worldCup) {
        return {
          code: -1,
          message: '初始化世界杯数据失败',
        };
      }

      const worldCup = tournament.worldCup;

      // 检查是否需要处理隔天逻辑
      if (!this.isToday(worldCup.reTime)) {
        await this.handleWorldCupDayChange(tournament);
      }

      return {
        code: 0,
        message: '获取成功',
        joinCount: worldCup.joinCount,
        buyCount: worldCup.buyCount,
        isJoin: worldCup.isJoin,
        groupNum: worldCup.groupNum,
        worldCupId: worldCup.worldCupId,
        isGetReward: worldCup.isGetReward,
        isOut: worldCup.isOut,
        groupMatch: worldCup.groupMatch,
        eliminateMatch: worldCup.eliminateMatch,
      };
    } catch (error) {
      this.logger.error('获取世界杯信息失败', error);
      return {
        code: -1,
        message: '获取世界杯信息失败',
      };
    }
  }

  /**
   * 参加世界杯
   * 基于old项目: WorldCup.prototype.joinMatch
   */
  async joinWorldCup(dto: JoinWorldCupDto): Promise<JoinWorldCupResponseDto> {
    this.logger.log(`参加世界杯: ${dto.characterId}, 副本${dto.worldCupId}`);

    try {
      // 参数验证
      if (dto.worldCupId < 1 || dto.worldCupId > 7) {
        return {
          code: -1,
          message: '副本ID范围错误',
        };
      }

      const tournament = await this.tournamentRepository.findByUid(dto.characterId);
      if (!tournament || !tournament.worldCup) {
        return {
          code: -2,
          message: '世界杯数据不存在',
        };
      }

      const worldCup = tournament.worldCup;

      // 检查次数
      if (worldCup.joinCount < 1) {
        return {
          code: -3,
          message: '次数不足',
        };
      }

      // 防止重复参加
      if (worldCup.isJoin === 1) {
        return {
          code: -4,
          message: '已在比赛中',
        };
      }

      // 获取配置队伍
      const definition = await this.getWorldCupDefinition(dto.worldCupId);
      if (!definition) {
        return {
          code: -5,
          message: '配置不存在',
        };
      }

      // 生成小组赛队伍
      const allTeam = this.generateWorldCupTeams(definition);

      // 更新世界杯数据
      worldCup.groupMatch = allTeam;
      worldCup.isJoin = 1;
      worldCup.worldCupId = dto.worldCupId;
      worldCup.isGetReward = 0;
      worldCup.joinCount--;

      await this.tournamentRepository.updateByUid(dto.characterId, { worldCup });

      // TODO: 触发任务系统 - Activity服务调用
      // 注意：Activity服务已开发但尚未测试，暂时注释以避免超时错误
      // 开启方法：
      // 1. 确保Activity服务已启动：npm run start:activity
      // 2. 验证Activity服务健康状态：GET http://localhost:3006/api/health
      // 3. 测试Activity服务的task.triggerTask接口正常工作
      // 4. 取消下面代码的注释
      // 5. 重新测试世界杯参加功能
      /*
      await this.microserviceClient.call(
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        'task.triggerTask',
        {
          characterId: dto.characterId,
          taskType: 31, // THIRTY_ONE 参加世界杯任务
        }
      );
      */

      return {
        code: 0,
        message: '参加成功',
        allTeam: this.getGroupMatchList(allTeam),
        joinCount: worldCup.joinCount,
      };
    } catch (error) {
      this.logger.error('参加世界杯失败', error);
      return {
        code: -1,
        message: '参加世界杯失败',
      };
    }
  }

  /**
   * 世界杯战斗
   * 基于old项目: WorldCup.prototype.worldCupBattle
   */
  async worldCupBattle(dto: WorldCupBattleDto): Promise<WorldCupBattleResponseDto> {
    this.logger.log(`世界杯战斗: ${dto.characterId}`);

    try {
      const tournament = await this.tournamentRepository.findByUid(dto.characterId);
      if (!tournament || !tournament.worldCup) {
        return {
          code: -1,
          message: '世界杯数据不存在',
        };
      }

      const worldCup = tournament.worldCup;

      // 检查是否在比赛中
      if (worldCup.isJoin !== 1) {
        return {
          code: -2,
          message: '未参加比赛',
        };
      }

      // 检查是否已出局
      if (worldCup.isOut === 1) {
        return {
          code: -3,
          message: '已出局',
        };
      }

      // 获取玩家阵容数据 - 修复：使用正确的微服务调用方法名
      const formationResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'formation.getFormations',
        {
          characterId: dto.characterId,
          serverId: 'server_001'
        }
      );

      if (!formationResult || formationResult.code !== 0) {
        return {
          code: -4,
          message: '获取阵容数据失败',
        };
      }

      // 转换阵容数据为战斗数据格式 - 使用共享服务
      const characterBattleData = await this.battleDataService.convertFormationToBattleData(formationResult.data, dto.characterId);

      if (!characterBattleData) {
        return {
          code: -4,
          message: '战斗数据转换失败',
        };
      }

      // 获取对手数据
      const enemyData = this.getWorldCupEnemyData(worldCup);
      if (!enemyData) {
        return {
          code: -5,
          message: '获取对手数据失败',
        };
      }

      // 直接调用本地战斗服务 - 修复：避免微服务自调用，参考杯赛成功实现
      const battleResult = await this.battleService.pveBattle({
        characterId: dto.characterId,
        characterBattleData: characterBattleData,
        enemyBattleData: enemyData,
        battleType: 'worldcup',
      });

      if (!battleResult || battleResult.code !== 0) {
        return {
          code: -6,
          message: '战斗计算失败',
        };
      }

      // 处理战斗结果
      const processResult = await this.processWorldCupBattleResult(
        dto.characterId,
        worldCup,
        battleResult.homeScore,
        battleResult.awayScore
      );

      return {
        code: 0,
        message: '战斗完成',
        result: processResult.result,
        isOut: processResult.isOut,
        isGetReward: processResult.isGetReward,
        groupNum: processResult.groupNum,
        battleRecord: battleResult.battleRecord,
        rewardInfo: processResult.rewardInfo,
      };
    } catch (error) {
      this.logger.error('世界杯战斗失败', error);
      return {
        code: -1,
        message: '战斗处理失败',
      };
    }
  }

  /**
   * 购买世界杯次数
   */
  async buyWorldCupTimes(dto: BuyWorldCupTimesDto): Promise<any> {
    this.logger.log(`购买世界杯次数: ${dto.characterId}`);

    try {
      const buyGoldCost = await this.getBuyWorldCupGoldCost();

      const characterInfo = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        {
          characterId: dto.characterId,
          serverId: 'server_001'
        }
      );

      if (!characterInfo || characterInfo.code !== 0) {
        return { code: -1, message: '获取角色信息失败' };
      }

      if (characterInfo.data.gold < buyGoldCost) {
        return { code: -2, message: '球币不足' };
      }

      // TODO 经济系统测试通过后开启
      // const deductResult = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.ECONOMY_SERVICE,
      //   'currency.deductCurrency',
      //   {
      //     characterId: dto.characterId,
      //     currencyType: 'gold',
      //     amount: buyGoldCost,
      //     source: 'buy_worldcup_times',
      //   }
      // );
      //
      // if (!deductResult || deductResult.code !== 0) {
      //   return { code: -3, message: '扣除球币失败' };
      // }

      const tournament = await this.tournamentRepository.findByUid(dto.characterId);
      if (tournament && tournament.worldCup) {
        tournament.worldCup.joinCount++;
        tournament.worldCup.buyCount++;
        await this.tournamentRepository.updateByUid(dto.characterId, { worldCup: tournament.worldCup });
      }

      return {
        code: 0,
        message: '购买成功',
        addedTimes: 1,
        totalCost: buyGoldCost,
      };
    } catch (error) {
      this.logger.error('购买世界杯次数失败', error);
      return { code: -1, message: '购买失败' };
    }
  }

  /**
   * 领取世界杯奖励
   */
  async getWorldCupReward(dto: GetWorldCupRewardDto): Promise<any> {
    this.logger.log(`领取世界杯奖励: ${dto.characterId}`);

    try {
      const tournament = await this.tournamentRepository.findByUid(dto.characterId);
      if (!tournament || !tournament.worldCup) {
        return { code: -1, message: '世界杯数据不存在' };
      }

      const worldCup = tournament.worldCup;

      if (worldCup.isGetReward !== 1) {
        return { code: -2, message: '无可领取奖励' };
      }

      const rewardDefinition = await this.getWorldCupRewardDefinition(worldCup.worldCupId, worldCup.groupNum);
      if (!rewardDefinition) {
        return { code: -3, message: '奖励配置不存在' };
      }

      const rewardResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.ECONOMY_SERVICE,
        'reward.giveRewards',
        {
          characterId: dto.characterId,
          rewards: rewardDefinition,
          source: 'worldcup_reward',
          sourceId: `${worldCup.worldCupId}_${worldCup.groupNum}`,
        }
      );

      if (rewardResult.code === 0) {
        worldCup.isGetReward = 4;
        await this.tournamentRepository.updateByUid(dto.characterId, { worldCup });
      }

      return {
        code: rewardResult.code,
        message: rewardResult.code === 0 ? '奖励领取成功' : '奖励发放失败',
        rewards: rewardResult.rewards,
      };
    } catch (error) {
      this.logger.error('领取世界杯奖励失败', error);
      return { code: -1, message: '奖励领取失败' };
    }
  }

  /**
   * 参加区域杯赛
   */
  async joinRegionalCup(dto: JoinRegionalCupDto): Promise<RegionalCupInfoResponseDto> {
    this.logger.log(`参加区域杯赛: ${dto.characterId}, 类型: ${dto.cupType}`);

    try {
      const tournament = await this.tournamentRepository.findByUid(dto.characterId);
      if (!tournament) {
        return { code: -1, message: '锦标赛数据不存在' };
      }

      const cupData = this.getRegionalCupData(tournament, dto.cupType);
      if (!cupData) {
        return { code: -2, message: '杯赛数据不存在' };
      }

      if (cupData.isBegin === 1) {
        return { code: -3, message: '已在挑战中' };
      }

      if (cupData.contestNum >= cupData.contestNumMax) {
        return { code: -4, message: '挑战次数已用完' };
      }

      const rivalTeamList = await this.generateRivalTeamList(dto.cupType, dto.teamIdList);

      cupData.isBegin = 1;
      cupData.rivalTeamList = rivalTeamList;
      cupData.teamList = dto.teamIdList;
      cupData.goal = 0;
      cupData.fumble = 0;
      cupData.diff = 0;
      cupData.win = 0;
      cupData.combatList = [];
      cupData.awardList = [];

      const updateData: any = {};
      updateData[`${dto.cupType}Cup`] = cupData;
      await this.tournamentRepository.updateByUid(dto.characterId, updateData);

      return {
        code: 0,
        message: '参加成功',
        contestNum: cupData.contestNum,
        contestNumMax: cupData.contestNumMax,
        isBegin: cupData.isBegin,
        sponsorId: cupData.sponsorId,
        luckyValue: cupData.luckyValue,
        luckyMax: cupData.luckyMax,
        rivalTeamList: cupData.rivalTeamList,
        awardList: cupData.awardList,
        combatList: cupData.combatList,
      };
    } catch (error) {
      this.logger.error('参加区域杯赛失败', error);
      return { code: -1, message: '参加失败' };
    }
  }

  /**
   * 区域杯赛战斗
   * 基于old项目: MiddleEastCupBattle、gulfCupBattle等的业务逻辑实现
   * 修复：实现缺失的区域杯赛战斗功能
   */
  async regionalCupBattle(dto: RegionalCupBattleDto): Promise<any> {
    this.logger.log(`区域杯赛战斗: ${dto.characterId}, 类型: ${dto.cupType}`);

    try {
      // 查找锦标赛数据
      const tournament = await this.tournamentRepository.findByUid(dto.characterId);
      if (!tournament) {
        return { code: -1, message: '锦标赛数据不存在' };
      }

      // 获取区域杯赛数据
      const cupData = this.getRegionalCupData(tournament, dto.cupType);
      if (!cupData) {
        return { code: -2, message: '不支持的杯赛类型' };
      }

      if (cupData.isBegin !== 1) {
        return { code: -3, message: '尚未参加该杯赛' };
      }

      // 检查是否还有未完成的战斗
      if (!cupData.rivalTeamList || cupData.rivalTeamList.length === 0) {
        return { code: -4, message: '没有可战斗的对手' };
      }

      // 获取当前对战的队伍ID
      let currentEnemyTeamId = null;
      if (dto.enemyTeamId) {
        // 如果指定了对手ID，验证其有效性
        if (!cupData.rivalTeamList.includes(dto.enemyTeamId)) {
          return { code: -5, message: '无效的对手队伍ID' };
        }
        currentEnemyTeamId = dto.enemyTeamId;
      } else {
        // 自动选择下一个对手（第一个未战斗的）
        currentEnemyTeamId = cupData.rivalTeamList[0];
      }

      // 获取角色阵容数据
      const formationResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'formation.getFormations',
        { characterId: dto.characterId, serverId: dto.serverId || 'server_001' }
      );

      if (!formationResult || formationResult.code !== 0) {
        return { code: -6, message: '获取阵容数据失败' };
      }

      // 转换阵容数据为战斗数据格式 - 使用共享服务
      const characterBattleData = await this.battleDataService.convertFormationToBattleData(formationResult.data, dto.characterId);

      if (!characterBattleData) {
        return { code: -7, message: '战斗数据转换失败' };
      }

      // 获取对手数据
      const enemyData = await this.getRegionalCupEnemyData(currentEnemyTeamId);
      if (!enemyData) {
        return { code: -8, message: '获取对手数据失败' };
      }

      // 调用战斗服务进行PVE战斗
      const battleResult = await this.battleService.pveBattle({
        characterId: dto.characterId,
        characterBattleData: characterBattleData,
        enemyBattleData: enemyData,
        battleType: `regional_${dto.cupType}`,
      });

      if (!battleResult || battleResult.code !== 0) {
        return { code: -9, message: '战斗计算失败' };
      }

      // 处理战斗结果
      const processResult = await this.processRegionalCupBattleResult(
        dto.characterId,
        dto.cupType,
        cupData,
        currentEnemyTeamId,
        battleResult.homeScore,
        battleResult.awayScore
      );

      return {
        code: 0,
        message: '战斗完成',
        result: processResult.result,
        isFinished: processResult.isFinished,
        currentRound: processResult.currentRound,
        totalRounds: processResult.totalRounds,
        battleRecord: battleResult.battleRecord,
        rewardInfo: processResult.rewardInfo,
        nextEnemyTeamId: processResult.nextEnemyTeamId,
      };
    } catch (error) {
      this.logger.error('区域杯赛战斗失败', error);
      return { code: -1, message: '战斗处理失败' };
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 初始化锦标赛数据
   */
  private async initTournamentData(characterId: string): Promise<Tournament> {
    try {
      const worldCupData: WorldCupData = {
        reTime: new Date(),
        buyCount: 0,
        joinCount: 1, // 默认每日1次
        isJoin: 0,
        groupMatch: {},
        groupNum: 1,
        worldCupId: 0,
        isGetReward: 0,
        isOut: 0,
        isRecord: 0,
        eliminateMatch: {},
      };

      const regionalCupData: RegionalCupData = {
        contestNum: 0,
        sponsorId: 0,
        isBegin: 0,
        rivalTeamList: [],
        teamList: [],
        awardList: [],
        flashTime: new Date(),
        contestNumMax: 3, // 默认每日3次
        luckyValue: 0,
        luckyMax: 100,
        conditionId: 0,
        goal: 0,
        fumble: 0,
        diff: 0,
        win: 0,
        combatList: [],
        rewardList: [],
      };

      return await this.tournamentRepository.upsert(characterId, {
        uid: characterId,
        worldCup: worldCupData,
        middleEastCup: { ...regionalCupData },
        gulfCup: { ...regionalCupData },
        mls: { ...regionalCupData },
        lastUpdateTime: new Date(),
      });
    } catch (error) {
      this.logger.error('初始化锦标赛数据失败', error);
      return null;
    }
  }

  /**
   * 检查每日重置
   */
  private async checkDailyReset(tournament: Tournament): Promise<void> {
    try {
      const now = new Date();
      const lastUpdate = new Date(tournament.lastUpdateTime);

      const isNewDay = now.getDate() !== lastUpdate.getDate() ||
                       now.getMonth() !== lastUpdate.getMonth() ||
                       now.getFullYear() !== lastUpdate.getFullYear();

      if (isNewDay) {
        await this.tournamentRepository.resetDailyData(tournament.uid);
      }
    } catch (error) {
      this.logger.error('检查每日重置失败', error);
    }
  }

  /**
   * 判断是否是今天
   */
  private isToday(date: Date): boolean {
    const now = new Date();
    const target = new Date(date);

    return now.getDate() === target.getDate() &&
           now.getMonth() === target.getMonth() &&
           now.getFullYear() === target.getFullYear();
  }

  /**
   * 处理世界杯跨天逻辑
   */
  private async handleWorldCupDayChange(tournament: Tournament): Promise<void> {
    try {
      if (tournament.worldCup) {
        tournament.worldCup.reTime = new Date();
        tournament.worldCup.joinCount = 1;
        tournament.worldCup.buyCount = 0;

        await this.tournamentRepository.updateByUid(tournament.uid, { worldCup: tournament.worldCup });
      }
    } catch (error) {
      this.logger.error('处理世界杯跨天逻辑失败', error);
    }
  }

  /**
   * 获取世界杯队伍配置
   */
  private async getWorldCupDefinition(worldCupId: number): Promise<any> {
    try {
      const definition = await this.gameConfig.worldCup.findOneBy('worldCupId', worldCupId);
      return definition;
    } catch (error) {
      this.logger.error('获取世界杯队伍配置失败', error);
      return null;
    }
  }

  /**
   * 生成世界杯队伍
   */
  private generateWorldCupTeams(config: any): any {
    // 简化的队伍生成逻辑，实际应该根据配置生成
    const teams = [];
    for (let i = 1; i <= 16; i++) {
      teams.push({
        teamId: i,
        teamName: `Team ${i}`,
        strength: Math.floor(Math.random() * 100000) + 50000,
        isCharacter: i === 1, // 第一个是玩家
      });
    }
    return { teams };
  }

  /**
   * 获取小组赛列表
   */
  private getGroupMatchList(allTeam: any): any {
    return allTeam;
  }

  /**
   * 获取世界杯对手数据
   */
  private getWorldCupEnemyData(worldCup: WorldCupData): any {
    // 简化的对手数据生成
    return {
      teamName: 'AI Team',
      strength: 80000,
      formation: 1,
      tactic: 1,
      heroes: [],
    };
  }

  /**
   * 处理世界杯战斗结果
   */
  private async processWorldCupBattleResult(
    characterId: string,
    worldCup: WorldCupData,
    selfScore: number,
    enemyScore: number
  ): Promise<any> {
    const result = selfScore > enemyScore ? 1 : 0;

    // 更新轮次
    worldCup.groupNum++;

    // 检查是否出局或完成
    if (result === 0 || worldCup.groupNum > 7) {
      worldCup.isOut = 1;
      worldCup.isGetReward = 1;
    }

    // 保存更新
    await this.tournamentRepository.updateByUid(characterId, { worldCup });

    return {
      result,
      isOut: worldCup.isOut,
      isGetReward: worldCup.isGetReward,
      groupNum: worldCup.groupNum,
      rewardInfo: {},
    };
  }

  /**
   * 获取购买世界杯费用
   */
  private async getBuyWorldCupGoldCost(): Promise<number> {
    try {
      const systemParam = await this.gameConfig.systemParam.get(6); // worldCupBuyGold的ID
      return systemParam?.parameter || 200;
    } catch (error) {
      return 200;
    }
  }

  /**
   * 获取世界杯奖励配置
   * 🔥 优化：使用filter方法，避免加载全量数据后查找
   */
  private async getWorldCupRewardDefinition(worldCupId: number, groupNum: number): Promise<any> {
    try {
      // 使用新的filter方法，直接根据复合条件筛选
      const worldCupRewardDefinitions = await this.gameConfig.worldCupReward.filter(definition =>
        definition.worldCupId === worldCupId && definition.round === groupNum
      );

      // 返回第一个匹配的配置，如果没有则返回null
      return worldCupRewardDefinitions.length > 0 ? worldCupRewardDefinitions[0] : null;
    } catch (error) {
      this.logger.error('获取世界杯奖励配置失败', error);
      return null;
    }
  }

  /**
   * 获取区域杯赛数据
   */
  private getRegionalCupData(tournament: Tournament, cupType: string): RegionalCupData | null {
    switch (cupType) {
      case 'middleEast':
        return tournament.middleEastCup;
      case 'gulf':
        return tournament.gulfCup;
      case 'mls':
        return tournament.mls;
      default:
        return null;
    }
  }

  /**
   * 生成对手队伍列表
   */
  private async generateRivalTeamList(cupType: string, teamIdList: number[]): Promise<number[]> {
    // 简化的对手生成逻辑
    const rivalList: number[] = [];
    for (let i = 0; i < 10; i++) {
      rivalList.push(Math.floor(Math.random() * 1000) + 1);
    }
    return rivalList;
  }

  /**
   * 获取区域杯赛对手数据
   * 基于现有League模块的成功实现，参考generateEnemyBattleData方法
   */
  private async getRegionalCupEnemyData(enemyTeamId: number): Promise<any> {
    try {
      // 参考League模块：获取队伍球员配置
      const definitions = await this.getTeamDefinitions(enemyTeamId);
      if (!definitions || definitions.length === 0) {
        this.logger.error(`未找到队伍球员配置: teamId=${enemyTeamId}`);
        return null;
      }

      // 使用默认等级（区域杯赛敌方等级）
      const enemyLevel = 15; // 区域杯赛敌方等级

      // 转换球员数据为战斗格式 - 参考League模块实现
      const heroes = definitions.map((teamDefinition, index) => ({
        heroId: `enemy_${enemyTeamId}_${teamDefinition.heroId}`,
        attack: this.calculateHeroAttribute(teamDefinition, 'attack', enemyLevel),
        defend: this.calculateHeroAttribute(teamDefinition, 'defend', enemyLevel),
        speed: this.calculateHeroAttribute(teamDefinition, 'speed', enemyLevel),
        power: this.calculateHeroAttribute(teamDefinition, 'power', enemyLevel),
        technique: this.calculateHeroAttribute(teamDefinition, 'technique', enemyLevel),
        position: this.convertPositionToEnum(teamDefinition.positionName),
        level: enemyLevel,
        skills: []
      }));

      // 构造敌方战斗数据 - 参考League模块格式
      const enemyBattleData = {
        characterId: `enemy_${enemyTeamId}`,
        teamName: definitions[0]?.name || `敌方队伍${enemyTeamId}`,
        formation: 442101, // 默认4-4-2阵型
        tactic: 101,       // 默认战术
        heroes: heroes,
        totalAttack: 0, // 由战斗引擎计算
        totalDefend: 0, // 由战斗引擎计算
        morale: 0,
        score: 0
      };

      this.logger.log(`区域杯赛敌方数据生成成功: 球员数量=${heroes.length}, 阵型=${enemyBattleData.formation}`);
      return enemyBattleData;

    } catch (error) {
      this.logger.error('获取区域杯赛对手数据失败', error);
      return null;
    }
  }

  /**
   * 获取指定队伍的球员配置列表
   * 从Team配置表中筛选出属于指定队伍的所有球员配置信息
   * 参考League模块的getTeamHeros方法
   */
  private async getTeamDefinitions(teamId: number): Promise<TeamDefinition[]> {
    try {
      const definitions = await this.gameConfig.team.findBy('teamId', teamId);

      if (!definitions || definitions.length === 0) {
        this.logger.warn(`未找到队伍球员配置: teamId=${teamId}`);
        return [];
      }

      this.logger.debug(`成功获取队伍${teamId}的球员配置，共${definitions.length}个球员`);
      return definitions;
    } catch (error) {
      this.logger.error('获取队伍球员配置失败', error);
      return [];
    }
  }

  /**
   * 计算球员属性值
   * 基于球员配置和等级计算最终属性值
   * 参考League模块的calculateHeroAttribute方法
   */
  private calculateHeroAttribute(teamDefinition: TeamDefinition, attributeType: string, level: number): number {
    const baseValue = 50; // 基础属性值
    const levelBonus = (level || 1) * 10; // 等级加成
    const randomFactor = Math.floor(Math.random() * 20); // 随机因子

    return baseValue + levelBonus + randomFactor;
  }

  /**
   * 转换位置名称为枚举值
   * 参考League模块的convertPositionToEnum方法
   */
  private convertPositionToEnum(positionName: string): HeroPosition {
    const positionMap: { [key: string]: HeroPosition } = {
      '门将': HeroPosition.GK,
      '中后卫': HeroPosition.DC,
      '左后卫': HeroPosition.DL,
      '右后卫': HeroPosition.DR,
      '中场': HeroPosition.MC,
      '左中场': HeroPosition.ML,
      '右中场': HeroPosition.MR,
      '左边锋': HeroPosition.WL,
      '右边锋': HeroPosition.WR,
      '前锋': HeroPosition.ST,
      '前腰': HeroPosition.AM,
      '后腰': HeroPosition.DM,
    };

    return positionMap[positionName] || HeroPosition.MC;
  }

  /**
   * 处理区域杯赛战斗结果
   * 基于old项目的战斗结果处理逻辑
   */
  private async processRegionalCupBattleResult(
    characterId: string,
    cupType: string,
    cupData: any,
    enemyTeamId: number,
    homeScore: number,
    awayScore: number
  ): Promise<any> {
    try {
      const isWin = homeScore > awayScore;
      const isDraw = homeScore === awayScore;

      // 更新杯赛数据
      cupData.goal += homeScore;
      cupData.fumble += awayScore;
      cupData.diff += (homeScore - awayScore);

      if (isWin) {
        cupData.win += 1;
      }

      // 记录战斗结果
      const battleRecord = {
        enemyTeamId,
        homeScore,
        awayScore,
        result: isWin ? 'win' : (isDraw ? 'draw' : 'lose'),
        timestamp: Date.now()
      };

      if (!cupData.combatList) {
        cupData.combatList = [];
      }
      cupData.combatList.push(battleRecord);

      // 移除已战斗的对手
      const enemyIndex = cupData.rivalTeamList.indexOf(enemyTeamId);
      if (enemyIndex > -1) {
        cupData.rivalTeamList.splice(enemyIndex, 1);
      }

      // 检查是否完成所有战斗
      const isFinished = cupData.rivalTeamList.length === 0;
      const currentRound = cupData.combatList.length;
      const totalRounds = 10; // 默认10轮战斗

      let rewardInfo = null;
      if (isFinished) {
        // 计算最终奖励
        rewardInfo = this.calculateRegionalCupRewards(cupData);
        cupData.isBegin = 0; // 重置状态
      }

      // 保存更新
      const updateData: any = {};
      updateData[`${cupType}Cup`] = cupData;
      await this.tournamentRepository.updateByUid(characterId, updateData);

      return {
        result: isWin ? 'win' : (isDraw ? 'draw' : 'lose'),
        isFinished,
        currentRound,
        totalRounds,
        rewardInfo,
        nextEnemyTeamId: cupData.rivalTeamList.length > 0 ? cupData.rivalTeamList[0] : null,
      };
    } catch (error) {
      this.logger.error('处理区域杯赛战斗结果失败', error);
      return {
        result: 'error',
        isFinished: false,
        currentRound: 0,
        totalRounds: 10,
        rewardInfo: null,
        nextEnemyTeamId: null,
      };
    }
  }

  /**
   * 计算区域杯赛奖励
   */
  private calculateRegionalCupRewards(cupData: any): any {
    // 基于胜场数和积分计算奖励
    const winCount = cupData.win || 0;
    const goalDiff = cupData.diff || 0;

    const rewards = [];

    // 基础奖励
    if (winCount >= 5) {
      rewards.push({ type: 'cash', amount: 10000 });
      rewards.push({ type: 'gold', amount: 1000 });
    } else if (winCount >= 3) {
      rewards.push({ type: 'cash', amount: 5000 });
      rewards.push({ type: 'gold', amount: 500 });
    }

    // 净胜球奖励
    if (goalDiff > 10) {
      rewards.push({ type: 'worldCoin', amount: 1000 });
    }

    return {
      winCount,
      goalDiff,
      rewards
    };
  }




}
