# 认证服务开发规范

## 🎯 **概述**

本文档定义了认证服务的开发规范，确保代码质量、架构一致性和团队协作效率。

## 🏗️ **架构规范**

### **分层架构遵循**

#### **模块放置规则**

| 层次 | 目录 | 用途 | 示例 |
|------|------|------|------|
| 应用层 | `app/` | 系统管理、运维功能 | AdminModule, HealthModule |
| 业务层 | `domain/` | 核心业务逻辑 | AuthModule, UsersModule |
| 核心层 | `core/` | 领域核心服务 | SecurityModule, SessionModule |
| 基础设施层 | `infrastructure/` | 技术基础设施 | GuardsModule, InterceptorsModule |
| 通用层 | `common/` | 跨层共享资源 | interfaces, constants, dto |

#### **依赖方向规则**

```typescript
// ✅ 正确的依赖方向
import { UsersService } from '../../domain/users/users.service';     // 应用层 → 业务层
import { PasswordService } from '../../core/shared/password.service'; // 业务层 → 核心层
import { CryptoService } from '../../infrastructure/services/crypto.service'; // 核心层 → 基础设施层

// ❌ 错误的依赖方向
import { AdminService } from '../../app/admin/admin.service';         // 业务层 → 应用层 (禁止)
import { AuthService } from '../../domain/auth/auth.service';         // 核心层 → 业务层 (禁止)
```

## 📁 **文件组织规范**

### **模块结构标准**

```
module-name/
├── controllers/           # 控制器
│   ├── module-name.controller.ts
│   └── sub-feature.controller.ts
├── services/             # 服务
│   ├── module-name.service.ts
│   └── sub-feature.service.ts
├── entities/             # 实体定义
│   └── module-name.entity.ts
├── dto/                  # 数据传输对象
│   ├── create-module-name.dto.ts
│   └── update-module-name.dto.ts
├── repositories/         # 仓储层 (可选)
│   └── module-name.repository.ts
├── interfaces/           # 接口定义 (可选)
│   └── module-name.interface.ts
├── guards/              # 模块专用守卫 (可选)
├── decorators/          # 模块专用装饰器 (可选)
├── tests/               # 测试文件
│   ├── module-name.service.spec.ts
│   └── module-name.controller.spec.ts
├── module-name.module.ts # 模块定义
└── index.ts             # 导出文件 (可选)
```

### **命名约定**

#### **文件命名**
- **模块**: `feature-name.module.ts`
- **控制器**: `feature-name.controller.ts`
- **服务**: `feature-name.service.ts`
- **实体**: `feature-name.entity.ts`
- **DTO**: `create-feature-name.dto.ts`, `update-feature-name.dto.ts`
- **接口**: `feature-name.interface.ts`
- **常量**: `feature-name.constants.ts`
- **测试**: `feature-name.spec.ts`

#### **类命名**
```typescript
// ✅ 正确命名
export class UsersController {}
export class UsersService {}
export class User {}
export class CreateUserDto {}
export class UserRepository {}

// ❌ 错误命名
export class userController {}    // 首字母应大写
export class Users_Service {}     // 不使用下划线
export class userEntity {}        // 缺少适当后缀
```

#### **变量和方法命名**
```typescript
// ✅ 正确命名
const maxRetryAttempts = 3;
const isUserActive = true;
async function validateUserCredentials() {}
private async hashPassword() {}

// ❌ 错误命名
const max_retry = 3;              // 使用驼峰命名
const user_active = true;         // 使用驼峰命名
async function validate_user() {} // 使用驼峰命名
```

## 🔧 **代码规范**

### **模块定义规范**

```typescript
// ✅ 标准模块结构
@Module({
  imports: [
    // 1. 外部模块 (NestJS, 第三方)
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema }
    ]),
    
    // 2. 依赖的业务模块
    CoreModule,
    
    // 3. 同层模块 (如需要)
    RolesModule,
  ],
  controllers: [
    UsersController,
  ],
  providers: [
    UsersService,
    UserRepository,
  ],
  exports: [
    UsersService,
    UserRepository,
  ],
})
export class UsersModule {}
```

### **服务定义规范**

```typescript
// ✅ 标准服务结构
@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private passwordService: PasswordService,
    private configService: ConfigService,
  ) {}

  // 公共方法
  async createUser(createUserDto: CreateUserDto): Promise<UserDocument> {
    this.logger.log(`Creating user: ${createUserDto.email}`);
    
    try {
      // 实现逻辑
      return await this.userModel.create(createUserDto);
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`);
      throw new BadRequestException('用户创建失败');
    }
  }

  // 私有方法
  private async validateUserData(userData: CreateUserDto): Promise<void> {
    // 验证逻辑
  }
}
```

### **控制器定义规范**

```typescript
// ✅ 标准控制器结构
@ApiTags('用户管理')
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({ status: 201, description: '用户创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @UseGuards(RolesGuard)
  @Roles('admin')
  async create(
    @Body() createUserDto: CreateUserDto,
    @CurrentUser() currentUser: UserDocument,
  ): Promise<ApiResponseDto<UserDocument>> {
    const user = await this.usersService.createUser(createUserDto);
    return {
      success: true,
      message: '用户创建成功',
      data: user,
    };
  }
}
```

## 🧪 **测试规范**

### **测试文件组织**

```
tests/
├── unit/                 # 单元测试
│   ├── services/
│   ├── controllers/
│   └── guards/
├── integration/          # 集成测试
│   ├── auth/
│   └── users/
├── e2e/                 # 端到端测试
│   ├── auth.e2e-spec.ts
│   └── users.e2e-spec.ts
└── fixtures/            # 测试数据
    ├── users.fixture.ts
    └── auth.fixture.ts
```

### **单元测试规范**

```typescript
// ✅ 标准测试结构
describe('UsersService', () => {
  let service: UsersService;
  let userModel: Model<UserDocument>;
  let passwordService: PasswordService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
        {
          provide: PasswordService,
          useValue: mockPasswordService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userModel = module.get<Model<UserDocument>>(getModelToken(User.name));
    passwordService = module.get<PasswordService>(PasswordService);
  });

  describe('createUser', () => {
    it('应该成功创建用户', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };
      
      const expectedUser = { ...createUserDto, _id: 'user-id' };
      jest.spyOn(userModel, 'create').mockResolvedValue(expectedUser as any);

      // Act
      const result = await service.createUser(createUserDto);

      // Assert
      expect(result).toEqual(expectedUser);
      expect(userModel.create).toHaveBeenCalledWith(createUserDto);
    });

    it('应该在邮箱重复时抛出异常', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };
      
      jest.spyOn(userModel, 'create').mockRejectedValue(new Error('Duplicate email'));

      // Act & Assert
      await expect(service.createUser(createUserDto)).rejects.toThrow(BadRequestException);
    });
  });
});
```

## 📝 **文档规范**

### **代码注释规范**

```typescript
/**
 * 用户服务
 * 
 * 提供用户管理的核心功能，包括：
 * - 用户创建和更新
 * - 用户查询和验证
 * - 密码管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Injectable()
export class UsersService {
  /**
   * 创建新用户
   * 
   * @param createUserDto 用户创建数据
   * @returns 创建的用户实体
   * @throws BadRequestException 当用户数据无效时
   * @throws ConflictException 当邮箱已存在时
   * 
   * @example
   * ```typescript
   * const user = await usersService.createUser({
   *   email: '<EMAIL>',
   *   password: 'securePassword',
   *   name: 'John Doe'
   * });
   * ```
   */
  async createUser(createUserDto: CreateUserDto): Promise<UserDocument> {
    // 实现逻辑
  }
}
```

### **API 文档规范**

```typescript
@ApiOperation({
  summary: '创建用户',
  description: '创建新的用户账户，需要管理员权限',
})
@ApiBody({
  type: CreateUserDto,
  description: '用户创建数据',
  examples: {
    example1: {
      summary: '标准用户',
      value: {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        name: 'John Doe',
        role: 'user'
      }
    }
  }
})
@ApiResponse({
  status: 201,
  description: '用户创建成功',
  type: ApiResponseDto,
})
@ApiResponse({
  status: 400,
  description: '请求参数错误',
  schema: {
    example: {
      success: false,
      message: '邮箱格式不正确',
      error: 'INVALID_EMAIL'
    }
  }
})
```

## 🔍 **代码审查清单**

### **架构合规性**
- [ ] 模块放置在正确的层次目录
- [ ] 依赖方向符合架构规则
- [ ] 没有循环依赖问题
- [ ] 模块职责单一明确

### **代码质量**
- [ ] 命名规范符合约定
- [ ] 代码结构清晰
- [ ] 错误处理完善
- [ ] 日志记录适当

### **测试覆盖**
- [ ] 单元测试覆盖率 > 80%
- [ ] 关键路径有集成测试
- [ ] 边界条件测试完整
- [ ] 错误场景测试覆盖

### **文档完整性**
- [ ] 公共API有完整注释
- [ ] Swagger文档准确
- [ ] README更新
- [ ] 变更日志记录

### **安全性**
- [ ] 输入验证完整
- [ ] 权限控制正确
- [ ] 敏感信息保护
- [ ] SQL注入防护

## 🚀 **开发流程**

### **新功能开发**

1. **需求分析**
   - 确定功能归属层次
   - 设计API接口
   - 评估影响范围

2. **设计阶段**
   - 创建接口定义 (common层)
   - 设计数据结构
   - 规划模块依赖

3. **实现阶段**
   - 按层次顺序开发
   - 编写单元测试
   - 实现集成测试

4. **验证阶段**
   - 运行架构测试
   - 执行功能测试
   - 进行代码审查

5. **文档更新**
   - 更新API文档
   - 补充使用示例
   - 记录变更日志

### **Bug修复流程**

1. **问题定位**
   - 确定问题所在层次
   - 分析影响范围
   - 制定修复方案

2. **修复实施**
   - 编写失败测试
   - 实现修复代码
   - 确保测试通过

3. **验证测试**
   - 运行相关测试
   - 检查回归问题
   - 验证修复效果

## 📊 **质量指标**

### **代码质量指标**
- 测试覆盖率: ≥ 80%
- 圈复杂度: ≤ 10
- 代码重复率: ≤ 5%
- 技术债务: ≤ 1小时/千行代码

### **架构健康度指标**
- 循环依赖: 0个
- 架构违规: 0个
- 模块耦合度: ≤ 30%
- 接口稳定性: ≥ 95%

遵循这些开发规范，确保认证服务的代码质量和架构一致性，提高团队开发效率和系统可维护性。
