# Hero微服务开发指南

## 概述

本文档总结了Hero微服务开发过程中的经验教训、最佳实践和必须遵守的约束规范，旨在指导后续开发工作，避免重复犯错。

## 核心原则（不可违背）

### 1. 严格遵循old项目功能
- **✅ 可以优化重构，但不能遗漏old中有的功能**
- **❌ 不能无中生有添加old项目中不存在的功能**
- **✅ 静态数据结构必须严格遵守game-config中的实际配置字段**

### 2. 配置表使用原则
- **✅ 只使用GameConfigFacade中实际存在的配置表**
- **✅ 只使用配置接口中实际存在的字段**
- **❌ 禁止假设或猜测配置表结构**
- **❌ 禁止使用不存在的配置表（如scoutScope、footballerPool等）**

### 3. 开发流程约束
- **必须先分析old项目的真实实现**
- **必须先查看配置表的实际字段结构**
- **必须先验证编译通过再继续下一步**

## 重大问题与解决方案

### 问题1：功能缺失
**问题描述**：模块创建时遗漏了old项目的核心功能

**典型案例**：
- ScoutModule缺失`getScoutReward`（球探探索）
- CultivationModule缺失`cultivateHero`、`breakOutHero`、`heroUpStar`
- TrainingModule被错误地从hero模块分离

**解决方案**：
1. 使用codebase-retrieval工具分析old项目完整功能
2. 创建功能对照表，确保100%覆盖
3. 优先实现核心功能，再考虑架构优化

### 问题2：配置表滥用
**问题描述**：使用了不存在的配置表或字段

**典型案例**：
```typescript
// ❌ 错误：使用不存在的配置
await this.gameConfig.scoutScope.getAll();
await this.gameConfig.footballerPool.getAll();

// ✅ 正确：使用实际存在的配置
await this.gameConfig.heroPool.getAll();
await this.gameConfig.hero.get(heroId);
```

**解决方案**：
1. 先查看`libs/game-config/src/facades/game-config.facade.ts`确认可用配置
2. 查看对应的interface文件确认字段结构
3. 严格按照实际字段编写代码

### 问题3：数据结构不匹配
**问题描述**：Schema字段与old项目数据结构不符

**典型案例**：
```typescript
// ❌ 错误：使用不存在的字段
hero.configId  // 应该是 hero.resId
hero.attack    // 应该是 hero.attributes.shooting

// ✅ 正确：使用实际字段
hero.resId
hero.attributes.shooting
```

**解决方案**：
1. 分析old项目的实际数据结构
2. 添加缺失的Schema字段
3. 修复字段访问路径

## 模块化架构最佳实践

### 1. 模块职责划分
```
apps/hero/src/modules/
├── hero/          # 基础CRUD操作
├── cultivation/   # 养成系统（cultivateHero, breakOutHero, heroUpStar）
├── scout/         # 球探系统（getScoutReward, signScoutHero）
├── career/        # 生涯管理（promoteHeroStatus, renewTheContract）
├── training/      # 训练系统（trainHero, getTrainInfo）
└── skill/         # 技能系统
```

### 2. 共享资源管理
- **GameConfigModule**：在app.module.ts中全局注册，子模块无需重复导入
- **MongooseModule.forFeature()**：每个使用Schema的模块都需要注册
- **HeroRepository**：在需要的模块中注入，避免重复实现

### 3. API设计原则
```typescript
// ✅ 正确：基于old项目的API设计
@MessagePattern('hero.cultivation.cultivate')  // 对应old项目的cultivateHero
@MessagePattern('hero.scout.explore')          // 对应old项目的getScoutReward
@MessagePattern('hero.career.renewContract')  // 对应old项目的renewTheContract

// ❌ 错误：自创的API
@MessagePattern('hero.enhance')
@MessagePattern('hero.upgrade')
```

## 技术实现规范

### 1. 错误处理
```typescript
// ✅ 正确：返回old项目兼容的错误码
return { code: -1, message: '球员不存在' };

// ❌ 错误：抛出异常
throw new NotFoundException('球员不存在');
```

### 2. 配置访问
```typescript
// ✅ 正确：先验证配置存在
const heroConfig = await this.gameConfig.hero.get(hero.resId);
if (!heroConfig) {
  return { code: -2, message: '球员配置不存在' };
}

// ❌ 错误：直接使用可能不存在的配置
const heroConfig = await this.gameConfig.hero.get(hero.resId);
const position = heroConfig.position; // 可能报错
```

### 3. 数据更新
```typescript
// ✅ 正确：使用实际字段路径
await this.heroRepository.updateById(heroId, {
  'attributes.shooting': newValue,
  oneLevelAttr: hero.oneLevelAttr,
  isStage: hero.isStage,
});

// ❌ 错误：使用不存在的字段
await this.heroRepository.updateById(heroId, {
  attack: newValue,
  configId: hero.configId,
});
```

## 开发检查清单

### 功能实现前
- [ ] 分析old项目中对应功能的完整实现
- [ ] 确认所需配置表在GameConfigFacade中存在
- [ ] 查看配置接口的实际字段结构
- [ ] 确认Schema中有所需的数据字段

### 功能实现中
- [ ] 严格按照old项目的参数和返回值结构
- [ ] 使用实际存在的配置表和字段
- [ ] 保持与old项目一致的错误码和消息
- [ ] 添加必要的TODO注释标记未完成部分

### 功能实现后
- [ ] 验证TypeScript编译通过
- [ ] 检查是否有无中生有的功能
- [ ] 确认API路径与功能对应关系正确
- [ ] 更新相关文档和注释

## 常见错误及避免方法

### 1. 配置表错误
```typescript
// ❌ 常见错误
this.gameConfig.scoutScope        // 不存在
this.gameConfig.footballerPool    // 不存在
this.gameConfig.trainingGround     // 不存在

// ✅ 正确使用
this.gameConfig.heroPool          // 存在
this.gameConfig.feildTrainning     // 存在（注意拼写）
this.gameConfig.heroTrain          // 存在
```

### 2. 字段访问错误
```typescript
// ❌ 常见错误
hero.configId                     // 应该是 resId
hero.position                     // 应该是 position1
heroConfig.position               // 应该是 position1
skillConfig.name                  // 可能不存在

// ✅ 正确访问
hero.resId
heroConfig.position1
skillConfig.skillName
```

### 3. 模块注册错误
```typescript
// ❌ 错误：重复注册全局模块
@Module({
  imports: [
    GameConfigModule,              // 已在app.module.ts中注册
    MongooseModule.forRootAsync(), // 已在app.module.ts中注册
  ],
})

// ✅ 正确：只注册需要的Schema
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Hero.name, schema: HeroSchema },
    ]),
  ],
})
```

## 配置系统优化经验（重要）

### **重大发现：game-config库职责分离问题**

在微服务开发过程中，我们发现了game-config公共库的严重架构问题，并成功进行了优化。

#### **问题描述**
- **双重OnModuleInit**：ConfigManager和ConfigPreloader都在OnModuleInit中执行配置加载
- **职责边界混乱**：两个服务都承担了配置预加载的职责
- **Redis API调用错误**：错误使用getClient()直接操作ioredis
- **性能问题**：每个微服务启动都重复加载配置，启动时间15秒

#### **解决方案**
1. **职责分离优化**：
   ```typescript
   // ConfigManager: OnModuleInit - 只做基础初始化
   async onModuleInit() {
     this.events.startWatching();
     this.events.onConfigUpdated(async (event) => {
       await this.handleConfigUpdate(event);
     });
   }

   // ConfigPreloader: OnApplicationBootstrap - 配置预加载
   async onApplicationBootstrap() {
     await this.ensureConfigsPreloaded();
   }
   ```

2. **Redis API修复**：
   ```typescript
   // ✅ 正确：使用RedisService封装方法 + global类型
   await this.redisService.exists('config:Hero:all', 'global');
   await this.redisService.set('config:loading:lock', lockValue, 300, 'global');

   // ❌ 错误：直接操作ioredis
   await this.redisService.getClient().set(key, value);
   ```

3. **分布式锁机制**：
   ```typescript
   // 检查配置是否已存在
   const isReady = await this.checkConfigReady();
   if (isReady) return;

   // 尝试获取分布式锁
   const lockAcquired = await this.tryAcquireLoadingLock();
   if (lockAcquired) {
     try {
       await this.preloadConfigs();
       await this.markConfigReady();
     } finally {
       await this.releaseLoadingLock();
     }
   } else {
     await this.waitForConfigReady();
   }
   ```

#### **性能提升结果**
- **第一个服务启动**：15秒 → 3.6秒（76%↓）
- **第二个服务启动**：15秒 → 1秒（93%↓）
- **配置重复加载**：完全消除
- **Redis写入操作**：减少90%+

### **关键经验教训**

1. **严格遵循NestJS生命周期**：
   - OnModuleInit：模块依赖初始化
   - OnApplicationBootstrap：应用启动后的优化任务

2. **正确使用Redis公共库**：
   - 配置数据：使用global类型，支持跨服务共享
   - 分布式锁：使用global类型，避免区服前缀
   - 避免直接操作ioredis，使用RedisService封装

3. **分布式系统设计原则**：
   - 使用分布式锁避免重复操作
   - 第一个服务执行，后续服务检查并跳过
   - 正确的错误处理和超时机制

## 总结

本指南基于Hero微服务开发过程中的实际经验总结，强调了严格遵循old项目实现和正确使用配置表的重要性。遵循这些原则和规范，可以避免大部分开发陷阱，确保代码质量和功能完整性。

**核心要点**：
1. **old项目是功能的唯一权威来源**
2. **game-config是数据结构的唯一权威来源**
3. **编译通过是代码质量的基本要求**
4. **模块化是架构优化的正确方向**
5. **配置系统优化是性能提升的关键**

### **重要提醒**
在开发新的微服务时，务必注意：
- 正确使用game-config库的职责分离模式
- 遵循Redis公共库的API调用规范
- 实施分布式锁机制避免配置重复加载
- 优先使用OnApplicationBootstrap进行配置预加载

---
*文档版本：v2.0*
*创建日期：2025-07-14*
*最后更新：2025-07-19*
