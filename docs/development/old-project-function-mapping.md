# Old项目功能对照表

## 概述

本文档详细列出了old项目中所有hero相关功能及其在新架构中的对应实现，确保功能迁移的完整性。

## PlayerHandler功能对照

### ✅ 已实现功能

| Old项目方法 | 功能描述 | 新架构位置 | 实现状态 |
|------------|----------|------------|----------|
| `getPlayerInfo` | 获取球员信息 | `hero.hero.getInfo` | ✅ 完成 |
| `getPlayerList` | 获取球员列表 | `hero.hero.getList` | ✅ 完成 |
| `getTrainInfo` | 获取训练信息 | `hero.training.getTrainInfo` | ✅ 完成 |
| `trainHero` | 训练球员 | `hero.training.train` | ✅ 完成 |
| `replaceHeroTrain` | 替换特训 | `hero.training.replaceHeroTrain` | ✅ 完成 |
| `cultivateHero` | 球员养成 | `hero.cultivation.cultivate` | ✅ 完成 |
| `promoteHeroStatus` | 提升球员状态 | `hero.career.promoteStatus` | ✅ 完成 |
| `renewTheContract` | 续约球员 | `hero.career.renewContract` | ✅ 完成 |

### ❌ 缺失功能

| Old项目方法 | 功能描述 | 计划位置 | 优先级 |
|------------|----------|----------|--------|
| `oneKeyCultivateHero` | 一键养成 | `hero.cultivation.oneKeyCultivate` | 高 |
| `breakOutHero` | 球员突破 | `hero.cultivation.breakOut` | 高 |
| `heroUpStar` | 球员升星 | `hero.cultivation.upStar` | 高 |
| `getScoutPackInfo` | 获取球探包信息 | `hero.scout.getPackInfo` | 中 |
| `delScoutPackHero` | 删除球探包球员 | `hero.scout.deletePackHero` | 中 |
| `signScoutHero` | 签约球探球员 | `hero.scout.signHero` | 中 |

### ✅ 新增核心功能

| 功能描述 | 新架构位置 | 基于Old项目 | 实现状态 |
|----------|------------|-------------|----------|
| 球探探索 | `hero.scout.explore` | `getScoutReward` | ✅ 完成 |

## GroundService功能对照

### ❌ 完全缺失（需要创建GroundModule）

| Old项目方法 | 功能描述 | 计划位置 | 优先级 |
|------------|----------|----------|--------|
| `getHeroTrainInfo` | 获取场地训练信息 | `hero.ground.getTrainInfo` | 高 |
| `heroTrainInGround` | 场地训练 | `hero.ground.train` | 高 |
| `getHeroTrainReward` | 获取场地训练奖励 | `hero.ground.getReward` | 高 |
| `inputNotablePos` | 名人堂入驻 | `hero.ground.inputNotable` | 中 |
| `getNotableList` | 获取名人堂列表 | `hero.ground.getNotableList` | 中 |

## 数据结构对照

### Hero实体字段对照

| Old项目字段 | 新架构字段 | 类型 | 说明 |
|------------|------------|------|------|
| `uid` | `_id` | ObjectId | 球员唯一ID |
| `configId` | `resId` | number | 球员配置ID |
| `lifeNum` | `lifeNum` | number | 生涯次数（续约次数） |
| `contractDays` | `contractDays` | number | 合约剩余天数 |
| `treatyReTime` | `treatyReTime` | number | 合约刷新时间 |
| `Health` | `Health` | number | 健康状态（1健康,2轻微伤,3轻伤,4重伤） |
| `oldBreakOut` | `oldBreakOut` | number | 历史突破值 |
| `breakthrough` | `breakthrough` | number[] | 突破记录数组 |
| `oneLevelAttr` | `oneLevelAttr` | number[] | 一级属性 |
| `isStage` | `isStage` | number[] | 培养阶段 |

### Scout实体字段对照

| Old项目字段 | 新架构字段 | 类型 | 说明 |
|------------|------------|------|------|
| `scoutRp` | `scoutRp` | number | 球探RP值 |
| `scoutEnergy` | `scoutEnergy` | number | 球探体力 |
| `scoutGroup` | `scoutGroup` | array | 球探组（初级、高级、顶级） |
| `scoutPack` | `scoutPack` | array | 球探包（抽到的球员） |

## API路径对照

### 已实现API

| Old项目路径 | 新架构路径 | 参数对照 |
|------------|------------|----------|
| `game.playerHandler.getPlayerInfo` | `hero.hero.getInfo` | `uid` → `heroId` |
| `game.playerHandler.getPlayerList` | `hero.hero.getList` | `playerId` → `characterId` |
| `game.playerHandler.trainHero` | `hero.training.train` | `uid, index, arr` → `trainDto` |
| `game.playerHandler.cultivateHero` | `hero.cultivation.cultivate` | `uid, type, index` → `heroId, type, index` |
| `game.playerHandler.promoteHeroStatus` | `hero.career.promoteStatus` | `playerId, msg` → `heroId, itemId` |
| `game.playerHandler.renewTheContract` | `hero.career.renewContract` | `playerId, msg` → `heroUidList` |

### 缺失API

| Old项目路径 | 计划路径 | 说明 |
|------------|----------|------|
| `game.playerHandler.oneKeyCultivateHero` | `hero.cultivation.oneKeyCultivate` | 一键养成 |
| `game.playerHandler.breakOutHero` | `hero.cultivation.breakOut` | 球员突破 |
| `game.playerHandler.heroUpStar` | `hero.cultivation.upStar` | 球员升星 |
| `game.groundService.getHeroTrainInfo` | `hero.ground.getTrainInfo` | 场地训练信息 |
| `game.groundService.heroTrainInGround` | `hero.ground.train` | 场地训练 |
| `game.groundService.getHeroTrainReward` | `hero.ground.getReward` | 场地训练奖励 |

## 错误码对照

### Old项目错误码

| 错误码 | 含义 | 新架构处理 |
|--------|------|------------|
| `0` | 成功 | `{ code: 0, message: 'success' }` |
| `-1` | 通用失败 | `{ code: -1, message: '操作失败' }` |
| `MAX_FAIL` | 属性已满 | `{ code: -1, message: '属性已达到最大值' }` |
| `CONFIG_FAIL` | 配置错误 | `{ code: -2, message: '配置不存在' }` |
| `MONEY_FAIL` | 金币不足 | `{ code: -3, message: '金币不足' }` |
| `ITEM_FAIL` | 道具不足 | `{ code: -4, message: '道具不足' }` |

## 实现优先级

### 高优先级（立即实现）
1. **CultivationModule补全**
   - `oneKeyCultivateHero` - 一键养成
   - `breakOutHero` - 球员突破
   - `heroUpStar` - 球员升星

2. **GroundModule创建**
   - 场地训练系统完整实现
   - 名人堂系统

### 中优先级（后续实现）
1. **ScoutModule补全**
   - `getScoutPackInfo` - 球探包信息
   - `delScoutPackHero` - 删除球探包球员
   - `signScoutHero` - 签约球探球员

2. **数据结构完善**
   - 添加缺失的Schema字段
   - 完善数据验证

### 低优先级（优化阶段）
1. **性能优化**
   - 缓存策略优化
   - 批量操作优化

2. **错误处理完善**
   - 统一错误码管理
   - 详细错误信息

## 验证清单

### 功能完整性验证
- [ ] 所有old项目的hero相关方法都有对应实现
- [ ] 所有API参数和返回值结构与old项目兼容
- [ ] 所有错误码与old项目一致

### 数据结构验证
- [ ] 所有old项目的数据字段都在新Schema中存在
- [ ] 字段类型与old项目一致
- [ ] 数据访问路径正确

### 配置使用验证
- [ ] 只使用实际存在的配置表
- [ ] 字段名与配置接口一致
- [ ] 处理了配置不存在的情况

---
*文档版本：v1.0*  
*创建日期：2025-07-14*  
*最后更新：2025-07-14*
