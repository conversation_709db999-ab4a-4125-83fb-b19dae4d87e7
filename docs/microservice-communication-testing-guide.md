# 微服务通信测试指南

## 📋 **测试概述**

本指南提供了完整的微服务通信测试方案，涵盖网关HTTP代理、WebSocket代理、透明微服务调用等多个层面的验证。

---

## 🎯 **测试目标**

### **1. 网关通信架构验证**
- HTTP代理功能测试 (REST API)
- WebSocket代理功能测试 (实时通信)
- 微服务透明调用测试
- 通信性能和稳定性验证

### **2. 使用场景分离验证**
根据 `gateway-comprehensive-design-guide.md` 文档：

#### **HTTP代理场景**
- RESTful API代理 (GET/POST/PUT/DELETE)
- 认证流程 (login/refresh/logout)
- 文件上传 (avatar/club-logo)
- 管理后台操作

#### **WebSocket代理场景**
- 实时比赛更新
- 即时聊天系统
- 微服务透明调用
- 实时通知推送
- 游戏状态同步

### **3. 高级特性验证**
- 缓存机制的有效性
- 熔断器的保护功能
- 限流和安全功能
- 监控和指标收集

---

## 🏗️ **当前架构状态**

### **网关通信架构**
```
┌─────────────────────────────────────────────────────────────┐
│                    网关层 (Gateway:3000)                   │
├─────────────────────────────────────────────────────────────┤
│  HTTP代理        │  WebSocket代理   │  监控模块             │
│  (ClientProxy)   │  (MicroserviceClient) │  (Monitoring)   │
├─────────────────────────────────────────────────────────────┤
│              Redis通信层 (192.168.200.200:6379)            │
│                     db:6 (微服务通信)                       │
├─────────────────────────────────────────────────────────────┤
│   Auth Service  │  User Service  │  Game Service  │ ...     │
│     (3001)      │     (3002)     │     (3003)     │         │
└─────────────────────────────────────────────────────────────┘
```

### **已实现的组件**
```
✅ 网关服务 (apps/gateway:3000)
├── HTTP代理: GatewayClientsModule (原生ClientProxy)
├── WebSocket代理: MicroserviceClient (共享库)
├── 监控模块: MonitoringModule (统一监控)
└── 启动成功，支持HTTP和WebSocket

✅ 认证服务 (apps/auth:3001)
├── 已添加 @ExposeToMicroservice 装饰器
├── 暴露方法: verifyToken, checkPermission, getUserInfo
├── 集成 MicroservicesModule
└── 启动成功，Redis通信正常

✅ 共享库 (libs/common/src/microservices)
├── 集成 WebSocket 网关
├── 支持透明微服务调用
├── 启动成功，端口 3000
└── 健康检查正常
```

---

## 🧪 **测试分层策略**

### **第1层：单元测试**
测试各个组件的独立功能

```typescript
// 测试装饰器注册
describe('ExposeToMicroservice Decorator', () => {
  it('should register method in registry', () => {
    // 验证方法是否正确注册到 MicroserviceMethodRegistry
  });
});

// 测试方法注册中心
describe('MicroserviceMethodRegistry', () => {
  it('should store and retrieve method info', () => {
    // 验证方法信息的存储和检索
  });
});
```

### **第2层：集成测试**
测试微服务间的通信

```typescript
// 测试微服务客户端
describe('MicroserviceClient', () => {
  it('should call remote service method', async () => {
    const result = await microserviceClient.call(
      MICROSERVICE_NAMES.AUTH_SERVICE,
      'verifyToken',
      { token: 'test-token' }
    );
    expect(result).toBeDefined();
  });
});
```

### **第3层：端到端测试**
测试完整的调用链路

```typescript
// 测试 WebSocket 透明调用
describe('WebSocket Transparent Calls', () => {
  it('should handle microservice call via WebSocket', async () => {
    const ws = new WebSocket('ws://localhost:3000');
    ws.send(JSON.stringify({
      event: 'microservice.call',
      data: {
        service: 'auth',
        method: 'verifyToken',
        params: { token: 'test-token' }
      }
    }));
    // 验证响应
  });
});
```

---

## 📝 **具体测试用例**

### **测试用例1: 方法注册验证**

**目标**: 验证认证服务的方法是否正确注册

**步骤**:
1. 启动认证服务
2. 检查控制台输出的方法注册信息
3. 验证注册的方法数量和名称

**预期结果**:
```
✅ Registered microservice method: auth.verifyToken
✅ Registered microservice method: auth.checkPermission  
✅ Registered microservice method: auth.getUserInfo
```

### **测试用例2: 透明调用验证**

**目标**: 验证通过网关的透明微服务调用

**步骤**:
1. 建立 WebSocket 连接到网关
2. 发送微服务调用请求
3. 验证响应格式和内容

**测试脚本**:
```javascript
// scripts/test-transparent-calls.js
const WebSocket = require('ws');

async function testTransparentCall() {
  const ws = new WebSocket('ws://localhost:3000');
  
  ws.on('open', () => {
    // 测试令牌验证调用
    ws.send(JSON.stringify({
      event: 'microservice.call',
      data: {
        service: 'auth',
        method: 'verifyToken',
        params: { token: 'test-token' },
        requestId: 'test_001'
      }
    }));
  });
  
  ws.on('message', (data) => {
    const response = JSON.parse(data);
    console.log('Response:', response);
    // 验证响应结构
    assert(response.success !== undefined);
    assert(response.requestId === 'test_001');
  });
}
```

### **测试用例3: 错误处理验证**

**目标**: 验证错误情况的正确处理

**步骤**:
1. 调用不存在的服务方法
2. 传递无效参数
3. 验证错误响应格式

**预期结果**:
```json
{
  "success": false,
  "error": "Method auth.nonExistentMethod not found",
  "requestId": "test_002"
}
```

### **测试用例4: 缓存机制验证**

**目标**: 验证方法调用的缓存功能

**步骤**:
1. 首次调用带缓存的方法
2. 立即再次调用相同方法
3. 验证第二次调用使用缓存

**验证方法**:
- 检查响应时间差异
- 查看日志中的缓存命中信息

---

## 🚀 **测试执行计划**

### **阶段1: 基础功能验证 (当前)**
```bash
# 1. 启动服务
npm run start:auth    # 端口 3001
npm run start:gateway # 端口 3000

# 2. 验证服务启动
curl http://localhost:3001/api/health
curl http://localhost:3000/health

# 3. 检查方法注册
# 查看认证服务启动日志中的注册信息
```

### **阶段2: 透明调用测试**
```bash
# 运行透明调用测试脚本
node scripts/test-transparent-calls.js
```

### **阶段3: 完整功能测试**
```bash
# 运行完整的微服务通信测试
node scripts/test-microservice-integration.js
```

---

## 🔍 **问题排查指南**

### **常见问题1: 方法未注册**
**症状**: 调用时提示 "Method not found"
**排查**:
1. 检查 `@ExposeToMicroservice` 装饰器是否正确添加
2. 确认服务启动时的注册日志
3. 验证方法名和服务名是否正确

### **常见问题2: WebSocket连接失败**
**症状**: WebSocket无法连接到网关
**排查**:
1. 确认网关服务正在运行
2. 检查端口是否被占用
3. 验证防火墙设置

### **常见问题3: 调用超时**
**症状**: 微服务调用超时
**排查**:
1. 检查目标服务是否正在运行
2. 验证Redis连接状态
3. 查看熔断器状态

---

## 📊 **测试验收标准**

### **功能验收**
- [ ] 所有暴露的方法都能正确注册
- [ ] 透明调用能够成功执行并返回结果
- [ ] 错误情况能够正确处理和返回
- [ ] 缓存机制能够正常工作

### **性能验收**
- [ ] 单次调用响应时间 < 100ms
- [ ] 缓存命中时响应时间 < 10ms
- [ ] 批量调用性能优于单独调用

### **稳定性验收**
- [ ] 连续调用1000次无错误
- [ ] 熔断器能够正确保护服务
- [ ] 服务重启后能够自动恢复

---

## 📋 **下一步计划**

1. **修复当前问题**: 解决类和接口重复定义问题
2. **完善测试脚本**: 编写专门的透明调用测试
3. **扩展服务集成**: 为其他微服务添加装饰器
4. **性能优化**: 根据测试结果优化调用性能

---

**状态**: 🔄 当前处于基础功能验证阶段  
**下次更新**: 修复重复定义问题后进行透明调用测试
