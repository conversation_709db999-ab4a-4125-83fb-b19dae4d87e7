# 认证架构设计 - 双层Token机制

## 📋 概述

本文档详细设计分区分服系统的认证架构，采用**双层Token机制**实现账号级认证和角色级认证的分离，确保安全性和功能边界的清晰。

## 🔐 双层Token机制设计

### **1. Token类型定义**

```typescript
// 账号级Token：用于账号管理和区服选择
interface AccountTokenPayload {
  sub: string;              // 账号ID
  username: string;         // 用户名
  email: string;           // 邮箱
  roles: string[];         // 账号级权限 ['user', 'vip', 'admin']
  iat: number;             // 签发时间
  exp: number;             // 过期时间（24小时）
  scope: 'account';        // Token作用域
  jti: string;             // Token唯一标识
}

// 角色级Token：用于游戏业务操作
interface CharacterTokenPayload {
  sub: string;              // 账号ID（继承自账号Token）
  username: string;         // 用户名（继承）
  characterId: string;      // 当前角色ID
  serverId: string;         // 当前区服ID
  sessionId: string;        // 角色会话ID
  iat: number;             // 签发时间
  exp: number;             // 过期时间（4小时）
  scope: 'character';       // Token作用域
  jti: string;             // Token唯一标识
}
```

### **2. Token职责界限**

#### **账号级Token权限范围**
```typescript
// ✅ 允许的操作
const ACCOUNT_TOKEN_PERMISSIONS = [
  // 账号管理
  'account.updateProfile',
  'account.changePassword',
  'account.updateEmail',
  'account.deleteAccount',
  
  // 区服相关
  'auth.getServerList',
  'auth.getServerHistory',
  'auth.enterServer',
  
  // 角色管理
  'character.createCharacter',
  'character.deleteCharacter',
  'character.getCharacterList',
  
  // 支付相关
  'payment.createOrder',
  'payment.getOrderHistory',
  
  // 系统功能
  'system.getNotifications',
  'system.updateSettings',
];

// ❌ 禁止的操作
const ACCOUNT_TOKEN_RESTRICTIONS = [
  // 游戏业务数据
  'character.getInfo',
  'character.updateInfo',
  'hero.getList',
  'match.getHistory',
  'social.getFriends',
  // ... 所有游戏内业务操作
];
```

#### **角色级Token权限范围**
```typescript
// ✅ 允许的操作
const CHARACTER_TOKEN_PERMISSIONS = [
  // 角色相关
  'character.*',           // 所有角色操作
  
  // 游戏业务
  'hero.*',               // 球员管理
  'match.*',              // 比赛系统
  'economy.*',            // 经济系统
  'social.*',             // 社交系统
  'activity.*',           // 活动系统
  
  // 跨服功能
  'cross.getRanking',
  'cross.findBattleOpponent',
  'cross.joinActivity',
];

// ❌ 禁止的操作
const CHARACTER_TOKEN_RESTRICTIONS = [
  // 账号级操作
  'account.updateProfile',
  'account.changePassword',
  'account.deleteAccount',
  
  // 支付相关
  'payment.*',
  
  // 其他区服操作
  'auth.enterServer',      // 不能切换区服
];
```

### **3. Token生成策略**

```typescript
@Injectable()
export class TokenGenerationService {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService
  ) {}

  // 生成账号级Token
  async generateAccountToken(user: User): Promise<string> {
    const payload: AccountTokenPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 24 * 3600, // 24小时
      scope: 'account',
      jti: this.generateJTI(),
    };

    return this.jwtService.sign(payload, {
      secret: this.configService.get('AUTH_JWT_SECRET'),
      algorithm: 'HS256',
    });
  }

  // 生成角色级Token
  async generateCharacterToken(
    accountPayload: AccountTokenPayload,
    characterId: string,
    serverId: string,
    sessionId: string
  ): Promise<string> {
    const payload: CharacterTokenPayload = {
      sub: accountPayload.sub,
      username: accountPayload.username,
      characterId,
      serverId,
      sessionId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 4 * 3600, // 4小时
      scope: 'character',
      jti: this.generateJTI(),
    };

    return this.jwtService.sign(payload, {
      secret: this.configService.get('CHARACTER_JWT_SECRET'), // 不同的密钥
      algorithm: 'HS256',
    });
  }

  // 验证Token并返回类型
  async verifyToken(token: string): Promise<TokenVerificationResult> {
    try {
      // 先尝试用账号密钥验证
      const accountPayload = this.jwtService.verify(token, {
        secret: this.configService.get('AUTH_JWT_SECRET'),
      });
      
      if (accountPayload.scope === 'account') {
        return {
          valid: true,
          type: 'account',
          payload: accountPayload,
        };
      }
    } catch (error) {
      // 账号Token验证失败，尝试角色Token
    }

    try {
      // 尝试用角色密钥验证
      const characterPayload = this.jwtService.verify(token, {
        secret: this.configService.get('CHARACTER_JWT_SECRET'),
      });
      
      if (characterPayload.scope === 'character') {
        return {
          valid: true,
          type: 'character',
          payload: characterPayload,
        };
      }
    } catch (error) {
      return {
        valid: false,
        error: 'Invalid token',
      };
    }
  }

  private generateJTI(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

## 🔒 认证中间件设计

### **1. 网关认证中间件**

```typescript
@Injectable()
export class GatewayAuthMiddleware implements NestMiddleware {
  constructor(
    private tokenService: TokenGenerationService,
    private sessionService: SessionService
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const token = this.extractToken(req);
    
    if (!token) {
      req.auth = { authenticated: false };
      return next();
    }

    try {
      const verification = await this.tokenService.verifyToken(token);
      
      if (!verification.valid) {
        req.auth = { authenticated: false, error: verification.error };
        return next();
      }

      // 设置认证信息
      req.auth = {
        authenticated: true,
        type: verification.type,
        payload: verification.payload,
      };

      // 如果是角色Token，验证会话有效性
      if (verification.type === 'character') {
        const sessionValid = await this.sessionService.validateSession(
          verification.payload.sessionId
        );
        
        if (!sessionValid) {
          req.auth.authenticated = false;
          req.auth.error = 'Session expired';
        }
      }

      next();
    } catch (error) {
      req.auth = { authenticated: false, error: error.message };
      next();
    }
  }

  private extractToken(req: Request): string | null {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }
}
```

### **2. 权限验证守卫**

```typescript
// 账号级权限守卫
@Injectable()
export class AccountAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const auth = request.auth;

    if (!auth?.authenticated) {
      throw new UnauthorizedException('Authentication required');
    }

    if (auth.type !== 'account') {
      throw new ForbiddenException('Account-level authentication required');
    }

    return true;
  }
}

// 角色级权限守卫
@Injectable()
export class CharacterAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const auth = request.auth;

    if (!auth?.authenticated) {
      throw new UnauthorizedException('Authentication required');
    }

    if (auth.type !== 'character') {
      throw new ForbiddenException('Character-level authentication required');
    }

    return true;
  }
}

// 灵活权限守卫（支持两种Token）
@Injectable()
export class FlexibleAuthGuard implements CanActivate {
  constructor(private allowedTypes: ('account' | 'character')[]) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const auth = request.auth;

    if (!auth?.authenticated) {
      throw new UnauthorizedException('Authentication required');
    }

    if (!this.allowedTypes.includes(auth.type)) {
      throw new ForbiddenException(`Required token types: ${this.allowedTypes.join(', ')}`);
    }

    return true;
  }
}
```

## 🔄 Token刷新机制

### **1. 账号Token刷新**

```typescript
@Injectable()
export class AccountTokenRefreshService {
  constructor(
    private tokenService: TokenGenerationService,
    private userService: UserService,
    private redisService: RedisService
  ) {}

  async refreshAccountToken(refreshToken: string): Promise<RefreshResult> {
    // 验证刷新Token
    const refreshPayload = await this.verifyRefreshToken(refreshToken);
    
    // 获取最新用户信息
    const user = await this.userService.findById(refreshPayload.sub);
    if (!user || !user.active) {
      throw new UnauthorizedException('User not found or inactive');
    }

    // 生成新的访问Token
    const newAccessToken = await this.tokenService.generateAccountToken(user);
    
    // 生成新的刷新Token
    const newRefreshToken = await this.generateRefreshToken(user.id);
    
    // 撤销旧的刷新Token
    await this.revokeRefreshToken(refreshToken);

    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
      expiresIn: 24 * 3600, // 24小时
    };
  }

  private async generateRefreshToken(userId: string): Promise<string> {
    const refreshToken = this.generateSecureToken();
    const expiresAt = new Date(Date.now() + 30 * 24 * 3600 * 1000); // 30天

    // 存储到Redis
    await this.redisService.set(
      `refresh_token:${refreshToken}`,
      JSON.stringify({ userId, expiresAt }),
      30 * 24 * 3600, // 30天TTL
      'global'
    );

    return refreshToken;
  }
}
```

### **2. 角色Token自动续期**

```typescript
@Injectable()
export class CharacterTokenRenewalService {
  constructor(
    private tokenService: TokenGenerationService,
    private sessionService: SessionService
  ) {}

  async renewCharacterToken(currentToken: string): Promise<string> {
    const verification = await this.tokenService.verifyToken(currentToken);
    
    if (!verification.valid || verification.type !== 'character') {
      throw new UnauthorizedException('Invalid character token');
    }

    const payload = verification.payload as CharacterTokenPayload;
    
    // 验证会话仍然有效
    const sessionValid = await this.sessionService.validateSession(payload.sessionId);
    if (!sessionValid) {
      throw new UnauthorizedException('Session expired');
    }

    // 检查是否需要续期（剩余时间少于1小时时自动续期）
    const timeLeft = payload.exp - Math.floor(Date.now() / 1000);
    if (timeLeft > 3600) { // 1小时
      return currentToken; // 不需要续期
    }

    // 生成新的角色Token
    const newToken = await this.tokenService.generateCharacterToken(
      { sub: payload.sub, username: payload.username } as AccountTokenPayload,
      payload.characterId,
      payload.serverId,
      payload.sessionId
    );

    // 更新会话的最后活动时间
    await this.sessionService.updateSessionActivity(payload.sessionId);

    return newToken;
  }
}
```

## 🛡️ 安全策略

### **1. Token安全配置**

```typescript
// config/auth.config.ts
export const authConfig = {
  // 账号Token配置
  account: {
    secret: process.env.AUTH_JWT_SECRET,
    expiresIn: '24h',
    algorithm: 'HS256',
    issuer: 'football-manager-auth',
    audience: 'football-manager-app',
  },
  
  // 角色Token配置
  character: {
    secret: process.env.CHARACTER_JWT_SECRET, // 不同的密钥
    expiresIn: '4h',
    algorithm: 'HS256',
    issuer: 'football-manager-game',
    audience: 'football-manager-app',
  },
  
  // 刷新Token配置
  refresh: {
    expiresIn: '30d',
    length: 32, // 随机字符串长度
  },
  
  // 安全策略
  security: {
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60, // 15分钟
    passwordMinLength: 8,
    requireMFA: false, // 多因子认证
    allowConcurrentSessions: true,
    maxConcurrentSessions: 3,
  },
};
```

### **2. Token撤销机制**

```typescript
@Injectable()
export class TokenRevocationService {
  constructor(private redisService: RedisService) {}

  // 撤销特定Token
  async revokeToken(jti: string, type: 'account' | 'character'): Promise<void> {
    const key = `revoked_token:${type}:${jti}`;
    const ttl = type === 'account' ? 24 * 3600 : 4 * 3600;
    
    await this.redisService.set(key, 'revoked', ttl, 'global');
  }

  // 撤销用户所有Token
  async revokeAllUserTokens(userId: string): Promise<void> {
    const pattern = `user_tokens:${userId}:*`;
    const tokens = await this.redisService.keys(pattern);
    
    for (const tokenKey of tokens) {
      const tokenData = await this.redisService.get(tokenKey, 'global');
      if (tokenData) {
        const { jti, type } = JSON.parse(tokenData);
        await this.revokeToken(jti, type);
      }
    }
  }

  // 检查Token是否被撤销
  async isTokenRevoked(jti: string, type: 'account' | 'character'): Promise<boolean> {
    const key = `revoked_token:${type}:${jti}`;
    const result = await this.redisService.get(key, 'global');
    return result === 'revoked';
  }
}
```

---

> **核心优势**：
> - 🔒 **安全分离**：账号和角色权限完全隔离
> - ⏱️ **灵活过期**：不同Token有不同的生命周期
> - 🔄 **自动续期**：角色Token支持无感知续期
> - 🛡️ **撤销机制**：支持精确的Token撤销控制
> - 📊 **审计追踪**：每个操作都有明确的权限来源
