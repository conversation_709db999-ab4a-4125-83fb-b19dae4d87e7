# 网关集成设计 - 统一认证与路由

## 📋 概述

本文档详细设计网关层的认证集成和路由机制，实现HTTP接口与WebSocket接口的统一管理，以及角色登录的网关级处理。

## 🌐 HTTP接口设计

### **1. 认证相关HTTP接口**

```typescript
// apps/gateway/src/domain/auth-http/ (新增模块)
├── auth-http.module.ts
├── controllers/
│   ├── auth.controller.ts              # 认证控制器
│   ├── server.controller.ts            # 区服控制器
│   └── character-auth.controller.ts    # 角色认证控制器
├── services/
│   ├── auth-http.service.ts            # HTTP认证服务
│   └── character-auth.service.ts       # 角色认证服务
└── dto/
    ├── login.dto.ts
    ├── register.dto.ts
    ├── server-list.dto.ts
    └── enter-server.dto.ts

// 认证控制器
@Controller('api/auth')
export class AuthController {
  constructor(
    private authHttpService: AuthHttpService,
    private characterAuthService: CharacterAuthService
  ) {}

  /**
   * 用户注册
   * POST /api/auth/register
   */
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  async register(@Body() registerDto: RegisterDto): Promise<RegisterResponse> {
    const result = await this.authHttpService.register(registerDto);
    return {
      success: true,
      message: '注册成功',
      data: {
        userId: result.userId,
        username: result.username,
        email: result.email,
      },
    };
  }

  /**
   * 用户登录
   * POST /api/auth/login
   */
  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: LoginDto): Promise<LoginResponse> {
    const result = await this.authHttpService.login(loginDto);
    return {
      success: true,
      message: '登录成功',
      data: {
        accountToken: result.accountToken,
        refreshToken: result.refreshToken,
        expiresIn: result.expiresIn,
        user: {
          id: result.user.id,
          username: result.user.username,
          email: result.user.email,
          roles: result.user.roles,
        },
      },
    };
  }

  /**
   * 刷新Token
   * POST /api/auth/refresh
   */
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  async refreshToken(@Body() refreshDto: RefreshTokenDto): Promise<RefreshResponse> {
    const result = await this.authHttpService.refreshToken(refreshDto.refreshToken);
    return {
      success: true,
      data: {
        accountToken: result.accessToken,
        refreshToken: result.refreshToken,
        expiresIn: result.expiresIn,
      },
    };
  }

  /**
   * 用户登出
   * POST /api/auth/logout
   */
  @Post('logout')
  @UseGuards(AccountAuthGuard)
  @HttpCode(HttpStatus.OK)
  async logout(@Req() req: AuthenticatedRequest): Promise<LogoutResponse> {
    await this.authHttpService.logout(req.auth.payload.sub, req.auth.payload.jti);
    return {
      success: true,
      message: '登出成功',
    };
  }
}
```

### **2. 区服管理HTTP接口**

```typescript
// 区服控制器
@Controller('api/servers')
export class ServerController {
  constructor(private serverService: ServerService) {}

  /**
   * 获取区服列表
   * GET /api/servers
   */
  @Get()
  @UseGuards(AccountAuthGuard)
  async getServerList(@Req() req: AuthenticatedRequest): Promise<ServerListResponse> {
    const userId = req.auth.payload.sub;
    const result = await this.serverService.getServerList(userId);
    
    return {
      success: true,
      data: result,
    };
  }

  /**
   * 获取区服详情
   * GET /api/servers/:serverId
   */
  @Get(':serverId')
  @UseGuards(AccountAuthGuard)
  async getServerDetail(
    @Param('serverId') serverId: string,
    @Req() req: AuthenticatedRequest
  ): Promise<ServerDetailResponse> {
    const userId = req.auth.payload.sub;
    const result = await this.serverService.getServerDetail(serverId, userId);
    
    return {
      success: true,
      data: result,
    };
  }

  /**
   * 获取玩家区服历史
   * GET /api/servers/history
   */
  @Get('history')
  @UseGuards(AccountAuthGuard)
  async getServerHistory(@Req() req: AuthenticatedRequest): Promise<ServerHistoryResponse> {
    const userId = req.auth.payload.sub;
    const result = await this.serverService.getPlayerHistory(userId);
    
    return {
      success: true,
      data: result,
    };
  }
}
```

### **3. 角色认证HTTP接口**

```typescript
// 角色认证控制器
@Controller('api/character-auth')
export class CharacterAuthController {
  constructor(private characterAuthService: CharacterAuthService) {}

  /**
   * 进入区服（角色登录）
   * POST /api/character-auth/enter-server
   */
  @Post('enter-server')
  @UseGuards(AccountAuthGuard)
  @HttpCode(HttpStatus.OK)
  async enterServer(
    @Body() enterServerDto: EnterServerDto,
    @Req() req: AuthenticatedRequest
  ): Promise<EnterServerResponse> {
    const accountPayload = req.auth.payload as AccountTokenPayload;
    
    const result = await this.characterAuthService.enterServer(
      accountPayload,
      enterServerDto.serverId,
      enterServerDto.characterId
    );
    
    return {
      success: true,
      message: '进入区服成功',
      data: {
        characterToken: result.characterToken,
        character: result.character,
        server: result.server,
        session: {
          id: result.session.id,
          expiresAt: result.session.expiresAt,
        },
      },
    };
  }

  /**
   * 退出区服
   * POST /api/character-auth/exit-server
   */
  @Post('exit-server')
  @UseGuards(CharacterAuthGuard)
  @HttpCode(HttpStatus.OK)
  async exitServer(@Req() req: AuthenticatedRequest): Promise<ExitServerResponse> {
    const characterPayload = req.auth.payload as CharacterTokenPayload;
    
    await this.characterAuthService.exitServer(
      characterPayload.sub,
      characterPayload.sessionId
    );
    
    return {
      success: true,
      message: '退出区服成功',
    };
  }

  /**
   * 获取当前会话信息
   * GET /api/character-auth/session
   */
  @Get('session')
  @UseGuards(CharacterAuthGuard)
  async getCurrentSession(@Req() req: AuthenticatedRequest): Promise<SessionInfoResponse> {
    const characterPayload = req.auth.payload as CharacterTokenPayload;
    
    const session = await this.characterAuthService.getSessionInfo(characterPayload.sessionId);
    
    return {
      success: true,
      data: {
        sessionId: session.id,
        characterId: session.characterId,
        serverId: session.serverId,
        serverName: session.serverName,
        lastActivity: session.lastActivity,
        expiresAt: session.expiresAt,
      },
    };
  }

  /**
   * 续期角色Token
   * POST /api/character-auth/renew-token
   */
  @Post('renew-token')
  @UseGuards(CharacterAuthGuard)
  @HttpCode(HttpStatus.OK)
  async renewToken(@Req() req: AuthenticatedRequest): Promise<RenewTokenResponse> {
    const currentToken = req.headers.authorization?.substring(7); // 移除 "Bearer "
    
    const newToken = await this.characterAuthService.renewCharacterToken(currentToken);
    
    return {
      success: true,
      data: {
        characterToken: newToken,
        expiresIn: 4 * 3600, // 4小时
      },
    };
  }
}
```

## 🔌 WebSocket集成设计

### **1. WebSocket认证增强**

```typescript
// apps/gateway/src/domain/websocket/websocket.gateway.ts (扩展现有)
@WebSocketGateway({
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true,
  },
  namespace: '/',
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
})
export class WebSocketGateway {
  constructor(
    private tokenService: TokenGenerationService,
    private sessionService: SessionService,
    private characterAuthService: CharacterAuthService,
    private microserviceClient: MicroserviceClientService,
    private logger: Logger
  ) {}

  // 扩展连接认证
  @SubscribeMessage('authenticate')
  async handleAuthentication(
    @ConnectedSocket() socket: AuthenticatedSocket,
    @MessageBody() authData: { token: string }
  ) {
    try {
      const verification = await this.tokenService.verifyToken(authData.token);
      
      if (!verification.valid) {
        socket.emit('auth_error', { message: 'Invalid token' });
        return;
      }

      // 设置Socket认证信息
      socket.authenticated = true;
      socket.userId = verification.payload.sub;
      socket.tokenType = verification.type;
      socket.tokenPayload = verification.payload;

      // 如果是角色Token，设置游戏上下文
      if (verification.type === 'character') {
        const characterPayload = verification.payload as CharacterTokenPayload;
        socket.characterId = characterPayload.characterId;
        socket.serverId = characterPayload.serverId;
        socket.sessionId = characterPayload.sessionId;

        // 验证会话有效性
        const sessionValid = await this.sessionService.validateSession(characterPayload.sessionId);
        if (!sessionValid) {
          socket.emit('auth_error', { message: 'Session expired' });
          socket.disconnect();
          return;
        }

        // 更新会话的Socket ID
        await this.sessionService.updateSessionSocket(characterPayload.sessionId, socket.id);
      }

      socket.emit('authenticated', {
        type: verification.type,
        userId: verification.payload.sub,
        ...(verification.type === 'character' && {
          characterId: socket.characterId,
          serverId: socket.serverId,
        }),
      });

      this.logger.log(`Socket authenticated: ${socket.id}, type: ${verification.type}`);

    } catch (error) {
      this.logger.error(`Authentication failed for socket ${socket.id}: ${error.message}`);
      socket.emit('auth_error', { message: 'Authentication failed' });
    }
  }

  // 扩展消息路由
  @SubscribeMessage('message')
  @UseGuards(WsAuthGuard, WsRateLimitGuard)
  @WsRateLimit({ windowMs: 60000, max: 200 })
  async handleMessage(
    @ConnectedSocket() socket: AuthenticatedSocket,
    @MessageBody() data: ClientWSMessageDto
  ) {
    try {
      // 验证认证状态
      if (!socket.authenticated) {
        socket.emit('error', { message: 'Authentication required' });
        return;
      }

      // 解析消息
      const parsedMessage = this.parseClientMessage(data);
      const { service, action, payload } = parsedMessage;

      // 权限检查
      const hasPermission = await this.checkPermission(socket, service, action);
      if (!hasPermission) {
        socket.emit('error', { 
          id: data.id,
          message: 'Permission denied',
          code: 'PERMISSION_DENIED'
        });
        return;
      }

      // 路由消息
      const result = await this.routeMessage(socket, parsedMessage);

      // 发送响应
      socket.emit('response', {
        id: data.id,
        success: true,
        data: result,
      });

    } catch (error) {
      this.logger.error(`Message handling error: ${error.message}`);
      socket.emit('error', {
        id: data.id,
        message: error.message,
        code: error.code || 'INTERNAL_ERROR',
      });
    }
  }

  // 权限检查
  private async checkPermission(
    socket: AuthenticatedSocket,
    service: string,
    action: string
  ): Promise<boolean> {
    const operation = `${service}.${action}`;

    // 账号级Token权限检查
    if (socket.tokenType === 'account') {
      return this.isAccountOperation(operation);
    }

    // 角色级Token权限检查
    if (socket.tokenType === 'character') {
      return this.isCharacterOperation(operation);
    }

    return false;
  }

  // 消息路由
  private async routeMessage(
    socket: AuthenticatedSocket,
    message: ParsedWSMessage
  ): Promise<any> {
    const { service, action, payload } = message;

    // 构建微服务调用参数
    const params = {
      ...payload,
      userId: socket.userId,
      ...(socket.tokenType === 'character' && {
        characterId: socket.characterId,
        serverId: socket.serverId,
        sessionId: socket.sessionId,
      }),
    };

    // 特殊处理：角色认证相关操作
    if (service === 'auth' && this.isCharacterAuthAction(action)) {
      return this.handleCharacterAuthAction(socket, action, params);
    }

    // 跨服操作处理
    if (action.startsWith('cross.') || action.startsWith('global.')) {
      return this.handleCrossServerOperation(socket, service, action, params);
    }

    // 常规微服务调用
    this.logger.debug(`🚀 Calling microservice: ${service}.${action}`);
    return await this.microserviceClient.call(service as MicroserviceName, action, params);
  }

  // 角色认证操作处理
  private async handleCharacterAuthAction(
    socket: AuthenticatedSocket,
    action: string,
    params: any
  ): Promise<any> {
    switch (action) {
      case 'enterServer':
        // 只有账号Token可以进入区服
        if (socket.tokenType !== 'account') {
          throw new ForbiddenException('Account token required for server entry');
        }
        
        const result = await this.characterAuthService.enterServer(
          socket.tokenPayload as AccountTokenPayload,
          params.serverId,
          params.characterId
        );

        // 更新Socket状态为角色级
        socket.tokenType = 'character';
        socket.characterId = result.character.id;
        socket.serverId = params.serverId;
        socket.sessionId = result.session.id;

        return result;

      case 'exitServer':
        // 只有角色Token可以退出区服
        if (socket.tokenType !== 'character') {
          throw new ForbiddenException('Character token required for server exit');
        }

        await this.characterAuthService.exitServer(socket.userId, socket.sessionId);

        // 重置Socket状态为账号级
        socket.tokenType = 'account';
        socket.characterId = undefined;
        socket.serverId = undefined;
        socket.sessionId = undefined;

        return { success: true };

      case 'getCurrentSession':
        if (socket.tokenType !== 'character') {
          throw new ForbiddenException('Character token required');
        }

        return await this.characterAuthService.getSessionInfo(socket.sessionId);

      default:
        throw new BadRequestException(`Unknown character auth action: ${action}`);
    }
  }
}
```

### **2. WebSocket会话管理**

```typescript
// apps/gateway/src/domain/websocket/session-manager.service.ts
@Injectable()
export class WebSocketSessionManager {
  private socketSessions = new Map<string, AuthenticatedSocket>();
  private userSockets = new Map<string, Set<string>>();

  constructor(
    private sessionService: SessionService,
    private logger: Logger
  ) {}

  // 注册Socket会话
  registerSocket(socket: AuthenticatedSocket): void {
    this.socketSessions.set(socket.id, socket);

    if (socket.userId) {
      if (!this.userSockets.has(socket.userId)) {
        this.userSockets.set(socket.userId, new Set());
      }
      this.userSockets.get(socket.userId)!.add(socket.id);
    }

    this.logger.debug(`Socket registered: ${socket.id}, user: ${socket.userId}`);
  }

  // 注销Socket会话
  unregisterSocket(socketId: string): void {
    const socket = this.socketSessions.get(socketId);
    if (socket) {
      this.socketSessions.delete(socketId);

      if (socket.userId) {
        const userSocketSet = this.userSockets.get(socket.userId);
        if (userSocketSet) {
          userSocketSet.delete(socketId);
          if (userSocketSet.size === 0) {
            this.userSockets.delete(socket.userId);
          }
        }
      }

      // 如果是角色会话，清理会话状态
      if (socket.sessionId) {
        this.sessionService.clearSessionSocket(socket.sessionId);
      }
    }

    this.logger.debug(`Socket unregistered: ${socketId}`);
  }

  // 获取用户的所有Socket连接
  getUserSockets(userId: string): AuthenticatedSocket[] {
    const socketIds = this.userSockets.get(userId) || new Set();
    return Array.from(socketIds)
      .map(id => this.socketSessions.get(id))
      .filter(socket => socket !== undefined) as AuthenticatedSocket[];
  }

  // 向用户发送消息
  sendToUser(userId: string, event: string, data: any): void {
    const sockets = this.getUserSockets(userId);
    sockets.forEach(socket => {
      socket.emit(event, data);
    });
  }

  // 向区服内所有用户发送消息
  sendToServer(serverId: string, event: string, data: any): void {
    this.socketSessions.forEach(socket => {
      if (socket.serverId === serverId) {
        socket.emit(event, data);
      }
    });
  }

  // 全服广播
  broadcast(event: string, data: any): void {
    this.socketSessions.forEach(socket => {
      socket.emit(event, data);
    });
  }

  // 获取在线统计
  getOnlineStats(): OnlineStats {
    const totalConnections = this.socketSessions.size;
    const authenticatedUsers = this.userSockets.size;
    
    const serverStats = new Map<string, number>();
    this.socketSessions.forEach(socket => {
      if (socket.serverId) {
        const count = serverStats.get(socket.serverId) || 0;
        serverStats.set(socket.serverId, count + 1);
      }
    });

    return {
      totalConnections,
      authenticatedUsers,
      serverStats: Object.fromEntries(serverStats),
    };
  }
}
```

## 🔄 角色认证服务实现

### **1. 网关级角色认证服务**

```typescript
// apps/gateway/src/domain/character-auth/character-auth.service.ts
@Injectable()
export class CharacterAuthService {
  constructor(
    private tokenService: TokenGenerationService,
    private sessionService: SessionService,
    private microserviceClient: MicroserviceClientService,
    private logger: Logger
  ) {}

  // 进入区服（核心方法）
  async enterServer(
    accountPayload: AccountTokenPayload,
    serverId: string,
    characterId?: string
  ): Promise<EnterServerResult> {
    this.logger.log(`用户 ${accountPayload.sub} 尝试进入区服 ${serverId}`);

    try {
      // 1. 验证区服状态
      const server = await this.validateServer(serverId);
      
      // 2. 获取或验证角色
      const character = await this.validateCharacter(accountPayload.sub, serverId, characterId);
      
      // 3. 创建角色会话
      const session = await this.createCharacterSession({
        accountId: accountPayload.sub,
        characterId: character.id,
        serverId,
        serverName: server.name,
      });
      
      // 4. 生成角色Token
      const characterToken = await this.tokenService.generateCharacterToken(
        accountPayload,
        character.id,
        serverId,
        session.id
      );
      
      // 5. 更新玩家历史记录
      await this.updatePlayerHistory(accountPayload.sub, serverId);
      
      this.logger.log(`用户 ${accountPayload.sub} 成功进入区服 ${serverId}`);
      
      return {
        characterToken,
        character,
        server,
        session,
      };

    } catch (error) {
      this.logger.error(`进入区服失败: ${error.message}`);
      throw error;
    }
  }

  // 验证区服状态
  private async validateServer(serverId: string): Promise<Server> {
    const server = await this.microserviceClient.call('auth', 'getServerInfo', { serverId });
    
    if (!server) {
      throw new NotFoundException('区服不存在');
    }
    
    if (server.status !== 'active') {
      throw new BadRequestException(`区服状态异常: ${server.status}`);
    }
    
    if (server.currentPlayerCount >= server.maxPlayerCount) {
      throw new BadRequestException('区服已满');
    }
    
    return server;
  }

  // 验证角色
  private async validateCharacter(
    accountId: string,
    serverId: string,
    characterId?: string
  ): Promise<Character> {
    if (characterId) {
      // 验证指定角色
      const character = await this.microserviceClient.call('character', 'getCharacterInfo', {
        characterId,
        serverId,
        accountId,
      });
      
      if (!character) {
        throw new NotFoundException('角色不存在或不属于该账号');
      }
      
      return character;
    } else {
      // 获取默认角色
      const characterList = await this.microserviceClient.call('character', 'getCharacterList', {
        accountId,
        serverId,
      });
      
      if (!characterList || characterList.length === 0) {
        throw new BadRequestException('该区服没有角色，请先创建角色');
      }
      
      // 返回最后登录的角色或第一个角色
      return characterList.find(c => c.isLastLogin) || characterList[0];
    }
  }

  // 创建角色会话
  private async createCharacterSession(sessionData: CreateSessionData): Promise<CharacterSession> {
    return await this.sessionService.createSession({
      accountId: sessionData.accountId,
      characterId: sessionData.characterId,
      serverId: sessionData.serverId,
      serverName: sessionData.serverName,
      expiresAt: new Date(Date.now() + 4 * 3600 * 1000), // 4小时
    });
  }

  // 更新玩家历史记录
  private async updatePlayerHistory(accountId: string, serverId: string): Promise<void> {
    await this.microserviceClient.call('auth', 'updatePlayerHistory', {
      accountId,
      serverId,
      lastPlayTime: new Date(),
    });
  }

  // 退出区服
  async exitServer(accountId: string, sessionId: string): Promise<void> {
    this.logger.log(`用户 ${accountId} 退出区服，会话 ${sessionId}`);
    
    await this.sessionService.terminateSession(sessionId);
  }

  // 获取会话信息
  async getSessionInfo(sessionId: string): Promise<CharacterSession> {
    const session = await this.sessionService.getSession(sessionId);
    
    if (!session) {
      throw new NotFoundException('会话不存在');
    }
    
    return session;
  }

  // 续期角色Token
  async renewCharacterToken(currentToken: string): Promise<string> {
    const verification = await this.tokenService.verifyToken(currentToken);
    
    if (!verification.valid || verification.type !== 'character') {
      throw new UnauthorizedException('无效的角色Token');
    }
    
    const payload = verification.payload as CharacterTokenPayload;
    
    // 验证会话有效性
    const sessionValid = await this.sessionService.validateSession(payload.sessionId);
    if (!sessionValid) {
      throw new UnauthorizedException('会话已过期');
    }
    
    // 检查是否需要续期
    const timeLeft = payload.exp - Math.floor(Date.now() / 1000);
    if (timeLeft > 3600) { // 剩余时间超过1小时，不需要续期
      return currentToken;
    }
    
    // 生成新Token
    const newToken = await this.tokenService.generateCharacterToken(
      { sub: payload.sub, username: payload.username } as AccountTokenPayload,
      payload.characterId,
      payload.serverId,
      payload.sessionId
    );
    
    // 更新会话活动时间
    await this.sessionService.updateSessionActivity(payload.sessionId);
    
    return newToken;
  }
}
```

---

> **核心优势**：
> - 🌐 **HTTP/WebSocket统一**：认证机制在两种协议间保持一致
> - 🎮 **网关级处理**：角色认证在网关层统一处理，避免逻辑分散
> - 🔄 **无缝切换**：支持账号Token和角色Token的平滑切换
> - 📊 **会话管理**：完整的WebSocket会话生命周期管理
> - 🛡️ **权限控制**：精确的操作权限验证机制
