# 微服务适配设计 - 数据隔离与路由

## 📋 概述

本文档详细设计各微服务的适配方案，实现基于现有架构的数据隔离、区服路由和跨服通信功能。

## 🔧 微服务公共库扩展

### **1. 区服感知的微服务客户端**

```typescript
// libs/common/src/microservice-kit/server-aware/ (新增模块)
├── server-aware.module.ts
├── services/
│   ├── server-aware-client.service.ts      # 区服感知客户端
│   ├── server-context.service.ts           # 区服上下文服务
│   └── cross-server-client.service.ts      # 跨服通信客户端
├── decorators/
│   ├── server-aware.decorator.ts           # 区服感知装饰器
│   └── cross-server.decorator.ts           # 跨服操作装饰器
└── interfaces/
    ├── server-context.interface.ts
    └── cross-server.interface.ts

// 区服上下文服务
@Injectable()
export class ServerContextService {
  private readonly contextStore = new AsyncLocalStorage<ServerContext>();

  // 设置当前请求的区服上下文
  setContext(context: ServerContext): void {
    this.contextStore.enterWith(context);
  }

  // 获取当前区服上下文
  getCurrentContext(): ServerContext | undefined {
    return this.contextStore.getStore();
  }

  // 在指定上下文中执行操作
  async runWithContext<T>(context: ServerContext, operation: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.contextStore.run(context, async () => {
        try {
          const result = await operation();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  // 获取当前区服ID
  getCurrentServerId(): string | undefined {
    const context = this.getCurrentContext();
    return context?.serverId;
  }

  // 获取当前角色ID
  getCurrentCharacterId(): string | undefined {
    const context = this.getCurrentContext();
    return context?.characterId;
  }

  // 检查是否为跨服操作
  isCrossServerOperation(): boolean {
    const context = this.getCurrentContext();
    return context?.isCrossServer || false;
  }
}

// 区服感知的微服务客户端
@Injectable()
export class ServerAwareMicroserviceClient extends MicroserviceClientService {
  constructor(
    @Inject('MICROSERVICE_CONFIG') config: MicroserviceKitConfig,
    @Inject('CONNECTED_SERVICES') connectedServices: MicroserviceName[],
    private serverContext: ServerContextService,
    private logger: Logger
  ) {
    super(config, connectedServices);
  }

  // 重写call方法，自动注入区服上下文
  async call<T = any>(
    serviceName: MicroserviceName,
    pattern: string,
    data?: any,
    options?: CallOptions
  ): Promise<T> {
    const context = this.serverContext.getCurrentContext();
    
    // 构建增强的数据对象
    const enhancedData = {
      ...data,
      // 自动注入区服上下文
      ...(context && {
        serverId: context.serverId,
        characterId: context.characterId,
        sessionId: context.sessionId,
        isCrossServer: context.isCrossServer,
      }),
    };

    this.logger.debug(`🚀 Calling microservice: ${serviceName}.${pattern}`, {
      serverId: context?.serverId,
      characterId: context?.characterId,
      isCrossServer: context?.isCrossServer,
    });

    return super.call(serviceName, pattern, enhancedData, options);
  }

  // 跨服调用
  async callCrossServer<T = any>(
    serviceName: MicroserviceName,
    pattern: string,
    data?: any,
    targetServerId?: string
  ): Promise<T> {
    const crossServerContext: ServerContext = {
      serverId: targetServerId || 'global',
      characterId: undefined,
      sessionId: undefined,
      isCrossServer: true,
    };

    return this.serverContext.runWithContext(crossServerContext, () =>
      this.call(serviceName, pattern, data)
    );
  }
}
```

### **2. 区服感知装饰器**

```typescript
// libs/common/src/microservice-kit/decorators/server-aware.decorator.ts
import { SetMetadata } from '@nestjs/common';

// 区服感知装饰器
export const ServerAware = (options?: ServerAwareOptions) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const serverContext = this.serverContext || Container.get(ServerContextService);
      const context = serverContext.getCurrentContext();

      if (!context?.serverId && options?.required !== false) {
        throw new BadRequestException('Server context is required');
      }

      // 记录操作日志
      if (options?.logging !== false) {
        this.logger?.debug(`[${context?.serverId || 'unknown'}] ${target.constructor.name}.${propertyName}`);
      }

      return originalMethod.apply(this, args);
    };

    // 设置元数据
    SetMetadata('server-aware', true)(target, propertyName, descriptor);
    if (options) {
      SetMetadata('server-aware-options', options)(target, propertyName, descriptor);
    }
  };
};

// 跨服操作装饰器
export const CrossServer = (options?: CrossServerOptions) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const serverContext = this.serverContext || Container.get(ServerContextService);
      
      // 设置跨服上下文
      const crossServerContext: ServerContext = {
        serverId: options?.targetServerId || 'global',
        characterId: undefined,
        sessionId: undefined,
        isCrossServer: true,
      };

      return serverContext.runWithContext(crossServerContext, () =>
        originalMethod.apply(this, args)
      );
    };

    SetMetadata('cross-server', true)(target, propertyName, descriptor);
  };
};

// 全局数据装饰器
export const GlobalData = () => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const serverContext = this.serverContext || Container.get(ServerContextService);
      
      const globalContext: ServerContext = {
        serverId: 'global',
        characterId: undefined,
        sessionId: undefined,
        isCrossServer: false,
      };

      return serverContext.runWithContext(globalContext, () =>
        originalMethod.apply(this, args)
      );
    };

    SetMetadata('global-data', true)(target, propertyName, descriptor);
  };
};
```

## 🗄️ 数据库路由设计

### **1. 区服感知的数据库服务**

```typescript
// libs/common/src/database/server-aware-database.service.ts
@Injectable()
export class ServerAwareDatabaseService {
  private connections = new Map<string, Connection>();
  private readonly logger = new Logger(ServerAwareDatabaseService.name);

  constructor(
    private configService: ConfigService,
    private serverContext: ServerContextService
  ) {}

  // 获取当前上下文的数据库连接
  getCurrentConnection(): Connection {
    const context = this.serverContext.getCurrentContext();
    
    if (!context?.serverId) {
      throw new BadRequestException('Server context is required for database operations');
    }

    if (context.serverId === 'global') {
      return this.getGlobalConnection();
    }

    return this.getServerConnection(context.serverId);
  }

  // 获取指定区服的数据库连接
  getServerConnection(serverId: string): Connection {
    const connectionKey = `server_${serverId}`;
    
    if (!this.connections.has(connectionKey)) {
      const connection = this.createServerConnection(serverId);
      this.connections.set(connectionKey, connection);
      this.logger.log(`Created database connection for server: ${serverId}`);
    }

    return this.connections.get(connectionKey)!;
  }

  // 获取全局数据库连接
  getGlobalConnection(): Connection {
    const connectionKey = 'global';
    
    if (!this.connections.has(connectionKey)) {
      const connection = this.createGlobalConnection();
      this.connections.set(connectionKey, connection);
      this.logger.log('Created global database connection');
    }

    return this.connections.get(connectionKey)!;
  }

  // 创建区服数据库连接
  private createServerConnection(serverId: string): Connection {
    const serviceName = this.getServiceName();
    const envKey = `${serviceName.toUpperCase()}_MONGODB_URI_${serverId.toUpperCase()}`;
    const fallbackKey = `${serviceName.toUpperCase()}_MONGODB_URI`;
    
    const uri = this.configService.get(envKey) || this.configService.get(fallbackKey);
    
    if (!uri) {
      throw new Error(`Database URI not found for service ${serviceName}, server ${serverId}`);
    }

    return createConnection({
      name: `${serviceName}_${serverId}`,
      uri,
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
  }

  // 创建全局数据库连接
  private createGlobalConnection(): Connection {
    const serviceName = this.getServiceName();
    const envKey = `${serviceName.toUpperCase()}_MONGODB_URI`;
    
    const uri = this.configService.get(envKey);
    
    if (!uri) {
      throw new Error(`Global database URI not found for service ${serviceName}`);
    }

    return createConnection({
      name: `${serviceName}_global`,
      uri,
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 20,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
  }

  // 获取当前服务名称
  private getServiceName(): string {
    return this.configService.get('SERVICE_NAME') || 'unknown';
  }

  // 关闭所有连接
  async closeAllConnections(): Promise<void> {
    const closePromises = Array.from(this.connections.values()).map(connection =>
      connection.close()
    );
    
    await Promise.all(closePromises);
    this.connections.clear();
    this.logger.log('All database connections closed');
  }

  // 获取连接统计信息
  getConnectionStats(): ConnectionStats {
    const stats: ConnectionStats = {
      totalConnections: this.connections.size,
      connections: {},
    };

    this.connections.forEach((connection, key) => {
      stats.connections[key] = {
        readyState: connection.readyState,
        name: connection.name,
        host: connection.host,
        port: connection.port,
      };
    });

    return stats;
  }
}
```

### **2. 区服感知的Repository基类**

```typescript
// libs/common/src/database/server-aware-repository.base.ts
export abstract class ServerAwareRepositoryBase<T extends Document> {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    protected readonly model: Model<T>,
    protected readonly serverDatabase: ServerAwareDatabaseService,
    protected readonly serverContext: ServerContextService
  ) {}

  // 获取当前模型（自动路由到正确的数据库）
  protected getCurrentModel(): Model<T> {
    const connection = this.serverDatabase.getCurrentConnection();
    return connection.model(this.model.modelName, this.model.schema);
  }

  // 基础查找方法
  @ServerAware()
  async findById(id: string): Promise<T | null> {
    const model = this.getCurrentModel();
    return model.findById(id).exec();
  }

  @ServerAware()
  async findOne(filter: FilterQuery<T>): Promise<T | null> {
    const model = this.getCurrentModel();
    return model.findOne(filter).exec();
  }

  @ServerAware()
  async find(filter: FilterQuery<T> = {}): Promise<T[]> {
    const model = this.getCurrentModel();
    return model.find(filter).exec();
  }

  // 基础创建方法
  @ServerAware()
  async create(doc: Partial<T>): Promise<T> {
    const model = this.getCurrentModel();
    const context = this.serverContext.getCurrentContext();
    
    // 自动添加区服ID
    const enhancedDoc = {
      ...doc,
      serverId: context?.serverId,
    };

    const created = new model(enhancedDoc);
    return created.save();
  }

  // 基础更新方法
  @ServerAware()
  async updateById(id: string, update: UpdateQuery<T>): Promise<T | null> {
    const model = this.getCurrentModel();
    return model.findByIdAndUpdate(id, update, { new: true }).exec();
  }

  // 基础删除方法
  @ServerAware()
  async deleteById(id: string): Promise<T | null> {
    const model = this.getCurrentModel();
    return model.findByIdAndDelete(id).exec();
  }

  // 分页查询
  @ServerAware()
  async findWithPagination(
    filter: FilterQuery<T>,
    options: PaginationOptions
  ): Promise<PaginatedResult<T>> {
    const model = this.getCurrentModel();
    const { page = 1, limit = 10, sort } = options;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      model.find(filter).sort(sort).skip(skip).limit(limit).exec(),
      model.countDocuments(filter).exec(),
    ]);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  // 跨服查询（需要明确指定）
  @CrossServer()
  async findCrossServer(filter: FilterQuery<T>, serverIds: string[]): Promise<CrossServerResult<T>> {
    const results: CrossServerResult<T> = {};

    for (const serverId of serverIds) {
      try {
        const connection = this.serverDatabase.getServerConnection(serverId);
        const model = connection.model(this.model.modelName, this.model.schema);
        const data = await model.find(filter).exec();
        results[serverId] = data;
      } catch (error) {
        this.logger.warn(`Failed to query server ${serverId}: ${error.message}`);
        results[serverId] = [];
      }
    }

    return results;
  }

  // 全局数据操作
  @GlobalData()
  async findGlobal(filter: FilterQuery<T>): Promise<T[]> {
    const connection = this.serverDatabase.getGlobalConnection();
    const model = connection.model(this.model.modelName, this.model.schema);
    return model.find(filter).exec();
  }

  @GlobalData()
  async createGlobal(doc: Partial<T>): Promise<T> {
    const connection = this.serverDatabase.getGlobalConnection();
    const model = connection.model(this.model.modelName, this.model.schema);
    const created = new model(doc);
    return created.save();
  }
}
```

## 🎮 Character服务适配

### **1. Character服务模块扩展**

```typescript
// apps/character/src/app.module.ts (扩展现有)
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // 数据库模块 - 支持动态区服数据库
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        // 默认连接（用于全局数据）
        const defaultUri = configService.get('CHARACTER_MONGODB_URI');
        return {
          uri: defaultUri,
          connectionName: 'default',
        };
      },
      inject: [ConfigService],
    }),

    // Redis模块 - 使用现有v3.0架构
    RedisModule.forRootAsync({
      service: 'character',
    }),

    // 微服务公共库 - 混合模式
    MicroserviceKitModule.forHybrid(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      {
        services: [
          MICROSERVICE_NAMES.AUTH_SERVICE,
          MICROSERVICE_NAMES.HERO_SERVICE,
          MICROSERVICE_NAMES.ECONOMY_SERVICE,
        ],
      }
    ),

    // 区服感知模块（新增）
    ServerAwareModule.forRoot({
      serviceName: 'character',
      enableCrossServer: true,
      enableGlobalData: true,
    }),

    // 业务模块
    CharacterModule,
    InventoryModule,
    TacticModule,
  ],
  providers: [
    // 全局异常过滤器
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    // 全局拦截器
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
  ],
})
export class AppModule {}
```

### **2. Character Repository适配**

```typescript
// apps/character/src/common/repositories/character.repository.ts (扩展现有)
@Injectable()
export class CharacterRepository extends ServerAwareRepositoryBase<CharacterDocument> {
  constructor(
    @InjectModel(Character.name) characterModel: Model<CharacterDocument>,
    serverDatabase: ServerAwareDatabaseService,
    serverContext: ServerContextService
  ) {
    super(characterModel, serverDatabase, serverContext);
  }

  // 扩展现有方法，支持区服隔离
  @ServerAware()
  async findByCharacterId(characterId: string): Promise<CharacterDocument | null> {
    try {
      const model = this.getCurrentModel();
      const context = this.serverContext.getCurrentContext();
      
      return await model.findOne({ 
        characterId,
        serverId: context?.serverId, // 自动添加区服过滤
      }).exec();
    } catch (error) {
      this.logger.error(`根据角色ID查找角色失败: ${characterId}`, error);
      throw error;
    }
  }

  // 新增：根据账号ID和区服ID查找角色列表
  @ServerAware()
  async findByAccountIdAndServerId(accountId: string, serverId?: string): Promise<CharacterDocument[]> {
    try {
      const model = this.getCurrentModel();
      const targetServerId = serverId || this.serverContext.getCurrentServerId();
      
      return await model.find({ 
        accountId,
        serverId: targetServerId,
      }).exec();
    } catch (error) {
      this.logger.error(`根据账号ID和区服ID查找角色列表失败: ${accountId}, ${serverId}`, error);
      throw error;
    }
  }

  // 新增：创建角色时自动设置区服ID
  @ServerAware()
  async createCharacterForServer(createDto: CreateCharacterDto, accountId: string): Promise<CharacterDocument> {
    try {
      const context = this.serverContext.getCurrentContext();
      
      if (!context?.serverId) {
        throw new BadRequestException('创建角色需要区服上下文');
      }

      const characterData = {
        ...createDto,
        accountId,
        serverId: context.serverId,
        characterId: this.generateCharacterId(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      return await this.create(characterData);
    } catch (error) {
      this.logger.error(`创建角色失败: ${JSON.stringify(createDto)}`, error);
      throw error;
    }
  }

  // 跨服查询：获取账号在所有区服的角色
  @CrossServer()
  async findCharactersAcrossServers(accountId: string, serverIds: string[]): Promise<CrossServerCharacterResult> {
    const results: CrossServerCharacterResult = {};

    for (const serverId of serverIds) {
      try {
        const connection = this.serverDatabase.getServerConnection(serverId);
        const model = connection.model(Character.name, this.model.schema);
        
        const characters = await model.find({ accountId }).exec();
        results[serverId] = characters;
      } catch (error) {
        this.logger.warn(`查询区服 ${serverId} 的角色失败: ${error.message}`);
        results[serverId] = [];
      }
    }

    return results;
  }

  // 合服数据迁移
  async migrateCharactersToServer(sourceServerId: string, targetServerId: string): Promise<MigrationResult> {
    const sourceConnection = this.serverDatabase.getServerConnection(sourceServerId);
    const targetConnection = this.serverDatabase.getServerConnection(targetServerId);
    
    const sourceModel = sourceConnection.model(Character.name, this.model.schema);
    const targetModel = targetConnection.model(Character.name, this.model.schema);

    const sourceCharacters = await sourceModel.find({}).exec();
    const migrationResult: MigrationResult = {
      migratedCount: 0,
      conflicts: [],
      errors: [],
    };

    for (const character of sourceCharacters) {
      try {
        // 检查目标服务器是否有同名角色
        const existingCharacter = await targetModel.findOne({ 
          name: character.name 
        }).exec();

        if (existingCharacter) {
          // 处理重名冲突
          character.name = `${character.name}_S${sourceServerId}`;
          migrationResult.conflicts.push({
            type: 'name_conflict',
            characterId: character.characterId,
            originalName: character.name,
            newName: character.name,
          });
        }

        // 更新区服ID
        character.serverId = targetServerId;
        character.migratedFrom = sourceServerId;
        character.migratedAt = new Date();

        // 保存到目标服务器
        const newCharacter = new targetModel(character.toObject());
        await newCharacter.save();

        migrationResult.migratedCount++;
      } catch (error) {
        migrationResult.errors.push({
          characterId: character.characterId,
          error: error.message,
        });
      }
    }

    return migrationResult;
  }

  private generateCharacterId(): string {
    return `char_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

### **3. Character Service适配**

```typescript
// apps/character/src/modules/character/character.service.ts (扩展现有)
@Injectable()
export class CharacterService {
  private readonly logger = new Logger(CharacterService.name);

  constructor(
    private readonly characterRepository: CharacterRepository,
    private readonly serverContext: ServerContextService,
    private readonly microserviceClient: ServerAwareMicroserviceClient,
    private readonly redisService: RedisService
  ) {}

  // 扩展现有方法，支持区服隔离
  @ServerAware()
  @Cacheable({ key: 'character:info:#{characterId}', ttl: 1800 })
  async getCharacterInfo(characterId: string): Promise<CharacterInfoDto> {
    const character = await this.characterRepository.findByCharacterId(characterId);
    
    if (!character) {
      throw new NotFoundException('角色不存在');
    }

    return this.buildCharacterInfo(character);
  }

  // 新增：获取账号在当前区服的角色列表
  @ServerAware()
  @Cacheable({ key: 'character:list:#{accountId}', ttl: 600 })
  async getCharacterList(accountId: string): Promise<CharacterListDto> {
    const characters = await this.characterRepository.findByAccountIdAndServerId(accountId);
    
    return {
      characters: characters.map(char => this.buildCharacterSummary(char)),
      total: characters.length,
      serverId: this.serverContext.getCurrentServerId(),
    };
  }

  // 新增：创建角色（自动设置区服）
  @ServerAware()
  async createCharacter(createDto: CreateCharacterDto, accountId: string): Promise<CharacterDocument> {
    // 验证角色名称唯一性（在当前区服内）
    const existingCharacter = await this.characterRepository.findOne({ 
      name: createDto.name 
    });
    
    if (existingCharacter) {
      throw new ConflictException('角色名称已存在');
    }

    // 检查账号在当前区服的角色数量限制
    const existingCharacters = await this.characterRepository.findByAccountIdAndServerId(accountId);
    if (existingCharacters.length >= 3) { // 假设每个区服最多3个角色
      throw new BadRequestException('角色数量已达上限');
    }

    const character = await this.characterRepository.createCharacterForServer(createDto, accountId);
    
    // 清除缓存
    await this.clearCharacterCache(accountId);
    
    this.logger.log(`角色创建成功: ${character.characterId}, 区服: ${character.serverId}`);
    
    return character;
  }

  // 跨服功能：获取全服排行榜
  @CrossServer()
  @Cacheable({ key: 'character:global_ranking:#{category}', ttl: 300, dataType: 'cross' })
  async getGlobalRanking(category: string, limit: number = 100): Promise<GlobalRankingDto> {
    // 获取所有活跃区服
    const activeServers = await this.getActiveServers();
    
    // 并行获取各区服排行榜
    const serverRankings = await Promise.all(
      activeServers.map(async (server) => {
        try {
          return await this.serverContext.runWithContext(
            { serverId: server.id, characterId: undefined, sessionId: undefined, isCrossServer: false },
            () => this.getServerRanking(category, limit)
          );
        } catch (error) {
          this.logger.warn(`获取区服 ${server.id} 排行榜失败: ${error.message}`);
          return [];
        }
      })
    );

    // 合并并排序
    const allRankings: RankingItem[] = [];
    serverRankings.forEach((rankings, index) => {
      rankings.forEach(ranking => {
        allRankings.push({
          ...ranking,
          serverId: activeServers[index].id,
          serverName: activeServers[index].name,
        });
      });
    });

    const globalRanking = allRankings
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map((item, index) => ({
        ...item,
        globalRank: index + 1,
      }));

    return {
      category,
      ranking: globalRanking,
      totalServers: activeServers.length,
      timestamp: new Date(),
    };
  }

  // 合服数据迁移接口
  async migrateServerData(sourceServerId: string, targetServerId: string): Promise<MigrationResult> {
    this.logger.log(`开始迁移角色数据: ${sourceServerId} -> ${targetServerId}`);
    
    const result = await this.characterRepository.migrateCharactersToServer(sourceServerId, targetServerId);
    
    // 清除相关缓存
    await this.clearServerCache(targetServerId);
    
    this.logger.log(`角色数据迁移完成: 迁移 ${result.migratedCount} 个角色, 冲突 ${result.conflicts.length} 个`);
    
    return result;
  }

  // 私有方法
  private async getActiveServers(): Promise<Server[]> {
    return await this.microserviceClient.call('auth', 'getActiveServers', {});
  }

  private async getServerRanking(category: string, limit: number): Promise<RankingItem[]> {
    // 实现区服内排行榜逻辑
    const characters = await this.characterRepository.find({});
    
    return characters
      .sort((a, b) => this.getScoreByCategory(b, category) - this.getScoreByCategory(a, category))
      .slice(0, limit)
      .map((char, index) => ({
        rank: index + 1,
        characterId: char.characterId,
        name: char.name,
        score: this.getScoreByCategory(char, category),
        level: char.level,
      }));
  }

  private getScoreByCategory(character: CharacterDocument, category: string): number {
    switch (category) {
      case 'level':
        return character.level;
      case 'power':
        return character.power || 0;
      default:
        return 0;
    }
  }

  private async clearCharacterCache(accountId: string): Promise<void> {
    const serverId = this.serverContext.getCurrentServerId();
    await this.redisService.del(`character:list:${accountId}`, 'server');
  }

  private async clearServerCache(serverId: string): Promise<void> {
    // 清除区服相关的所有缓存
    const pattern = `*character*`;
    await this.redisService.delPattern(pattern, 'server');
  }

  private buildCharacterInfo(character: CharacterDocument): CharacterInfoDto {
    return {
      characterId: character.characterId,
      name: character.name,
      level: character.level,
      power: character.power,
      serverId: character.serverId,
      // ... 其他字段
    };
  }

  private buildCharacterSummary(character: CharacterDocument): CharacterSummaryDto {
    return {
      characterId: character.characterId,
      name: character.name,
      level: character.level,
      lastLoginTime: character.lastLoginTime,
      isLastLogin: character.isLastLogin,
    };
  }
}
```

---

> **核心优势**：
> - 🔧 **无缝集成**：基于现有微服务公共库扩展，保持架构一致性
> - 🗄️ **智能路由**：自动根据上下文路由到正确的数据库
> - 🎯 **装饰器驱动**：通过装饰器实现声明式的区服感知
> - 🔄 **跨服支持**：内置跨服查询和数据迁移功能
> - 📊 **缓存优化**：支持区服级别的缓存隔离
