# 跨服功能设计 - 全服互动与竞技

## 📋 概述

本文档详细设计跨服功能的实现方案，包括跨服排行榜、跨服战斗、跨服活动和跨服聊天等核心功能。

## 🏆 跨服排行榜系统

### **1. 排行榜数据聚合服务**

```typescript
// libs/common/src/cross-server/ranking/ (新增模块)
├── cross-server-ranking.module.ts
├── services/
│   ├── ranking-aggregator.service.ts       # 排行榜聚合器
│   ├── ranking-cache.service.ts            # 排行榜缓存服务
│   └── ranking-scheduler.service.ts        # 排行榜调度器
├── interfaces/
│   ├── ranking.interface.ts
│   └── ranking-config.interface.ts
└── dto/
    ├── global-ranking.dto.ts
    └── server-ranking.dto.ts

// 排行榜聚合器服务
@Injectable()
export class RankingAggregatorService {
  private readonly logger = new Logger(RankingAggregatorService.name);

  constructor(
    private readonly microserviceClient: ServerAwareMicroserviceClient,
    private readonly rankingCache: RankingCacheService,
    private readonly redisService: RedisService,
    private readonly configService: ConfigService
  ) {}

  // 获取全服排行榜
  @CrossServer()
  async getGlobalRanking(category: RankingCategory, options: RankingOptions = {}): Promise<GlobalRankingResult> {
    const { limit = 100, forceRefresh = false } = options;
    const cacheKey = `global:ranking:${category}:${limit}`;

    // 尝试从缓存获取
    if (!forceRefresh) {
      const cached = await this.rankingCache.get(cacheKey);
      if (cached) {
        return cached;
      }
    }

    this.logger.log(`开始聚合全服排行榜: ${category}`);

    try {
      // 获取所有活跃区服
      const activeServers = await this.getActiveServers();
      
      // 并行获取各区服排行榜
      const serverRankings = await this.fetchServerRankings(activeServers, category, limit);
      
      // 聚合并排序
      const globalRanking = await this.aggregateRankings(serverRankings, limit);
      
      // 缓存结果
      await this.rankingCache.set(cacheKey, globalRanking, 300); // 5分钟缓存
      
      // 发布排行榜更新事件
      await this.publishRankingUpdate(category, globalRanking);
      
      this.logger.log(`全服排行榜聚合完成: ${category}, 总计 ${globalRanking.items.length} 条记录`);
      
      return globalRanking;

    } catch (error) {
      this.logger.error(`全服排行榜聚合失败: ${category}, ${error.message}`);
      throw new InternalServerErrorException('排行榜聚合失败');
    }
  }

  // 获取区服排行榜对比
  @CrossServer()
  async getServerComparison(category: RankingCategory, serverIds: string[]): Promise<ServerComparisonResult> {
    const serverRankings = await this.fetchServerRankings(
      serverIds.map(id => ({ id, name: `Server ${id}` })),
      category,
      50
    );

    const comparison: ServerComparisonResult = {
      category,
      servers: {},
      statistics: {
        totalPlayers: 0,
        averageScore: 0,
        highestScore: 0,
        lowestScore: Number.MAX_VALUE,
      },
    };

    let allScores: number[] = [];

    for (const [serverId, ranking] of Object.entries(serverRankings)) {
      const scores = ranking.map(item => item.score);
      allScores.push(...scores);

      comparison.servers[serverId] = {
        ranking,
        statistics: {
          playerCount: ranking.length,
          averageScore: scores.reduce((a, b) => a + b, 0) / scores.length || 0,
          highestScore: Math.max(...scores, 0),
          lowestScore: Math.min(...scores, Number.MAX_VALUE),
        },
      };
    }

    // 计算全局统计
    comparison.statistics = {
      totalPlayers: allScores.length,
      averageScore: allScores.reduce((a, b) => a + b, 0) / allScores.length || 0,
      highestScore: Math.max(...allScores, 0),
      lowestScore: Math.min(...allScores, Number.MAX_VALUE),
    };

    return comparison;
  }

  // 获取玩家跨服排名
  async getPlayerGlobalRank(characterId: string, category: RankingCategory): Promise<PlayerGlobalRankResult> {
    // 先获取玩家所在区服的排名
    const playerInfo = await this.microserviceClient.call('character', 'getCharacterInfo', { characterId });
    
    if (!playerInfo) {
      throw new NotFoundException('角色不存在');
    }

    // 获取全服排行榜
    const globalRanking = await this.getGlobalRanking(category, { limit: 10000 });
    
    // 查找玩家在全服的排名
    const playerRankIndex = globalRanking.items.findIndex(item => item.characterId === characterId);
    
    if (playerRankIndex === -1) {
      return {
        characterId,
        category,
        globalRank: null,
        serverRank: null,
        score: this.getPlayerScore(playerInfo, category),
        totalPlayers: globalRanking.totalPlayers,
      };
    }

    const playerRankItem = globalRanking.items[playerRankIndex];
    
    return {
      characterId,
      category,
      globalRank: playerRankIndex + 1,
      serverRank: playerRankItem.serverRank,
      score: playerRankItem.score,
      totalPlayers: globalRanking.totalPlayers,
      percentile: ((globalRanking.totalPlayers - playerRankIndex) / globalRanking.totalPlayers) * 100,
    };
  }

  // 私有方法：获取活跃区服
  private async getActiveServers(): Promise<ServerInfo[]> {
    const cacheKey = 'global:active_servers';
    const cached = await this.redisService.get(cacheKey, 'global');
    
    if (cached) {
      return JSON.parse(cached);
    }

    const servers = await this.microserviceClient.call('auth', 'getActiveServers', {});
    await this.redisService.set(cacheKey, JSON.stringify(servers), 60, 'global'); // 1分钟缓存
    
    return servers;
  }

  // 私有方法：获取各区服排行榜
  private async fetchServerRankings(
    servers: ServerInfo[],
    category: RankingCategory,
    limit: number
  ): Promise<Record<string, RankingItem[]>> {
    const rankings: Record<string, RankingItem[]> = {};

    const fetchPromises = servers.map(async (server) => {
      try {
        const ranking = await this.microserviceClient.callCrossServer(
          'character',
          'getRanking',
          { category, limit },
          server.id
        );
        
        rankings[server.id] = ranking.map((item: any, index: number) => ({
          ...item,
          serverId: server.id,
          serverName: server.name,
          serverRank: index + 1,
        }));
      } catch (error) {
        this.logger.warn(`获取区服 ${server.id} 排行榜失败: ${error.message}`);
        rankings[server.id] = [];
      }
    });

    await Promise.all(fetchPromises);
    return rankings;
  }

  // 私有方法：聚合排行榜
  private async aggregateRankings(
    serverRankings: Record<string, RankingItem[]>,
    limit: number
  ): Promise<GlobalRankingResult> {
    const allItems: RankingItem[] = [];
    let totalPlayers = 0;

    // 合并所有区服的排行榜
    Object.values(serverRankings).forEach(ranking => {
      allItems.push(...ranking);
      totalPlayers += ranking.length;
    });

    // 全局排序
    allItems.sort((a, b) => b.score - a.score);

    // 截取前N名并添加全服排名
    const topItems = allItems.slice(0, limit).map((item, index) => ({
      ...item,
      globalRank: index + 1,
    }));

    return {
      items: topItems,
      totalPlayers,
      totalServers: Object.keys(serverRankings).length,
      lastUpdated: new Date(),
    };
  }

  // 私有方法：发布排行榜更新事件
  private async publishRankingUpdate(category: RankingCategory, ranking: GlobalRankingResult): Promise<void> {
    await this.redisService.publish('ranking:updated', JSON.stringify({
      category,
      topPlayers: ranking.items.slice(0, 10), // 只发布前10名
      timestamp: new Date(),
    }));
  }

  private getPlayerScore(playerInfo: any, category: RankingCategory): number {
    switch (category) {
      case 'level':
        return playerInfo.level || 0;
      case 'power':
        return playerInfo.power || 0;
      case 'wealth':
        return playerInfo.gold || 0;
      default:
        return 0;
    }
  }
}
```

### **2. 排行榜缓存服务**

```typescript
// 排行榜缓存服务
@Injectable()
export class RankingCacheService {
  private readonly logger = new Logger(RankingCacheService.name);

  constructor(private readonly redisService: RedisService) {}

  // 获取缓存的排行榜
  async get(key: string): Promise<GlobalRankingResult | null> {
    try {
      const cached = await this.redisService.get(key, 'cross');
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      this.logger.warn(`获取排行榜缓存失败: ${key}, ${error.message}`);
      return null;
    }
  }

  // 设置排行榜缓存
  async set(key: string, ranking: GlobalRankingResult, ttl: number): Promise<void> {
    try {
      await this.redisService.set(key, JSON.stringify(ranking), ttl, 'cross');
    } catch (error) {
      this.logger.error(`设置排行榜缓存失败: ${key}, ${error.message}`);
    }
  }

  // 删除排行榜缓存
  async del(key: string): Promise<void> {
    try {
      await this.redisService.del(key, 'cross');
    } catch (error) {
      this.logger.warn(`删除排行榜缓存失败: ${key}, ${error.message}`);
    }
  }

  // 批量删除排行榜缓存
  async delPattern(pattern: string): Promise<void> {
    try {
      await this.redisService.delPattern(pattern, 'cross');
    } catch (error) {
      this.logger.warn(`批量删除排行榜缓存失败: ${pattern}, ${error.message}`);
    }
  }

  // 预热排行榜缓存
  async warmup(categories: RankingCategory[]): Promise<void> {
    this.logger.log('开始预热排行榜缓存');

    const warmupPromises = categories.map(async (category) => {
      try {
        // 这里需要注入RankingAggregatorService，但为了避免循环依赖，
        // 实际实现中应该通过事件或者其他方式触发
        this.logger.log(`预热排行榜缓存: ${category}`);
      } catch (error) {
        this.logger.warn(`预热排行榜缓存失败: ${category}, ${error.message}`);
      }
    });

    await Promise.all(warmupPromises);
    this.logger.log('排行榜缓存预热完成');
  }
}
```

## ⚔️ 跨服战斗系统

### **1. 跨服匹配服务**

```typescript
// libs/common/src/cross-server/battle/ (新增模块)
├── cross-server-battle.module.ts
├── services/
│   ├── battle-matcher.service.ts           # 战斗匹配器
│   ├── battle-coordinator.service.ts       # 战斗协调器
│   └── battle-result.service.ts            # 战斗结果处理
├── interfaces/
│   ├── battle-match.interface.ts
│   └── battle-result.interface.ts
└── dto/
    ├── match-request.dto.ts
    └── battle-result.dto.ts

// 跨服战斗匹配器
@Injectable()
export class CrossServerBattleMatcherService {
  private readonly logger = new Logger(CrossServerBattleMatcherService.name);
  private readonly matchingQueue = new Map<string, MatchRequest>();

  constructor(
    private readonly microserviceClient: ServerAwareMicroserviceClient,
    private readonly redisService: RedisService,
    private readonly battleCoordinator: BattleCoordinatorService
  ) {
    // 启动匹配处理器
    this.startMatchProcessor();
  }

  // 寻找跨服对手
  async findCrossServerOpponent(request: CrossServerMatchRequest): Promise<CrossServerMatchResult> {
    const { characterId, serverId, powerRange, battleType } = request;
    
    this.logger.log(`开始跨服匹配: ${characterId} (${serverId}), 战力范围: ${powerRange.join('-')}`);

    try {
      // 1. 验证角色信息
      const character = await this.validateCharacter(characterId, serverId);
      
      // 2. 检查匹配冷却
      const cooldownKey = `battle:cooldown:${characterId}`;
      const cooldown = await this.redisService.get(cooldownKey, 'server');
      if (cooldown) {
        throw new BadRequestException('匹配冷却中，请稍后再试');
      }

      // 3. 创建匹配请求
      const matchRequest: MatchRequest = {
        id: this.generateMatchId(),
        characterId,
        serverId,
        character,
        powerRange,
        battleType,
        timestamp: new Date(),
        status: 'searching',
      };

      // 4. 加入匹配队列
      this.matchingQueue.set(matchRequest.id, matchRequest);

      // 5. 发布匹配请求到其他区服
      await this.publishMatchRequest(matchRequest);

      // 6. 等待匹配结果
      const matchResult = await this.waitForMatch(matchRequest.id, 30000); // 30秒超时

      if (matchResult) {
        // 设置匹配冷却
        await this.redisService.set(cooldownKey, '1', 60, 'server'); // 1分钟冷却
        
        this.logger.log(`跨服匹配成功: ${characterId} vs ${matchResult.opponent.characterId}`);
        return matchResult;
      } else {
        this.logger.log(`跨服匹配超时: ${characterId}`);
        throw new NotFoundException('未找到合适的对手');
      }

    } catch (error) {
      this.logger.error(`跨服匹配失败: ${characterId}, ${error.message}`);
      throw error;
    } finally {
      // 清理匹配请求
      this.matchingQueue.delete(request.characterId);
    }
  }

  // 响应匹配请求
  async respondToMatchRequest(requestId: string, response: MatchResponse): Promise<void> {
    const { accept, characterId, serverId } = response;

    if (!accept) {
      return; // 拒绝匹配，不做处理
    }

    try {
      // 验证响应角色
      const character = await this.validateCharacter(characterId, serverId);

      // 发布匹配响应
      await this.publishMatchResponse(requestId, {
        characterId,
        serverId,
        character,
        timestamp: new Date(),
      });

      this.logger.log(`响应跨服匹配: ${characterId} 接受了匹配请求 ${requestId}`);

    } catch (error) {
      this.logger.error(`响应跨服匹配失败: ${characterId}, ${error.message}`);
    }
  }

  // 私有方法：验证角色
  private async validateCharacter(characterId: string, serverId: string): Promise<BattleCharacter> {
    const character = await this.microserviceClient.callCrossServer(
      'character',
      'getCharacterInfo',
      { characterId },
      serverId
    );

    if (!character) {
      throw new NotFoundException('角色不存在');
    }

    if (character.status !== 'online') {
      throw new BadRequestException('角色不在线');
    }

    return {
      characterId: character.characterId,
      name: character.name,
      level: character.level,
      power: character.power,
      serverId,
      avatar: character.avatar,
    };
  }

  // 私有方法：发布匹配请求
  private async publishMatchRequest(request: MatchRequest): Promise<void> {
    const message = {
      type: 'match_request',
      data: {
        requestId: request.id,
        character: request.character,
        powerRange: request.powerRange,
        battleType: request.battleType,
        sourceServerId: request.serverId,
      },
    };

    await this.redisService.publish('cross_server:battle_match', JSON.stringify(message));
  }

  // 私有方法：发布匹配响应
  private async publishMatchResponse(requestId: string, response: MatchResponseData): Promise<void> {
    const message = {
      type: 'match_response',
      data: {
        requestId,
        opponent: response.character,
        timestamp: response.timestamp,
      },
    };

    await this.redisService.publish('cross_server:battle_match', JSON.stringify(message));
  }

  // 私有方法：等待匹配结果
  private async waitForMatch(requestId: string, timeout: number): Promise<CrossServerMatchResult | null> {
    return new Promise((resolve) => {
      const timeoutId = setTimeout(() => resolve(null), timeout);

      // 订阅匹配响应
      const subscription = this.redisService.subscribe('cross_server:battle_match', (message) => {
        try {
          const parsed = JSON.parse(message);
          
          if (parsed.type === 'match_response' && parsed.data.requestId === requestId) {
            clearTimeout(timeoutId);
            resolve({
              success: true,
              opponent: parsed.data.opponent,
              battleId: this.generateBattleId(),
              timestamp: new Date(),
            });
          }
        } catch (error) {
          this.logger.warn(`解析匹配响应失败: ${error.message}`);
        }
      });

      // 超时后取消订阅
      setTimeout(() => {
        subscription.unsubscribe();
      }, timeout + 1000);
    });
  }

  // 私有方法：启动匹配处理器
  private startMatchProcessor(): void {
    // 订阅匹配请求
    this.redisService.subscribe('cross_server:battle_match', async (message) => {
      try {
        const parsed = JSON.parse(message);
        
        if (parsed.type === 'match_request') {
          await this.processMatchRequest(parsed.data);
        }
      } catch (error) {
        this.logger.warn(`处理匹配请求失败: ${error.message}`);
      }
    });

    this.logger.log('跨服战斗匹配处理器已启动');
  }

  // 私有方法：处理匹配请求
  private async processMatchRequest(requestData: any): Promise<void> {
    const { requestId, character, powerRange, battleType, sourceServerId } = requestData;

    // 跳过来自同一区服的请求
    const currentServerId = this.getCurrentServerId();
    if (sourceServerId === currentServerId) {
      return;
    }

    try {
      // 在当前区服寻找合适的对手
      const opponent = await this.findLocalOpponent(character, powerRange, battleType);

      if (opponent) {
        // 发送匹配响应
        await this.respondToMatchRequest(requestId, {
          accept: true,
          characterId: opponent.characterId,
          serverId: currentServerId,
        });
      }
    } catch (error) {
      this.logger.warn(`处理匹配请求失败: ${requestId}, ${error.message}`);
    }
  }

  // 私有方法：在本地寻找对手
  private async findLocalOpponent(
    requestCharacter: BattleCharacter,
    powerRange: [number, number],
    battleType: string
  ): Promise<BattleCharacter | null> {
    // 调用本地角色服务寻找合适的对手
    const opponents = await this.microserviceClient.call('character', 'findBattleOpponents', {
      powerRange,
      battleType,
      excludeCharacterId: requestCharacter.characterId,
      limit: 10,
    });

    if (opponents && opponents.length > 0) {
      // 随机选择一个对手
      const randomIndex = Math.floor(Math.random() * opponents.length);
      return opponents[randomIndex];
    }

    return null;
  }

  private generateMatchId(): string {
    return `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateBattleId(): string {
    return `battle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getCurrentServerId(): string {
    return process.env.SERVER_ID || 'unknown';
  }
}
```

### **2. 战斗协调器**

```typescript
// 战斗协调器服务
@Injectable()
export class BattleCoordinatorService {
  private readonly logger = new Logger(BattleCoordinatorService.name);
  private readonly activeBattles = new Map<string, CrossServerBattle>();

  constructor(
    private readonly microserviceClient: ServerAwareMicroserviceClient,
    private readonly redisService: RedisService,
    private readonly battleResultService: BattleResultService
  ) {}

  // 创建跨服战斗
  async createCrossServerBattle(battleData: CreateCrossServerBattleData): Promise<CrossServerBattle> {
    const { player1, player2, battleType } = battleData;
    
    const battle: CrossServerBattle = {
      id: this.generateBattleId(),
      type: battleType,
      status: 'preparing',
      players: [player1, player2],
      startTime: new Date(),
      rounds: [],
      result: null,
    };

    // 保存战斗信息
    this.activeBattles.set(battle.id, battle);
    await this.saveBattleToRedis(battle);

    // 通知双方玩家
    await this.notifyPlayers(battle, 'battle_created');

    this.logger.log(`创建跨服战斗: ${battle.id}, ${player1.characterId} vs ${player2.characterId}`);

    return battle;
  }

  // 执行战斗
  async executeBattle(battleId: string): Promise<BattleResult> {
    const battle = this.activeBattles.get(battleId);
    
    if (!battle) {
      throw new NotFoundException('战斗不存在');
    }

    if (battle.status !== 'preparing') {
      throw new BadRequestException('战斗状态异常');
    }

    try {
      // 更新战斗状态
      battle.status = 'fighting';
      await this.saveBattleToRedis(battle);

      // 通知战斗开始
      await this.notifyPlayers(battle, 'battle_started');

      // 执行战斗逻辑
      const battleResult = await this.runBattleSimulation(battle);

      // 更新战斗结果
      battle.status = 'finished';
      battle.result = battleResult;
      battle.endTime = new Date();
      await this.saveBattleToRedis(battle);

      // 处理战斗结果
      await this.battleResultService.processBattleResult(battle);

      // 通知战斗结束
      await this.notifyPlayers(battle, 'battle_finished');

      this.logger.log(`跨服战斗完成: ${battleId}, 胜者: ${battleResult.winner.characterId}`);

      return battleResult;

    } catch (error) {
      // 战斗异常处理
      battle.status = 'error';
      battle.error = error.message;
      await this.saveBattleToRedis(battle);

      this.logger.error(`跨服战斗异常: ${battleId}, ${error.message}`);
      throw error;
    } finally {
      // 清理活跃战斗
      setTimeout(() => {
        this.activeBattles.delete(battleId);
      }, 300000); // 5分钟后清理
    }
  }

  // 私有方法：战斗模拟
  private async runBattleSimulation(battle: CrossServerBattle): Promise<BattleResult> {
    const [player1, player2] = battle.players;
    
    // 获取双方详细战斗数据
    const [player1Data, player2Data] = await Promise.all([
      this.getBattleData(player1),
      this.getBattleData(player2),
    ]);

    // 执行战斗计算
    const rounds: BattleRound[] = [];
    let currentHp1 = player1Data.maxHp;
    let currentHp2 = player2Data.maxHp;

    for (let round = 1; round <= 10 && currentHp1 > 0 && currentHp2 > 0; round++) {
      const roundResult = this.calculateRound(player1Data, player2Data, currentHp1, currentHp2);
      
      currentHp1 = roundResult.player1Hp;
      currentHp2 = roundResult.player2Hp;
      
      rounds.push({
        round,
        actions: roundResult.actions,
        player1Hp: currentHp1,
        player2Hp: currentHp2,
      });
    }

    // 确定胜者
    const winner = currentHp1 > currentHp2 ? player1 : player2;
    const loser = winner === player1 ? player2 : player1;

    return {
      winner,
      loser,
      rounds,
      duration: rounds.length,
      rewards: await this.calculateRewards(winner, loser, battle.type),
    };
  }

  // 私有方法：获取战斗数据
  private async getBattleData(player: BattlePlayer): Promise<BattlePlayerData> {
    const characterData = await this.microserviceClient.callCrossServer(
      'character',
      'getBattleData',
      { characterId: player.characterId },
      player.serverId
    );

    return {
      ...player,
      maxHp: characterData.maxHp,
      attack: characterData.attack,
      defense: characterData.defense,
      speed: characterData.speed,
      skills: characterData.skills,
    };
  }

  // 私有方法：计算回合
  private calculateRound(
    player1: BattlePlayerData,
    player2: BattlePlayerData,
    hp1: number,
    hp2: number
  ): RoundResult {
    // 简化的战斗计算逻辑
    const damage1to2 = Math.max(1, player1.attack - player2.defense);
    const damage2to1 = Math.max(1, player2.attack - player1.defense);

    return {
      actions: [
        { player: player1.characterId, action: 'attack', damage: damage1to2 },
        { player: player2.characterId, action: 'attack', damage: damage2to1 },
      ],
      player1Hp: Math.max(0, hp1 - damage2to1),
      player2Hp: Math.max(0, hp2 - damage1to2),
    };
  }

  // 私有方法：计算奖励
  private async calculateRewards(winner: BattlePlayer, loser: BattlePlayer, battleType: string): Promise<BattleRewards> {
    // 根据战斗类型和双方实力差距计算奖励
    const basePrize = battleType === 'ranked' ? 100 : 50;
    
    return {
      winner: {
        experience: basePrize,
        gold: basePrize * 10,
        items: [],
        ranking: 10, // 排名积分
      },
      loser: {
        experience: basePrize * 0.3,
        gold: basePrize * 3,
        items: [],
        ranking: -5,
      },
    };
  }

  // 私有方法：通知玩家
  private async notifyPlayers(battle: CrossServerBattle, event: string): Promise<void> {
    const notifications = battle.players.map(player => 
      this.microserviceClient.callCrossServer(
        'gateway',
        'notifyPlayer',
        {
          characterId: player.characterId,
          event,
          data: {
            battleId: battle.id,
            battle: this.sanitizeBattleForClient(battle),
          },
        },
        player.serverId
      )
    );

    await Promise.allSettled(notifications);
  }

  // 私有方法：保存战斗到Redis
  private async saveBattleToRedis(battle: CrossServerBattle): Promise<void> {
    const key = `cross_battle:${battle.id}`;
    await this.redisService.set(key, JSON.stringify(battle), 3600, 'cross'); // 1小时TTL
  }

  // 私有方法：清理客户端数据
  private sanitizeBattleForClient(battle: CrossServerBattle): any {
    return {
      id: battle.id,
      type: battle.type,
      status: battle.status,
      players: battle.players.map(p => ({
        characterId: p.characterId,
        name: p.name,
        level: p.level,
        serverId: p.serverId,
      })),
      startTime: battle.startTime,
      endTime: battle.endTime,
    };
  }

  private generateBattleId(): string {
    return `cross_battle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

---

> **核心优势**：
> - 🏆 **实时聚合**：多区服排行榜实时聚合和缓存优化
> - ⚔️ **智能匹配**：基于战力和在线状态的跨服战斗匹配
> - 🔄 **异步协调**：通过Redis PubSub实现跨服事件协调
> - 📊 **数据统计**：完整的跨服数据统计和分析
> - 🎮 **用户体验**：无缝的跨服交互体验
