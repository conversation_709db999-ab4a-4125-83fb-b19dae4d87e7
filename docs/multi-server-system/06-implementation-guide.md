# 实施指南 - 分阶段部署与测试

## 📋 概述

本文档提供分区分服系统的详细实施指南，包括分阶段部署计划、测试策略、监控方案和运维指南。

## 🚀 分阶段实施计划

### **阶段一：基础认证架构（2周）**

#### **1.1 双层Token机制实现**

```typescript
// 第1周：Token生成和验证
// 实施步骤：
// 1. 扩展JWT Service支持双层Token
// 2. 实现Token验证中间件
// 3. 创建认证守卫
// 4. 单元测试覆盖

// apps/auth/src/services/token-generation.service.ts
@Injectable()
export class TokenGenerationService {
  // 实现账号级和角色级Token生成
  // 参考：docs/multi-server-system/01-authentication-architecture.md
}

// 测试清单：
// ✅ 账号Token生成和验证
// ✅ 角色Token生成和验证
// ✅ Token刷新机制
// ✅ Token撤销机制
// ✅ 权限验证守卫
```

#### **1.2 区服管理基础设施**

```typescript
// 第2周：区服管理和会话管理
// 实施步骤：
// 1. 创建区服相关数据表
// 2. 实现区服管理服务
// 3. 实现角色会话管理
// 4. HTTP接口开发

// apps/auth/src/domain/server-management/
// 参考：docs/multi-server-system/01-authentication-architecture.md

// 测试清单：
// ✅ 区服CRUD操作
// ✅ 角色会话创建和管理
// ✅ 玩家历史记录
// ✅ HTTP接口功能测试
```

### **阶段二：网关集成（1周）**

#### **2.1 HTTP接口集成**

```typescript
// 实施步骤：
// 1. 扩展网关HTTP控制器
// 2. 实现认证中间件
// 3. 集成角色认证服务
// 4. API文档更新

// apps/gateway/src/domain/auth-http/
// 参考：docs/multi-server-system/02-gateway-integration.md

// 关键接口：
// POST /api/auth/login          - 账号登录
// GET  /api/servers             - 获取区服列表
// POST /api/character-auth/enter-server - 进入区服
```

#### **2.2 WebSocket集成增强**

```typescript
// 实施步骤：
// 1. 扩展WebSocket认证逻辑
// 2. 实现区服路由中间件
// 3. 增强消息权限验证
// 4. WebSocket会话管理

// apps/gateway/src/domain/websocket/
// 参考：docs/multi-server-system/02-gateway-integration.md

// 测试清单：
// ✅ WebSocket认证流程
// ✅ 账号Token和角色Token切换
// ✅ 消息权限验证
// ✅ 会话状态管理
```

### **阶段三：微服务适配（2周）**

#### **3.1 微服务公共库扩展**

```typescript
// 第1周：公共库开发
// 实施步骤：
// 1. 开发区服感知客户端
// 2. 实现上下文服务
// 3. 创建装饰器系统
// 4. 数据库路由服务

// libs/common/src/microservice-kit/server-aware/
// 参考：docs/multi-server-system/03-microservice-adaptation.md

// 核心组件：
// - ServerContextService
// - ServerAwareMicroserviceClient  
// - ServerAwareDatabaseService
// - @ServerAware, @CrossServer, @GlobalData 装饰器
```

#### **3.2 Character服务适配**

```typescript
// 第2周：Character服务改造
// 实施步骤：
// 1. 集成区服感知模块
// 2. 扩展Repository支持区服路由
// 3. 修改Service层支持数据隔离
// 4. 实现跨服查询功能

// apps/character/src/
// 参考：docs/multi-server-system/03-microservice-adaptation.md

// 测试清单：
// ✅ 数据库自动路由
// ✅ 区服数据隔离
// ✅ 跨服查询功能
// ✅ 缓存隔离机制
```

### **阶段四：跨服功能（1周）**

#### **4.1 跨服排行榜**

```typescript
// 实施步骤：
// 1. 实现排行榜聚合服务
// 2. 开发缓存优化策略
// 3. 创建调度任务
// 4. WebSocket实时推送

// libs/common/src/cross-server/ranking/
// 参考：docs/multi-server-system/04-cross-server-features.md

// 功能验证：
// ✅ 全服排行榜聚合
// ✅ 区服对比功能
// ✅ 玩家全服排名查询
// ✅ 实时排行榜更新
```

#### **4.2 跨服战斗系统**

```typescript
// 实施步骤：
// 1. 实现战斗匹配器
// 2. 开发战斗协调器
// 3. 创建结果处理服务
// 4. Redis PubSub通信

// libs/common/src/cross-server/battle/
// 参考：docs/multi-server-system/04-cross-server-features.md

// 功能验证：
// ✅ 跨服对手匹配
// ✅ 战斗执行和协调
// ✅ 结果处理和奖励
// ✅ 实时战斗通知
```

### **阶段五：合服系统（2周）**

#### **5.1 合服基础设施**

```typescript
// 第1周：合服框架
// 实施步骤：
// 1. 实现合服管理服务
// 2. 开发数据迁移框架
// 3. 创建冲突检测机制
// 4. 实现进度监控

// apps/auth/src/domain/server-merge/
// 参考：docs/multi-server-system/05-server-merge-system.md
```

#### **5.2 合服执行引擎**

```typescript
// 第2周：合服执行
// 实施步骤：
// 1. 实现数据备份和恢复
// 2. 开发冲突解决策略
// 3. 创建补偿计算系统
// 4. 实现回滚机制

// 测试清单：
// ✅ 完整合服流程测试
// ✅ 数据完整性验证
// ✅ 冲突解决验证
// ✅ 补偿发放验证
// ✅ 回滚机制验证
```

## 🧪 测试策略

### **1. 单元测试**

```typescript
// 测试覆盖率要求：>90%
// 关键测试用例：

describe('TokenGenerationService', () => {
  it('should generate valid account token', async () => {
    const user = createMockUser();
    const token = await tokenService.generateAccountToken(user);
    const payload = jwtService.verify(token);
    
    expect(payload.scope).toBe('account');
    expect(payload.sub).toBe(user.id);
  });

  it('should generate valid character token', async () => {
    const accountPayload = createMockAccountPayload();
    const token = await tokenService.generateCharacterToken(
      accountPayload, 'char_123', 'server_001', 'session_456'
    );
    const payload = jwtService.verify(token);
    
    expect(payload.scope).toBe('character');
    expect(payload.characterId).toBe('char_123');
    expect(payload.serverId).toBe('server_001');
  });
});

describe('ServerAwareDatabaseService', () => {
  it('should route to correct server database', async () => {
    serverContext.setContext({ serverId: 'server_001' });
    const connection = dbService.getCurrentConnection();
    
    expect(connection.name).toContain('server_001');
  });

  it('should route to global database for global context', async () => {
    serverContext.setContext({ serverId: 'global' });
    const connection = dbService.getCurrentConnection();
    
    expect(connection.name).toContain('global');
  });
});
```

### **2. 集成测试**

```typescript
// 端到端流程测试
describe('Multi-Server Integration', () => {
  it('should complete full authentication flow', async () => {
    // 1. 账号登录
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({ identifier: '<EMAIL>', password: 'password' });
    
    expect(loginResponse.status).toBe(200);
    const accountToken = loginResponse.body.data.accountToken;

    // 2. 获取区服列表
    const serversResponse = await request(app)
      .get('/api/servers')
      .set('Authorization', `Bearer ${accountToken}`);
    
    expect(serversResponse.status).toBe(200);
    expect(serversResponse.body.data.servers).toHaveLength(2);

    // 3. 进入区服
    const enterResponse = await request(app)
      .post('/api/character-auth/enter-server')
      .set('Authorization', `Bearer ${accountToken}`)
      .send({ serverId: 'server_001' });
    
    expect(enterResponse.status).toBe(200);
    const characterToken = enterResponse.body.data.characterToken;

    // 4. WebSocket连接和认证
    const socket = io('ws://localhost:3000', {
      auth: { token: characterToken }
    });

    await new Promise(resolve => {
      socket.on('authenticated', (data) => {
        expect(data.type).toBe('character');
        expect(data.serverId).toBe('server_001');
        resolve(data);
      });
    });

    // 5. 游戏业务操作
    const response = await new Promise(resolve => {
      socket.emit('message', {
        id: 'test_001',
        command: 'character.getInfo',
        payload: {}
      });

      socket.on('response', resolve);
    });

    expect(response.success).toBe(true);
    expect(response.data.serverId).toBe('server_001');
  });
});
```

### **3. 压力测试**

```typescript
// 性能测试脚本
// scripts/performance-test.js

const { performance } = require('perf_hooks');
const io = require('socket.io-client');

async function performanceTest() {
  const concurrentUsers = 1000;
  const testDuration = 60000; // 1分钟

  console.log(`开始压力测试: ${concurrentUsers} 并发用户, ${testDuration/1000}秒`);

  const sockets = [];
  const metrics = {
    connections: 0,
    authentications: 0,
    messages: 0,
    errors: 0,
    responseTime: [],
  };

  // 创建并发连接
  for (let i = 0; i < concurrentUsers; i++) {
    const socket = io('ws://localhost:3000', {
      auth: { token: await getTestToken() }
    });

    socket.on('connect', () => {
      metrics.connections++;
    });

    socket.on('authenticated', () => {
      metrics.authentications++;
    });

    socket.on('error', () => {
      metrics.errors++;
    });

    sockets.push(socket);
  }

  // 发送消息测试
  const startTime = performance.now();
  const interval = setInterval(() => {
    sockets.forEach(socket => {
      const messageStart = performance.now();
      
      socket.emit('message', {
        id: `test_${Date.now()}`,
        command: 'character.getInfo',
        payload: {}
      });

      socket.once('response', () => {
        const responseTime = performance.now() - messageStart;
        metrics.responseTime.push(responseTime);
        metrics.messages++;
      });
    });
  }, 1000);

  // 测试结束
  setTimeout(() => {
    clearInterval(interval);
    
    const avgResponseTime = metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length;
    
    console.log('压力测试结果:');
    console.log(`- 连接成功: ${metrics.connections}/${concurrentUsers}`);
    console.log(`- 认证成功: ${metrics.authentications}/${concurrentUsers}`);
    console.log(`- 消息处理: ${metrics.messages}`);
    console.log(`- 错误数量: ${metrics.errors}`);
    console.log(`- 平均响应时间: ${avgResponseTime.toFixed(2)}ms`);
    
    sockets.forEach(socket => socket.disconnect());
  }, testDuration);
}

performanceTest().catch(console.error);
```

## 📊 监控与运维

### **1. 监控指标**

```typescript
// 关键监控指标
interface MonitoringMetrics {
  // 认证相关
  authentication: {
    accountTokenGenerated: number;
    characterTokenGenerated: number;
    tokenValidationSuccess: number;
    tokenValidationFailure: number;
    sessionCreated: number;
    sessionExpired: number;
  };

  // 区服相关
  servers: {
    activeServers: number;
    totalPlayers: number;
    serverCapacityUtilization: Record<string, number>;
    crossServerOperations: number;
  };

  // 性能相关
  performance: {
    databaseConnectionPool: Record<string, number>;
    redisOperations: number;
    microserviceCallLatency: Record<string, number>;
    websocketConnections: number;
  };

  // 业务相关
  business: {
    playerLogins: number;
    serverSwitches: number;
    crossServerBattles: number;
    rankingUpdates: number;
  };
}

// 监控服务实现
@Injectable()
export class MonitoringService {
  constructor(
    private readonly metricsService: MetricsService,
    private readonly alertService: AlertService
  ) {}

  @Cron('*/30 * * * * *') // 每30秒
  async collectMetrics(): Promise<void> {
    // 收集各项指标
    const metrics = await this.gatherAllMetrics();
    
    // 发送到监控系统
    await this.metricsService.send(metrics);
    
    // 检查告警条件
    await this.checkAlerts(metrics);
  }

  private async checkAlerts(metrics: MonitoringMetrics): Promise<void> {
    // 数据库连接池告警
    Object.entries(metrics.performance.databaseConnectionPool).forEach(([server, usage]) => {
      if (usage > 0.9) {
        this.alertService.send({
          level: 'warning',
          message: `Server ${server} database connection pool usage: ${(usage * 100).toFixed(1)}%`,
        });
      }
    });

    // 服务器容量告警
    Object.entries(metrics.servers.serverCapacityUtilization).forEach(([server, usage]) => {
      if (usage > 0.95) {
        this.alertService.send({
          level: 'critical',
          message: `Server ${server} capacity nearly full: ${(usage * 100).toFixed(1)}%`,
        });
      }
    });

    // Token验证失败率告警
    const tokenFailureRate = metrics.authentication.tokenValidationFailure / 
      (metrics.authentication.tokenValidationSuccess + metrics.authentication.tokenValidationFailure);
    
    if (tokenFailureRate > 0.1) {
      this.alertService.send({
        level: 'warning',
        message: `High token validation failure rate: ${(tokenFailureRate * 100).toFixed(1)}%`,
      });
    }
  }
}
```

### **2. 健康检查**

```typescript
// 健康检查服务
@Injectable()
export class HealthCheckService {
  constructor(
    private readonly serverDatabase: ServerAwareDatabaseService,
    private readonly redisService: RedisService,
    private readonly microserviceClient: ServerAwareMicroserviceClient
  ) {}

  @Get('/health')
  async getHealthStatus(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkDatabaseHealth(),
      this.checkRedisHealth(),
      this.checkMicroservicesHealth(),
      this.checkServerStatus(),
    ]);

    const results = checks.map((check, index) => ({
      component: ['database', 'redis', 'microservices', 'servers'][index],
      status: check.status === 'fulfilled' ? 'healthy' : 'unhealthy',
      ...(check.status === 'fulfilled' && { details: check.value }),
      ...(check.status === 'rejected' && { error: check.reason.message }),
    }));

    const overallStatus = results.every(r => r.status === 'healthy') ? 'healthy' : 'degraded';

    return {
      status: overallStatus,
      timestamp: new Date(),
      components: results,
    };
  }

  private async checkDatabaseHealth(): Promise<DatabaseHealth> {
    const stats = this.serverDatabase.getConnectionStats();
    
    return {
      totalConnections: stats.totalConnections,
      connections: Object.entries(stats.connections).map(([name, conn]) => ({
        name,
        status: conn.readyState === 1 ? 'connected' : 'disconnected',
        host: conn.host,
        port: conn.port,
      })),
    };
  }

  private async checkRedisHealth(): Promise<RedisHealth> {
    const info = await this.redisService.info();
    
    return {
      connected: true,
      memory: info.used_memory_human,
      connectedClients: info.connected_clients,
      uptime: info.uptime_in_seconds,
    };
  }

  private async checkMicroservicesHealth(): Promise<MicroserviceHealth> {
    const services = ['auth', 'character', 'hero', 'economy', 'social', 'activity', 'match'];
    const healthChecks = await Promise.allSettled(
      services.map(service => 
        this.microserviceClient.call(service as MicroserviceName, 'health', {})
      )
    );

    return {
      services: services.map((service, index) => ({
        name: service,
        status: healthChecks[index].status === 'fulfilled' ? 'healthy' : 'unhealthy',
        ...(healthChecks[index].status === 'rejected' && { 
          error: healthChecks[index].reason.message 
        }),
      })),
    };
  }
}
```

### **3. 日志管理**

```typescript
// 结构化日志配置
// config/logging.config.ts
export const loggingConfig = {
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
      return JSON.stringify({
        timestamp,
        level,
        message,
        context,
        serverId: process.env.SERVER_ID,
        service: process.env.SERVICE_NAME,
        ...meta,
        ...(trace && { trace }),
      });
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    }),
  ],
};

// 关键操作日志记录
@Injectable()
export class AuditLogger {
  private readonly logger = new Logger(AuditLogger.name);

  logAuthentication(event: AuthEvent): void {
    this.logger.log({
      event: 'authentication',
      type: event.type, // 'login', 'logout', 'token_refresh', 'enter_server'
      accountId: event.accountId,
      serverId: event.serverId,
      characterId: event.characterId,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      success: event.success,
      ...(event.error && { error: event.error }),
    });
  }

  logCrossServerOperation(event: CrossServerEvent): void {
    this.logger.log({
      event: 'cross_server_operation',
      type: event.type, // 'ranking', 'battle', 'activity'
      sourceServerId: event.sourceServerId,
      targetServerId: event.targetServerId,
      characterId: event.characterId,
      duration: event.duration,
      success: event.success,
    });
  }

  logServerMerge(event: MergeEvent): void {
    this.logger.log({
      event: 'server_merge',
      type: event.type, // 'started', 'progress', 'completed', 'failed'
      taskId: event.taskId,
      sourceServerIds: event.sourceServerIds,
      targetServerId: event.targetServerId,
      progress: event.progress,
      operatorId: event.operatorId,
    });
  }
}
```

## 🔧 运维指南

### **1. 部署脚本**

```bash
#!/bin/bash
# deploy-multi-server.sh

set -e

echo "🚀 开始部署分区分服系统..."

# 环境变量检查
required_vars=("NODE_ENV" "SERVER_ID" "MONGODB_URI" "REDIS_HOST" "JWT_SECRET")
for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    echo "❌ 环境变量 $var 未设置"
    exit 1
  fi
done

# 构建应用
echo "📦 构建应用..."
npm run build

# 数据库迁移
echo "🗄️ 执行数据库迁移..."
npm run migration:run

# 启动服务
echo "🔧 启动服务..."
case "$SERVICE_TYPE" in
  "gateway")
    npm run start:gateway
    ;;
  "auth")
    npm run start:auth
    ;;
  "character")
    npm run start:character
    ;;
  *)
    echo "❌ 未知的服务类型: $SERVICE_TYPE"
    exit 1
    ;;
esac

echo "✅ 部署完成"
```

### **2. 故障排查指南**

```typescript
// 常见问题排查
interface TroubleshootingGuide {
  tokenValidationFailure: {
    symptoms: string[];
    causes: string[];
    solutions: string[];
  };
  databaseConnectionIssues: {
    symptoms: string[];
    causes: string[];
    solutions: string[];
  };
  crossServerCommunicationFailure: {
    symptoms: string[];
    causes: string[];
    solutions: string[];
  };
}

const troubleshootingGuide: TroubleshootingGuide = {
  tokenValidationFailure: {
    symptoms: [
      "用户无法登录",
      "Token验证失败错误",
      "WebSocket连接被拒绝"
    ],
    causes: [
      "JWT密钥不匹配",
      "Token过期时间配置错误",
      "Redis会话数据丢失"
    ],
    solutions: [
      "检查JWT_SECRET环境变量",
      "验证Token过期时间配置",
      "检查Redis连接状态",
      "清理过期会话数据"
    ]
  },
  databaseConnectionIssues: {
    symptoms: [
      "数据库操作超时",
      "连接池耗尽",
      "数据查询失败"
    ],
    causes: [
      "数据库URI配置错误",
      "连接池配置不当",
      "网络连接问题"
    ],
    solutions: [
      "验证MONGODB_URI配置",
      "调整连接池参数",
      "检查网络连通性",
      "监控数据库性能"
    ]
  },
  crossServerCommunicationFailure: {
    symptoms: [
      "跨服功能无法使用",
      "排行榜数据不更新",
      "跨服战斗匹配失败"
    ],
    causes: [
      "Redis PubSub配置问题",
      "微服务网络不通",
      "服务发现失败"
    ],
    solutions: [
      "检查Redis PubSub配置",
      "验证微服务间网络连通性",
      "检查服务注册状态",
      "重启相关微服务"
    ]
  }
};
```

---

> **实施成功关键**：
> - 📅 **严格按阶段执行**：每个阶段完成后进行充分测试
> - 🧪 **全面测试覆盖**：单元测试、集成测试、压力测试
> - 📊 **实时监控**：关键指标监控和告警机制
> - 🔧 **运维自动化**：部署脚本和故障排查指南
> - 📚 **文档完善**：详细的操作手册和故障排查指南
