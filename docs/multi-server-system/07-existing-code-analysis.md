# 现有代码分析与融合设计

## 📋 概述

基于对现有代码的深度分析，本文档详细分析已有的认证架构、数据库设计和服务器相关代码，并提出与现有系统完美融合的分区分服扩展方案。

## 🔍 现有代码分析

### **1. 网关认证架构分析**

#### **1.1 现有认证机制**

<augment_code_snippet path="apps/gateway/src/core/auth/auth.service.ts" mode="EXCERPT">
````typescript
/**
 * 验证 JWT Token
 */
async validateJwtToken(token: string): Promise<AuthContext> {
  const payload = this.jwtService.verify(token, { secret: this.jwtSecret });
  
  // 检查 token 是否在黑名单中
  const isBlacklisted = await this.isTokenBlacklisted(token);
  
  // 获取用户信息（从缓存）
  const user = await this.getUserById(payload.sub);
  
  // 轻量级验证逻辑...
}
````
</augment_code_snippet>

**现有特性**：
- ✅ **多种认证方式**：JWT、API Key、Session
- ✅ **完整的中间件系统**：AuthMiddleware、AuthGuard
- ✅ **WebSocket认证**：WsAuthGuard
- ✅ **Token黑名单机制**
- ✅ **会话管理**：SessionService

#### **1.2 现有WebSocket认证**

<augment_code_snippet path="apps/gateway/src/domain/websocket/websocket.gateway.ts" mode="EXCERPT">
````typescript
private async authenticateSocket(socket: AuthenticatedSocket): Promise<void> {
  const token = socket.handshake.auth?.token || socket.handshake.query?.token;
  
  if (!token) {
    socket.authenticated = false;
    return; // 允许匿名连接，但功能受限
  }

  try {
    const payload = this.jwtService.verify(token as string);
    
    if (payload) {
      socket.userId = payload.sub;
      socket.user = {
        id: payload.sub,
        username: payload.username,
        email: payload.email,
        roles: payload.roles || [],
      };
      socket.authenticated = true;
      socket.metadata = {};

      // 创建会话
      await this.sessionService.createSession(payload.sub, socket.id);
    }
  } catch (error) {
    throw new Error('Invalid authentication token');
  }
}
````
</augment_code_snippet>

### **2. Auth服务架构分析**

#### **2.1 现有JWT服务**

<augment_code_snippet path="apps/auth/src/domain/auth/jwt.service.ts" mode="EXCERPT">
````typescript
/**
 * 生成访问令牌
 */
generateAccessToken(payload: Omit<JwtPayload, 'iat' | 'exp' | 'jti'>): string {
  const jti = this.generateTokenId();
  const now = Math.floor(Date.now() / 1000);
  
  const tokenPayload: JwtPayload = {
    ...payload,
    iat: now,
    jti,
  };

  return jwt.sign(tokenPayload, this.jwtSecret, {
    algorithm: this.jwtAlgorithm as jwt.Algorithm,
    expiresIn: this.accessTokenTTL,
    issuer: this.jwtIssuer,
    audience: this.jwtAudience,
  });
}
````
</augment_code_snippet>

**现有特性**：
- ✅ **完整的JWT管理**：生成、验证、刷新
- ✅ **Token对机制**：AccessToken + RefreshToken
- ✅ **安全配置**：算法、过期时间、签发者
- ✅ **用户管理**：完整的User实体和服务

#### **2.2 现有用户实体**

<augment_code_snippet path="apps/auth/src/domain/users/entities/user.entity.ts" mode="EXCERPT">
````typescript
@Schema({
  timestamps: true,
  collection: 'users',
})
export class User {
  @ApiProperty({ description: '用户ID' })
  id?: string;

  @ApiProperty({ description: '用户名', uniqueItems: true })
  @Prop({ 
    required: true, 
    unique: true, 
    trim: true, 
    lowercase: true,
    minlength: 3,
    maxlength: 30,
    match: /^[a-zA-Z0-9_-]+$/
  })
  username: string;

  @ApiProperty({ description: '邮箱地址', uniqueItems: true })
  @Prop({ 
    required: true, 
    unique: true, 
    trim: true, 
    lowercase: true,
    match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  })
  email: string;
  // ... 其他字段
}
````
</augment_code_snippet>

### **3. 现有服务器相关代码**

#### **3.1 Character服务中的serverId字段**

<augment_code_snippet path="apps/character/src/common/repositories/character.repository.ts" mode="EXCERPT">
````typescript
/**
 * 根据配置表ID查找球员
 */
async findByResId(resId: number, serverId?: string): Promise<HeroDocument[]> {
  try {
    const filter: FilterQuery<HeroDocument> = { resId };
    if (serverId) {
      filter.serverId = serverId;
    }
    return await this.heroModel.find(filter).exec();
  } catch (error) {
    this.logger.error(`根据配置表ID查找球员失败: ${resId}`, error);
    throw error;
  }
}

/**
 * 根据角色名查找角色
 * 增强版：支持可选serverId和软删除过滤
 */
async findByName(name: string, serverId?: string): Promise<CharacterDocument | null> {
  try {
    const filter: any = {
      name: name.trim(),
      deletedAt: { $exists: false }
    };

    if (serverId) {
      filter.serverId = serverId;
    }

    return await this.characterModel.findOne(filter).exec();
  } catch (error) {
    this.logger.error(`根据角色名查找角色失败: ${name}`, error);
    throw error;
  }
}
````
</augment_code_snippet>

**发现**：
- ✅ **已有serverId支持**：Character和Hero服务已部分支持serverId
- ✅ **数据库过滤**：已有按serverId过滤的逻辑
- ✅ **搜索功能**：支持跨服和单服搜索

#### **3.2 Activity服务中的服务器支持**

<augment_code_snippet path="apps/activity/src/common/repositories/guide.repository.ts" mode="EXCERPT">
````typescript
/**
 * 根据区服查找引导记录
 */
async findByServerId(serverId: string): Promise<GuideDocument[]> {
  try {
    return await this.guideModel.find({ serverId }).exec();
  } catch (error) {
    this.logger.error(`根据区服查找引导记录失败: ${serverId}`, error);
    throw error;
  }
}
````
</augment_code_snippet>

## 🔧 融合设计方案

### **1. 扩展现有网关认证**

#### **1.1 扩展AuthService而非重写**

```typescript
// apps/gateway/src/core/auth/auth.service.ts (扩展现有)
@Injectable()
export class AuthService {
  // ... 保留所有现有方法

  /**
   * 扩展：验证角色级JWT Token
   * 在现有validateJwtToken基础上增加角色验证
   */
  async validateCharacterToken(token: string): Promise<CharacterAuthContext> {
    // 先使用现有的JWT验证逻辑
    const baseAuthContext = await this.validateJwtToken(token);
    
    // 检查是否为角色级Token
    const payload = this.jwtService.verify(token);
    if (payload.type !== 'character') {
      throw new UnauthorizedException('需要角色级认证');
    }

    // 验证角色会话
    const session = await this.validateCharacterSession(payload.sessionId);
    
    return {
      ...baseAuthContext,
      characterId: payload.characterId,
      serverId: payload.serverId,
      sessionId: payload.sessionId,
      type: 'character',
    };
  }

  /**
   * 扩展：生成角色级Token
   * 基于现有Token生成逻辑
   */
  async generateCharacterToken(
    accountContext: AuthContext,
    characterId: string,
    serverId: string,
    sessionId: string
  ): Promise<string> {
    const payload = {
      sub: accountContext.user.id,
      username: accountContext.user.username,
      email: accountContext.user.email,
      roles: accountContext.user.roles,
      characterId,
      serverId,
      sessionId,
      type: 'character',
    };

    // 使用现有的JWT配置，但使用不同的过期时间
    return this.jwtService.sign(payload, {
      expiresIn: '4h', // 角色Token 4小时过期
      secret: this.configService.get('gateway.security.characterJwtSecret') || 
              this.configService.get('gateway.security.jwtSecret'),
    });
  }

  /**
   * 新增：验证角色会话
   */
  private async validateCharacterSession(sessionId: string): Promise<CharacterSession> {
    const session = await this.redisService.get(`character_session:${sessionId}`, 'global');
    
    if (!session) {
      throw new UnauthorizedException('角色会话不存在');
    }

    const sessionData = JSON.parse(session);
    if (sessionData.expiresAt < Date.now()) {
      await this.redisService.del(`character_session:${sessionId}`, 'global');
      throw new UnauthorizedException('角色会话已过期');
    }

    return sessionData;
  }
}
```

#### **1.2 扩展现有WebSocket认证**

```typescript
// apps/gateway/src/domain/websocket/websocket.gateway.ts (扩展现有)
export class WebSocketGateway {
  // ... 保留所有现有代码

  /**
   * 扩展现有认证方法，支持角色级Token
   */
  private async authenticateSocket(socket: AuthenticatedSocket): Promise<void> {
    const token = socket.handshake.auth?.token || socket.handshake.query?.token;

    if (!token) {
      socket.authenticated = false;
      return;
    }

    try {
      const payload = this.jwtService.verify(token as string);

      if (payload) {
        socket.userId = payload.sub;
        socket.user = {
          id: payload.sub,
          username: payload.username,
          email: payload.email,
          roles: payload.roles || [],
        };
        socket.authenticated = true;

        // 扩展：支持角色级认证
        if (payload.type === 'character') {
          socket.characterId = payload.characterId;
          socket.serverId = payload.serverId;
          socket.sessionId = payload.sessionId;
          socket.tokenType = 'character';
          
          // 验证角色会话
          await this.authService.validateCharacterSession(payload.sessionId);
          
          // 更新会话的Socket ID
          await this.updateCharacterSessionSocket(payload.sessionId, socket.id);
        } else {
          socket.tokenType = 'account';
        }

        socket.metadata = {
          tokenType: socket.tokenType,
          ...(socket.characterId && { characterId: socket.characterId }),
          ...(socket.serverId && { serverId: socket.serverId }),
        };

        // 使用现有的会话创建逻辑
        await this.sessionService.createSession(payload.sub, socket.id);
      }
    } catch (error) {
      throw new Error('Invalid authentication token');
    }
  }

  /**
   * 扩展现有消息路由，支持区服路由
   */
  private async routeMessage(message: WSMessageDto, userId: string): Promise<ServiceResponse> {
    const { service, action, payload } = message;

    // 获取Socket信息
    const socket = this.getSocketByUserId(userId);
    
    // 构建微服务调用参数，自动注入区服上下文
    const params = {
      ...payload,
      userId,
      ...(socket?.serverId && { serverId: socket.serverId }),
      ...(socket?.characterId && { characterId: socket.characterId }),
    };

    this.logger.debug(`🚀 Calling microservice: ${service}.${action}`, {
      serverId: socket?.serverId,
      characterId: socket?.characterId,
    });

    // 使用现有的微服务调用逻辑
    const result = await this.microserviceClient.call(service as MicroserviceName, action, params);
    
    return {
      success: true,
      data: result
    };
  }
}
```

### **2. 扩展Auth服务**

#### **2.1 新增区服管理模块**

```typescript
// apps/auth/src/domain/server-management/ (新增模块，融合现有架构)
├── server-management.module.ts
├── services/
│   ├── server.service.ts                    # 区服管理服务
│   └── character-session.service.ts         # 角色会话管理
├── controllers/
│   └── server-management.controller.ts      # 区服管理控制器
├── entities/
│   ├── server.entity.ts                     # 区服信息实体
│   └── character-session.entity.ts          # 角色会话实体
└── dto/
    ├── server-list.dto.ts
    └── enter-server.dto.ts

// 融合现有架构的服务实现
@Injectable()
export class ServerService {
  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly usersService: UsersService, // 使用现有的UsersService
    private readonly jwtService: JwtService,     // 使用现有的JwtService
    private readonly logger: Logger
  ) {}

  /**
   * 获取区服列表
   * 基于现有的配置和Redis缓存机制
   */
  async getServerList(userId: string): Promise<ServerListResponse> {
    // 从配置或数据库获取区服列表
    const servers = await this.getAvailableServers();
    
    // 获取玩家历史记录（使用现有Redis结构）
    const historyKey = `user:${userId}:server_history`;
    const history = await this.redisService.get(historyKey, 'global');
    const playerHistory = history ? JSON.parse(history) : [];

    return {
      servers: servers.map(server => ({
        serverId: server.id,
        serverName: server.name,
        status: server.status,
        playerCount: server.currentPlayers,
        maxPlayerCount: server.maxPlayers,
        hasCharacter: playerHistory.some(h => h.serverId === server.id),
        lastPlayTime: playerHistory.find(h => h.serverId === server.id)?.lastPlayTime,
      })),
      lastServerId: playerHistory[0]?.serverId,
      totalServers: servers.length,
    };
  }

  /**
   * 进入区服
   * 融合现有的用户验证和JWT生成逻辑
   */
  async enterServer(userId: string, serverId: string, characterId?: string): Promise<EnterServerResponse> {
    // 使用现有的用户服务验证用户
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 验证区服状态
    const server = await this.validateServer(serverId);
    
    // 获取或验证角色（调用character微服务）
    const character = await this.getCharacterInServer(userId, serverId, characterId);
    
    // 创建角色会话（使用现有Redis结构）
    const session = await this.createCharacterSession({
      userId,
      characterId: character.characterId,
      serverId,
      serverName: server.name,
    });

    // 生成角色级Token（扩展现有JWT逻辑）
    const characterToken = await this.generateCharacterToken(user, character, session);

    // 更新玩家历史记录（使用现有Redis结构）
    await this.updatePlayerHistory(userId, serverId);

    return {
      characterToken,
      character,
      server,
      session,
    };
  }

  /**
   * 基于现有配置获取可用区服
   */
  private async getAvailableServers(): Promise<ServerInfo[]> {
    // 优先从Redis缓存获取
    const cacheKey = 'servers:list';
    const cached = await this.redisService.get(cacheKey, 'global');
    
    if (cached) {
      return JSON.parse(cached);
    }

    // 从配置文件或环境变量获取
    const serverConfig = this.configService.get('servers') || this.getDefaultServers();
    
    // 缓存结果
    await this.redisService.set(cacheKey, JSON.stringify(serverConfig), 300, 'global');
    
    return serverConfig;
  }

  /**
   * 使用现有JWT服务生成角色Token
   */
  private async generateCharacterToken(user: User, character: any, session: any): Promise<string> {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      characterId: character.characterId,
      serverId: session.serverId,
      sessionId: session.id,
      type: 'character',
    };

    // 使用现有的JWT配置，但调整过期时间
    return this.jwtService.sign(payload, {
      expiresIn: '4h',
      secret: this.configService.get('auth.jwt.characterSecret') || 
              this.configService.get('auth.jwt.secret'),
    });
  }
}
```

### **3. 优化现有Character服务**

#### **3.1 增强现有Repository**

```typescript
// apps/character/src/common/repositories/character.repository.ts (优化现有)
@Injectable()
export class CharacterRepository {
  // ... 保留所有现有方法

  /**
   * 优化现有的findByUserId方法，强化serverId支持
   */
  async findByUserId(userId: string, serverId?: string): Promise<CharacterDocument[]> {
    try {
      const filter: FilterQuery<CharacterDocument> = { userId };
      
      // 强化serverId过滤逻辑
      if (serverId) {
        filter.serverId = serverId;
      }
      
      return await this.characterModel.find(filter).exec();
    } catch (error) {
      this.logger.error(`根据用户ID查找角色失败: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 优化现有的findByName方法，增强区服支持
   */
  async findByName(name: string, serverId?: string): Promise<CharacterDocument | null> {
    try {
      const filter: any = {
        name: name.trim(),
        deletedAt: { $exists: false }
      };

      // 如果指定了serverId，则只在该区服内搜索
      if (serverId) {
        filter.serverId = serverId;
      }

      return await this.characterModel.findOne(filter).exec();
    } catch (error) {
      this.logger.error(`根据角色名查找角色失败: ${name}`, error);
      throw error;
    }
  }

  /**
   * 新增：创建角色时自动设置serverId
   * 基于现有的create逻辑扩展
   */
  async createForServer(characterData: any, serverId: string): Promise<CharacterDocument> {
    try {
      const enhancedData = {
        ...characterData,
        serverId, // 自动设置区服ID
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const character = new this.characterModel(enhancedData);
      return await character.save();
    } catch (error) {
      this.logger.error(`创建角色失败: ${JSON.stringify(characterData)}`, error);
      throw error;
    }
  }
}
```

#### **3.2 扩展现有Service**

```typescript
// apps/character/src/modules/character/character.service.ts (扩展现有)
@Injectable()
export class CharacterService {
  // ... 保留所有现有方法

  /**
   * 扩展现有的searchByName方法，增强区服支持
   */
  async searchByName(name: string, serverId?: string): Promise<any> {
    this.logger.log(`根据名称搜索角色: ${name}, 服务器: ${serverId || 'all'}`);

    try {
      if (!name || name.trim() === '') {
        return null;
      }

      // 使用优化后的Repository方法
      const character = await this.characterRepository.findByName(name.trim(), serverId);

      if (!character) {
        this.logger.log(`未找到角色: ${name}`);
        return null;
      }

      // 返回角色信息（保持现有格式）
      return this.buildCharacterInfo(character);
    } catch (error) {
      this.logger.error(`搜索角色失败: ${name}`, error);
      throw error;
    }
  }

  /**
   * 新增：获取用户在指定区服的角色列表
   */
  async getCharactersByUserAndServer(userId: string, serverId: string): Promise<CharacterDocument[]> {
    return this.characterRepository.findByUserId(userId, serverId);
  }

  /**
   * 新增：为指定区服创建角色
   */
  async createCharacterForServer(createDto: any, userId: string, serverId: string): Promise<CharacterDocument> {
    // 验证角色名在该区服内的唯一性
    const existingCharacter = await this.characterRepository.findByName(createDto.name, serverId);
    if (existingCharacter) {
      throw new ConflictException('该区服内角色名已存在');
    }

    // 使用扩展的Repository方法
    return this.characterRepository.createForServer({
      ...createDto,
      userId,
    }, serverId);
  }
}
```

### **4. 配置融合**

#### **4.1 扩展现有配置**

```typescript
// apps/auth/src/config/auth.config.ts (扩展现有)
export const authConfig = registerAs('auth', () => {
  const config = {
    // 保留所有现有JWT配置
    jwt: {
      secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
      algorithm: process.env.JWT_ALGORITHM || 'HS256',
      issuer: process.env.JWT_ISSUER || 'football-manager-auth',
      audience: process.env.JWT_AUDIENCE || 'football-manager-app',
      accessTokenTTL: process.env.JWT_ACCESS_TOKEN_TTL || '15m',
      refreshTokenTTL: process.env.JWT_REFRESH_TOKEN_TTL || '7d',
      
      // 新增：角色级Token配置
      characterSecret: process.env.JWT_CHARACTER_SECRET || process.env.JWT_SECRET,
      characterTokenTTL: process.env.JWT_CHARACTER_TOKEN_TTL || '4h',
    },

    // 新增：多服务器配置
    multiServer: {
      enabled: process.env.MULTI_SERVER_ENABLED === 'true',
      defaultServerId: process.env.DEFAULT_SERVER_ID || 'server_001',
      maxServers: parseInt(process.env.MAX_SERVERS || '10', 10),
    },

    // 保留所有现有配置...
  };

  return config;
});
```

#### **4.2 环境变量扩展**

```bash
# .env (扩展现有配置)

# 现有配置保持不变
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_ACCESS_TOKEN_TTL=15m
JWT_REFRESH_TOKEN_TTL=7d

# 新增：多服务器配置
MULTI_SERVER_ENABLED=true
DEFAULT_SERVER_ID=server_001
MAX_SERVERS=10

# 新增：角色级Token配置
JWT_CHARACTER_SECRET=your-character-jwt-secret
JWT_CHARACTER_TOKEN_TTL=4h

# 现有数据库配置保持不变，新增区服数据库配置
MONGODB_URI=mongodb://localhost:27017/football-manager-auth
CHARACTER_MONGODB_URI=************************************************************************
CHARACTER_MONGODB_URI_SERVER_001=************************************************************************_server_001
CHARACTER_MONGODB_URI_SERVER_002=***********************************************************************************
```

---

> **融合设计原则**：
> - 🔧 **扩展而非重写**：在现有代码基础上扩展功能
> - 🏗️ **架构一致性**：保持现有的模块结构和命名规范
> - 📊 **数据兼容性**：充分利用现有的serverId字段和数据结构
> - 🔄 **渐进式迁移**：支持逐步启用多服务器功能
> - 🛡️ **向后兼容**：现有功能完全不受影响
