# Auth服务开发指南和最佳实践

## 📋 概述

本文档为Auth服务的开发人员提供详细的开发指南、代码规范、测试策略和最佳实践。

## 🏗️ 开发环境搭建

### 环境要求
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **MongoDB**: >= 5.0 (可使用Docker)
- **Redis**: >= 6.0 (可使用Docker)
- **IDE**: 推荐使用VSCode

### 快速开始
```bash
# 1. 克隆项目
git clone https://github.com/your-org/football-manager.git
cd football-manager

# 2. 安装依赖
npm install

# 3. 启动开发环境依赖
docker-compose -f docker-compose.dev.yml up -d

# 4. 复制环境变量
cp .env.example .env

# 5. 启动Auth服务
npm run start:auth:dev
```

### VSCode配置
创建 `.vscode/settings.json`:
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true
  }
}
```

创建 `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Auth Service",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/dist/apps/auth/main.js",
      "env": {
        "NODE_ENV": "development"
      },
      "sourceMaps": true,
      "outFiles": ["${workspaceFolder}/dist/**/*.js"]
    }
  ]
}
```

## 📁 项目结构详解

### 目录结构
```
apps/auth/src/
├── common/                  # 公共模块
│   ├── decorators/         # 装饰器
│   ├── guards/             # 守卫
│   ├── interceptors/       # 拦截器
│   └── pipes/              # 管道
├── config/                 # 配置文件
│   ├── auth.config.ts      # Auth配置
│   └── database.config.ts  # 数据库配置
├── core/                   # 核心模块
│   ├── shared/             # 共享核心模块
│   └── session/            # 会话模块
├── domain/                 # 业务领域
│   ├── auth/               # 基础认证
│   ├── character-auth/     # 角色认证
│   └── user-history/       # 用户历史
├── infrastructure/         # 基础设施
│   ├── database/           # 数据库
│   ├── guards/             # 基础设施守卫
│   └── middleware/         # 中间件
└── main.ts                 # 应用入口
```

### 模块设计原则
1. **单一职责**: 每个模块只负责一个业务领域
2. **依赖注入**: 使用NestJS的依赖注入系统
3. **接口隔离**: 定义清晰的接口边界
4. **开闭原则**: 对扩展开放，对修改关闭

## 🔧 核心组件开发

### 服务层开发

#### 服务基类
```typescript
// base.service.ts
export abstract class BaseService {
  protected readonly logger = new Logger(this.constructor.name);

  protected handleError(error: any, context: string): never {
    this.logger.error(`${context} failed`, error);
    
    if (error instanceof HttpException) {
      throw error;
    }
    
    throw new InternalServerErrorException(`${context} failed`);
  }

  protected validateInput<T>(data: T, schema: any): T {
    const { error, value } = schema.validate(data);
    if (error) {
      throw new BadRequestException(error.details[0].message);
    }
    return value;
  }
}
```

#### 服务实现示例
```typescript
// example.service.ts
@Injectable()
export class ExampleService extends BaseService {
  constructor(
    @InjectModel(Example.name)
    private readonly exampleModel: Model<ExampleDocument>,
    private readonly redisService: RedisService,
  ) {
    super();
  }

  async create(createDto: CreateExampleDto): Promise<Example> {
    try {
      // 1. 验证输入
      this.validateInput(createDto, createExampleSchema);

      // 2. 业务逻辑
      const example = new this.exampleModel(createDto);
      const savedExample = await example.save();

      // 3. 缓存更新
      await this.updateCache(savedExample);

      // 4. 日志记录
      this.logger.log(`Example created: ${savedExample.id}`);

      return savedExample;
    } catch (error) {
      this.handleError(error, 'Create example');
    }
  }

  private async updateCache(example: Example): Promise<void> {
    const cacheKey = `example:${example.id}`;
    await this.redisService.set(cacheKey, JSON.stringify(example), 3600, 'global');
  }
}
```

### 控制器层开发

#### 控制器基类
```typescript
// base.controller.ts
export abstract class BaseController {
  protected readonly logger = new Logger(this.constructor.name);

  protected success<T>(data: T, message: string = 'Success'): ApiResponse<T> {
    return {
      success: true,
      message,
      data,
    };
  }

  protected error(message: string, code?: string): ApiErrorResponse {
    return {
      success: false,
      message,
      code,
    };
  }
}
```

#### 控制器实现示例
```typescript
// example.controller.ts
@ApiTags('示例')
@Controller('example')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ExampleController extends BaseController {
  constructor(private readonly exampleService: ExampleService) {
    super();
  }

  @Post()
  @ApiOperation({ summary: '创建示例' })
  @ApiResponse({ status: 201, description: '创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async create(
    @Body() createDto: CreateExampleDto,
    @CurrentUser() user: User,
  ): Promise<ApiResponse<Example>> {
    this.logger.log(`Create example request: ${user.id}`);

    try {
      const example = await this.exampleService.create(createDto);
      return this.success(example, '创建示例成功');
    } catch (error) {
      this.logger.error(`Create example failed: ${user.id}`, error);
      throw error;
    }
  }
}
```

### 实体层开发

#### 实体定义规范
```typescript
// example.entity.ts
@Schema({
  timestamps: true,
  collection: 'examples',
})
export class Example {
  @ApiProperty({ description: '示例ID' })
  id?: string;

  @ApiProperty({ description: '名称' })
  @Prop({ 
    required: true, 
    index: true,
    comment: '示例名称'
  })
  name: string;

  @ApiProperty({ description: '描述' })
  @Prop({ 
    required: false,
    comment: '示例描述'
  })
  description?: string;

  @ApiProperty({ description: '状态' })
  @Prop({ 
    required: true,
    default: 'active',
    enum: ['active', 'inactive'],
    comment: '示例状态'
  })
  status: 'active' | 'inactive';

  @ApiProperty({ description: '创建时间' })
  createdAt?: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt?: Date;
}

export const ExampleSchema = SchemaFactory.createForClass(Example);

// 索引定义
ExampleSchema.index({ name: 1 }, { unique: true });
ExampleSchema.index({ status: 1, createdAt: -1 });

// 虚拟字段
ExampleSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// 序列化配置
ExampleSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

// 中间件
ExampleSchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.name = this.name.trim().toLowerCase();
  }
  next();
});

// 静态方法
ExampleSchema.statics.findByStatus = function(status: string) {
  return this.find({ status }).exec();
};

// 实例方法
ExampleSchema.methods.activate = function() {
  this.status = 'active';
  return this.save();
};

export type ExampleDocument = Example & Document;
```

### DTO开发规范

#### DTO定义
```typescript
// create-example.dto.ts
export class CreateExampleDto {
  @ApiProperty({
    description: '名称',
    example: '示例名称',
    minLength: 1,
    maxLength: 100,
  })
  @IsString({ message: '名称必须为字符串' })
  @IsNotEmpty({ message: '名称不能为空' })
  @Length(1, 100, { message: '名称长度必须在1-100个字符之间' })
  name: string;

  @ApiProperty({
    description: '描述',
    example: '这是一个示例',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: '描述必须为字符串' })
  @MaxLength(500, { message: '描述长度不能超过500个字符' })
  description?: string;

  @ApiProperty({
    description: '状态',
    example: 'active',
    enum: ['active', 'inactive'],
  })
  @IsEnum(['active', 'inactive'], { message: '状态必须为active或inactive' })
  status: 'active' | 'inactive';
}
```

#### 响应DTO
```typescript
// example-response.dto.ts
export class ExampleResponseDto {
  @ApiProperty({ description: '请求是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '操作成功' })
  message: string;

  @ApiProperty({ description: '示例数据', type: Example })
  data: Example;
}
```

## 🧪 测试策略

### 单元测试

#### 测试配置
```typescript
// test/setup.ts
import { Test } from '@nestjs/testing';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongooseModule } from '@nestjs/mongoose';

let mongod: MongoMemoryServer;

export const setupTestDatabase = async () => {
  mongod = await MongoMemoryServer.create();
  const uri = mongod.getUri();

  const module = await Test.createTestingModule({
    imports: [MongooseModule.forRoot(uri)],
  }).compile();

  return module;
};

export const teardownTestDatabase = async () => {
  if (mongod) {
    await mongod.stop();
  }
};
```

#### 服务测试示例
```typescript
// example.service.spec.ts
describe('ExampleService', () => {
  let service: ExampleService;
  let model: Model<ExampleDocument>;

  beforeEach(async () => {
    const module = await setupTestDatabase();
    
    service = module.get<ExampleService>(ExampleService);
    model = module.get<Model<ExampleDocument>>(getModelToken(Example.name));
  });

  afterEach(async () => {
    await teardownTestDatabase();
  });

  describe('create', () => {
    it('should create an example successfully', async () => {
      // Arrange
      const createDto: CreateExampleDto = {
        name: 'Test Example',
        description: 'Test Description',
        status: 'active',
      };

      // Act
      const result = await service.create(createDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.name).toBe(createDto.name);
      expect(result.status).toBe(createDto.status);
    });

    it('should throw error for invalid input', async () => {
      // Arrange
      const invalidDto = { name: '' };

      // Act & Assert
      await expect(service.create(invalidDto as any)).rejects.toThrow();
    });
  });
});
```

### 集成测试

#### E2E测试示例
```typescript
// example.e2e-spec.ts
describe('ExampleController (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // 获取认证Token
    authToken = await getAuthToken(app);
  });

  afterEach(async () => {
    await app.close();
  });

  it('/example (POST)', () => {
    return request(app.getHttpServer())
      .post('/example')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: 'Test Example',
        status: 'active',
      })
      .expect(201)
      .expect((res) => {
        expect(res.body.success).toBe(true);
        expect(res.body.data.name).toBe('Test Example');
      });
  });
});
```

## 🔒 安全开发规范

### 输入验证
```typescript
// 使用class-validator进行输入验证
@IsEmail({}, { message: '邮箱格式不正确' })
@IsNotEmpty({ message: '邮箱不能为空' })
email: string;

// 自定义验证器
@ValidatorConstraint({ name: 'isStrongPassword', async: false })
export class IsStrongPasswordConstraint implements ValidatorConstraintInterface {
  validate(password: string) {
    return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(password);
  }

  defaultMessage() {
    return '密码必须包含大小写字母、数字和特殊字符，长度至少8位';
  }
}

export function IsStrongPassword(validationOptions?: ValidationOptions) {
  return ValidatorConstraint(IsStrongPasswordConstraint, validationOptions);
}
```

### 权限控制
```typescript
// 权限装饰器
export const RequirePermissions = (...permissions: string[]) =>
  SetMetadata('permissions', permissions);

// 权限守卫
@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.get<string[]>('permissions', context.getHandler());
    if (!requiredPermissions) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    return requiredPermissions.every(permission => 
      user.permissions?.includes(permission)
    );
  }
}
```

### 数据脱敏
```typescript
// 敏感数据脱敏
export class UserResponseDto {
  @Exclude()
  password: string;

  @Transform(({ value }) => value ? '***' + value.slice(-4) : null)
  phone?: string;

  @Transform(({ value }) => value ? value.replace(/(.{3}).*(.{2}@.*)/, '$1***$2') : null)
  email: string;
}
```

## 📊 性能优化

### 数据库优化
```typescript
// 查询优化
async findWithPagination(query: any, page: number, limit: number) {
  const skip = (page - 1) * limit;
  
  return Promise.all([
    this.model.find(query).skip(skip).limit(limit).lean().exec(),
    this.model.countDocuments(query).exec(),
  ]);
}

// 聚合查询
async getStatistics() {
  return this.model.aggregate([
    { $match: { status: 'active' } },
    { $group: { _id: '$category', count: { $sum: 1 } } },
    { $sort: { count: -1 } },
  ]).exec();
}
```

### 缓存策略
```typescript
// 缓存装饰器
export function Cacheable(ttl: number = 3600) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`;
      
      // 尝试从缓存获取
      const cached = await this.redisService.get(cacheKey, 'global');
      if (cached) {
        return JSON.parse(cached);
      }
      
      // 执行原方法
      const result = await method.apply(this, args);
      
      // 缓存结果
      await this.redisService.set(cacheKey, JSON.stringify(result), ttl, 'global');
      
      return result;
    };
  };
}

// 使用示例
@Cacheable(1800) // 30分钟缓存
async getExpensiveData(id: string) {
  return this.performExpensiveOperation(id);
}
```

## 🔍 调试和日志

### 日志规范
```typescript
// 结构化日志
this.logger.log('User login attempt', {
  userId: user.id,
  ip: request.ip,
  userAgent: request.headers['user-agent'],
  timestamp: new Date().toISOString(),
});

// 错误日志
this.logger.error('Database connection failed', {
  error: error.message,
  stack: error.stack,
  context: 'DatabaseService.connect',
});

// 性能日志
const startTime = Date.now();
const result = await this.performOperation();
const duration = Date.now() - startTime;

this.logger.log('Operation completed', {
  operation: 'performOperation',
  duration,
  resultCount: result.length,
});
```

### 调试技巧
```typescript
// 条件断点
if (process.env.NODE_ENV === 'development' && userId === 'debug-user') {
  debugger; // 只在开发环境和特定用户时触发
}

// 性能监控
const performanceObserver = new PerformanceObserver((list) => {
  list.getEntries().forEach((entry) => {
    console.log(`${entry.name}: ${entry.duration}ms`);
  });
});
performanceObserver.observe({ entryTypes: ['measure'] });

performance.mark('operation-start');
await this.performOperation();
performance.mark('operation-end');
performance.measure('operation-duration', 'operation-start', 'operation-end');
```

## 📝 代码规范

### 命名规范
- **类名**: PascalCase (UserService, AuthController)
- **方法名**: camelCase (getUserById, validateToken)
- **常量**: UPPER_SNAKE_CASE (MAX_RETRY_COUNT, DEFAULT_TIMEOUT)
- **接口**: PascalCase with 'I' prefix (IUserRepository, IAuthService)

### 注释规范
```typescript
/**
 * 用户认证服务
 * 负责用户登录、登出、Token管理等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Injectable()
export class AuthService {
  /**
   * 用户登录
   * 
   * @param loginDto 登录信息
   * @returns 认证结果，包含Token和用户信息
   * @throws {UnauthorizedException} 当用户名或密码错误时
   * @throws {BadRequestException} 当输入参数无效时
   * 
   * @example
   * ```typescript
   * const result = await authService.login({
   *   email: '<EMAIL>',
   *   password: 'password123'
   * });
   * ```
   */
  async login(loginDto: LoginDto): Promise<AuthResult> {
    // 实现逻辑
  }
}
```

## 🚀 部署和发布

### 构建优化
```typescript
// webpack.config.js
module.exports = {
  // 生产环境优化
  optimization: {
    minimize: true,
    splitChunks: {
      chunks: 'all',
    },
  },
  
  // 排除不必要的依赖
  externals: {
    'aws-sdk': 'aws-sdk',
  },
};
```

### 健康检查
```typescript
// health.controller.ts
@Controller('health')
export class HealthController {
  constructor(
    private readonly mongoHealthIndicator: MongooseHealthIndicator,
    private readonly redisHealthIndicator: RedisHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.mongoHealthIndicator.pingCheck('mongodb'),
      () => this.redisHealthIndicator.pingCheck('redis'),
    ]);
  }
}
```

## 📚 学习资源

### 推荐阅读
- [NestJS官方文档](https://docs.nestjs.com/)
- [MongoDB最佳实践](https://docs.mongodb.com/manual/administration/production-notes/)
- [Redis使用指南](https://redis.io/documentation)
- [JWT最佳实践](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)

### 工具推荐
- **API测试**: Postman, Insomnia
- **数据库管理**: MongoDB Compass, Redis Desktop Manager
- **性能监控**: New Relic, DataDog
- **日志分析**: ELK Stack, Grafana
