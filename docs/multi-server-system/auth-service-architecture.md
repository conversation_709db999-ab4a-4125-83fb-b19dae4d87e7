# Auth服务架构设计文档

## 📋 概述

Auth服务是分区分服系统的核心认证服务，负责用户身份认证、角色认证、权限管理和用户历史记录管理。本文档详细描述了Auth服务的架构设计、实现细节和使用规范。

## 🏗️ 整体架构

### 模块结构
```
apps/auth/src/domain/
├── auth/                    # 基础认证模块
│   ├── jwt.service.ts       # JWT Token管理服务
│   ├── auth.service.ts      # 认证核心服务
│   └── auth.controller.ts   # 认证HTTP接口
├── character-auth/          # 角色认证模块
│   ├── services/
│   │   ├── character-auth.service.ts      # 角色认证服务
│   │   └── character-session.service.ts   # 角色会话服务
│   ├── controllers/
│   │   └── character-auth.controller.ts   # 角色认证接口
│   └── dto/                 # 数据传输对象
└── user-history/           # 用户历史模块
    ├── services/
    │   ├── user-history.service.ts        # 用户历史服务
    │   └── user-history-sync.service.ts   # 历史同步服务
    ├── controllers/
    │   └── user-history.controller.ts     # 用户历史接口
    └── entities/
        └── user-history.entity.ts         # 用户历史实体
```

## 🔐 双层Token机制

### Token类型定义

#### 账号级Token (Account Token)
- **用途**: 账号管理、区服选择
- **有效期**: 15分钟
- **作用域**: `account`
- **载荷结构**:
```typescript
interface AccountTokenPayload {
  sub: string;              // 用户ID
  username: string;         // 用户名
  email: string;           // 邮箱
  roles: string[];         // 账号级权限
  permissions: string[];   // 权限列表
  sessionId: string;       // 会话ID
  deviceId?: string;       // 设备ID
  scope: 'account';        // Token作用域
  iat: number;             // 签发时间
  exp: number;             // 过期时间
  jti: string;             // Token唯一标识
}
```

#### 角色级Token (Character Token)
- **用途**: 游戏业务功能访问
- **有效期**: 4小时
- **作用域**: `character`
- **载荷结构**:
```typescript
interface CharacterTokenPayload {
  sub: string;              // 账号ID
  username: string;         // 用户名
  email: string;           // 邮箱
  roles: string[];         // 账号级权限
  permissions: string[];   // 权限列表
  characterId: string;     // 角色ID
  serverId: string;        // 区服ID
  sessionId: string;       // 会话ID
  deviceId?: string;       // 设备ID
  scope: 'character';      // Token作用域
  iat: number;             // 签发时间
  exp: number;             // 过期时间
  jti: string;             // Token唯一标识
}
```

### 权限边界控制

#### 权限验证守卫
```typescript
// 账号Token守卫
@UseGuards(JwtAuthGuard)
@AccountToken()  // 只允许账号Token访问

// 角色Token守卫
@UseGuards(CharacterAuthGuard)
@CharacterToken()  // 只允许角色Token访问
```

#### 权限边界验证
- **账号Token**: 不能包含角色和区服信息
- **角色Token**: 必须包含角色和区服信息
- **作用域验证**: 严格验证Token的scope字段

## 🎮 角色认证流程

### 角色登录流程
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Auth as Auth服务
    participant Character as Character服务
    participant History as 用户历史服务

    Client->>Auth: 角色登录请求 (账号Token + 区服ID + 角色ID)
    Auth->>Auth: 验证账号Token
    Auth->>Character: 获取角色信息
    Character-->>Auth: 返回角色数据
    Auth->>Auth: 创建角色会话
    Auth->>Auth: 生成角色Token
    Auth->>History: 同步登录历史
    Auth-->>Client: 返回角色Token
```

### 角色登出流程
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Auth as Auth服务
    participant History as 用户历史服务

    Client->>Auth: 角色登出请求 (角色Token)
    Auth->>Auth: 验证角色Token
    Auth->>Auth: 终止角色会话
    Auth->>History: 同步登出历史
    Auth-->>Client: 登出成功
```

## 📊 用户历史管理

### 数据结构设计

#### 三层历史结构
```typescript
UserHistory {                           // 用户全局历史
  ├── globalStats: GlobalUserStats      // 全局统计数据
  ├── serverHistory: ServerHistoryItem[] // 区服历史列表
  │   ├── serverStats: ServerUserStats  // 区服内统计
  │   └── characters: CharacterSummary[] // 角色摘要列表
  └── globalAchievements: string[]      // 跨服成就
}
```

#### 分区分服特有字段
```typescript
interface ServerHistoryItem {
  // 基础信息
  serverId: string;
  serverName: string;
  firstLoginTime: Date;
  lastLoginTime: Date;
  
  // 分区分服特有字段
  mergeHistory: Array<{              // 合服历史
    fromServerId: string;
    toServerId: string;
    mergeTime: Date;
    dataTransferred: boolean;
  }>;
  
  migrationHistory: Array<{          // 迁移历史
    fromServerId: string;
    toServerId: string;
    migrationType: 'manual' | 'auto' | 'merge';
    migrationTime: Date;
    status: 'pending' | 'completed' | 'failed';
    reason?: string;
  }>;
}
```

### 智能推荐算法
```typescript
// 多维度推荐分数计算
private calculateRecommendationScore(server, serverHistory): number {
  let score = 0;
  
  // 历史记录加分 (50分)
  if (serverHistory) score += 50;
  
  // 最近游戏时间加分 (30分)
  const daysSinceLastPlay = calculateDays(serverHistory.lastLoginTime);
  if (daysSinceLastPlay <= 1) score += 30;
  else if (daysSinceLastPlay <= 7) score += 20;
  else if (daysSinceLastPlay <= 30) score += 10;
  
  // 角色数量加分 (20分)
  const characterCount = serverHistory.characters?.length || 0;
  score += Math.min(characterCount * 5, 20);
  
  // 服务器负载加分 (20分)
  const loadRatio = server.currentPlayers / server.maxPlayers;
  if (loadRatio < 0.3) score += 20;      // 负载很低
  else if (loadRatio < 0.7) score += 15; // 负载适中
  else if (loadRatio < 0.9) score += 5;  // 负载较高
  
  // 新服加分 (25分)
  if (isNewServer(server.openTime)) score += 25;
  
  return Math.min(score, 100);
}
```

## 🔧 配置管理

### JWT配置结构
```typescript
export const authConfig = registerAs('auth', () => ({
  jwt: {
    // 账号级Token配置
    secret: process.env.JWT_SECRET,
    accessTokenTTL: process.env.JWT_ACCESS_TOKEN_TTL || '15m',
    refreshTokenTTL: process.env.JWT_REFRESH_TOKEN_TTL || '7d',
    algorithm: process.env.JWT_ALGORITHM || 'HS256',
    issuer: process.env.JWT_ISSUER || 'football-manager-auth',
    audience: process.env.JWT_AUDIENCE || 'football-manager-app',
    
    // 角色级Token配置
    character: {
      secret: process.env.CHARACTER_JWT_SECRET,
      expiresIn: process.env.CHARACTER_JWT_EXPIRES_IN || '4h',
      algorithm: process.env.CHARACTER_JWT_ALGORITHM || 'HS256',
      issuer: process.env.CHARACTER_JWT_ISSUER || 'football-manager-game',
      audience: process.env.CHARACTER_JWT_AUDIENCE || 'football-manager-app',
    },
  },
  
  // 多服务器配置
  multiServer: {
    enabled: process.env.MULTI_SERVER_ENABLED === 'true',
    defaultServerId: process.env.DEFAULT_SERVER_ID || 'server_001',
    maxServers: parseInt(process.env.MAX_SERVERS || '10', 10),
    sessionTimeout: parseInt(process.env.CHARACTER_SESSION_TIMEOUT || '14400', 10),
  },
}));
```

### 环境变量配置
```bash
# 基础JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_ACCESS_TOKEN_TTL=15m
JWT_REFRESH_TOKEN_TTL=7d
JWT_ALGORITHM=HS256
JWT_ISSUER=football-manager-auth
JWT_AUDIENCE=football-manager-app

# 角色级Token配置
CHARACTER_JWT_SECRET=your-character-jwt-secret-change-in-production
CHARACTER_JWT_EXPIRES_IN=4h
CHARACTER_JWT_ALGORITHM=HS256
CHARACTER_JWT_ISSUER=football-manager-game
CHARACTER_JWT_AUDIENCE=football-manager-app

# 多服务器配置
MULTI_SERVER_ENABLED=true
DEFAULT_SERVER_ID=server_001
MAX_SERVERS=10
CHARACTER_SESSION_TIMEOUT=14400
```

## 🔄 数据同步机制

### 自动同步触发点
1. **角色登录时**: 同步登录历史、更新区服历史、更新角色摘要
2. **角色登出时**: 同步登出历史、更新游戏时长
3. **角色数据变化时**: 异步同步角色属性变化

### 定时任务
- **数据一致性检查**: 每天凌晨2点
- **过期数据清理**: 每周日凌晨3点
- **历史报告生成**: 每周一凌晨1点

## 🛡️ 安全特性

### Token安全
- **密钥分离**: 账号Token和角色Token使用不同密钥
- **作用域隔离**: 严格的权限边界控制
- **黑名单机制**: 支持Token撤销
- **时间验证**: 严格的过期时间检查

### 会话安全
- **单点登录**: 自动终止现有会话
- **活动跟踪**: 实时更新最后活动时间
- **自动清理**: 定时清理过期会话

## 📈 性能优化

### 缓存策略
- **Redis缓存**: 用户历史记录1小时TTL
- **会话缓存**: 角色会话双重缓存（MongoDB + Redis）
- **配置缓存**: 区服列表5分钟TTL

### 异步处理
- **历史同步**: 异步更新，不影响主业务流程
- **数据修复**: 后台定时任务处理数据一致性

## 🔗 微服务集成

### 对外接口
- **账号认证**: `/api/auth/login`, `/api/auth/logout`
- **角色认证**: `/api/auth/character-auth/login`, `/api/auth/character-auth/logout`
- **用户历史**: `/api/auth/user-history/*`

### 微服务调用
- **Character服务**: 获取角色信息
- **Gateway服务**: Token验证和路由
- **其他业务服务**: 权限验证和用户上下文

## 📝 使用示例

### 角色登录示例
```typescript
// 1. 使用账号Token调用角色登录
const response = await fetch('/api/auth/character-auth/login', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accountToken}`,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    serverId: 'server_001',
    characterId: 'char_123456', // 可选
  }),
});

const { characterToken } = await response.json();

// 2. 使用角色Token访问游戏功能
const gameResponse = await fetch('/api/game/some-action', {
  headers: {
    'Authorization': `Bearer ${characterToken}`,
  },
});
```

## 🎯 最佳实践

1. **Token管理**: 客户端应正确管理两种Token的生命周期
2. **权限控制**: 严格按照Token作用域使用相应的接口
3. **错误处理**: 正确处理Token过期和权限不足的情况
4. **安全存储**: Token应安全存储，避免XSS和CSRF攻击
5. **日志记录**: 重要的认证操作应有完整的日志记录

## 📊 监控指标

- **Token生成速率**: 监控Token生成的频率和成功率
- **认证失败率**: 监控认证失败的原因和频率
- **会话时长**: 监控用户会话的平均时长
- **历史同步延迟**: 监控历史数据同步的延迟情况
