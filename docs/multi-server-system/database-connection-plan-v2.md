# 数据库连接方案V2.0（基于正确架构）

## 📋 概述

基于正确的分区分服架构理解，本文档重新制定数据库连接方案。核心原则：**每个区服的服务实例固定连接对应区服的数据库，无需复杂的动态路由**。

## 🎯 核心原则

### 架构理解纠正
- ✅ **每个区服独立部署微服务实例集群**
- ✅ **每个服务实例固定连接对应区服数据库**
- ✅ **Gateway负责路由到正确的服务实例**
- ❌ **不需要服务内部的动态数据库路由**

### 简化设计哲学
- **固定连接**：每个实例启动时确定数据库连接
- **配置驱动**：通过环境变量配置数据库连接
- **实例隔离**：不同区服的实例完全隔离
- **部署简化**：标准化的实例部署模式

## 🏗️ 正确的架构设计

### 服务实例部署模式
```
区服1 (server001):
├── character-server001-1 → character_db_server001
├── character-server001-2 → character_db_server001
├── hero-server001-1 → hero_db_server001
├── hero-server001-2 → hero_db_server001
├── match-server001-1 → match_db_server001
└── match-server001-2 → match_db_server001

区服2 (server002):
├── character-server002-1 → character_db_server002
├── character-server002-2 → character_db_server002
├── hero-server002-1 → hero_db_server002
├── hero-server002-2 → hero_db_server002
├── match-server002-1 → match_db_server002
└── match-server002-2 → match_db_server002

全局服务（不分区服）:
├── auth-1 → auth_db
├── auth-2 → auth_db
└── global-messaging-1 → global_messaging_db
```

### 数据库连接配置
```typescript
// apps/character-server001/src/app.module.ts
@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: ['.env.server001', '.env'],
      isGlobal: true,
    }),
    
    // 固定连接到server001的character数据库
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('CHARACTER_MONGODB_URI_SERVER_001'),
        connectionName: 'character',
      }),
      inject: [ConfigService],
    }),
    
    // 固定连接到server001的Redis
    RedisModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        host: configService.get<string>('REDIS_HOST'),
        port: configService.get<number>('REDIS_PORT'),
        keyPrefix: 'game:server001:character:',
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

## 🔧 实施方案

### 阶段一：配置模板系统（第1周）

#### 1.1 创建区服配置生成器
```typescript
// tools/config-generator/src/server-config-generator.ts
export class ServerConfigGenerator {
  /**
   * 为指定区服生成环境配置文件
   */
  generateServerEnvFile(serverId: string, serviceName: string): string {
    const template = `
# ${serviceName.toUpperCase()} Service Configuration for ${serverId}
NODE_ENV=production
SERVICE_NAME=${serviceName}
SERVER_ID=${serverId}
PORT=\${${serviceName.toUpperCase()}_PORT_${serverId.toUpperCase()}}

# Database Configuration
${serviceName.toUpperCase()}_MONGODB_URI=mongodb://${serviceName}-admin:password@***************:27017/${serviceName}_db_${serverId}

# Redis Configuration
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_KEY_PREFIX=game:${serverId}:${serviceName}:

# Microservice Configuration
MICROSERVICE_CLIENT_ID=${serviceName}-${serverId}
MICROSERVICE_CLIENT_SECRET=\${MICROSERVICE_CLIENT_SECRET}

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PORT=\${METRICS_PORT_${serviceName.toUpperCase()}_${serverId.toUpperCase()}}
`;
    return template.trim();
  }

  /**
   * 生成Docker Compose配置
   */
  generateDockerComposeService(serverId: string, serviceName: string, instanceIndex: number): string {
    const instanceName = `${serviceName}-${serverId}-${instanceIndex}`;
    const port = this.calculatePort(serviceName, serverId, instanceIndex);
    
    return `
  ${instanceName}:
    build:
      context: .
      dockerfile: apps/${serviceName}/Dockerfile
    container_name: ${instanceName}
    environment:
      - NODE_ENV=production
      - SERVER_ID=${serverId}
      - SERVICE_NAME=${serviceName}
      - PORT=${port}
      - ${serviceName.toUpperCase()}_MONGODB_URI=mongodb://${serviceName}-admin:password@mongodb:27017/${serviceName}_db_${serverId}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_KEY_PREFIX=game:${serverId}:${serviceName}:
    ports:
      - "${port}:${port}"
    depends_on:
      - mongodb
      - redis
    networks:
      - microservices
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${port}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
`;
  }
}
```

#### 1.2 创建部署脚本生成器
```typescript
// tools/deployment/src/deployment-script-generator.ts
export class DeploymentScriptGenerator {
  /**
   * 生成新区服部署脚本
   */
  generateServerDeploymentScript(serverId: string, services: string[]): string {
    const script = `#!/bin/bash
# 新区服 ${serverId} 部署脚本

set -e

echo "🚀 开始部署区服: ${serverId}"

# 1. 创建数据库
echo "📊 创建数据库..."
${services.map(service => `
mongo --host ***************:27017 --eval "
  db = db.getSiblingDB('${service}_db_${serverId}');
  db.createUser({
    user: '${service}-admin',
    pwd: 'password',
    roles: [{ role: 'readWrite', db: '${service}_db_${serverId}' }]
  });
"
`).join('')}

# 2. 生成配置文件
echo "⚙️ 生成配置文件..."
${services.map(service => `
mkdir -p config/${serverId}
cat > config/${serverId}/.env.${service} << EOF
$(node tools/config-generator/dist/main.js --server=${serverId} --service=${service})
EOF
`).join('')}

# 3. 部署服务实例
echo "🐳 部署服务实例..."
${services.map(service => {
  const instanceCount = this.getInstanceCount(service);
  return Array.from({length: instanceCount}, (_, i) => `
docker-compose up -d ${service}-${serverId}-${i + 1}
`).join('');
}).join('')}

# 4. 等待服务就绪
echo "⏳ 等待服务就绪..."
${services.map(service => {
  const instanceCount = this.getInstanceCount(service);
  return Array.from({length: instanceCount}, (_, i) => {
    const port = this.calculatePort(service, serverId, i + 1);
    return `
while ! curl -f http://localhost:${port}/health; do
  echo "等待 ${service}-${serverId}-${i + 1} 启动..."
  sleep 5
done
`;
  }).join('');
}).join('')}

# 5. 注册到服务发现
echo "📋 注册服务到服务发现..."
${services.map(service => {
  const instanceCount = this.getInstanceCount(service);
  return Array.from({length: instanceCount}, (_, i) => {
    const port = this.calculatePort(service, serverId, i + 1);
    const instanceName = `${service}-${serverId}-${i + 1}`;
    return `
curl -X POST http://gateway:3000/api/admin/service-registry/register \\
  -H "Content-Type: application/json" \\
  -d '{
    "serviceName": "${service}",
    "serverId": "${serverId}",
    "instanceName": "${instanceName}",
    "host": "${instanceName}",
    "port": ${port},
    "healthy": true
  }'
`;
  }).join('');
}).join('')}

echo "✅ 区服 ${serverId} 部署完成！"
`;
    return script;
  }
}
```

### 阶段二：服务实例标准化（第2-3周）

#### 2.1 创建标准化的服务模板
```typescript
// templates/microservice-template/src/app.module.ts
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      envFilePath: [
        `.env.${process.env.SERVER_ID}`,
        `.env.${process.env.SERVICE_NAME}`,
        '.env'
      ],
      isGlobal: true,
    }),
    
    // 数据库连接（固定连接）
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => {
        const serviceName = configService.get<string>('SERVICE_NAME');
        const serverId = configService.get<string>('SERVER_ID');
        const uriKey = `${serviceName.toUpperCase()}_MONGODB_URI`;
        
        return {
          uri: configService.get<string>(uriKey),
          connectionName: serviceName,
        };
      },
      inject: [ConfigService],
    }),
    
    // Redis连接（固定前缀）
    RedisModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        host: configService.get<string>('REDIS_HOST'),
        port: configService.get<number>('REDIS_PORT'),
        keyPrefix: configService.get<string>('REDIS_KEY_PREFIX'),
      }),
      inject: [ConfigService],
    }),
    
    // 微服务客户端
    MicroserviceClientModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        clientId: configService.get<string>('MICROSERVICE_CLIENT_ID'),
        clientSecret: configService.get<string>('MICROSERVICE_CLIENT_SECRET'),
      }),
      inject: [ConfigService],
    }),
    
    // 健康检查
    HealthModule,
    
    // 监控指标
    MetricsModule,
    
    // 业务模块
    // ... 具体的业务模块
  ],
  controllers: [HealthController],
  providers: [
    // 服务实例信息提供者
    {
      provide: 'SERVICE_INSTANCE_INFO',
      useFactory: (configService: ConfigService) => ({
        serviceName: configService.get<string>('SERVICE_NAME'),
        serverId: configService.get<string>('SERVER_ID'),
        instanceId: `${configService.get<string>('SERVICE_NAME')}-${configService.get<string>('SERVER_ID')}-${process.env.INSTANCE_INDEX || '1'}`,
        port: configService.get<number>('PORT'),
      }),
      inject: [ConfigService],
    },
  ],
})
export class AppModule implements OnModuleInit {
  constructor(
    @Inject('SERVICE_INSTANCE_INFO') private readonly instanceInfo: any,
    private readonly configService: ConfigService,
  ) {}
  
  async onModuleInit() {
    // 启动时自动注册到服务发现
    await this.registerToServiceDiscovery();
  }
  
  private async registerToServiceDiscovery() {
    const gatewayUrl = this.configService.get<string>('GATEWAY_URL', 'http://gateway:3000');
    
    try {
      await fetch(`${gatewayUrl}/api/admin/service-registry/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          serviceName: this.instanceInfo.serviceName,
          serverId: this.instanceInfo.serverId,
          instanceName: this.instanceInfo.instanceId,
          host: this.instanceInfo.instanceId,
          port: this.instanceInfo.port,
          healthy: true,
          metadata: {
            startTime: new Date().toISOString(),
            version: process.env.npm_package_version,
          },
        }),
      });
      
      console.log(`✅ 服务实例已注册: ${this.instanceInfo.instanceId}`);
    } catch (error) {
      console.error(`❌ 服务注册失败: ${this.instanceInfo.instanceId}`, error);
    }
  }
}
```

#### 2.2 创建健康检查控制器
```typescript
// templates/microservice-template/src/health/health.controller.ts
@Controller('health')
export class HealthController {
  constructor(
    @Inject('SERVICE_INSTANCE_INFO') private readonly instanceInfo: any,
    private readonly configService: ConfigService,
  ) {}
  
  @Get()
  async getHealth(): Promise<any> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkDiskSpace(),
      this.checkMemory(),
    ]);
    
    const healthy = checks.every(check => check.status === 'fulfilled' && check.value);
    
    return {
      status: healthy ? 'healthy' : 'unhealthy',
      instance: this.instanceInfo,
      timestamp: new Date().toISOString(),
      checks: {
        database: checks[0].status === 'fulfilled' ? checks[0].value : false,
        redis: checks[1].status === 'fulfilled' ? checks[1].value : false,
        disk: checks[2].status === 'fulfilled' ? checks[2].value : false,
        memory: checks[3].status === 'fulfilled' ? checks[3].value : false,
      },
    };
  }
  
  private async checkDatabase(): Promise<boolean> {
    try {
      // 检查MongoDB连接
      const connection = mongoose.connection;
      return connection.readyState === 1;
    } catch {
      return false;
    }
  }
  
  private async checkRedis(): Promise<boolean> {
    try {
      // 检查Redis连接
      // 实现Redis连接检查
      return true;
    } catch {
      return false;
    }
  }
  
  private async checkDiskSpace(): Promise<boolean> {
    // 检查磁盘空间
    return true;
  }
  
  private async checkMemory(): Promise<boolean> {
    // 检查内存使用
    const usage = process.memoryUsage();
    const maxMemory = 1024 * 1024 * 1024; // 1GB
    return usage.heapUsed < maxMemory * 0.8;
  }
}
```

### 阶段三：现有服务迁移（第4-6周）

#### 3.1 Character服务迁移
```bash
# 1. 为每个区服创建character实例
./scripts/create-server-instances.sh character server001 2
./scripts/create-server-instances.sh character server002 2
./scripts/create-server-instances.sh character server003 2

# 2. 部署实例
docker-compose up -d character-server001-1 character-server001-2
docker-compose up -d character-server002-1 character-server002-2
docker-compose up -d character-server003-1 character-server003-2

# 3. 验证部署
./scripts/verify-server-instances.sh character
```

#### 3.2 其他服务迁移
```bash
# Hero服务
./scripts/migrate-service.sh hero

# Economy服务
./scripts/migrate-service.sh economy

# Social服务
./scripts/migrate-service.sh social

# Activity服务
./scripts/migrate-service.sh activity

# Match服务
./scripts/migrate-service.sh match
```

## 📊 配置管理

### 环境变量配置模板
```bash
# .env.server001.character
NODE_ENV=production
SERVICE_NAME=character
SERVER_ID=server001
PORT=3002
INSTANCE_INDEX=1

# Database
CHARACTER_MONGODB_URI=*******************************************************************************

# Redis
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_KEY_PREFIX=game:server001:character:

# Microservice
MICROSERVICE_CLIENT_ID=character-server001-1
MICROSERVICE_CLIENT_SECRET=${MICROSERVICE_CLIENT_SECRET}

# Gateway
GATEWAY_URL=http://gateway:3000

# Monitoring
METRICS_ENABLED=true
METRICS_PORT=9102
```

### Docker Compose配置
```yaml
# docker-compose.server001.yml
version: '3.8'
services:
  character-server001-1:
    build:
      context: .
      dockerfile: apps/character/Dockerfile
    container_name: character-server001-1
    env_file:
      - .env.server001.character
    environment:
      - INSTANCE_INDEX=1
    ports:
      - "3002:3002"
      - "9102:9102"
    depends_on:
      - mongodb
      - redis
    networks:
      - microservices
    restart: unless-stopped

  character-server001-2:
    build:
      context: .
      dockerfile: apps/character/Dockerfile
    container_name: character-server001-2
    env_file:
      - .env.server001.character
    environment:
      - INSTANCE_INDEX=2
      - PORT=3012
      - METRICS_PORT=9112
    ports:
      - "3012:3012"
      - "9112:9112"
    depends_on:
      - mongodb
      - redis
    networks:
      - microservices
    restart: unless-stopped
```

## 🚀 部署自动化

### 新区服一键部署脚本
```bash
#!/bin/bash
# scripts/deploy-new-server.sh

SERVER_ID=$1
SERVICES=("character" "hero" "economy" "social" "activity" "match")

if [ -z "$SERVER_ID" ]; then
  echo "Usage: $0 <server_id>"
  exit 1
fi

echo "🚀 开始部署新区服: $SERVER_ID"

# 1. 生成配置文件
echo "⚙️ 生成配置文件..."
for service in "${SERVICES[@]}"; do
  node tools/config-generator/dist/main.js --server=$SERVER_ID --service=$service
done

# 2. 创建数据库
echo "📊 创建数据库..."
./scripts/create-server-databases.sh $SERVER_ID

# 3. 部署服务实例
echo "🐳 部署服务实例..."
docker-compose -f docker-compose.$SERVER_ID.yml up -d

# 4. 等待服务就绪
echo "⏳ 等待服务就绪..."
./scripts/wait-for-services.sh $SERVER_ID

# 5. 验证部署
echo "✅ 验证部署..."
./scripts/verify-server-deployment.sh $SERVER_ID

echo "🎉 区服 $SERVER_ID 部署完成！"
```

## 📝 总结

基于正确的分区分服架构理解，数据库连接方案大幅简化：

### ✅ 核心优势
1. **架构简单**：每个实例固定连接，无需复杂路由
2. **部署标准化**：统一的实例部署模式
3. **配置清晰**：环境变量驱动的配置管理
4. **运维友好**：标准化的健康检查和监控

### ✅ 实施要点
1. **配置模板化**：自动生成区服配置
2. **部署自动化**：一键部署新区服
3. **服务标准化**：统一的服务实例模板
4. **监控完善**：实例级别的健康检查

这个方案完全符合正确的分区分服架构，避免了过度设计，实现了简单、高效、可维护的数据库连接管理。

*计划版本: v2.0*  
*制定日期: 2024-01-01*  
*基于正确架构理解*
