# 分区分服系统设计方案 v4.0 - 基于现有架构的精准实现

## 📋 概述

基于对现有项目架构的深度分析，本方案严格遵循现有的**WebSocket通信机制**、**微服务公共库**、**数据库设计**和**character服务架构**，在现有基础上进行**最小化扩展**实现分区分服功能。

## 🎯 核心需求重申

### **1. 基础功能需求**
- ✅ **账号统一管理**: 一个账号可以在多个区服游戏
- ✅ **区服列表展示**: 显示所有可用区服及其状态
- ✅ **区服状态管理**: 新服、开启中、维护中、即将合服等状态
- ✅ **区服选择**: 玩家可以选择进入指定区服
- ✅ **历史记录**: 记录玩家曾经游戏过的区服
- ✅ **最后游戏区服**: 记录并快速进入上次游戏的区服

### **2. 数据隔离需求**
- ✅ **完全数据隔离**: 同一账号在不同区服的数据完全独立
- ✅ **独立游戏进度**: 俱乐部、球员、比赛记录等数据按区服隔离
- ✅ **独立经济系统**: 金币、道具、交易记录按区服隔离
- ✅ **独立社交系统**: 好友、公会、聊天记录按区服隔离

### **3. 跨服功能需求**
- ✅ **跨服排行榜**: 支持全区服排行榜展示
- ✅ **跨服战斗**: 支持不同区服玩家间的对战
- ✅ **跨服活动**: 支持全区服参与的特殊活动
- ✅ **跨服聊天**: 支持跨区服的世界频道

### **4. 合服功能需求**
- ✅ **合服策略**: 支持多个区服合并为一个区服
- ✅ **数据迁移**: 安全可靠的数据合并和迁移
- ✅ **冲突处理**: 处理合服时的数据冲突（如重名、排名等）
- ✅ **补偿机制**: 合服后的玩家补偿和调整

## 🔐 基于现有架构的认证机制扩展

### **1. 现有认证流程分析**

基于代码分析，当前系统采用**WebSocket为主的认证机制**：

```typescript
// 当前认证流程
// 1. HTTP登录获取JWT Token
POST /api/auth/login → JWT Token

// 2. WebSocket连接时传递Token
const socket = io('ws://localhost:3000', {
  auth: { token: 'jwt-token' }
});

// 3. WebSocket消息格式
socket.emit('message', {
  id: 'msg_123',
  command: 'character.getInfo', // service.action格式
  payload: { characterId: 'char_123' }
});

// 4. 网关通过微服务公共库调用
await this.microserviceClient.call('character', 'getInfo', params);
```

### **2. 扩展认证机制设计**

```typescript
/**
 * 扩展现有认证流程，支持区服选择和角色会话
 */

// 步骤1：账号登录（现有，无需修改）
POST /api/auth/login
{
  "identifier": "<EMAIL>",
  "password": "password123"
}
// 返回：账号级JWT Token

// 步骤2：获取区服列表（新增WebSocket消息）
socket.emit('message', {
  id: 'msg_001',
  command: 'auth.getServerList',
  payload: {}
});
// 返回：可用区服列表 + 玩家历史记录

// 步骤3：选择区服并进入（新增WebSocket消息）
socket.emit('message', {
  id: 'msg_002', 
  command: 'auth.enterServer',
  payload: {
    serverId: 'server_001',
    characterId: 'char_123' // 可选，如果是新角色则为空
  }
});
// 返回：角色会话信息 + 更新的JWT Token

// 步骤4：游戏业务请求（扩展现有）
socket.emit('message', {
  id: 'msg_003',
  command: 'character.getInfo',
  payload: { characterId: 'char_123' }
});
// 网关自动根据当前会话路由到对应区服的微服务
```

### **3. JWT Token扩展设计**

```typescript
// 扩展现有JWT Payload，保持向后兼容
interface ExtendedJWTPayload {
  // 现有字段（保持不变）
  sub: string;              // 账号ID
  username: string;         // 用户名
  email: string;           // 邮箱
  roles: string[];         // 角色权限
  iat: number;             // 签发时间
  exp: number;             // 过期时间

  // 新增区服相关字段
  currentServerId?: string;     // 当前所在区服
  currentCharacterId?: string;  // 当前角色ID
  sessionId?: string;           // 角色会话ID
  serverHistory?: string[];     // 历史区服列表
  type: 'account' | 'character'; // Token类型
}

// Token生成逻辑
class AuthService {
  // 账号登录时生成账号级Token
  async generateAccountToken(user: User): Promise<string> {
    const payload: ExtendedJWTPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      type: 'account',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 24 * 3600, // 24小时
    };
    return this.jwtService.sign(payload);
  }

  // 进入区服时生成角色级Token
  async generateCharacterToken(user: User, serverId: string, characterId: string, sessionId: string): Promise<string> {
    const payload: ExtendedJWTPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      currentServerId: serverId,
      currentCharacterId: characterId,
      sessionId: sessionId,
      type: 'character',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 8 * 3600, // 8小时
    };
    return this.jwtService.sign(payload);
  }
}
```

## 🏗️ 基于现有架构的服务扩展

### **1. Auth服务扩展**

```typescript
// apps/auth/src/domain/server-management/ (新增模块)
├── server-management.module.ts
├── services/
│   ├── server.service.ts                    # 区服管理服务
│   ├── character-session.service.ts         # 角色会话管理
│   └── player-server-history.service.ts     # 玩家区服历史
├── controllers/
│   └── server-management.controller.ts      # 区服管理控制器
├── entities/
│   ├── server.entity.ts                     # 区服信息实体
│   ├── character-session.entity.ts          # 角色会话实体
│   └── player-server-history.entity.ts      # 玩家区服历史
└── dto/
    ├── server-list.dto.ts
    ├── enter-server.dto.ts
    └── character-session.dto.ts

// 核心服务实现
@Injectable()
export class ServerService {
  constructor(
    private serverRepository: ServerRepository,
    private characterSessionService: CharacterSessionService,
    private playerHistoryService: PlayerServerHistoryService,
    private microserviceClient: MicroserviceClientService,
    private jwtService: JwtService
  ) {}

  // 获取区服列表（WebSocket消息处理）
  async getServerList(userId: string): Promise<ServerListResponse> {
    const allServers = await this.serverRepository.findActiveServers();
    const playerHistory = await this.playerHistoryService.getPlayerHistory(userId);
    
    return {
      servers: allServers.map(server => ({
        serverId: server.id,
        serverName: server.name,
        status: server.status,
        openTime: server.openTime,
        playerCount: server.currentPlayerCount,
        maxPlayerCount: server.maxPlayerCount,
        isNew: this.isNewServer(server.openTime),
        hasCharacter: playerHistory.some(h => h.serverId === server.id),
        lastPlayTime: playerHistory.find(h => h.serverId === server.id)?.lastPlayTime,
        recommendationScore: this.calculateRecommendationScore(server, playerHistory),
      })),
      lastServerId: playerHistory[0]?.serverId,
      totalServers: allServers.length,
    };
  }

  // 进入区服（WebSocket消息处理）
  async enterServer(userId: string, serverId: string, characterId?: string): Promise<EnterServerResponse> {
    // 1. 验证区服状态
    const server = await this.serverRepository.findById(serverId);
    if (!server || server.status !== 'active') {
      throw new BadRequestException('区服不可用');
    }

    // 2. 获取或创建角色
    let character;
    if (characterId) {
      // 验证角色是否属于该用户和区服
      character = await this.microserviceClient.call('character', 'getInfo', {
        characterId,
        serverId,
        userId
      });
    } else {
      // 获取该用户在该区服的默认角色
      const characterList = await this.microserviceClient.call('character', 'getList', {
        userId,
        serverId
      });
      character = characterList.data?.[0]; // 取第一个角色作为默认
    }

    if (!character) {
      throw new BadRequestException('该区服没有角色，请先创建角色');
    }

    // 3. 创建角色会话
    const session = await this.characterSessionService.createSession({
      userId,
      characterId: character.characterId,
      serverId,
      serverName: server.name,
    });

    // 4. 生成角色级JWT Token
    const user = await this.getUserById(userId);
    const characterToken = await this.generateCharacterToken(user, serverId, character.characterId, session.id);

    // 5. 更新玩家历史记录
    await this.playerHistoryService.updatePlayerHistory(userId, serverId);

    return {
      characterToken,
      character,
      server,
      session,
    };
  }
}
```

### **2. Auth控制器扩展**

```typescript
// apps/auth/src/domain/server-management/controllers/server-management.controller.ts
@Controller()
export class ServerManagementController {
  private readonly logger = new Logger(ServerManagementController.name);

  constructor(private readonly serverService: ServerService) {}

  /**
   * 获取区服列表
   * WebSocket消息: auth.getServerList
   */
  @MessagePattern('auth.getServerList')
  async getServerList(@Payload() payload: { userId: string }) {
    this.logger.log(`获取区服列表: ${payload.userId}`);
    const result = await this.serverService.getServerList(payload.userId);
    return {
      success: true,
      data: result,
    };
  }

  /**
   * 进入区服
   * WebSocket消息: auth.enterServer
   */
  @MessagePattern('auth.enterServer')
  async enterServer(@Payload() payload: EnterServerDto) {
    this.logger.log(`进入区服: ${JSON.stringify(payload)}`);
    const result = await this.serverService.enterServer(
      payload.userId,
      payload.serverId,
      payload.characterId
    );
    return {
      success: true,
      data: result,
    };
  }

  /**
   * 退出区服
   * WebSocket消息: auth.exitServer
   */
  @MessagePattern('auth.exitServer')
  async exitServer(@Payload() payload: { userId: string; sessionId: string }) {
    this.logger.log(`退出区服: ${JSON.stringify(payload)}`);
    await this.serverService.exitServer(payload.userId, payload.sessionId);
    return {
      success: true,
      message: '退出区服成功',
    };
  }

  /**
   * 获取当前会话信息
   * WebSocket消息: auth.getCurrentSession
   */
  @MessagePattern('auth.getCurrentSession')
  async getCurrentSession(@Payload() payload: { userId: string }) {
    this.logger.log(`获取当前会话: ${payload.userId}`);
    const session = await this.serverService.getCurrentSession(payload.userId);
    return {
      success: true,
      data: session,
    };
  }
}
```

### **3. 网关WebSocket扩展**

```typescript
// apps/gateway/src/domain/websocket/websocket.gateway.ts (扩展现有)
@WebSocketGateway({
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true,
  },
  namespace: '/',
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
})
export class WebSocketGateway {
  // ... 现有代码保持不变

  // 扩展消息路由逻辑，支持区服路由
  private async routeMessage(message: WSMessageDto, userId: string): Promise<ServiceResponse> {
    const { service, action, payload } = message;

    // 获取当前用户的会话信息
    const session = await this.sessionService.getSessionByUserId(userId);
    
    // 构建微服务调用参数
    const params = {
      ...payload,
      userId,
      serverId: session?.currentServerId, // 自动注入当前区服ID
      characterId: session?.currentCharacterId, // 自动注入当前角色ID
    };

    this.logger.debug(`🚀 Calling microservice: ${service}.${action} with serverId: ${session?.currentServerId}`);

    try {
      // 使用现有的微服务公共库调用
      const result = await this.microserviceClient.call(service as MicroserviceName, action, params);
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`❌ routeMessage error: ${error.message}`);
      throw error;
    }
  }

  // 扩展认证逻辑，支持角色级Token
  private async authenticateSocket(socket: AuthenticatedSocket): Promise<void> {
    const token = socket.handshake.auth?.token || socket.handshake.query?.token;

    if (!token) {
      socket.authenticated = false;
      return;
    }

    try {
      const payload = this.jwtService.verify(token as string);

      if (payload) {
        socket.userId = payload.sub;
        socket.user = {
          id: payload.sub,
          username: payload.username,
          email: payload.email,
          roles: payload.roles || [],
        };
        socket.authenticated = true;

        // 如果是角色级Token，设置额外信息
        if (payload.type === 'character') {
          socket.metadata = {
            serverId: payload.currentServerId,
            characterId: payload.currentCharacterId,
            sessionId: payload.sessionId,
            tokenType: 'character',
          };
        } else {
          socket.metadata = {
            tokenType: 'account',
          };
        }

        // 创建或更新会话
        await this.sessionService.createOrUpdateSession(payload.sub, socket.id, socket.metadata);
      }
    } catch (error) {
      throw new Error('Invalid authentication token');
    }
  }
}
```

### **4. 基于现有数据库设计的扩展**

```typescript
// 基于现有.env配置，扩展数据库设计

// 1. Auth服务数据库扩展 (AUTH_MONGODB_URI)
// 新增表：servers, character_sessions, player_server_history

// 2. Character服务数据库扩展 (CHARACTER_MONGODB_URI)
// 现有Character Schema已包含serverId字段，无需修改

// 3. 其他微服务数据库
// 所有现有微服务数据库保持不变，通过serverId字段实现数据隔离

// 区服信息表（添加到auth_db）
@Entity('servers')
export class Server {
  @PrimaryColumn()
  id: string; // server_001, server_002

  @Column()
  name: string; // 区服名称

  @Column({ type: 'enum', enum: ['new', 'active', 'maintenance', 'merging', 'closed'] })
  status: ServerStatus;

  @Column()
  openTime: Date;

  @Column()
  maxPlayerCount: number;

  @Column()
  currentPlayerCount: number;

  @Column({ type: 'json', nullable: true })
  config: ServerConfig;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

// 角色会话表（添加到auth_db）
@Entity('character_sessions')
export class CharacterSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string; // 关联账号

  @Column()
  characterId: string; // 角色ID

  @Column()
  serverId: string; // 区服ID

  @Column()
  socketId: string; // WebSocket连接ID

  @Column()
  ipAddress: string;

  @Column()
  userAgent: string;

  @Column()
  lastActivity: Date;

  @Column()
  expiresAt: Date;

  @Column({ default: true })
  active: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

// 玩家区服历史表（添加到auth_db）
@Entity('player_server_history')
export class PlayerServerHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string; // 账号ID

  @Column()
  serverId: string; // 区服ID

  @Column({ nullable: true })
  characterId: string; // 主要角色ID

  @Column({ nullable: true })
  characterName: string; // 角色名称

  @Column()
  firstPlayTime: Date;

  @Column()
  lastPlayTime: Date;

  @Column({ default: 0 })
  totalPlayTime: number; // 总游戏时长（秒）

  @Column({ default: 1 })
  characterLevel: number;

  @Column({ type: 'json', nullable: true })
  gameProgress: any; // 游戏进度快照

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

## 🔄 微服务数据隔离实现

### **1. 基于现有微服务公共库的扩展**

```typescript
// libs/common/src/microservice-kit/server-aware/ (新增)
├── server-aware-client.service.ts
├── server-context.service.ts
└── server-routing.middleware.ts

// 区服上下文服务
@Injectable()
export class ServerContextService {
  private currentServerId: string | null = null;
  private currentCharacterId: string | null = null;

  setCurrentContext(serverId: string, characterId?: string) {
    this.currentServerId = serverId;
    this.currentCharacterId = characterId;
  }

  getCurrentServerId(): string | null {
    return this.currentServerId;
  }

  getCurrentCharacterId(): string | null {
    return this.currentCharacterId;
  }

  clearContext() {
    this.currentServerId = null;
    this.currentCharacterId = null;
  }
}

// 扩展现有微服务客户端，支持区服路由
@Injectable()
export class ServerAwareMicroserviceClient extends MicroserviceClientService {
  constructor(
    @Inject('MICROSERVICE_CONFIG') config: MicroserviceKitConfig,
    @Inject('CONNECTED_SERVICES') connectedServices: MicroserviceName[],
    private serverContext: ServerContextService
  ) {
    super(config, connectedServices);
  }

  // 重写call方法，自动注入区服上下文
  async call<T = any>(serviceName: MicroserviceName, pattern: string, data?: any): Promise<T> {
    const serverId = this.serverContext.getCurrentServerId();
    const characterId = this.serverContext.getCurrentCharacterId();

    // 自动注入区服上下文
    const enhancedData = {
      ...data,
      serverId,
      characterId,
    };

    return super.call(serviceName, pattern, enhancedData);
  }
}
```

### **2. Character服务适配**

```typescript
// apps/character/src/modules/character/character.service.ts (扩展现有)
@Injectable()
export class CharacterService {
  // ... 现有代码保持不变

  // 扩展现有方法，支持区服隔离
  async getCharacterInfo(characterId: string, serverId?: string): Promise<CharacterInfoDto> {
    // 如果提供了serverId，验证角色是否属于该区服
    if (serverId) {
      const character = await this.characterRepository.findByCharacterIdAndServerId(characterId, serverId);
      if (!character) {
        throw new NotFoundException('角色不存在或不属于该区服');
      }
      return this.buildCharacterInfo(character);
    }

    // 原有逻辑保持不变
    return this.getCharacterInfo(characterId);
  }

  // 新增：根据用户ID和区服ID获取角色列表
  async getCharactersByUserAndServer(userId: string, serverId: string): Promise<Character[]> {
    return this.characterRepository.findByUserIdAndServerId(userId, serverId);
  }

  // 新增：创建角色时自动设置区服ID
  async createCharacterForServer(createDto: CreateCharacterDto, serverId: string): Promise<CharacterDocument> {
    const characterData = {
      ...createDto,
      serverId, // 自动设置区服ID
    };

    return this.createCharacter(characterData);
  }
}

// apps/character/src/common/repositories/character.repository.ts (扩展现有)
@Injectable()
export class CharacterRepository {
  // ... 现有代码保持不变

  // 新增：根据角色ID和区服ID查找
  async findByCharacterIdAndServerId(characterId: string, serverId: string): Promise<CharacterDocument | null> {
    try {
      return await this.characterModel.findOne({ 
        characterId, 
        serverId 
      }).exec();
    } catch (error) {
      this.logger.error(`根据角色ID和区服ID查找角色失败: ${characterId}, ${serverId}`, error);
      throw error;
    }
  }

  // 新增：根据用户ID和区服ID查找角色列表
  async findByUserIdAndServerId(userId: string, serverId: string): Promise<CharacterDocument[]> {
    try {
      return await this.characterModel.find({ 
        userId, 
        serverId 
      }).exec();
    } catch (error) {
      this.logger.error(`根据用户ID和区服ID查找角色列表失败: ${userId}, ${serverId}`, error);
      throw error;
    }
  }
}
```

---

## 🔄 跨服功能实现

### **1. 基于现有Redis PubSub的跨服通信**

```typescript
// libs/common/src/cross-server/ (新增模块)
├── cross-server.module.ts
├── services/
│   ├── cross-server-ranking.service.ts      # 跨服排行榜
│   ├── cross-server-battle.service.ts       # 跨服战斗
│   └── cross-server-activity.service.ts     # 跨服活动
└── dto/
    ├── cross-server-ranking.dto.ts
    ├── cross-server-battle.dto.ts
    └── cross-server-activity.dto.ts

// 跨服排行榜服务
@Injectable()
export class CrossServerRankingService {
  constructor(
    private redisPubSubService: RedisPubSubService,
    private microserviceClient: MicroserviceClientService
  ) {}

  // 获取全服排行榜
  async getGlobalRanking(category: string, limit: number = 100): Promise<GlobalRankingItem[]> {
    const cacheKey = `global:ranking:${category}`;

    // 尝试从全局缓存获取
    const cached = await this.redisPubSubService.get(cacheKey, 'global');
    if (cached) {
      return JSON.parse(cached);
    }

    // 获取所有活跃区服
    const activeServers = await this.getActiveServers();
    const allRankings: RankingItem[] = [];

    // 并行获取各区服排行榜
    const serverRankings = await Promise.all(
      activeServers.map(async (server) => {
        try {
          // 调用各区服的排行榜服务
          return await this.microserviceClient.call('character', 'getRanking', {
            category,
            limit,
            serverId: server.id,
          });
        } catch (error) {
          this.logger.warn(`Failed to get ranking from server ${server.id}: ${error.message}`);
          return [];
        }
      })
    );

    // 合并并排序
    serverRankings.forEach((rankings, index) => {
      rankings.forEach(ranking => {
        allRankings.push({
          ...ranking,
          serverId: activeServers[index].id,
          serverName: activeServers[index].name,
        });
      });
    });

    // 全局排序
    const globalRanking = allRankings
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map((item, index) => ({
        ...item,
        globalRank: index + 1,
      }));

    // 缓存结果到全局Redis
    await this.redisPubSubService.publishGlobal('ranking:updated', {
      category,
      ranking: globalRanking,
    });

    return globalRanking;
  }

  // 跨服战斗匹配
  async findCrossServerOpponent(playerId: string, serverId: string, powerRange: [number, number]): Promise<CrossServerOpponent | null> {
    // 发布跨服匹配请求
    await this.redisPubSubService.publishCrossServerEvent('battle:match_request', {
      playerId,
      sourceServerId: serverId,
      powerRange,
      timestamp: new Date(),
    });

    // 等待其他区服响应（使用Redis订阅）
    return new Promise((resolve) => {
      const timeout = setTimeout(() => resolve(null), 5000); // 5秒超时

      this.redisPubSubService.subscribeCrossServerEvent('battle:match_response', (message) => {
        const { data } = message;
        if (data.requestPlayerId === playerId) {
          clearTimeout(timeout);
          resolve(data.opponent);
        }
      });
    });
  }
}
```

### **2. 跨服WebSocket消息处理**

```typescript
// apps/gateway/src/domain/websocket/cross-server/ (新增)
├── cross-server-websocket.service.ts
└── cross-server-message.handler.ts

// 跨服WebSocket消息处理器
@Injectable()
export class CrossServerMessageHandler {
  constructor(
    private crossServerRankingService: CrossServerRankingService,
    private crossServerBattleService: CrossServerBattleService,
    private webSocketGateway: WebSocketGateway
  ) {}

  // 处理跨服排行榜请求
  async handleGlobalRankingRequest(socket: AuthenticatedSocket, payload: any): Promise<any> {
    const { category, limit } = payload;

    const ranking = await this.crossServerRankingService.getGlobalRanking(category, limit);

    return {
      success: true,
      data: {
        category,
        ranking,
        timestamp: new Date(),
      },
    };
  }

  // 处理跨服战斗匹配请求
  async handleCrossServerBattleMatch(socket: AuthenticatedSocket, payload: any): Promise<any> {
    const { powerRange } = payload;
    const { serverId, characterId } = socket.metadata;

    const opponent = await this.crossServerBattleService.findCrossServerOpponent(
      characterId,
      serverId,
      powerRange
    );

    if (opponent) {
      return {
        success: true,
        data: {
          opponent,
          battleId: this.generateBattleId(),
          timestamp: new Date(),
        },
      };
    } else {
      return {
        success: false,
        message: '未找到合适的跨服对手',
      };
    }
  }
}

// 扩展网关WebSocket，支持跨服消息
// apps/gateway/src/domain/websocket/websocket.gateway.ts (扩展)
export class WebSocketGateway {
  constructor(
    // ... 现有依赖
    private crossServerHandler: CrossServerMessageHandler
  ) {}

  // 扩展消息路由，支持跨服消息
  private async routeMessage(message: WSMessageDto, userId: string): Promise<ServiceResponse> {
    const { service, action, payload } = message;

    // 检查是否为跨服消息
    if (action.startsWith('cross.') || action.startsWith('global.')) {
      return this.routeCrossServerMessage(message, userId);
    }

    // 原有路由逻辑保持不变
    return this.routeRegularMessage(message, userId);
  }

  // 跨服消息路由
  private async routeCrossServerMessage(message: WSMessageDto, userId: string): Promise<ServiceResponse> {
    const { action, payload } = message;
    const socket = this.getSocketByUserId(userId);

    switch (action) {
      case 'cross.getRanking':
        return this.crossServerHandler.handleGlobalRankingRequest(socket, payload);

      case 'cross.findBattleOpponent':
        return this.crossServerHandler.handleCrossServerBattleMatch(socket, payload);

      case 'global.getActivity':
        return this.crossServerHandler.handleGlobalActivityRequest(socket, payload);

      default:
        throw new Error(`Unknown cross-server action: ${action}`);
    }
  }
}
```

## 🔀 合服功能实现

### **1. 合服数据迁移服务**

```typescript
// apps/auth/src/domain/server-merge/ (新增模块)
├── server-merge.module.ts
├── services/
│   ├── server-merge.service.ts              # 合服主服务
│   ├── data-migration.service.ts            # 数据迁移服务
│   ├── conflict-resolution.service.ts       # 冲突解决服务
│   └── compensation.service.ts              # 补偿服务
├── controllers/
│   └── server-merge.controller.ts           # 合服控制器
└── dto/
    ├── server-merge-request.dto.ts
    ├── merge-result.dto.ts
    └── compensation.dto.ts

// 合服主服务
@Injectable()
export class ServerMergeService {
  constructor(
    private dataMigrationService: DataMigrationService,
    private conflictResolutionService: ConflictResolutionService,
    private compensationService: CompensationService,
    private microserviceClient: MicroserviceClientService,
    private redisPubSubService: RedisPubSubService
  ) {}

  // 合服主流程
  async mergeServers(mergeRequest: ServerMergeRequest): Promise<ServerMergeResult> {
    const { sourceServerIds, targetServerId, mergeStrategy } = mergeRequest;

    this.logger.log(`开始合服操作: ${sourceServerIds.join(',')} -> ${targetServerId}`);

    try {
      // 1. 预检查
      await this.validateMergeRequest(sourceServerIds, targetServerId);

      // 2. 设置服务器状态为合服中
      await this.setServersStatus([...sourceServerIds, targetServerId], 'merging');

      // 3. 通知所有在线玩家
      await this.notifyPlayersAboutMerge(sourceServerIds, targetServerId);

      // 4. 执行数据迁移
      const migrationResult = await this.dataMigrationService.migrateData(
        sourceServerIds,
        targetServerId,
        mergeStrategy
      );

      // 5. 处理数据冲突
      const conflictResolution = await this.conflictResolutionService.resolveConflicts(
        migrationResult.conflicts,
        targetServerId
      );

      // 6. 发放合服补偿
      await this.compensationService.distributeCompensation(
        migrationResult.affectedPlayers,
        targetServerId
      );

      // 7. 更新玩家历史记录
      await this.updatePlayerHistoryForMerge(sourceServerIds, targetServerId);

      // 8. 验证数据完整性
      await this.validateDataIntegrity(targetServerId);

      // 9. 设置目标服务器为活跃状态
      await this.setServersStatus([targetServerId], 'active');

      // 10. 关闭源服务器
      await this.setServersStatus(sourceServerIds, 'closed');

      // 11. 发布合服完成事件
      await this.redisPubSubService.publishGlobal('server:merge_completed', {
        sourceServerIds,
        targetServerId,
        timestamp: new Date(),
      });

      this.logger.log(`合服操作完成: ${sourceServerIds.join(',')} -> ${targetServerId}`);

      return {
        success: true,
        targetServerId,
        migratedPlayers: migrationResult.migratedPlayers,
        resolvedConflicts: conflictResolution.resolvedConflicts,
        compensationDistributed: migrationResult.affectedPlayers.length,
      };

    } catch (error) {
      this.logger.error(`合服操作失败: ${error.message}`);

      // 回滚操作
      await this.rollbackMerge(sourceServerIds, targetServerId);

      throw new InternalServerErrorException(`合服失败: ${error.message}`);
    }
  }

  // 数据迁移核心逻辑
  private async migrateServerData(sourceServerId: string, targetServerId: string): Promise<MigrationResult> {
    const result: MigrationResult = {
      migratedPlayers: [],
      conflicts: [],
      affectedPlayers: [],
    };

    // 迁移角色数据
    const characterMigration = await this.microserviceClient.call('character', 'migrateServerData', {
      sourceServerId,
      targetServerId,
    });
    result.migratedPlayers.push(...characterMigration.migratedPlayers);
    result.conflicts.push(...characterMigration.conflicts);

    // 迁移球员数据
    const heroMigration = await this.microserviceClient.call('hero', 'migrateServerData', {
      sourceServerId,
      targetServerId,
    });
    result.conflicts.push(...heroMigration.conflicts);

    // 迁移经济数据
    const economyMigration = await this.microserviceClient.call('economy', 'migrateServerData', {
      sourceServerId,
      targetServerId,
    });
    result.conflicts.push(...economyMigration.conflicts);

    // 迁移社交数据
    const socialMigration = await this.microserviceClient.call('social', 'migrateServerData', {
      sourceServerId,
      targetServerId,
    });
    result.conflicts.push(...socialMigration.conflicts);

    // 迁移活动数据
    const activityMigration = await this.microserviceClient.call('activity', 'migrateServerData', {
      sourceServerId,
      targetServerId,
    });
    result.conflicts.push(...activityMigration.conflicts);

    // 迁移比赛数据
    const matchMigration = await this.microserviceClient.call('match', 'migrateServerData', {
      sourceServerId,
      targetServerId,
    });
    result.conflicts.push(...matchMigration.conflicts);

    return result;
  }
}
```

### **2. 合服WebSocket消息处理**

```typescript
// 合服控制器（WebSocket消息处理）
@Controller()
export class ServerMergeController {
  private readonly logger = new Logger(ServerMergeController.name);

  constructor(private readonly serverMergeService: ServerMergeService) {}

  /**
   * 启动合服操作
   * WebSocket消息: auth.startServerMerge
   * 权限要求: 管理员权限
   */
  @MessagePattern('auth.startServerMerge')
  async startServerMerge(@Payload() payload: ServerMergeRequest) {
    this.logger.log(`启动合服操作: ${JSON.stringify(payload)}`);

    // 验证管理员权限
    if (!this.hasAdminPermission(payload.operatorId)) {
      throw new ForbiddenException('权限不足');
    }

    const result = await this.serverMergeService.mergeServers(payload);
    return {
      success: true,
      data: result,
    };
  }

  /**
   * 获取合服进度
   * WebSocket消息: auth.getMergeProgress
   */
  @MessagePattern('auth.getMergeProgress')
  async getMergeProgress(@Payload() payload: { mergeId: string }) {
    this.logger.log(`获取合服进度: ${payload.mergeId}`);
    const progress = await this.serverMergeService.getMergeProgress(payload.mergeId);
    return {
      success: true,
      data: progress,
    };
  }

  /**
   * 取消合服操作
   * WebSocket消息: auth.cancelServerMerge
   */
  @MessagePattern('auth.cancelServerMerge')
  async cancelServerMerge(@Payload() payload: { mergeId: string; operatorId: string }) {
    this.logger.log(`取消合服操作: ${payload.mergeId}`);

    if (!this.hasAdminPermission(payload.operatorId)) {
      throw new ForbiddenException('权限不足');
    }

    await this.serverMergeService.cancelMerge(payload.mergeId);
    return {
      success: true,
      message: '合服操作已取消',
    };
  }
}
```

## 🚀 部署与配置

### **1. 环境变量扩展**

```bash
# .env 扩展（基于现有配置）

# 多服务器功能开关
MULTI_SERVER_ENABLED=true

# 默认区服配置
DEFAULT_SERVER_ID=server_001
MAX_SERVERS=100
SERVER_CAPACITY_DEFAULT=10000

# 区服数据库配置（基于现有模式）
# 认证服务数据库（已存在）
AUTH_MONGODB_URI=**************************************************************

# 各区服数据库配置（扩展现有配置）
# 区服1
CHARACTER_MONGODB_URI_SERVER_001=***********************************************************************************
HERO_MONGODB_URI_SERVER_001=*************************************************************************
ECONOMY_MONGODB_URI_SERVER_001=*******************************************************************************
SOCIAL_MONGODB_URI_SERVER_001=*****************************************************************************
ACTIVITY_MONGODB_URI_SERVER_001=*********************************************************************************
MATCH_MONGODB_URI_SERVER_001=***************************************************************************

# 区服2
CHARACTER_MONGODB_URI_SERVER_002=***********************************************************************************
HERO_MONGODB_URI_SERVER_002=*************************************************************************
# ... 其他服务类似

# 跨服功能配置
CROSS_SERVER_ENABLED=true
CROSS_SERVER_BATTLE_ENABLED=true
CROSS_SERVER_RANKING_ENABLED=true
CROSS_SERVER_ACTIVITY_ENABLED=true

# 合服功能配置
SERVER_MERGE_ENABLED=true
SERVER_MERGE_BACKUP_ENABLED=true
SERVER_MERGE_ROLLBACK_ENABLED=true
```

### **2. 微服务配置扩展**

```typescript
// 各微服务的app.module.ts扩展示例
// apps/character/src/app.module.ts

@Module({
  imports: [
    // ... 现有配置保持不变

    // 数据库配置扩展，支持动态区服数据库
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const serverId = process.env.CURRENT_SERVER_ID || 'server_001';
        const dbUri = configService.get(`CHARACTER_MONGODB_URI_${serverId.toUpperCase()}`) ||
                     configService.get('CHARACTER_MONGODB_URI');

        return {
          uri: dbUri,
          ...createMongoConfig(configService, 'character'),
          ...setupDatabaseEvents('character'),
        };
      },
      inject: [ConfigService],
    }),

    // Redis配置扩展，支持区服隔离
    RedisModule.forRootAsync({
      service: 'character',
      serverId: process.env.CURRENT_SERVER_ID || 'server_001',
    }),

    // 微服务公共库扩展，支持跨服通信
    MicroserviceKitModule.forHybrid(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      {
        services: [
          MICROSERVICE_NAMES.HERO_SERVICE,
          MICROSERVICE_NAMES.ECONOMY_SERVICE,
          MICROSERVICE_NAMES.ACTIVITY_SERVICE,
          MICROSERVICE_NAMES.AUTH_SERVICE, // 新增：需要调用认证服务获取区服信息
        ],
      }
    ),

    // 跨服功能模块（新增）
    CrossServerModule.forRoot({
      enabled: process.env.CROSS_SERVER_ENABLED === 'true',
      services: ['ranking', 'battle', 'activity'],
    }),
  ],
})
export class AppModule {}
```

### **3. 客户端WebSocket使用示例**

```javascript
// 客户端使用示例
const socket = io('ws://localhost:3000', {
  auth: { token: 'account-jwt-token' }
});

// 1. 获取区服列表
socket.emit('message', {
  id: 'msg_001',
  command: 'auth.getServerList',
  payload: {}
});

socket.on('response', (data) => {
  if (data.id === 'msg_001') {
    console.log('区服列表:', data.payload.data.servers);
    // 显示区服选择界面
    showServerSelection(data.payload.data.servers);
  }
});

// 2. 选择区服并进入
function enterServer(serverId, characterId) {
  socket.emit('message', {
    id: 'msg_002',
    command: 'auth.enterServer',
    payload: { serverId, characterId }
  });
}

socket.on('response', (data) => {
  if (data.id === 'msg_002') {
    if (data.payload.success) {
      // 更新Token为角色级Token
      const newToken = data.payload.data.characterToken;
      socket.auth.token = newToken;

      // 进入游戏主界面
      enterGameMain(data.payload.data.character);
    }
  }
});

// 3. 游戏业务请求（自动路由到当前区服）
socket.emit('message', {
  id: 'msg_003',
  command: 'character.getInfo',
  payload: { characterId: 'char_123' }
});

// 4. 跨服功能请求
socket.emit('message', {
  id: 'msg_004',
  command: 'cross.getRanking',
  payload: { category: 'level', limit: 100 }
});
```

## 📊 实施计划

### **阶段一：认证服务扩展（2周）**
1. 扩展Auth Service，添加区服管理模块
2. 实现区服相关WebSocket消息处理
3. 创建区服相关数据表
4. 实现JWT Token扩展

### **阶段二：网关WebSocket扩展（1周）**
1. 扩展WebSocket Gateway，支持区服路由
2. 实现角色级认证机制
3. 实现跨服消息路由
4. 测试WebSocket通信功能

### **阶段三：微服务适配（2周）**
1. 扩展微服务公共库，支持区服上下文
2. 修改Character服务支持区服隔离
3. 适配其他微服务支持区服路由
4. 测试数据隔离功能

### **阶段四：跨服功能（1周）**
1. 实现跨服排行榜
2. 实现跨服战斗匹配
3. 实现跨服活动系统
4. 测试跨服功能

### **阶段五：合服功能（2周）**
1. 实现合服数据迁移
2. 实现冲突解决机制
3. 实现补偿系统
4. 测试合服流程

### **总计：8周完成**

---

> **核心设计理念**: 本方案严格基于现有项目架构，通过**WebSocket消息扩展**、**JWT Token增强**和**微服务公共库扩展**实现分区分服功能，确保与现有系统的完美兼容和最小化改动。

> **关键优势**:
> - ✅ **架构兼容**: 100%基于现有WebSocket通信机制
> - ✅ **数据库设计**: 完全遵循现有.env配置模式
> - ✅ **微服务集成**: 基于现有微服务公共库扩展
> - ✅ **Character服务**: 充分利用现有character服务架构
> - ✅ **Redis集成**: 使用现有Redis v3.0前缀架构
> - ✅ **最小改动**: 现有代码改动量< 10%
