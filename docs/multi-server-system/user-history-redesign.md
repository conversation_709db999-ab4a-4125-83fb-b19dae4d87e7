# User-History服务重新设计方案

## 背景分析

### 当前问题
1. **职责混乱** - user-history模块在Auth服务中，违反了单一职责原则
2. **实时性误区** - 被当作实时数据处理，实际上是快照数据
3. **客户端需求不明确** - 没有从用户体验角度分析真实需求

### 客户端真实需求分析

#### 用户登录流程
```
1. 账户登录 → 获取账户Token
2. 选择区服 → 需要看到：
   - 所有可用服务器列表
   - 自己最近游玩的服务器
   - 最后游玩的角色信息
3. 角色登录 → 进入游戏
```

#### 数据特征分析
| 数据类型 | 实时性 | 更新频率 | 用途 |
|----------|--------|----------|------|
| 服务器列表 | 高 | 实时 | 显示可用服务器 |
| 服务器状态 | 高 | 实时 | 显示在线人数、维护状态 |
| 用户游玩历史 | 低 | 每小时/每天 | 用户体验优化 |
| 角色快照 | 低 | 每天 | 快速角色选择 |

## 重新设计方案

### 1. 架构重新设计

#### 服务拆分
```
apps/profile/                    # 新建Profile服务
├── src/
│   ├── domain/
│   │   ├── user-server-history/     # 用户区服历史
│   │   ├── character-snapshots/     # 角色快照
│   │   ├── user-preferences/        # 用户偏好设置
│   │   └── server-recommendations/  # 服务器推荐
│   ├── infrastructure/
│   │   ├── sync/                    # 数据同步工具
│   │   └── cache/                   # 缓存管理
│   └── interfaces/
│       ├── http/                    # HTTP接口
│       └── grpc/                    # 微服务接口

tools/data-sync/                 # 独立的数据同步工具
├── character-sync/              # 角色数据同步
├── playtime-sync/              # 游戏时长同步
└── server-config-sync/         # 服务器配置同步
```

#### 数据流设计
```mermaid
graph TD
    A[管理后台] --> B[服务器配置表]
    C[各区服Character服务] --> D[定时同步工具]
    D --> E[Profile服务]
    F[Gateway] --> B
    F --> E
    F --> G[客户端]
```

### 2. 数据模型重新设计

#### 服务器配置（管理后台维护）
```typescript
interface ServerConfig {
  serverId: string;
  serverName: string;
  region: string;
  status: 'active' | 'maintenance' | 'new' | 'merged';
  openTime: Date;
  mergeTime?: Date;
  maxPlayers: number;
  currentPlayers: number;
  features: string[];
  recommendedFor: 'new_player' | 'veteran' | 'pvp' | 'pve';
  tags: string[];
}
```

#### 用户服务器历史（定时同步）
```typescript
interface UserServerHistory {
  userId: string;
  servers: {
    serverId: string;
    firstLoginTime: Date;
    lastLoginTime: Date;
    totalPlayTime: number;        // 秒
    totalLoginDays: number;
    characterCount: number;
    maxCharacterLevel: number;
    totalRecharge: number;
    lastCharacter?: {
      characterId: string;
      name: string;
      level: number;
      power: number;
      avatar?: string;
    };
  }[];
  lastSyncTime: Date;
}
```

#### 角色快照（定时同步）
```typescript
interface CharacterSnapshot {
  characterId: string;
  userId: string;
  serverId: string;
  name: string;
  level: number;
  power: number;
  avatar?: string;
  profession?: string;
  guildName?: string;
  lastActiveTime: Date;
  snapshotTime: Date;
  isActive: boolean;
}
```

### 3. 接口设计

#### Gateway聚合接口
```typescript
@Controller('server-discovery')
export class ServerDiscoveryController {
  
  /**
   * 获取服务器列表和用户历史
   * 客户端登录后调用此接口选择区服
   */
  @Get('list')
  @UseGuards(JwtAuthGuard)
  async getServerListWithHistory(@CurrentUser() user: User) {
    const [serverConfigs, userHistory] = await Promise.all([
      this.serverConfigService.getAvailableServers(),
      this.profileService.getUserServerHistory(user.id)
    ]);
    
    return {
      servers: this.enrichServersWithUserData(serverConfigs, userHistory),
      recentServers: this.getRecentServers(userHistory),
      recommendations: this.getServerRecommendations(user.id, serverConfigs),
      lastCharacter: this.getLastPlayedCharacter(userHistory)
    };
  }
  
  /**
   * 获取指定服务器的详细信息
   */
  @Get('servers/:serverId')
  async getServerDetail(@Param('serverId') serverId: string, @CurrentUser() user: User) {
    const [serverConfig, userServerData, characters] = await Promise.all([
      this.serverConfigService.getServerConfig(serverId),
      this.profileService.getUserServerData(user.id, serverId),
      this.profileService.getUserCharacters(user.id, serverId)
    ]);
    
    return {
      server: serverConfig,
      userStats: userServerData,
      characters: characters
    };
  }
}
```

#### Profile服务内部接口
```typescript
@Controller('user-history')
export class UserHistoryController {
  
  /**
   * 获取用户服务器历史
   */
  @Get(':userId/servers')
  async getUserServerHistory(@Param('userId') userId: string) {
    return await this.userHistoryService.getUserServerHistory(userId);
  }
  
  /**
   * 获取用户在指定服务器的角色列表
   */
  @Get(':userId/servers/:serverId/characters')
  async getUserCharacters(
    @Param('userId') userId: string,
    @Param('serverId') serverId: string
  ) {
    return await this.characterSnapshotService.getUserCharacters(userId, serverId);
  }
  
  /**
   * 更新用户服务器数据（供同步工具调用）
   */
  @Post(':userId/servers/:serverId/sync')
  async syncUserServerData(
    @Param('userId') userId: string,
    @Param('serverId') serverId: string,
    @Body() syncData: UserServerSyncData
  ) {
    return await this.userHistoryService.syncUserServerData(userId, serverId, syncData);
  }
}
```

### 4. 数据同步策略

#### 同步工具设计
```typescript
// tools/data-sync/character-sync/character-sync.service.ts
@Injectable()
export class CharacterSyncService {
  
  /**
   * 同步所有区服的角色数据
   */
  @Cron('0 2 * * *') // 每天凌晨2点
  async syncAllServersCharacterData() {
    const servers = await this.serverConfigService.getAllActiveServers();
    
    for (const server of servers) {
      await this.syncServerCharacterData(server.serverId);
    }
  }
  
  /**
   * 同步指定区服的角色数据
   */
  private async syncServerCharacterData(serverId: string) {
    try {
      // 调用Character服务获取所有角色数据
      const characters = await this.characterServiceClient.getAllCharacters(serverId);
      
      // 转换为快照格式
      const snapshots = characters.map(char => this.convertToSnapshot(char, serverId));
      
      // 批量更新Profile服务
      await this.profileServiceClient.updateCharacterSnapshots(serverId, snapshots);
      
      this.logger.log(`同步服务器${serverId}角色数据完成，共${snapshots.length}个角色`);
      
    } catch (error) {
      this.logger.error(`同步服务器${serverId}角色数据失败`, error);
    }
  }
}
```

#### 增量同步策略
```typescript
// 用户登录时触发增量同步
@EventHandler('user.character.login')
export class UserLoginSyncHandler {
  
  async handle(event: CharacterLoginEvent) {
    // 异步更新用户最后登录时间和角色快照
    await this.profileService.updateUserLastLogin(
      event.userId,
      event.serverId,
      event.characterId,
      event.loginTime
    );
    
    // 更新角色快照（如果数据有变化）
    const characterData = await this.characterService.getCharacter(
      event.serverId,
      event.characterId
    );
    
    await this.profileService.updateCharacterSnapshot(
      event.userId,
      event.serverId,
      this.convertToSnapshot(characterData)
    );
  }
}
```

### 5. 缓存策略

#### 多级缓存设计
```typescript
@Injectable()
export class ProfileCacheService {
  
  // L1缓存：内存缓存（最热数据）
  private memoryCache = new Map<string, any>();
  
  // L2缓存：Redis缓存（热数据）
  constructor(private readonly redis: RedisService) {}
  
  /**
   * 获取用户服务器历史（多级缓存）
   */
  async getUserServerHistory(userId: string): Promise<UserServerHistory> {
    const cacheKey = `user_server_history:${userId}`;
    
    // L1缓存
    if (this.memoryCache.has(cacheKey)) {
      return this.memoryCache.get(cacheKey);
    }
    
    // L2缓存
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      const data = JSON.parse(cached);
      this.memoryCache.set(cacheKey, data);
      return data;
    }
    
    // 从数据库获取
    const data = await this.userHistoryRepository.findByUserId(userId);
    
    // 写入缓存
    await this.redis.setex(cacheKey, 3600, JSON.stringify(data)); // 1小时
    this.memoryCache.set(cacheKey, data);
    
    return data;
  }
}
```

### 6. 性能优化

#### 预加载策略
```typescript
// 用户登录时预加载常用数据
@EventHandler('user.account.login')
export class UserLoginPreloadHandler {
  
  async handle(event: AccountLoginEvent) {
    // 异步预加载用户数据到缓存
    Promise.all([
      this.profileCacheService.preloadUserServerHistory(event.userId),
      this.profileCacheService.preloadUserCharacters(event.userId),
      this.serverConfigService.preloadServerConfigs()
    ]).catch(error => {
      this.logger.warn('用户数据预加载失败', error);
    });
  }
}
```

#### 批量查询优化
```typescript
// 批量获取多个用户的数据
async getUserServerHistoryBatch(userIds: string[]): Promise<Map<string, UserServerHistory>> {
  const results = new Map();
  const uncachedUserIds = [];
  
  // 检查缓存
  for (const userId of userIds) {
    const cached = await this.getCachedUserHistory(userId);
    if (cached) {
      results.set(userId, cached);
    } else {
      uncachedUserIds.push(userId);
    }
  }
  
  // 批量查询未缓存的数据
  if (uncachedUserIds.length > 0) {
    const dbResults = await this.userHistoryRepository.findByUserIds(uncachedUserIds);
    
    for (const result of dbResults) {
      results.set(result.userId, result);
      await this.cacheUserHistory(result.userId, result);
    }
  }
  
  return results;
}
```

## 迁移计划

### 阶段1：创建Profile服务（3-5天）
1. 创建Profile服务基础架构
2. 设计数据模型和接口
3. 实现基础的CRUD功能
4. 编写单元测试

### 阶段2：数据迁移（2-3天）
1. 从Auth服务导出user-history数据
2. 转换数据格式
3. 导入到Profile服务
4. 验证数据完整性

### 阶段3：同步工具开发（3-4天）
1. 开发角色数据同步工具
2. 开发游戏时长同步工具
3. 实现增量同步机制
4. 添加监控和告警

### 阶段4：Gateway接口更新（1-2天）
1. 实现新的聚合接口
2. 更新客户端调用
3. 性能测试和优化
4. 上线验证

### 阶段5：清理和优化（1-2天）
1. 删除Auth服务中的user-history模块
2. 清理相关依赖
3. 性能监控和调优
4. 文档更新

## 总结

通过重新设计user-history服务，我们实现了：

1. **职责清晰** - Profile服务专门负责用户档案数据
2. **性能优化** - 通过定时同步和多级缓存提升性能
3. **用户体验** - 快速的服务器选择和角色展示
4. **可维护性** - 独立的服务便于开发和维护
5. **可扩展性** - 支持更多用户档案相关功能

这个设计更符合微服务架构原则，也更好地满足了客户端的真实需求。
