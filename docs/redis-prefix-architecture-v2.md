# Redis前缀架构设计方案 v2.0

## 🎯 设计目标

基于之前的讨论，重新设计一个更简洁、用户友好的Redis前缀架构：

1. **简化前缀结构** - 去掉业务域层，简化为 `{环境}:{项目}:{服务}:{具体键}`
2. **透明化处理** - 用户无需手动构建前缀，系统自动处理
3. **统一Redis模块** - 所有微服务使用统一的RedisModule
4. **装饰器支持** - 缓存装饰器无缝集成新的前缀机制
5. **服务上下文感知** - 自动推断或配置服务身份

## 🏗️ 架构设计

### 前缀格式
```
{环境}:{项目}:{服务}:{具体键}
```

### 示例
```typescript
// 用户会话
'dev:fm:auth:session:user123'

// 网关路由
'prod:fm:gateway:routes'

// 游戏状态  
'test:fm:game:match:456'

// 俱乐部缓存
'dev:fm:club:profile:club789'
```

## 🔧 核心实现

### 前缀处理策略

**采用连接级前缀 + 特殊方法封装的混合策略**：

1. **连接级前缀**：在Redis连接时设置 `keyPrefix`，自动处理大部分操作
2. **特殊方法封装**：对于 `keys()` 等无法自动处理前缀的方法，在RedisService中封装处理逻辑
3. **用户层透明**：用户始终使用业务键名，无需关心前缀细节

**优势**：
- ✅ 性能最优：大部分操作使用底层C++前缀处理
- ✅ 完全透明：用户API保持简洁
- ✅ 逻辑一致：所有Redis操作统一前缀处理

### 1. 增强的RedisModule

#### 支持服务上下文的模块配置
```typescript
// libs/common/src/redis/redis.module.ts
@Global()
@Module({})
export class RedisModule {
  /**
   * 异步模块工厂 - 支持服务上下文
   */
  static forRootAsync(options?: {
    service?: string;           // 服务名称（可选，自动推断）
    database?: number;          // Redis数据库（可选，自动分配）
    useFactory?: (...args: any[]) => any;
    inject?: any[];
  }): DynamicModule {
    return {
      module: RedisModule,
      imports: [ConfigModule],
      providers: [
        // 服务上下文提供者
        {
          provide: 'REDIS_SERVICE_CONTEXT',
          useFactory: (configService: ConfigService) => {
            return options?.service || 
                   configService.get('MICROSERVICE_NAME') ||
                   this.inferServiceFromPath();
          },
          inject: [ConfigService],
        },
        
        // Redis连接提供者
        {
          provide: 'REDIS_CONNECTION',
          useFactory: async (configService: ConfigService, serviceContext: string) => {
            // 构建配置
            const baseConfig = options?.useFactory 
              ? await options.useFactory(configService)
              : this.getDefaultConfig(configService);
            
            // 增强配置
            const enhancedConfig = {
              ...baseConfig,
              db: options?.database || this.getServiceDatabase(serviceContext),
              keyPrefix: this.buildServicePrefix(serviceContext, configService),
            };
            
            const redisService = new RedisService(enhancedConfig, serviceContext);
            await redisService.onModuleInit();
            return redisService;
          },
          inject: [ConfigService, 'REDIS_SERVICE_CONTEXT'],
        },
        
        // Redis服务别名
        {
          provide: RedisService,
          useExisting: 'REDIS_CONNECTION',
        },
        
        // 其他服务
        RedisHealthService,
        RedisCacheService,
        RedisQueueService,
        RedisPubSubService,
        RedisLockService,
        RedisProtectionService,
        RedisBloomFilterService,
        RedisMonitoringService,
        CacheManagerService,
        CacheInterceptor,
        DataSourceFactory,
        CacheStrategyFactory,
      ],
      exports: [
        RedisService,
        'REDIS_SERVICE_CONTEXT',
        RedisHealthService,
        RedisCacheService,
        RedisQueueService,
        RedisPubSubService,
        RedisLockService,
        RedisProtectionService,
        RedisBloomFilterService,
        RedisMonitoringService,
        CacheManagerService,
        CacheInterceptor,
        DataSourceFactory,
        CacheStrategyFactory,
      ],
      global: true,
    };
  }

  /**
   * 自动推断服务名称
   */
  private static inferServiceFromPath(): string {
    const stack = new Error().stack;
    const callerPath = stack?.split('\n')[3] || '';
    
    // 匹配 apps/{service}/src/ 模式
    const match = callerPath.match(/apps[\/\\]([^\/\\]+)[\/\\]/);
    return match ? match[1] : 'unknown';
  }

  /**
   * 获取默认Redis配置
   */
  private static getDefaultConfig(configService: ConfigService) {
    return {
      host: configService.get('REDIS_HOST', 'localhost'),
      port: configService.get('REDIS_PORT', 6379),
      password: configService.get('REDIS_PASSWORD'),
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: false,
      keepAlive: 30000,
      family: 4,
      connectTimeout: 10000,
      commandTimeout: 5000,
    };
  }

  /**
   * 根据服务分配数据库
   */
  private static getServiceDatabase(service: string): number {
    const dbMapping = {
      'gateway': 0,
      'auth': 1,
      'user': 2,
      'game': 3,
      'club': 4,
      'match': 5,
      'player': 6,
      'card': 7,
      'transfer': 8,
      'achievement': 9,
      'notification': 10,
    };
    return dbMapping[service] || 0;
  }

  /**
   * 构建服务前缀
   */
  private static buildServicePrefix(service: string, configService: ConfigService): string {
    const env = configService.get('NODE_ENV', 'dev');
    const project = configService.get('PROJECT_NAME', 'fm');
    return `${env}:${project}:${service}:`;
  }
}
```

### 2. 增强的RedisService

#### 支持透明前缀处理
```typescript
// libs/common/src/redis/redis.service.ts
@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private redis: Redis;
  private subscriber: Redis;
  private serviceContext: string;
  private keyPrefix: string;

  constructor(
    config: any,
    @Inject('REDIS_SERVICE_CONTEXT') serviceContext: string
  ) {
    this.serviceContext = serviceContext;
    this.keyPrefix = config.keyPrefix || '';
    
    // 创建Redis连接（keyPrefix已经在config中设置）
    this.redis = new Redis(config);
    this.subscriber = this.redis.duplicate();
  }

  // ==================== 用户友好的API ====================

  /**
   * 设置键值对 - 用户只需提供业务键
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    // keyPrefix会自动添加，用户无需关心
    const serializedValue = this.serialize(value);
    if (ttl) {
      await this.redis.setex(key, ttl, serializedValue);
    } else {
      await this.redis.set(key, serializedValue);
    }
  }

  /**
   * 获取值 - 用户只需提供业务键
   */
  async get<T>(key: string): Promise<T | null> {
    const value = await this.redis.get(key);
    return value ? this.deserialize<T>(value) : null;
  }

  /**
   * 删除键 - 用户只需提供业务键
   */
  async del(key: string): Promise<number> {
    return await this.redis.del(key);
  }

  /**
   * 模式匹配 - 自动处理前缀
   * 用户传入业务模式，自动添加连接级前缀进行查询，返回时移除前缀保持透明
   */
  async keys(pattern: string): Promise<string[]> {
    // 手动添加连接级前缀到模式（因为redis.keys无法自动应用keyPrefix）
    const keyPrefix = this.redisConfig?.keyPrefix || '';
    const fullPattern = `${keyPrefix}${pattern}`;
    const keys = await this.redis.keys(fullPattern);

    // 返回时移除前缀，保持用户层透明
    return keys.map(key =>
      key.startsWith(keyPrefix) ? key.substring(keyPrefix.length) : key
    );
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    const result = await this.redis.exists(key);
    return result === 1;
  }

  /**
   * 设置过期时间
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    const result = await this.redis.expire(key, seconds);
    return result === 1;
  }

  // ==================== 工具方法 ====================

  /**
   * 获取服务上下文
   */
  getServiceContext(): string {
    return this.serviceContext;
  }

  /**
   * 获取Redis客户端实例（用于高级操作）
   */
  getClient(): Redis {
    return this.redis;
  }

  /**
   * 批量删除匹配的键（自动处理前缀）
   */
  async deletePattern(pattern: string): Promise<number> {
    // 使用封装的keys方法获取匹配的业务键名
    const businessKeys = await this.keys(pattern);
    if (businessKeys.length === 0) {
      return 0;
    }
    // 使用RedisService的del方法，会自动添加前缀
    return await this.del(businessKeys);
  }

  /**
   * 序列化值
   */
  private serialize(value: any): string {
    if (typeof value === 'string') {
      return value;
    }
    return JSON.stringify(value);
  }

  /**
   * 反序列化值
   */
  private deserialize<T>(value: string): T {
    try {
      return JSON.parse(value);
    } catch {
      return value as unknown as T;
    }
  }

  // ==================== 原有方法保持不变 ====================
  // ... 其他现有方法
}
```

### 3. 装饰器无缝集成

#### 缓存装饰器自动处理前缀
```typescript
// libs/common/src/redis/cache/cache.interceptor.ts
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(
    private readonly redisService: RedisService,
    @Inject('REDIS_SERVICE_CONTEXT') private readonly serviceContext: string
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const cacheMetadata = this.getCacheMetadata(context);
    if (!cacheMetadata) {
      return next.handle();
    }

    // 构建缓存键 - 用户提供的键会自动加上服务前缀
    const cacheKey = this.buildCacheKey(cacheMetadata.key, context);
    
    // 检查缓存
    const cachedValue = await this.redisService.get(cacheKey);
    if (cachedValue !== null) {
      return of(cachedValue);
    }

    // 执行方法并缓存结果
    return next.handle().pipe(
      tap(async (result) => {
        if (result !== undefined) {
          await this.redisService.set(cacheKey, result, cacheMetadata.ttl);
        }
      })
    );
  }

  private buildCacheKey(keyTemplate: string, context: ExecutionContext): string {
    // 解析参数占位符
    const args = context.getArgs();
    const parsedKey = this.parseKeyTemplate(keyTemplate, args);
    
    // 返回业务键，RedisService会自动添加前缀
    return parsedKey;
  }

  private parseKeyTemplate(template: string, args: any[]): string {
    // 解析 #{param} 占位符
    return template.replace(/#{(\w+)}/g, (match, paramName) => {
      // 从参数中查找对应值
      const value = this.findParameterValue(paramName, args);
      return value?.toString() || match;
    });
  }
}
```

#### 装饰器使用示例
```typescript
// 用户使用 - 保持简单
@Controller('users')
export class UsersController {
  
  @Cacheable({
    key: 'user:profile:#{userId}',  // 简单的业务键
    ttl: 300
  })
  async getUserProfile(userId: string) {
    // 实际Redis键: dev:fm:auth:user:profile:user123
    // 用户完全不需要关心前缀处理
  }
  
  @CacheEvict({
    key: 'user:profile:#{userId}'  // 相同的业务键
  })
  async updateUserProfile(userId: string, data: any) {
    // 自动清理对应的缓存
  }
  
  @CachePut({
    key: 'user:stats:#{userId}',
    ttl: 600
  })
  async updateUserStats(userId: string) {
    // 更新缓存
  }
}
```

## 📋 微服务配置示例

### 网关服务配置
```typescript
// apps/gateway/src/app.module.ts
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [gatewayConfig],
    }),
    
    // Redis模块 - 自动推断为 'gateway' 服务
    RedisModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        host: configService.get('REDIS_HOST'),
        port: configService.get('REDIS_PORT'),
        password: configService.get('REDIS_PASSWORD'),
      }),
      inject: [ConfigService],
    }),
    
    // 其他模块...
  ],
})
export class AppModule {}
```

### 认证服务配置
```typescript
// apps/auth/src/app.module.ts
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [authConfig],
    }),
    
    // 替换原有的 CacheModule，使用统一的 RedisModule
    RedisModule.forRootAsync({
      service: 'auth',  // 显式指定服务名
      database: 1,      // 可选：指定数据库
      useFactory: (configService: ConfigService) => ({
        host: configService.get('REDIS_HOST'),
        port: configService.get('REDIS_PORT'),
        password: configService.get('REDIS_PASSWORD'),
      }),
      inject: [ConfigService],
    }),
    
    // 移除原有的 CacheModule.registerAsync
    
    // 其他模块...
  ],
})
export class AppModule {}
```

### 用户服务配置
```typescript
// apps/user/src/app.module.ts
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env', '.env.user'],
    }),
    
    // Redis模块 - 通过环境变量指定服务名
    RedisModule.forRootAsync({
      // service: 从 MICROSERVICE_NAME 常量读取
      useFactory: (configService: ConfigService) => ({
        host: configService.get('REDIS_HOST'),
        port: configService.get('REDIS_PORT'),
        password: configService.get('REDIS_PASSWORD'),
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

## 🔄 迁移指南

### 1. 认证服务迁移

#### 移除旧的CacheModule配置
```typescript
// 删除这部分配置
CacheModule.registerAsync({
  isGlobal: true,
  useFactory: async (configService: ConfigService) => ({
    store: redisStore,
    host: configService.get<string>('redis.host'),
    port: configService.get<number>('redis.port'),
    // ...
  }),
  inject: [ConfigService],
}),
```

#### 添加新的RedisModule配置
```typescript
// 添加这部分配置
RedisModule.forRootAsync({
  service: 'auth',
  useFactory: (configService: ConfigService) => ({
    host: configService.get('REDIS_HOST'),
    port: configService.get('REDIS_PORT'),
    password: configService.get('REDIS_PASSWORD'),
  }),
  inject: [ConfigService],
}),
```

### 2. 缓存装饰器迁移

#### 现有装饰器无需修改
```typescript
// 这些装饰器使用方式保持不变
@Cacheable({
  key: 'user:profile:#{userId}',
  ttl: 300
})

@CacheEvict({
  key: 'user:statistics',
  condition: '#{user != null}',
  paramNames: ['user', 'updateUserDto']
})
```

#### 实际效果对比
```typescript
// 迁移前的实际Redis键
'user:profile:user123'

// 迁移后的实际Redis键  
'dev:fm:auth:user:profile:user123'
```

### 3. 备份系统迁移

#### 更新备份服务以适配新前缀架构
```typescript
// libs/common/src/backup/services/redis-backup.service.ts
@Injectable()
export class RedisBackupService {
  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
    @Inject('REDIS_SERVICE_CONTEXT') private readonly serviceContext: string
  ) {}

  /**
   * 生成服务感知的备份模式
   */
  private generateBackupPatterns(): string[] {
    const env = this.configService.get('NODE_ENV', 'dev');
    const project = this.configService.get('PROJECT_NAME', 'fm');

    // 支持的服务列表
    const services = ['gateway', 'auth', 'user', 'game', 'club', 'match', 'player', 'card', 'transfer'];

    // 为每个服务生成备份模式
    const patterns = services.map(service => `${env}:${project}:${service}:*`);

    // 支持自定义模式
    const customPatterns = this.configService.get<string>('REDIS_BACKUP_CUSTOM_PATTERNS', '');
    if (customPatterns) {
      patterns.push(...customPatterns.split(','));
    }

    return patterns;
  }

  /**
   * 按数据库进行备份
   */
  async backupByDatabase(): Promise<BackupResult> {
    const databaseMapping = {
      0: 'gateway',
      1: 'auth',
      2: 'user',
      3: 'game',
      4: 'club',
      5: 'match',
      6: 'player',
      7: 'card',
      8: 'transfer',
    };

    const backupResults = [];

    for (const [db, service] of Object.entries(databaseMapping)) {
      const dbNumber = parseInt(db);
      const client = this.redisService.getClient().duplicate();
      await client.select(dbNumber);

      const keys = await client.keys('*');
      if (keys.length > 0) {
        const result = await this.backupDatabase(dbNumber, service, keys);
        backupResults.push(result);
      }

      await client.quit();
    }

    return this.combineBackupResults(backupResults);
  }

  /**
   * 按服务进行备份
   */
  async backupByService(targetService?: string): Promise<BackupResult> {
    const env = this.configService.get('NODE_ENV', 'dev');
    const project = this.configService.get('PROJECT_NAME', 'fm');

    if (targetService) {
      // 备份特定服务
      const pattern = `${env}:${project}:${targetService}:*`;
      return await this.backupServiceData(targetService, pattern);
    } else {
      // 备份所有服务
      const patterns = this.generateBackupPatterns();
      return await this.backupMultipleServices(patterns);
    }
  }
}
```

#### 更新备份配置文件
```bash
# .env.backup - 新的备份配置
# 备份策略：按服务备份
REDIS_BACKUP_STRATEGY=service  # service | database | pattern

# 要备份的服务列表（逗号分隔，空表示全部）
REDIS_BACKUP_SERVICES=gateway,auth,user,game,club

# 要备份的数据库列表（逗号分隔，空表示全部）
REDIS_BACKUP_DATABASES=0,1,2,3,4

# 自定义备份模式（可选）
REDIS_BACKUP_CUSTOM_PATTERNS=

# 排除模式（支持新前缀格式）
REDIS_BACKUP_EXCLUDE_PATTERNS=*:lock:*,*:temp:*,*:rate_limit:*

# 环境特定配置
REDIS_BACKUP_ENVIRONMENT=dev
REDIS_BACKUP_PROJECT=fm
```

### 4. 环境变量配置

#### 添加服务标识环境变量
```bash
# apps/gateway/.env
MICROSERVICE_NAME=gateway
NODE_ENV=development
PROJECT_NAME=fm

# apps/auth/.env
MICROSERVICE_NAME=auth
NODE_ENV=development
PROJECT_NAME=fm
```

## 🎯 优势总结

### 1. 用户体验优化
- ✅ **零学习成本** - 现有装饰器使用方式不变
- ✅ **透明处理** - 前缀自动添加，用户无需关心
- ✅ **简单直观** - 只需要写业务键名

### 2. 架构优化
- ✅ **统一模块** - 所有微服务使用相同的RedisModule
- ✅ **服务隔离** - 自动的环境和服务前缀
- ✅ **数据库分离** - 不同服务使用不同数据库

### 3. 运维优化
- ✅ **环境隔离** - dev/test/prod完全分离
- ✅ **服务识别** - 键名包含服务信息
- ✅ **监控友好** - 便于按服务统计和监控

### 4. 开发优化
- ✅ **自动推断** - 服务名自动推断，减少配置
- ✅ **类型安全** - 保持现有的类型检查
- ✅ **向后兼容** - 平滑迁移，不破坏现有功能

## 📝 实施计划

### 阶段1：核心模块开发（1-2天）
1. 修改 `RedisModule` 支持服务上下文
2. 增强 `RedisService` 支持透明前缀
3. 更新缓存拦截器集成新机制

### 阶段2：微服务迁移（2-3天）
1. 迁移认证服务到新的RedisModule
2. 迁移其他微服务配置
3. 验证缓存装饰器正常工作

### 阶段3：测试验证（1天）
1. 运行现有测试确保兼容性
2. 验证Redis键格式正确
3. 测试跨环境隔离效果

### 阶段4：文档更新（1天）
1. 更新使用文档
2. 创建迁移指南
3. 更新最佳实践

## 🧪 测试策略

### 1. 单元测试
```typescript
// libs/common/src/redis/redis.service.spec.ts
describe('RedisService with Service Context', () => {
  let service: RedisService;
  let mockRedis: jest.Mocked<Redis>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        {
          provide: 'REDIS_SERVICE_CONTEXT',
          useValue: 'auth',
        },
        {
          provide: RedisService,
          useFactory: () => new RedisService(mockConfig, 'auth'),
        },
      ],
    }).compile();

    service = module.get<RedisService>(RedisService);
  });

  it('should add correct prefix to keys', async () => {
    await service.set('user:profile:123', { name: 'test' });

    expect(mockRedis.set).toHaveBeenCalledWith(
      'user:profile:123',  // 业务键
      '{"name":"test"}'
    );

    // 实际Redis键应该是: dev:fm:auth:user:profile:123
  });

  it('should handle cache decorators correctly', async () => {
    // 测试装饰器集成
  });
});
```

### 2. 集成测试
```typescript
// test/redis-integration.spec.ts
describe('Redis Integration with Service Context', () => {
  let app: INestApplication;
  let redisService: RedisService;

  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [
        RedisModule.forRootAsync({
          service: 'test',
          useFactory: () => ({
            host: 'localhost',
            port: 6379,
          }),
        }),
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    redisService = app.get<RedisService>(RedisService);
    await app.init();
  });

  it('should isolate keys by service', async () => {
    await redisService.set('test:key', 'value');

    // 验证键格式
    const keys = await redisService.keys('test:*');
    expect(keys).toContain('test:key');

    // 验证实际Redis中的键包含前缀
    const client = redisService.getClient();
    const fullKeys = await client.keys('*');
    expect(fullKeys.some(key => key.includes('test:test:key'))).toBe(true);
  });
});
```

### 3. 端到端测试
```typescript
// test/e2e/cache-decorator.e2e-spec.ts
describe('Cache Decorator E2E', () => {
  let app: INestApplication;

  @Controller('test')
  class TestController {
    @Cacheable({
      key: 'user:profile:#{id}',
      ttl: 300
    })
    async getUser(id: string) {
      return { id, name: `User ${id}` };
    }
  }

  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [
        RedisModule.forRootAsync({
          service: 'test',
          useFactory: () => testRedisConfig,
        }),
      ],
      controllers: [TestController],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('should cache with correct prefix', async () => {
    const response = await request(app.getHttpServer())
      .get('/test/user/123')
      .expect(200);

    expect(response.body).toEqual({ id: '123', name: 'User 123' });

    // 验证缓存键格式
    const redisService = app.get<RedisService>(RedisService);
    const cachedValue = await redisService.get('user:profile:123');
    expect(cachedValue).toEqual({ id: '123', name: 'User 123' });
  });
});
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 服务名推断失败
```typescript
// 问题：自动推断的服务名不正确
// 解决：显式指定服务名
RedisModule.forRootAsync({
  service: 'auth',  // 明确指定
  useFactory: (configService: ConfigService) => ({...}),
})
```

#### 2. 键前缀重复
```typescript
// 问题：键前缀被重复添加
// 原因：用户手动添加了前缀
// 错误示例
await redisService.set('dev:fm:auth:user:123', data);  // ❌

// 正确示例
await redisService.set('user:123', data);  // ✅
```

#### 3. 缓存装饰器不工作
```typescript
// 问题：装饰器缓存失效
// 检查：确保CacheInterceptor正确注册
@Module({
  providers: [
    // 确保CacheInterceptor在providers中
    CacheInterceptor,
  ],
})

// 或者全局注册
app.useGlobalInterceptors(new CacheInterceptor(redisService));
```

#### 4. 跨服务键访问
```typescript
// 问题：需要访问其他服务的Redis键
// 解决：使用完整键名或专门的跨服务方法
class CrossServiceRedisService {
  async getFromService(service: string, key: string) {
    const fullKey = `${env}:fm:${service}:${key}`;
    return await this.redis.get(fullKey);
  }
}
```

## 📊 性能考虑

### 1. 连接池优化
```typescript
// Redis连接池配置
const redisConfig = {
  host: 'localhost',
  port: 6379,
  // 连接池配置
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  lazyConnect: false,
  keepAlive: 30000,

  // 性能优化
  enableAutoPipelining: true,  // 自动管道化
  maxRedirections: 16,         // 集群重定向
};
```

### 2. 键命名优化
```typescript
// 推荐的键命名模式
const goodKeys = [
  'user:profile:123',           // ✅ 简洁明了
  'session:active:user456',     // ✅ 层次清晰
  'cache:club:stats:789',       // ✅ 功能明确
];

// 避免的键命名模式
const badKeys = [
  'very:long:nested:key:structure:that:is:hard:to:read',  // ❌ 过于复杂
  'user123profile',                                        // ❌ 缺乏分隔
  'temp_data_for_user_123_in_game_456',                   // ❌ 下划线混用
];
```

### 3. 缓存策略优化
```typescript
// 不同类型数据的TTL建议
export const CacheTTL = {
  USER_SESSION: 7200,      // 2小时 - 用户会话
  USER_PROFILE: 3600,      // 1小时 - 用户档案
  GAME_STATE: 1800,        // 30分钟 - 游戏状态
  CLUB_STATS: 600,         // 10分钟 - 俱乐部统计
  LEADERBOARD: 300,        // 5分钟 - 排行榜
  REAL_TIME_DATA: 60,      // 1分钟 - 实时数据
};
```

## 🔒 安全考虑

### 1. 键名安全
```typescript
// 键名验证
class RedisKeyValidator {
  static validateKey(key: string): boolean {
    // 检查键名长度
    if (key.length > 512) return false;

    // 检查危险字符
    if (/[<>\"'&\x00-\x1f\x7f-\x9f]/.test(key)) return false;

    // 检查路径遍历
    if (key.includes('..')) return false;

    return true;
  }

  static sanitizeKey(key: string): string {
    return key
      .replace(/[^a-zA-Z0-9:_-]/g, '')  // 移除特殊字符
      .substring(0, 512);               // 限制长度
  }
}
```

### 2. 访问控制
```typescript
// 基于服务的访问控制
@Injectable()
export class RedisAccessGuard {
  canAccess(requestingService: string, targetKey: string): boolean {
    const keyParts = targetKey.split(':');
    if (keyParts.length < 3) return false;

    const [env, project, service] = keyParts;

    // 只能访问自己服务的键
    return service === requestingService;
  }
}
```

## 📈 监控和指标

### 1. Redis使用监控
```typescript
// Redis监控服务
@Injectable()
export class RedisMonitoringService {
  private metrics = new Map<string, number>();

  recordOperation(service: string, operation: string, key: string) {
    const metricKey = `${service}:${operation}`;
    this.metrics.set(metricKey, (this.metrics.get(metricKey) || 0) + 1);
  }

  getServiceMetrics(service: string) {
    const serviceMetrics = {};
    for (const [key, value] of this.metrics.entries()) {
      if (key.startsWith(`${service}:`)) {
        serviceMetrics[key] = value;
      }
    }
    return serviceMetrics;
  }
}
```

### 2. 性能指标
```typescript
// 性能监控装饰器
export function MonitorRedisPerformance() {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function(...args: any[]) {
      const start = Date.now();
      try {
        const result = await originalMethod.apply(this, args);
        const duration = Date.now() - start;

        // 记录性能指标
        this.monitoringService?.recordPerformance(propertyKey, duration);

        return result;
      } catch (error) {
        // 记录错误指标
        this.monitoringService?.recordError(propertyKey, error);
        throw error;
      }
    };
  };
}
```

---

**版本**: 2.0.0
**设计日期**: 2025-07-03
**状态**: 设计完成，待实施
**预计实施时间**: 5-7个工作日
