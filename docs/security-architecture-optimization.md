# 安全架构优化方案：渐进式网络隔离 + IP白名单

## 📋 **方案概述**

本文档详细阐述足球经理游戏服务端的安全架构优化方案，采用渐进式网络隔离结合IP白名单的策略，确保分布式部署环境下的服务安全。

**方案特点**: 改动小、安全性高、配置简单、适合开发阶段

---

## 🎯 **核心目标**

### **1. 网络层安全隔离**
- 确保只有网关服务对外暴露
- 内部微服务完全隐藏在内网
- 建立多层网络防护体系

### **2. 分布式IP访问控制**
- 支持服务部署在不同物理机
- 精确控制服务间访问权限
- 防止内网横向移动攻击

### **3. 开发友好的实施策略**
- 最小化代码改动
- 配置驱动的安全控制
- 开发阶段易于调试和测试

---

## 🏗️ **架构设计**

### **简化的安全架构**

```
┌─────────────────────────────────────────────────────────────────┐
│                        公网 (Internet)                          │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                      网关层 (对外暴露)                           │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                   API Gateway                               │ │
│  │                 ***********0:3000                          │ │
│  │              + IP白名单中间件                                │ │
│  │              + 服务间认证                                    │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────┬───────────────────────────────────────┘
                          │ (IP白名单控制)
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                    微服务层 (内网访问)                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Auth Service   │  │  User Service   │  │  Game Service   │  │
│  │  ***********0   │  │  ************   │  │  ************   │  │
│  │   :3001         │  │   :3002         │  │   :3003         │  │
│  │ + IP白名单      │  │ + IP白名单      │  │ + IP白名单      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ Match Service   │  │  Card Service   │  │  Club Service   │  │
│  │  ************   │  │  ************   │  │  ************   │  │
│  │   :3005         │  │   :3006         │  │   :3004         │  │
│  │ + IP白名单      │  │ + IP白名单      │  │ + IP白名单      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────┬───────────────────────────────────────┘
                          │ (数据库访问控制)
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                      数据层                                     │
│  ┌─────────────────┐  ┌─────────────────┐                      │
│  │    MongoDB      │  │      Redis      │                      │
│  │ *************** │  │ *************** │                      │
│  │   :27017        │  │   :6379         │                      │
│  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────┘
```

### **IP白名单访问控制矩阵**

| 目标服务 | 允许访问的IP | 访问目的 | 端口 |
|---------|-------------|----------|------|
| **Auth Service** | ***********0 (Gateway) | 用户认证、令牌验证 | 3001 |
| | ************ (User Service) | 用户信息验证 | 3001 |
| | 127.0.0.1 (Local) | 本地调试 | 3001 |
| **User Service** | ***********0 (Gateway) | 用户管理操作 | 3002 |
| | ***********0 (Auth Service) | 用户认证回调 | 3002 |
| **Game Service** | ***********0 (Gateway) | 游戏逻辑处理 | 3003 |
| | ************ (Match Service) | 比赛数据同步 | 3003 |
| **Match Service** | ***********0 (Gateway) | 比赛管理 | 3005 |
| | ************ (Game Service) | 游戏状态同步 | 3005 |
| | ************ (Club Service) | 俱乐部数据 | 3005 |
| **Card Service** | ***********0 (Gateway) | 卡片管理 | 3006 |
| | ************ (User Service) | 用户卡片数据 | 3006 |
| **Club Service** | ***********0 (Gateway) | 俱乐部管理 | 3004 |
| | ************ (Match Service) | 比赛结果更新 | 3004 |

---

## 🔧 **技术实现**

### **实施优先级**

| 阶段 | 实施内容 | 优先级 | 改动程度 | 安全效果 |
|------|----------|--------|----------|----------|
| **阶段1** | IP白名单中间件 | 🔴 最高 | 🟢 最小 | 🟢 高 |
| **阶段2** | 服务间认证 | 🟡 高 | 🟡 中等 | 🟢 高 |
| **阶段3** | 配置管理优化 | 🟡 中等 | 🟢 小 | 🟡 中等 |
| **阶段4** | Docker网络隔离 | 🔵 低 | 🔴 大 | 🟢 最高 |

> **注**: Docker网络隔离优先级最低，适合生产环境部署时实施

### **阶段1: IP白名单中间件 (最高优先级)**

#### **增强的IP白名单实现**
```typescript
// libs/common/src/security/ip-whitelist.middleware.ts
import { Injectable, NestMiddleware, ForbiddenException, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import { isIP } from 'net';

interface IPNetwork {
  network: string;
  cidr?: number;
  isRange: boolean;
}

@Injectable()
export class IPWhitelistMiddleware implements NestMiddleware {
  private readonly logger = new Logger(IPWhitelistMiddleware.name);
  private readonly allowedNetworks: IPNetwork[];
  private readonly trustedProxies: Set<string>;

  constructor(private readonly configService: ConfigService) {
    // 从环境变量加载IP白名单配置
    const allowedIPsStr = this.configService.get<string>('ALLOWED_IPS', '127.0.0.1');
    const trustedProxiesStr = this.configService.get<string>('TRUSTED_PROXIES', '');

    this.allowedNetworks = this.parseNetworkConfig(allowedIPsStr);
    this.trustedProxies = new Set(trustedProxiesStr.split(',').map(ip => ip.trim()).filter(Boolean));

    this.logger.log(`Loaded ${this.allowedNetworks.length} allowed networks`);
    this.logger.log(`Loaded ${this.trustedProxies.size} trusted proxies`);
  }

  use(req: Request, res: Response, next: NextFunction) {
    const clientIP = this.extractClientIP(req);

    // 开发环境跳过检查
    if (this.isDevelopmentMode() && this.isLocalIP(clientIP)) {
      this.logger.debug(`Development mode: allowing local IP ${clientIP}`);
      next();
      return;
    }

    const isAllowed = this.isIPAllowed(clientIP);

    if (!isAllowed) {
      this.logger.warn(`Access denied for IP: ${clientIP}, User-Agent: ${req.headers['user-agent']}`);
      throw new ForbiddenException(`Access denied for IP: ${clientIP}`);
    }

    // 记录成功访问
    this.logger.debug(`Access granted for IP: ${clientIP}`);
    next();
  }

  /**
   * 增强的IP提取逻辑，支持代理链和IPv6
   */
  private extractClientIP(req: Request): string {
    // 1. 检查 X-Forwarded-For 头（支持代理链）
    const xForwardedFor = req.headers['x-forwarded-for'];
    if (xForwardedFor) {
      const ips = xForwardedFor.toString().split(',').map(ip => ip.trim());
      // 从右到左查找第一个非信任代理的IP
      for (let i = ips.length - 1; i >= 0; i--) {
        const ip = this.normalizeIP(ips[i]);
        if (this.isValidIP(ip) && !this.trustedProxies.has(ip)) {
          return ip;
        }
      }
      // 如果都是信任代理，返回最左边的IP
      const firstIP = this.normalizeIP(ips[0]);
      if (this.isValidIP(firstIP)) {
        return firstIP;
      }
    }

    // 2. 检查 X-Real-IP 头
    const xRealIP = req.headers['x-real-ip'];
    if (xRealIP) {
      const ip = this.normalizeIP(xRealIP.toString());
      if (this.isValidIP(ip)) {
        return ip;
      }
    }

    // 3. 检查连接信息
    const connectionIP = req.connection?.remoteAddress || req.socket?.remoteAddress;
    if (connectionIP) {
      const ip = this.normalizeIP(connectionIP);
      if (this.isValidIP(ip)) {
        return ip;
      }
    }

    // 4. 默认返回本地IP
    return '127.0.0.1';
  }

  /**
   * 解析网络配置，支持CIDR和IP范围
   */
  private parseNetworkConfig(configStr: string): IPNetwork[] {
    return configStr.split(',')
      .map(item => item.trim())
      .filter(Boolean)
      .map(item => {
        // 支持CIDR格式 (***********/24)
        if (item.includes('/')) {
          const [network, cidr] = item.split('/');
          return {
            network: this.normalizeIP(network),
            cidr: parseInt(cidr, 10),
            isRange: true
          };
        }
        // 单个IP地址
        return {
          network: this.normalizeIP(item),
          isRange: false
        };
      })
      .filter(item => this.isValidIP(item.network));
  }

  /**
   * 检查IP是否在允许列表中
   */
  private isIPAllowed(ip: string): boolean {
    const normalizedIP = this.normalizeIP(ip);

    for (const network of this.allowedNetworks) {
      if (!network.isRange) {
        // 精确匹配
        if (network.network === normalizedIP) {
          return true;
        }
      } else {
        // CIDR匹配
        if (this.isIPInCIDR(normalizedIP, network.network, network.cidr!)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 规范化IP地址（移除IPv6的::ffff:前缀）
   */
  private normalizeIP(ip: string): string {
    if (!ip) return '';

    // 移除IPv4映射的IPv6前缀
    if (ip.startsWith('::ffff:')) {
      return ip.substring(7);
    }

    // 移除端口号
    if (ip.includes(':') && !ip.includes('::')) {
      const lastColonIndex = ip.lastIndexOf(':');
      const possiblePort = ip.substring(lastColonIndex + 1);
      if (/^\d+$/.test(possiblePort)) {
        return ip.substring(0, lastColonIndex);
      }
    }

    return ip;
  }

  /**
   * 验证IP地址格式
   */
  private isValidIP(ip: string): boolean {
    return isIP(ip) !== 0;
  }

  /**
   * 检查是否为本地IP
   */
  private isLocalIP(ip: string): boolean {
    return ip === '127.0.0.1' || ip === '::1' || ip === 'localhost';
  }

  /**
   * 检查是否为开发模式
   */
  private isDevelopmentMode(): boolean {
    return process.env.NODE_ENV === 'development';
  }

  /**
   * 检查IP是否在CIDR网段内
   */
  private isIPInCIDR(ip: string, network: string, cidr: number): boolean {
    // 简化实现，仅支持IPv4 CIDR
    if (isIP(ip) !== 4 || isIP(network) !== 4) {
      return false;
    }

    const ipNum = this.ipToNumber(ip);
    const networkNum = this.ipToNumber(network);
    const mask = (0xffffffff << (32 - cidr)) >>> 0;

    return (ipNum & mask) === (networkNum & mask);
  }

  /**
   * 将IPv4地址转换为数字
   */
  private ipToNumber(ip: string): number {
    return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet, 10), 0) >>> 0;
  }
}
```

#### **增强的环境变量配置**

```bash
# .env 文件配置示例

# ===== 网关服务配置 =====
# 网关对外开放，但建议限制可信网段
GATEWAY_ALLOWED_IPS=***********/16,10.0.0.0/8,**********/12,127.0.0.1
# 信任的代理服务器（如负载均衡器、CDN）
GATEWAY_TRUSTED_PROXIES=***********,***********

# ===== 微服务IP白名单配置 =====
# 认证服务 - 只允许网关和用户服务访问
AUTH_ALLOWED_IPS=***********0,************,127.0.0.1
AUTH_TRUSTED_PROXIES=

# 用户服务 - 只允许网关和认证服务访问
USER_ALLOWED_IPS=***********0,***********0,127.0.0.1
USER_TRUSTED_PROXIES=

# 游戏服务 - 只允许网关和比赛服务访问
GAME_ALLOWED_IPS=***********0,************,127.0.0.1
GAME_TRUSTED_PROXIES=

# 比赛服务 - 只允许网关、游戏服务和俱乐部服务访问
MATCH_ALLOWED_IPS=***********0,************,************,127.0.0.1
MATCH_TRUSTED_PROXIES=

# 卡片服务 - 只允许网关和用户服务访问
CARD_ALLOWED_IPS=***********0,************,127.0.0.1
CARD_TRUSTED_PROXIES=

# 俱乐部服务 - 只允许网关和比赛服务访问
CLUB_ALLOWED_IPS=***********0,************,127.0.0.1
CLUB_TRUSTED_PROXIES=

# 通知服务 - 只允许网关访问
NOTIFICATION_ALLOWED_IPS=***********0,127.0.0.1
NOTIFICATION_TRUSTED_PROXIES=

# ===== 配置说明 =====
# 支持的格式：
# - 单个IP: ***********0
# - CIDR网段: ***********/24
# - 多个IP用逗号分隔: ***********0,***********0,127.0.0.1
# - 私有网段: ***********/16, 10.0.0.0/8, **********/12
```

#### **服务集成示例**

```typescript
// apps/auth/src/main.ts
import { IPWhitelistMiddleware } from '@common/security';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 应用IP白名单中间件
  app.use(new IPWhitelistMiddleware(app.get(ConfigService)));

  await app.listen(3001);
}
```

### **阶段2: 服务间认证 (高优先级)**

#### **简化的服务间认证**
```typescript
// libs/common/src/security/service-auth.middleware.ts
@Injectable()
export class ServiceAuthMiddleware implements NestMiddleware {
  private readonly serviceSecret: string;

  constructor(private readonly configService: ConfigService) {
    this.serviceSecret = this.configService.get<string>('SERVICE_SECRET', 'default-secret');
  }

  use(req: Request, res: Response, next: NextFunction) {
    const serviceToken = req.headers['x-service-token'] as string;

    // 开发环境跳过检查
    if (process.env.NODE_ENV === 'development') {
      next();
      return;
    }

    if (!serviceToken || serviceToken !== this.serviceSecret) {
      throw new UnauthorizedException('Invalid service token');
    }

    next();
  }
}
```

#### **环境变量配置**
```bash
# 所有服务使用相同的服务密钥（简化配置）
SERVICE_SECRET=your-strong-service-secret-key-here

# 或者为每个服务配置不同的密钥
AUTH_SERVICE_SECRET=auth-secret-key
USER_SERVICE_SECRET=user-secret-key
GAME_SERVICE_SECRET=game-secret-key
```

### **阶段3: 配置管理优化 (中等优先级)**

#### **统一安全配置管理**
```typescript
// libs/common/src/security/config/security.config.ts
import { registerAs } from '@nestjs/config';

export interface SecurityConfig {
  ipWhitelist: {
    enabled: boolean;
    allowedIPs: string[];
    trustedProxies: string[];
    developmentMode: boolean;
  };
  serviceAuth: {
    enabled: boolean;
    serviceSecret: string;
    tokenExpiry: number;
    requireHttps: boolean;
  };
  monitoring: {
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    logSuccessfulAccess: boolean;
    logFailedAccess: boolean;
    enableMetrics: boolean;
  };
  rateLimit: {
    enabled: boolean;
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests: boolean;
  };
}

export const securityConfig = registerAs('security', (): SecurityConfig => ({
  ipWhitelist: {
    enabled: process.env.ENABLE_IP_WHITELIST !== 'false', // 默认启用
    allowedIPs: process.env.ALLOWED_IPS?.split(',').map(ip => ip.trim()).filter(Boolean) || ['127.0.0.1'],
    trustedProxies: process.env.TRUSTED_PROXIES?.split(',').map(ip => ip.trim()).filter(Boolean) || [],
    developmentMode: process.env.NODE_ENV === 'development',
  },
  serviceAuth: {
    enabled: process.env.ENABLE_SERVICE_AUTH !== 'false', // 默认启用
    serviceSecret: process.env.SERVICE_SECRET || 'default-secret-change-in-production',
    tokenExpiry: parseInt(process.env.SERVICE_TOKEN_EXPIRY || '3600', 10), // 1小时
    requireHttps: process.env.NODE_ENV === 'production',
  },
  monitoring: {
    logLevel: (process.env.SECURITY_LOG_LEVEL as any) || 'info',
    logSuccessfulAccess: process.env.LOG_SUCCESSFUL_ACCESS === 'true',
    logFailedAccess: process.env.LOG_FAILED_ACCESS !== 'false', // 默认记录失败访问
    enableMetrics: process.env.ENABLE_SECURITY_METRICS === 'true',
  },
  rateLimit: {
    enabled: process.env.RATE_LIMIT_ENABLED === 'true',
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000', 10), // 1分钟
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
    skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESS === 'true',
  },
}));

/**
 * 配置验证器
 */
export class SecurityConfigValidator {
  static validate(config: SecurityConfig): string[] {
    const errors: string[] = [];

    // 验证IP白名单配置
    if (config.ipWhitelist.enabled && config.ipWhitelist.allowedIPs.length === 0) {
      errors.push('IP白名单已启用但未配置允许的IP地址');
    }

    // 验证服务认证配置
    if (config.serviceAuth.enabled) {
      if (!config.serviceAuth.serviceSecret || config.serviceAuth.serviceSecret === 'default-secret-change-in-production') {
        errors.push('生产环境必须配置安全的服务密钥');
      }
      if (config.serviceAuth.tokenExpiry < 300) {
        errors.push('服务令牌过期时间不能少于5分钟');
      }
    }

    // 验证生产环境配置
    if (process.env.NODE_ENV === 'production') {
      if (!config.serviceAuth.requireHttps) {
        errors.push('生产环境建议启用HTTPS');
      }
      if (config.ipWhitelist.allowedIPs.includes('0.0.0.0/0')) {
        errors.push('生产环境不建议允许所有IP访问');
      }
    }

    return errors;
  }
}
```

#### **配置模块集成**
```typescript
// libs/common/src/security/network-security.module..ts
import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { securityConfig, SecurityConfigValidator } from './config/security.config';
import { IPWhitelistMiddleware } from './ip-whitelist.middleware';
import { ServiceAuthMiddleware } from './service-auth.middleware';

@Global()
@Module({
  imports: [
    ConfigModule.forFeature(securityConfig),
  ],
  providers: [
    IPWhitelistMiddleware,
    ServiceAuthMiddleware,
    {
      provide: 'SECURITY_CONFIG_VALIDATOR',
      useFactory: (config) => {
        const errors = SecurityConfigValidator.validate(config);
        if (errors.length > 0) {
          throw new Error(`安全配置验证失败:\n${errors.join('\n')}`);
        }
        return config;
      },
      inject: [securityConfig.KEY],
    },
  ],
  exports: [
    IPWhitelistMiddleware,
    ServiceAuthMiddleware,
  ],
})
export class NetworkSecurityModule {}
```

#### **完整的环境变量配置模板**
```bash
# ===== 安全配置模板 =====
# 复制此模板到各服务的 .env 文件中

# IP白名单配置
ENABLE_IP_WHITELIST=true
ALLOWED_IPS=***********0,127.0.0.1
TRUSTED_PROXIES=

# 服务间认证配置
ENABLE_SERVICE_AUTH=true
SERVICE_SECRET=your-strong-secret-key-here
SERVICE_TOKEN_EXPIRY=3600

# 监控和日志配置
SECURITY_LOG_LEVEL=info
LOG_SUCCESSFUL_ACCESS=false
LOG_FAILED_ACCESS=true
ENABLE_SECURITY_METRICS=false

# 频率限制配置（可选）
RATE_LIMIT_ENABLED=false
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESS=false

# 环境配置
NODE_ENV=development
```

### **阶段4: Docker网络隔离 (最低优先级)**

> **注意**: 此阶段适合生产环境部署，开发阶段可跳过

#### **生产环境Docker配置**
```yaml
# docker-compose.production.yml
version: '3.8'
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true

services:
  gateway:
    networks:
      - frontend
      - backend
    ports:
      - "3000:3000"

  auth-service:
    networks:
      - backend
    # 不暴露端口
```

---

## 🚀 **实施指南**

### **快速开始 (5分钟)**

1. **安装IP白名单中间件**
```bash
# 复制中间件文件
cp libs/common/src/security/ip-whitelist.middleware.ts your-service/

# 配置环境变量
echo "ALLOWED_IPS=***********0,127.0.0.1" >> .env
```

2. **集成到服务**
```typescript
// 在 main.ts 中添加
app.use(new IPWhitelistMiddleware(app.get(ConfigService)));
```

3. **测试验证**
```bash
# 从允许的IP测试
curl http://***********0:3001/health

# 从不允许的IP测试（应该返回403）
curl http://************:3001/health
```

### **配置检查清单**

#### **基础配置检查**
- [ ] 每个服务配置了正确的ALLOWED_IPS
- [ ] 网关配置了合适的网段而非0.0.0.0/0
- [ ] 开发环境允许127.0.0.1访问
- [ ] 生产环境禁用开发模式跳过
- [ ] 服务间调用使用正确的IP地址

#### **安全配置检查**
- [ ] 生产环境使用强密钥（非默认值）
- [ ] 配置了信任的代理服务器列表
- [ ] 启用了访问日志记录
- [ ] 验证了CIDR网段配置正确性
- [ ] 测试了IPv4和IPv6地址处理

#### **网络配置检查**
- [ ] 确认服务部署的实际IP地址
- [ ] 验证负载均衡器和代理配置
- [ ] 测试跨网段的服务通信
- [ ] 确认防火墙规则与IP白名单一致

#### **监控配置检查**
- [ ] 配置了安全事件日志级别
- [ ] 启用了失败访问记录
- [ ] 设置了合理的日志保留策略
- [ ] 验证了配置验证器正常工作

---

## 📝 **讨论记录**

### **讨论状态**: 🔄 持续更新中
### **最后更新**: 2025-01-28

#### **已确认的设计要点**:
- ✅ 采用渐进式网络隔离策略
- ✅ 支持分布式物理机部署
- ✅ IP白名单精确控制服务间访问
- ✅ 配置简单，改动最小

### **讨论阶段2: 方案简化和优先级调整** (2025-01-28)

#### **用户要求**:
1. **改动小**: 最小化代码修改，易于集成
2. **安全性高**: 保证核心安全防护效果
3. **配置简单**: 避免复杂的配置文件和热重载
4. **Docker优先级降低**: 开发阶段暂不使用Docker网络隔离

#### **方案调整**:
- ✅ **简化IP白名单实现**: 移除复杂的配置文件和热重载机制
- ✅ **环境变量配置**: 使用简单的环境变量替代JSON配置文件
- ✅ **开发友好**: 开发环境自动跳过安全检查
- ✅ **优先级重排**: IP白名单 > 服务间认证 > 配置管理 > Docker隔离

#### **实施效果**:
- **5分钟快速集成**: 复制中间件 + 配置环境变量 + 一行代码集成
- **零配置文件**: 完全基于环境变量配置
- **开发友好**: 本地开发无需额外配置

#### **待讨论的技术细节**:
- 🔄 服务间认证的具体实现方式
- 🔄 生产环境的部署策略
- 🔄 监控和日志记录方案
- 🔄 故障排查和调试方法

---

**状态**: 🔄 等待进一步讨论和完善  
**下次更新**: 根据讨论结果更新技术实现细节
