# 游戏客户端模拟器 - API参考文档

## 🎯 核心API

### GameClient 类

#### 构造函数
```javascript
const client = new GameClient(options);
```

**参数**：
- `options.gateway` (string) - 网关地址，默认 'ws://localhost:3000'
- `options.timeout` (number) - 请求超时时间，默认 30000ms
- `options.autoReconnect` (boolean) - 是否自动重连，默认 true

#### 主要方法

##### connect()
连接到游戏服务器
```javascript
await client.connect();
```

##### performAction(actionName, params)
执行游戏操作
```javascript
const result = await client.performAction('auth.login', {
  username: 'testuser',
  password: 'password123'
});
```

##### playScenario(scenarioName, playerData)
执行游戏场景
```javascript
const result = await client.playScenario('newbie-flow', {
  username: 'newplayer',
  characterName: '新手玩家'
});
```

##### callAPI(command, data)
直接调用游戏API
```javascript
const result = await client.callAPI('character.character.getInfo', {
  characterId: 'char_123'
});
```

### VirtualPlayer 类

#### 构造函数
```javascript
const player = new VirtualPlayer(client, playerData);
```

**参数**：
- `client` (GameClient) - 游戏客户端实例
- `playerData` (Object) - 玩家数据

#### 主要方法

##### perform(actionName, params)
执行游戏操作
```javascript
await player.perform('auth.login');
await player.perform('character.create', { name: '测试角色' });
```

##### getGameState(key)
获取游戏状态
```javascript
const character = player.getGameState('character');
const heroes = player.getGameState('heroes');
```

## 🎮 游戏操作API

### 认证操作 (auth.*)

#### auth.register
用户注册
```javascript
await client.performAction('auth.register', {
  username: 'newuser',      // 必需：用户名
  password: 'password123',  // 必需：密码
  email: '<EMAIL>' // 必需：邮箱
});
```

#### auth.login
用户登录
```javascript
await client.performAction('auth.login', {
  username: 'testuser',     // 必需：用户名
  password: 'password123'   // 必需：密码
});
```

#### auth.logout
用户登出
```javascript
await client.performAction('auth.logout');
```

### 角色操作 (character.*)

#### character.create
创建角色
```javascript
await client.performAction('character.create', {
  name: '我的角色',         // 必需：角色名
  serverId: 'server_001'   // 可选：服务器ID
});
```

#### character.getInfo
获取角色信息
```javascript
await client.performAction('character.getInfo', {
  characterId: 'char_123'  // 可选：角色ID，默认使用当前角色
});
```

#### character.updateResources
更新角色资源
```javascript
await client.performAction('character.updateResources', {
  gold: 1000,              // 可选：金币数量
  diamond: 100,            // 可选：钻石数量
  energy: 50               // 可选：体力值
});
```

### 球员操作 (hero.*)

#### hero.recruit
招募球员
```javascript
await client.performAction('hero.recruit', {
  type: 'normal',          // 必需：招募类型 (normal/premium)
  count: 1                 // 可选：招募数量，默认1
});
```

#### hero.getList
获取球员列表
```javascript
await client.performAction('hero.getList', {
  characterId: 'char_123'  // 可选：角色ID
});
```

#### hero.train
训练球员
```javascript
await client.performAction('hero.train', {
  heroId: 'hero_123',      // 必需：球员ID
  trainType: 'speed',      // 必需：训练类型
  duration: 3600           // 可选：训练时长（秒）
});
```

#### hero.evolve
球员升星
```javascript
await client.performAction('hero.evolve', {
  heroId: 'hero_123',      // 必需：球员ID
  materials: ['item1', 'item2'] // 必需：升星材料
});
```

### 社交操作 (social.*)

#### social.createGuild
创建公会
```javascript
await client.performAction('social.createGuild', {
  name: '测试公会',         // 必需：公会名称
  description: '公会描述', // 可选：公会描述
  isPublic: true          // 可选：是否公开
});
```

#### social.joinGuild
加入公会
```javascript
await client.performAction('social.joinGuild', {
  guildId: 'guild_123'    // 必需：公会ID
});
```

#### social.addFriend
添加好友
```javascript
await client.performAction('social.addFriend', {
  friendId: 'user_456'    // 必需：好友用户ID
});
```

### 经济操作 (economy.*)

#### economy.addGold
添加金币
```javascript
await client.performAction('economy.addGold', {
  amount: 1000,           // 必需：金币数量
  reason: '测试充值'       // 可选：充值原因
});
```

#### economy.shopPurchase
商店购买
```javascript
await client.performAction('economy.shopPurchase', {
  itemId: 'item_123',     // 必需：商品ID
  quantity: 1             // 可选：购买数量
});
```

### 比赛操作 (match.*)

#### match.autoFormation
自动布阵
```javascript
await client.performAction('match.autoFormation', {
  formation: '4-4-2'      // 可选：阵型，默认自动选择
});
```

#### match.startMatch
开始比赛
```javascript
await client.performAction('match.startMatch', {
  matchType: 'pve',       // 必需：比赛类型
  opponentId: 'ai_001'    // 可选：对手ID
});
```

## 🎬 场景配置API

### 场景配置格式
```yaml
name: "场景名称"
description: "场景描述"
estimatedTime: "预估时间"

player:
  username: "玩家用户名"
  password: "玩家密码"
  characterName: "角色名称"

steps:
  - action: "操作名称"
    description: "步骤描述"
    params:
      param1: "参数值1"
      param2: "参数值2"
    condition: "执行条件"
    retry: 3
    timeout: 30000

expected:
  - "期望结果1"
  - "期望结果2"
```

### 场景执行API
```javascript
// 加载并执行场景
const result = await client.playScenario('newbie-flow', {
  username: 'testplayer',
  characterName: '新手角色'
});

// 检查执行结果
if (result.success) {
  console.log('场景执行成功:', result.data);
} else {
  console.error('场景执行失败:', result.error);
}
```

## 🛠️ 工具API

### API扫描器
```javascript
const scanner = new APIScanner();

// 扫描网关接口
const interfaces = await scanner.scanGatewayAPIs();

// 生成操作代码
await scanner.generateActions(interfaces);
```

### 数据生成器
```javascript
const generator = new DataGenerator();

// 生成测试用户
const user = generator.generateUser();

// 生成测试角色
const character = generator.generateCharacter();

// 生成测试公会
const guild = generator.generateGuild();
```

## 📊 响应格式

### 标准响应格式
```javascript
{
  success: true,           // 是否成功
  data: {                  // 响应数据
    // 具体数据内容
  },
  message: "操作成功",     // 响应消息
  timestamp: "2024-01-01T00:00:00.000Z", // 时间戳
  duration: 1234           // 执行耗时（毫秒）
}
```

### 错误响应格式
```javascript
{
  success: false,
  error: {
    code: "ERROR_CODE",    // 错误码
    message: "错误描述",   // 错误消息
    details: {             // 错误详情
      // 具体错误信息
    }
  },
  timestamp: "2024-01-01T00:00:00.000Z"
}
```

## 🔧 配置选项

### 客户端配置
```javascript
const config = {
  gateway: {
    url: 'ws://localhost:3000',
    timeout: 30000,
    reconnect: true,
    maxRetries: 5
  },
  logging: {
    level: 'info',
    file: 'logs/simulator.log'
  },
  performance: {
    concurrent: 10,
    delay: 1000
  }
};
```

### 操作配置
```javascript
const actionConfig = {
  timeout: 30000,          // 操作超时时间
  retries: 3,              // 重试次数
  delay: 1000,             // 重试延迟
  validateResponse: true   // 是否验证响应
};
```

---

**下一个文档**：场景编写指南，详细说明如何编写和配置游戏场景。
