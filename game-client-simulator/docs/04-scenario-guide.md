# 游戏客户端模拟器 - 场景编写指南

## 🎬 场景概述

场景是模拟器的核心功能，用于定义一系列游戏操作的组合，模拟真实玩家的游戏流程。每个场景代表一个完整的游戏体验，如新手流程、公会创建、日常任务等。

## 📝 场景配置格式

### 基础YAML格式
```yaml
# scenarios/example-flow.yaml
name: "示例游戏流程"
description: "这是一个示例场景，展示基本的配置格式"
version: "1.0.0"
author: "开发团队"
estimatedTime: "5分钟"
tags: ["示例", "基础", "教程"]

# 玩家配置
player:
  username: "example_player_{{timestamp}}"
  password: "Test123456!"
  email: "test_{{timestamp}}@example.com"
  characterName: "示例角色_{{timestamp}}"
  serverId: "server_001"

# 前置条件
preconditions:
  - condition: "environment === 'test'"
    description: "必须在测试环境执行"
  - condition: "gateway.status === 'online'"
    description: "网关必须在线"

# 执行步骤
steps:
  - name: "用户注册"
    action: "auth.register"
    description: "注册新用户账号"
    params:
      username: "{{player.username}}"
      password: "{{player.password}}"
      email: "{{player.email}}"
    timeout: 10000
    retry: 2
    
  - name: "用户登录"
    action: "auth.login"
    description: "登录到游戏"
    params:
      username: "{{player.username}}"
      password: "{{player.password}}"
    saveAs: "loginResult"
    
  - name: "创建角色"
    action: "character.create"
    description: "创建游戏角色"
    params:
      name: "{{player.characterName}}"
      serverId: "{{player.serverId}}"
    condition: "!context.character"
    saveAs: "character"

# 后置验证
postconditions:
  - condition: "context.loginResult.success === true"
    description: "登录必须成功"
  - condition: "context.character.characterId"
    description: "角色必须创建成功"

# 清理操作
cleanup:
  - action: "auth.logout"
    description: "登出账号"
  - action: "test.cleanup"
    description: "清理测试数据"
```

## 🎮 实际游戏场景示例

### 1. 新手完整流程
```yaml
# scenarios/newbie-complete-flow.yaml
name: "新手完整流程"
description: "模拟新玩家从注册到完成新手引导的完整体验"
estimatedTime: "10分钟"
tags: ["新手", "完整流程", "引导"]

player:
  username: "newbie_{{timestamp}}"
  password: "Newbie123!"
  characterName: "新手玩家_{{timestamp}}"

steps:
  # 账号准备
  - name: "注册账号"
    action: "auth.register"
    description: "注册新手账号"
    
  - name: "登录游戏"
    action: "auth.login"
    description: "首次登录游戏"
    
  # 角色创建
  - name: "创建角色"
    action: "character.create"
    description: "创建游戏角色"
    params:
      name: "{{player.characterName}}"
      serverId: "server_001"
    
  # 新手引导
  - name: "开始新手引导"
    action: "activity.startGuide"
    description: "开始新手引导流程"
    params:
      guideId: "newbie_guide"
    
  - name: "完成基础教学"
    action: "activity.completeGuideStep"
    description: "完成基础操作教学"
    params:
      stepId: "basic_tutorial"
    
  # 初始资源
  - name: "领取新手礼包"
    action: "activity.claimNewbieGift"
    description: "领取新手礼包奖励"
    
  # 球员招募
  - name: "免费招募球员"
    action: "hero.freeRecruit"
    description: "使用免费招募券招募球员"
    
  - name: "查看球员列表"
    action: "hero.getList"
    description: "查看已招募的球员"
    saveAs: "heroList"
    
  # 阵容设置
  - name: "自动布阵"
    action: "match.autoFormation"
    description: "使用自动布阵功能"
    condition: "context.heroList.length >= 11"
    
  # 首场比赛
  - name: "进行首场比赛"
    action: "match.playTutorialMatch"
    description: "进行新手教学比赛"
    
  # 完成引导
  - name: "完成新手引导"
    action: "activity.completeGuide"
    description: "完成整个新手引导流程"

postconditions:
  - condition: "context.character.level >= 2"
    description: "角色等级至少达到2级"
  - condition: "context.heroList.length >= 11"
    description: "至少拥有11名球员"
```

### 2. 公会会长流程
```yaml
# scenarios/guild-master-flow.yaml
name: "公会会长流程"
description: "模拟玩家创建和管理公会的完整流程"
estimatedTime: "15分钟"
tags: ["公会", "管理", "社交"]

player:
  username: "guild_master_{{timestamp}}"
  password: "GuildMaster123!"
  characterName: "会长_{{timestamp}}"

steps:
  # 账号准备
  - name: "登录账号"
    action: "auth.login"
    description: "登录已有账号"
    
  - name: "确保角色存在"
    action: "character.ensureExists"
    description: "确保有可用的角色"
    
  # 资源准备
  - name: "检查金币"
    action: "character.getResources"
    description: "检查当前资源状况"
    saveAs: "resources"
    
  - name: "充值金币"
    action: "economy.addGold"
    description: "确保有足够金币创建公会"
    params:
      amount: 50000
      reason: "公会创建准备"
    condition: "context.resources.gold < 10000"
    
  # 公会创建
  - name: "创建公会"
    action: "social.createGuild"
    description: "创建新公会"
    params:
      name: "测试公会_{{timestamp}}"
      description: "这是一个自动化测试创建的公会"
      isPublic: true
    saveAs: "guild"
    
  # 公会管理
  - name: "设置公会公告"
    action: "social.setGuildNotice"
    description: "设置公会公告"
    params:
      guildId: "{{guild.guildId}}"
      notice: "欢迎加入我们的公会！"
    
  - name: "升级公会"
    action: "social.upgradeGuild"
    description: "升级公会等级"
    params:
      guildId: "{{guild.guildId}}"
    condition: "context.resources.gold >= 20000"
    
  # 成员管理
  - name: "邀请成员"
    action: "social.inviteGuildMembers"
    description: "邀请其他玩家加入公会"
    params:
      guildId: "{{guild.guildId}}"
      count: 3
    
  - name: "查看公会信息"
    action: "social.getGuildInfo"
    description: "查看公会详细信息"
    params:
      guildId: "{{guild.guildId}}"
    saveAs: "guildInfo"

postconditions:
  - condition: "context.guild.success === true"
    description: "公会创建成功"
  - condition: "context.guildInfo.memberCount >= 1"
    description: "公会至少有1名成员（会长）"
```

### 3. 日常玩家流程
```yaml
# scenarios/daily-player-flow.yaml
name: "日常玩家流程"
description: "模拟老玩家的日常游戏操作"
estimatedTime: "20分钟"
tags: ["日常", "老玩家", "常规操作"]

player:
  username: "daily_player_{{timestamp}}"
  password: "DailyPlayer123!"

steps:
  # 登录签到
  - name: "登录游戏"
    action: "auth.login"
    description: "每日登录"
    
  - name: "每日签到"
    action: "activity.dailySign"
    description: "完成每日签到"
    
  # 体力管理
  - name: "检查体力"
    action: "character.getEnergy"
    description: "检查当前体力值"
    saveAs: "energy"
    
  - name: "购买体力"
    action: "economy.buyEnergy"
    description: "购买体力"
    condition: "context.energy.current < 50"
    
  # 球员训练
  - name: "获取球员列表"
    action: "hero.getList"
    description: "获取所有球员"
    saveAs: "heroes"
    
  - name: "训练主力球员"
    action: "hero.trainMainPlayers"
    description: "训练主力阵容球员"
    params:
      heroIds: "{{heroes.mainFormation}}"
      trainType: "comprehensive"
    
  # 比赛活动
  - name: "参加联赛"
    action: "match.playLeague"
    description: "参加联赛比赛"
    retry: 3
    
  - name: "挑战副本"
    action: "match.challengeDungeon"
    description: "挑战副本获取奖励"
    
  # 社交互动
  - name: "查看好友"
    action: "social.getFriendsList"
    description: "查看好友列表"
    saveAs: "friends"
    
  - name: "赠送体力"
    action: "social.sendEnergyToFriends"
    description: "给好友赠送体力"
    params:
      friendIds: "{{friends.activeIds}}"
    
  # 商店购买
  - name: "查看商店"
    action: "economy.getShopItems"
    description: "查看商店商品"
    saveAs: "shopItems"
    
  - name: "购买推荐商品"
    action: "economy.buyRecommendedItems"
    description: "购买推荐商品"
    params:
      items: "{{shopItems.recommended}}"

postconditions:
  - condition: "context.energy.current >= 100"
    description: "体力值充足"
  - condition: "context.heroes.trained > 0"
    description: "至少训练了一名球员"
```

## 🔧 高级场景功能

### 条件执行
```yaml
steps:
  - name: "条件步骤"
    action: "some.action"
    condition: "context.level >= 10"  # 只有等级>=10才执行
    
  - name: "复杂条件"
    action: "another.action"
    condition: "context.gold > 1000 && context.vip === true"
```

### 循环执行
```yaml
steps:
  - name: "重复训练"
    action: "hero.train"
    loop:
      count: 5                    # 重复5次
      condition: "context.energy > 10"  # 满足条件时继续
      delay: 2000                 # 每次间隔2秒
```

### 并行执行
```yaml
steps:
  - name: "并行任务组"
    parallel:
      - action: "hero.train"
        params: { heroId: "hero1" }
      - action: "hero.train"
        params: { heroId: "hero2" }
      - action: "hero.train"
        params: { heroId: "hero3" }
    timeout: 30000
```

### 错误处理
```yaml
steps:
  - name: "可能失败的操作"
    action: "risky.action"
    onError:
      - action: "recovery.action"
        description: "恢复操作"
      - action: "notification.send"
        params:
          message: "操作失败，已执行恢复"
```

## 📊 场景执行报告

### 执行结果格式
```javascript
{
  scenarioName: "新手完整流程",
  success: true,
  duration: 298765,  // 执行时长（毫秒）
  steps: [
    {
      name: "注册账号",
      action: "auth.register",
      success: true,
      duration: 1234,
      result: { userId: "user_123" }
    }
    // ... 其他步骤
  ],
  player: {
    username: "newbie_1234567890",
    characterId: "char_456",
    finalState: {
      level: 2,
      gold: 5000,
      heroes: 11
    }
  },
  errors: [],
  warnings: [
    "步骤3执行较慢，耗时5秒"
  ]
}
```

## 🎯 最佳实践

### 1. 场景设计原则
- **单一职责**：每个场景专注一个主要流程
- **可重用性**：设计可复用的步骤和参数
- **容错性**：添加适当的重试和错误处理
- **可观测性**：提供详细的日志和状态信息

### 2. 参数化设计
```yaml
# 使用模板变量
player:
  username: "test_{{timestamp}}_{{random}}"
  level: "{{config.testLevel || 1}}"
  
# 支持环境变量
params:
  serverId: "{{env.TEST_SERVER_ID || 'server_001'}}"
```

### 3. 模块化组织
```yaml
# 引用其他场景的步骤
steps:
  - include: "common/login-flow.yaml"
  - include: "common/character-setup.yaml"
  - name: "自定义步骤"
    action: "custom.action"
```

---

**下一个文档**：部署和使用指南，说明如何部署和运行游戏客户端模拟器。
