/**
 * 领取荣誉任务奖励 对应old项目中的getHonorTaskReward方法 - 这是缺失的核心功能
 * 
 * 微服务: activity
 * 模块: honor
 * Controller: honor
 * Pattern: honor.claimTaskReward
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.270Z
 */

const BaseAction = require('../../../core/base-action');

class HonorclaimTaskRewardAction extends BaseAction {
  static metadata = {
    name: '领取荣誉任务奖励 对应old项目中的getHonorTaskReward方法 - 这是缺失的核心功能',
    description: '领取荣誉任务奖励 对应old项目中的getHonorTaskReward方法 - 这是缺失的核心功能',
    category: 'activity',
    serviceName: 'activity',
    module: 'honor',
    actionName: 'honor.claimTaskReward',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "taskId": {
            "type": "number",
            "required": true,
            "description": "taskId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, taskId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      taskId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取荣誉任务奖励 对应old项目中的getHonorTaskReward方法 - 这是缺失的核心功能成功'
      };
    } else {
      throw new Error(`领取荣誉任务奖励 对应old项目中的getHonorTaskReward方法 - 这是缺失的核心功能失败: ${response.message}`);
    }
  }
}

module.exports = HonorclaimTaskRewardAction;