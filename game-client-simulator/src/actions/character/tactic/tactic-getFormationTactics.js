/**
 * 获取阵容战术配置 对应old项目: getFormationTactics方法
 * 
 * 微服务: character
 * 模块: tactic
 * Controller: tactic
 * Pattern: tactic.getFormationTactics
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.373Z
 */

const BaseAction = require('../../../core/base-action');

class TacticgetFormationTacticsAction extends BaseAction {
  static metadata = {
    name: '获取阵容战术配置 对应old项目: getFormationTactics方法',
    description: '获取阵容战术配置 对应old项目: getFormationTactics方法',
    category: 'character',
    serviceName: 'character',
    module: 'tactic',
    actionName: 'tactic.getFormationTactics',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "formationId": {
            "type": "string",
            "required": true,
            "description": "formationId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, formationId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      formationId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取阵容战术配置 对应old项目: getFormationTactics方法成功'
      };
    } else {
      throw new Error(`获取阵容战术配置 对应old项目: getFormationTactics方法失败: ${response.message}`);
    }
  }
}

module.exports = TacticgetFormationTacticsAction;