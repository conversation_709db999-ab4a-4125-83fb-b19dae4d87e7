import {Injectable, Logger} from '@nestjs/common';
import {ConfigService} from '@nestjs/config';
import * as fs from 'fs/promises';
import * as path from 'path';
import {BackupResult, RestoreResult, RestoreOptions} from '../interfaces/backup.interface';
import {RedisService} from '../../redis/redis.service';
import {CompressionService} from './compression.service';
import {EncryptionService} from './encryption.service';
import {MICROSERVICE_NAMES} from '@shared/constants';

@Injectable()
export class RedisBackupService {
  private readonly logger = new Logger(RedisBackupService.name);

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
    private readonly compressionService: CompressionService,
    private readonly encryptionService: EncryptionService,
  ) {
    // 在服务初始化时验证备份配置
    this.validateBackupConfiguration();
  }

  /**
   * 验证备份配置
   */
  private validateBackupConfiguration(): void {
    const redisConfig = this.configService.get('backup.redis');
    const backupMode = redisConfig?.mode || 'rdb';

    if (!['rdb', 'export'].includes(backupMode)) {
      this.logger.warn(`无效的Redis备份模式: ${backupMode}, 将使用默认模式: rdb`);
    }

    const modeInfo = this.getBackupModeInfo();
    this.logger.log(`Redis备份服务已初始化，当前模式: ${modeInfo.description}`);

    // 验证RDB模式特定配置
    if (backupMode === 'rdb') {
      const timeout = redisConfig?.timeout || 300000;
      if (timeout < 30000) {
        this.logger.warn('RDB备份超时时间过短，建议至少设置30秒');
      }
    }

    // 验证export模式特定配置
    if (backupMode === 'export') {
      const batchSize = redisConfig?.batchSize || 1000;
      if (batchSize > 10000) {
        this.logger.warn('键值导出批处理大小过大，可能影响性能');
      }
    }
  }

  /**
   * 创建Redis备份
   */
  async createBackup(backupId: string): Promise<BackupResult> {
    const timestamp = new Date().toISOString();
    const backupPath = this.getBackupPath(backupId, 'redis');

    try {
      this.logger.log(`开始创建Redis备份: ${backupId}`);

      // 确保备份目录存在
      await fs.mkdir(backupPath, {recursive: true});

      // 根据配置的备份模式选择备份方式
      const backupMode = process.env.REDIS_BACKUP_MODE || 'rdb';
      const modeInfo = this.getBackupModeInfo();

      this.logger.log(`备份模式: ${modeInfo.description} (${modeInfo.mode})`);
      this.logger.log(`模式特性: ${modeInfo.features.join(', ')}`);

      let backupResult: { keyCount: number; keys: string[]; mode: string };

      if (backupMode === 'rdb') {
        // RDB快照备份模式（推荐，性能更好）
        backupResult = await this.createRDBBackup(backupPath);
      } else {
        // 键值导出备份模式（兼容性更好）
        const exportResult = await this.exportKeyData(backupPath);
        backupResult = {...exportResult, mode: 'export'};
      }

      // 压缩和加密
      const finalPath = await this.processBackupFile(backupPath);

      const metadata = await this.generateBackupMetadata(finalPath, 'redis', backupResult);

      this.logger.log(`Redis备份完成: ${backupId}, 模式: ${backupResult.mode}, 导出键数: ${backupResult.keyCount}, 大小: ${this.formatBytes(metadata.size)}`);

      return {
        id: backupId,
        type: 'redis',
        status: 'success',
        path: finalPath,
        size: metadata.size,
        timestamp,
        metadata: {
          ...metadata,
          backupMode: backupResult.mode as 'rdb' | 'export',
        },
      };

    } catch (error) {
      this.logger.error(`Redis备份失败: ${backupId}`, error);

      // 清理失败的备份文件
      try {
        await fs.rm(backupPath, {recursive: true, force: true});
      } catch (cleanupError) {
        this.logger.warn(`清理失败备份文件时出错: ${cleanupError.message}`);
      }

      return {
        id: backupId,
        type: 'redis',
        status: 'failed',
        error: error.message,
        timestamp,
      };
    }
  }

  /**
   * 创建增量备份
   */
  async createIncrementalBackup(backupId: string, since: Date): Promise<BackupResult> {
    const timestamp = new Date().toISOString();
    const backupPath = this.getBackupPath(backupId, 'redis-incremental');

    try {
      this.logger.log(`开始创建Redis增量备份: ${backupId}, 起始时间: ${since.toISOString()}`);

      // 确保备份目录存在
      await fs.mkdir(backupPath, {recursive: true});

      // 检查备份模式
      const redisConfig = this.configService.get('backup.redis');
      const backupMode = redisConfig?.mode || 'rdb';

      if (backupMode === 'rdb') {
        // RDB模式不支持增量备份，提示用户
        this.logger.warn('RDB模式不支持增量备份，将创建完整备份');
        const fullBackupResult = await this.createRDBBackup(backupPath);

        // 压缩和加密
        const finalPath = await this.processBackupFile(backupPath);
        const metadata = await this.generateBackupMetadata(finalPath, 'redis-full-as-incremental', fullBackupResult);

        this.logger.log(`Redis完整备份（代替增量）完成: ${backupId}, 总键数: ${fullBackupResult.keyCount}, 大小: ${this.formatBytes(metadata.size)}`);

        return {
          id: backupId,
          type: 'redis',
          status: 'success',
          path: finalPath,
          size: metadata.size,
          timestamp,
          metadata: {
            ...metadata,
            backupMode: 'rdb-full-as-incremental',
            note: 'RDB模式不支持增量备份，已创建完整备份',
          },
        };
      } else {
        // 键值导出模式支持增量备份
        const exportResult = await this.exportChangedKeys(backupPath, since);

        // 压缩和加密
        const finalPath = await this.processBackupFile(backupPath);
        const metadata = await this.generateBackupMetadata(finalPath, 'redis-incremental', exportResult);

        this.logger.log(`Redis增量备份完成: ${backupId}, 变更键数: ${exportResult.keyCount}, 大小: ${this.formatBytes(metadata.size)}`);

        return {
          id: backupId,
          type: 'redis',
          status: 'success',
          path: finalPath,
          size: metadata.size,
          timestamp,
          metadata: {
            ...metadata,
            backupMode: 'export-incremental',
          },
        };
      }

    } catch (error) {
      this.logger.error(`Redis增量备份失败: ${backupId}`, error);

      return {
        id: backupId,
        type: 'redis',
        status: 'failed',
        error: error.message,
        timestamp,
      };
    }
  }

  /**
   * 恢复Redis数据
   */
  async restore(backupId: string, options: RestoreOptions): Promise<RestoreResult> {
    const startTime = Date.now();

    try {
      this.logger.log(`开始恢复Redis备份: ${backupId}`);

      // 获取备份文件路径
      const backupPath = await this.getBackupFilePath(backupId);

      // 下载、解密、解压备份文件
      const localPath = await this.downloadBackupIfNeeded(backupPath);
      const decryptedPath = await this.decryptBackupIfNeeded(localPath);
      const extractedPath = await this.extractBackupIfNeeded(decryptedPath);

      // 自动检测备份类型
      const backupType = await this.detectBackupType(extractedPath);
      let restoredRecords = 0;

      // 根据备份类型和用户选项决定恢复方式
      const restoreMethod = options.restoreMethod || backupType;

      if (restoreMethod === 'rdb') {
        restoredRecords = await this.restoreFromRDB(extractedPath, options);
      } else {
        restoredRecords = await this.restoreKeyData(extractedPath, options);
      }

      // 清理临时文件
      await this.cleanupTempFiles([localPath, decryptedPath, extractedPath]);

      const duration = Date.now() - startTime;
      this.logger.log(`Redis恢复完成: ${backupId}, 恢复方式: ${restoreMethod}, 恢复键数: ${restoredRecords}, 耗时: ${duration}ms`);

      return {
        id: backupId,
        type: 'redis',
        status: 'success',
        timestamp: new Date().toISOString(),
        duration,
        restoredRecords,
      };

    } catch (error) {
      this.logger.error(`Redis恢复失败: ${backupId}`, error);

      return {
        id: backupId,
        type: 'redis',
        status: 'failed',
        error: error.message,
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * 自动检测备份类型
   */
  private async detectBackupType(extractedPath: string): Promise<'rdb' | 'keys'> {
    try {
      // 检查是否存在RDB备份元数据文件
      const rdbMetadataPath = path.join(extractedPath, 'rdb-backup-metadata.json');
      const rdbReadmePath = path.join(extractedPath, 'RDB-BACKUP-README.txt');

      if (await this.fileExists(rdbMetadataPath) || await this.fileExists(rdbReadmePath)) {
        return 'rdb';
      }

      // 检查是否存在键值导出文件
      const exportPath = path.join(extractedPath, 'redis-export.json');
      if (await this.fileExists(exportPath)) {
        return 'keys';
      }

      // 默认返回键值恢复
      this.logger.warn('无法检测备份类型，默认使用键值恢复方式');
      return 'keys';

    } catch (error) {
      this.logger.warn('检测备份类型时出错，默认使用键值恢复方式', error);
      return 'keys';
    }
  }

  /**
   * 检查文件是否存在
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  // 私有方法
  private async exportKeyData(outputPath: string): Promise<{ keyCount: number; keys: string[] }> {
    this.logger.log('开始键值导出备份...');

    // 使用服务感知备份模式
    const backupStrategy = 'service'; // 统一使用服务感知模式
    this.logger.log(`使用备份策略: ${backupStrategy}`);

    return await this.exportServiceAwareData(outputPath);
  }

  /**
   * 获取当前备份配置信息
   */
  private getBackupModeInfo(): { mode: string; description: string; features: string[] } {
    const redisConfig = this.configService.get('backup.redis');
    const mode = redisConfig?.mode || 'rdb';

    if (mode === 'rdb') {
      return {
        mode: 'rdb',
        description: 'RDB快照备份',
        features: [
          '性能更好，备份速度快',
          '文件体积小',
          '适合大数据量备份',
          '恢复需要停止Redis服务',
          '不支持增量备份'
        ]
      };
    } else {
      return {
        mode: 'export',
        description: '键值导出备份',
        features: [
          '兼容性好，支持选择性备份',
          '支持增量备份',
          '可在线恢复',
          '备份文件可读性好',
          '适合小到中等数据量'
        ]
      };
    }
  }

  /**
   * 服务感知的数据导出
   */
  private async exportServiceAwareData(outputPath: string): Promise<{ keyCount: number; keys: string[] }> {
    const env = this.configService.get('NODE_ENV') || 'dev';
    const project = this.configService.get('PROJECT_NAME') || 'fm';
    const services = this.getBackupServices();

    const exportData = {
      timestamp: new Date().toISOString(),
      version: '2.0',
      strategy: 'service-aware',
      environment: env,
      project: project,
      services: {},
    };

    const exportedKeys: string[] = [];

    for (const service of services) {
      const serviceData = await this.backupServiceData(env, project, service);
      if (serviceData.size > 0) {
        exportData.services[service] = Object.fromEntries(serviceData);
        exportedKeys.push(...Array.from(serviceData.keys()));
      }
    }

    // 写入导出文件
    const exportFilePath = path.join(outputPath, 'redis-export.json');
    await fs.writeFile(exportFilePath, JSON.stringify(exportData, null, 2));

    // 写入键列表文件
    const keyListPath = path.join(outputPath, 'key-list.txt');
    await fs.writeFile(keyListPath, exportedKeys.join('\n'));

    this.logger.log(`Redis服务感知数据导出完成: ${exportedKeys.length} 个键`);

    return {
      keyCount: exportedKeys.length,
      keys: exportedKeys,
    };
  }

  /**
   * 传统模式匹配的数据导出
   */
  private async exportPatternBasedData(outputPath: string, includePatterns: string[]): Promise<{
    keyCount: number;
    keys: string[]
  }> {
    // 获取Redis客户端
    const client = this.redisService.getClient();

    // 注意：不再手动添加前缀，RedisService已经在连接级别自动处理
    // 直接使用业务模式，Redis连接会自动添加 {env}:{project}:{service}: 前缀
    const prefixedPatterns = includePatterns;

    // 简化排除模式，使用默认的缓存排除规则
    const excludePatterns = ['cache:*', 'temp:*', 'lock:*', 'rate_limit:*'];
    const prefixedExcludePatterns = excludePatterns;

    const exportedKeys: string[] = [];
    const exportData = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      strategy: 'pattern-based',
      keys: {},
    };

    // let allkeys = await client.keys('*');
    // console.log('allkeys', allkeys);
    // let key = 'fm:gateway:routes'
    // let key2 = 'gateway:routes'
    // let value = await client.get(key);
    // let value2 = await client.get(key2);
    // console.log('value', value);
    // console.log('value2', value2);
    //
    // let patternkeys = await client.keys('gateway:*');
    // console.log('patternkeys', patternkeys);
    // let patternkeys2 = await client.keys('fm:gateway:*');
    // console.log('patternkeys2', patternkeys2);

    for (const pattern of prefixedPatterns) {
      // 使用RedisService的keys方法，自动处理前缀
      const keys = await this.redisService.keys(pattern);

      for (const key of keys) {
        // 检查是否应该排除
        if (this.shouldExcludeKey(key, prefixedExcludePatterns)) {
          continue;
        }

        try {
          const type = await client.type(key);
          const ttl = await client.ttl(key);

          // 跳过不存在或已过期的键
          if (type === 'none') {
            this.logger.warn(`键不存在或已过期: ${key}`);
            continue;
          }

          let value;
          switch (type) {
            case 'string':
              value = await client.get(key);
              break;
            case 'hash':
              value = await client.hgetall(key);
              break;
            case 'list':
              value = await client.lrange(key, 0, -1);
              break;
            case 'set':
              value = await client.smembers(key);
              break;
            case 'zset':
              value = await client.zrange(key, 0, -1, 'WITHSCORES');
              break;
            default:
              this.logger.warn(`不支持的Redis数据类型: ${type}, 键: ${key}`);
              continue;
          }

          exportData.keys[key] = {
            type,
            value,
            ttl: ttl > 0 ? ttl : null,
            exportTime: new Date().toISOString()
          };

          exportedKeys.push(key);

        } catch (error) {
          this.logger.warn(`导出键失败: ${key}`, error);
        }
      }
    }

    // 写入导出文件
    const exportFilePath = path.join(outputPath, 'redis-export.json');
    await fs.writeFile(exportFilePath, JSON.stringify(exportData, null, 2));

    // 写入键列表文件
    const keyListPath = path.join(outputPath, 'key-list.txt');
    await fs.writeFile(keyListPath, exportedKeys.join('\n'));

    this.logger.log(`Redis数据导出完成: ${exportedKeys.length} 个键`);

    return {
      keyCount: exportedKeys.length,
      keys: exportedKeys,
    };
  }

  private async exportChangedKeys(outputPath: string, since: Date): Promise<{ keyCount: number; keys: string[] }> {
    // 增量备份：识别自上次备份以来变更的键
    // 简化实现：由于Redis没有内置的修改时间戳，采用全量扫描方式

    const client = this.redisService.getClient();
    const exportData = {
      timestamp: new Date().toISOString(),
      incrementalSince: since.toISOString(),
      version: '1.0',
      mode: 'incremental',
      keys: {},
    };

    const changedKeys: string[] = [];

    try {
      // 使用服务感知模式获取所有业务键
      const env = this.configService.get('NODE_ENV') || 'dev';
      const project = this.configService.get('PROJECT_NAME') || 'fm';
      const services = this.getBackupServices();

      for (const service of services) {
        // 构建服务键模式
        const pattern = `${env}:${project}:${service}:*`;
        const keys = await client.keys(pattern);

        for (const key of keys) {
          try {
            // 简化的变更检测：检查键的TTL和类型
            const ttl = await client.ttl(key);
            const type = await client.type(key);

            // 简单策略：假设所有键都可能有变更（实际项目中可以基于业务逻辑优化）
            let value;
            switch (type) {
              case 'string':
                value = await client.get(key);
                break;
              case 'hash':
                value = await client.hgetall(key);
                break;
              case 'list':
                value = await client.lrange(key, 0, -1);
                break;
              case 'set':
                value = await client.smembers(key);
                break;
              case 'zset':
                value = await client.zrange(key, 0, -1, 'WITHSCORES');
                break;
              default:
                this.logger.warn(`不支持的键类型: ${type}, 键: ${key}`);
                continue;
            }

            // 记录键数据
            exportData.keys[key] = {
              type,
              value,
              ttl: ttl > 0 ? ttl : null,
              changeType: 'modified', // 简化：标记为修改
              service,
            };

            changedKeys.push(key);

          } catch (error) {
            this.logger.warn(`导出键失败: ${key}`, error);
          }
        }
      }

      // 生成增量备份文件
      const exportFilePath = path.join(outputPath, 'redis-incremental.json');
      await fs.writeFile(exportFilePath, JSON.stringify(exportData, null, 2));

      this.logger.log(`增量备份完成: 导出 ${changedKeys.length} 个键`);

    } catch (error) {
      this.logger.error('增量备份过程中发生错误', error);
      throw error;
    }

    return {
      keyCount: changedKeys.length,
      keys: changedKeys,
    };
  }

  /**
   * 创建RDB快照备份
   */
  private async createRDBBackup(outputPath: string): Promise<{ keyCount: number; keys: string[]; mode: string }> {
    const client = this.redisService.getClient();

    try {
      this.logger.log('开始创建RDB快照备份...');

      // 获取当前数据库信息
      const info = await client.info('keyspace');
      const dbInfo = this.parseKeyspaceInfo(info);
      const totalKeys = Object.values(dbInfo).reduce((sum, db) => sum + db.keys, 0);

      // 触发后台保存，如果有冲突则等待重试
      let saveResult;
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          saveResult = await client.bgsave();
          this.logger.log(`BGSAVE命令执行结果: ${saveResult}`);
          break;
        } catch (error) {
          if (error.message.includes('Background save already in progress')) {
            retryCount++;
            this.logger.warn(`检测到后台保存正在进行，等待重试 (${retryCount}/${maxRetries})`);
            if (retryCount < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒
              continue;
            }
          }
          throw error;
        }
      }

      // 等待保存完成
      const startTime = Date.now();
      const timeout = 300000; // 5分钟超时
      let saveInProgress = true;
      let lastSaveTime = await client.lastsave();

      while (saveInProgress && (Date.now() - startTime) < timeout) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
        const newLastSaveTime = await client.lastsave();
        saveInProgress = lastSaveTime === newLastSaveTime;

        if (!saveInProgress) {
          this.logger.log(`RDB快照保存完成，最后保存时间: ${new Date(newLastSaveTime * 1000).toISOString()}`);
          break;
        }
      }

      if (saveInProgress) {
        throw new Error('RDB备份超时，可能是数据量过大或Redis服务器繁忙');
      }

// 创建备份元数据文件
      const backupMetadata = {
        timestamp: new Date().toISOString(),
        version: '2.0',
        mode: 'rdb',
        totalKeys: totalKeys,
        databases: dbInfo,
        rdbInfo: {
          lastSaveTime: new Date(await client.lastsave() * 1000).toISOString(),
          saveMethod: 'bgsave',
        },
      };

// 写入元数据文件
      const metadataPath = path.join(outputPath, 'rdb-backup-metadata.json');
      await fs.writeFile(metadataPath, JSON.stringify(backupMetadata, null, 2));

// 创建备份说明文件
      const readmePath = path.join(outputPath, 'RDB-BACKUP-README.txt');
      const readmeContent = [
        'Redis RDB快照备份',
        '==================',
        '',
        '备份时间: ' + backupMetadata.timestamp,
        '备份模式: RDB快照',
        '总键数: ' + totalKeys,
        '',
        '注意事项:',
        '1. RDB备份是Redis数据的完整快照',
        '2. 恢复时需要停止Redis服务并替换dump.rdb文件',
        '3. 建议在维护窗口期间进行恢复操作',
        '4. 恢复前请备份当前的dump.rdb文件',
        '',
        '数据库信息:',
        ...Object.entries(dbInfo).map(([db, info]) =>
          `  DB${db}: ${info.keys} 键, ${info.expires} 过期键`
        ),
      ].join('\n');

      await fs.writeFile(readmePath, readmeContent);

      this.logger.log(`RDB备份创建完成，总键数: ${totalKeys}`);

      return {
        keyCount: totalKeys,
        keys: [`RDB快照包含${totalKeys}个键`],
        mode: 'rdb',
      };

    } catch (error) {
      this.logger.error('创建RDB备份失败', error);
      throw error;
    }
  }

  /**
   * 解析Redis keyspace信息
   */
  private parseKeyspaceInfo(info: string): Record<string, { keys: number; expires: number }> {
    const result: Record<string, { keys: number; expires: number }> = {};
    const lines = info.split('\n');

    for (const line of lines) {
      const match = line.match(/^db(\d+):keys=(\d+),expires=(\d+)/);
      if (match) {
        const [, db, keys, expires] = match;
        result[db] = {
          keys: parseInt(keys, 10),
          expires: parseInt(expires, 10),
        };
      }
    }

    return result;
  }

  private async restoreKeyData(extractedPath: string, options: RestoreOptions): Promise<number> {
    const client = this.redisService.getClient();
    const exportFilePath = path.join(extractedPath, 'redis-export.json');

    const exportData = JSON.parse(await fs.readFile(exportFilePath, 'utf-8'));
    const keys = Object.keys(exportData.keys);
    let restoredCount = 0;

    // 切换到目标数据库
    if (options.targetRedisDb !== undefined) {
      await client.select(options.targetRedisDb);
    }

    for (const key of keys) {
      try {
        const keyData = exportData.keys[key];
        const {type, value, ttl} = keyData;

        // 如果需要，先删除现有键
        if (options.dropBeforeRestore) {
          await client.del(key);
        }

        // 根据类型恢复数据
        switch (type) {
          case 'string':
            await client.set(key, value);
            break;
          case 'hash':
            await client.hset(key, value);
            break;
          case 'list':
            if (Array.isArray(value) && value.length > 0) {
              await client.lpush(key, ...value.reverse());
            }
            break;
          case 'set':
            if (Array.isArray(value) && value.length > 0) {
              await client.sadd(key, ...value);
            }
            break;
          case 'zset':
            if (Array.isArray(value) && value.length > 0) {
              const args = [];
              for (let i = 0; i < value.length; i += 2) {
                args.push(value[i + 1], value[i]); // score, member
              }
              if (args.length > 0) {
                await client.zadd(key, ...args);
              }
            }
            break;
        }

        // 设置TTL
        if (ttl && ttl > 0) {
          await client.expire(key, ttl);
        }

        restoredCount++;

      } catch (error) {
        this.logger.warn(`恢复键失败: ${key}`, error);
      }
    }

    return restoredCount;
  }

  /**
   * 从RDB备份恢复数据
   */
  private async restoreFromRDB(extractedPath: string, options: RestoreOptions): Promise<number> {
    try {
      // 读取备份元数据
      const metadataPath = path.join(extractedPath, 'rdb-backup-metadata.json');

      if (!await this.fileExists(metadataPath)) {
        throw new Error('找不到RDB备份元数据文件，无法进行RDB恢复');
      }

      const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf-8'));
      this.logger.log(`RDB备份信息: 备份时间=${metadata.timestamp}, 总键数=${metadata.totalKeys}`);

// 警告用户RDB恢复的风险
      this.logger.warn('='.repeat(60));
      this.logger.warn('警告: RDB恢复是危险操作！');
      this.logger.warn('1. 需要停止Redis服务');
      this.logger.warn('2. 会覆盖当前所有数据');
      this.logger.warn('3. 建议在维护窗口期间执行');
      this.logger.warn('4. 请确保已备份当前数据');
      this.logger.warn('='.repeat(60));

// 在实际生产环境中，这里应该：
// 1. 验证用户权限
// 2. 检查Redis服务状态
// 3. 备份当前RDB文件
// 4. 停止Redis服务
// 5. 替换RDB文件
// 6. 启动Redis服务
// 7. 验证恢复结果

// 目前提供模拟实现，实际部署时需要根据环境调整
      if (process.env.NODE_ENV === 'production') {
        throw new Error('生产环境暂不支持自动RDB恢复，请手动操作');
      }

// 开发/测试环境的模拟实现
      this.logger.log('检测到非生产环境，执行模拟RDB恢复...');

// 使用FLUSHALL清空当前数据（模拟RDB恢复效果）
      if (options.dropBeforeRestore) {
        const client = this.redisService.getClient();
        await client.flushall();
        this.logger.log('已清空Redis数据库（模拟RDB恢复）');
      }

// 创建恢复日志
      const restoreLogPath = path.join(extractedPath, 'rdb-restore-log.txt');
      const restoreLog = [
        'RDB恢复操作日志',
        '================',
        '',
        '恢复时间: ' + new Date().toISOString(),
        '备份时间: ' + metadata.timestamp,
        '原始键数: ' + metadata.totalKeys,
        '恢复模式: 模拟恢复（非生产环境）',
        '',
        '注意: 在生产环境中，需要手动执行以下步骤：',
        '1. 停止Redis服务: systemctl stop redis',
        '2. 备份当前RDB: cp /var/lib/redis/dump.rdb /backup/',
        '3. 替换RDB文件: cp backup-dump.rdb /var/lib/redis/dump.rdb',
        '4. 启动Redis服务: systemctl start redis',
        '5. 验证数据完整性',
      ].join('\n');

      await fs.writeFile(restoreLogPath, restoreLog);

      return metadata.totalKeys || 0;

    } catch (error) {
      this.logger.error('RDB恢复失败', error);
      throw error;
    }
  }

  private shouldExcludeKey(key: string, excludePatterns: string[]): boolean {
    return excludePatterns.some(pattern => {
      const regex = new RegExp(pattern.replace('*', '.*'));
      return regex.test(key);
    });
  }

  private getBackupPath(backupId: string, type: string): string {
    const basePath = this.configService.get<string>('backup.localPath', '/app/backups');
    return path.join(basePath, type, backupId);
  }

  private async processBackupFile(backupPath: string): Promise<string> {
    let finalPath = backupPath;

    // 压缩
    if (this.configService.get<boolean>('backup.compressionEnabled', true)) {
      finalPath = await this.compressionService.compressDirectory(backupPath);
      await fs.rm(backupPath, {recursive: true, force: true});
    }

// 加密
    if (this.configService.get<boolean>('backup.encryptionEnabled', false)) {
      finalPath = await this.encryptionService.encryptFile(finalPath);
    }

    return finalPath;
  }

  private async generateBackupMetadata(filePath: string, type: string, exportResult: any) {
    const stats = await fs.stat(filePath);

    // 计算实际的文件校验和
    const checksum = await this.encryptionService.generateFileHash(filePath);

    return {
      size: stats.size,
      checksum: checksum,
      compression: this.configService.get<boolean>('backup.compressionEnabled', true) ? 'gzip' : 'none',
      encryption: this.configService.get<boolean>('backup.encryptionEnabled', false),
      recordCount: exportResult.keyCount,
      keys: exportResult.keys.slice(0, 100), // 只保存前100个键名作为示例
    };
  }

  private async getBackupFilePath(backupId: string): Promise<string> {
    // 从元数据服务获取备份文件路径
    return '';
  }

  private async downloadBackupIfNeeded(backupPath: string): Promise<string> {
    return backupPath;
  }

  private async decryptBackupIfNeeded(filePath: string): Promise<string> {
    if (this.configService.get<boolean>('backup.encryptionEnabled', false)) {
      return await this.encryptionService.decryptFile(filePath);
    }
    return filePath;
  }

  private async extractBackupIfNeeded(filePath: string): Promise<string> {
    if (this.configService.get<boolean>('backup.compressionEnabled', true)) {
      return await this.compressionService.extractArchive(filePath);
    }
    return filePath;
  }

  private async cleanupTempFiles(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        if (filePath) {
          await fs.rm(filePath, {recursive: true, force: true});
        }
      } catch (error) {
        this.logger.warn(`清理临时文件失败: ${filePath}`, error);
      }
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取要备份的服务列表
   */
  private getBackupServices(): string[] {
    // 统一使用服务感知模式，从共享常量获取服务列表
    return Object.values(MICROSERVICE_NAMES);
  }

  /**
   * 备份特定服务的数据
   */
  private async backupServiceData(env: string, project: string, service: string): Promise<Map<string, any>> {
    const client = this.redisService.getClient();

    // 获取服务对应的数据库
    const database = this.getServiceDatabase(service);
    if (database !== undefined) {
      const dbClient = client.duplicate();
      await dbClient.select(database);

      const keys = await dbClient.keys('*'); // 在特定数据库中查找所有键
      const serviceData = new Map<string, any>();

      for (const key of keys) {
        const value = await dbClient.get(key);
        if (value !== null) {
          serviceData.set(key, value);
        }
      }

      await dbClient.quit();
      return serviceData;
    } else {
      // 如果没有专用数据库，使用模式匹配
      const pattern = `${env}:${project}:${service}:*`;
      const keys = await client.keys(pattern);
      const serviceData = new Map<string, any>();

      for (const key of keys) {
        const value = await client.get(key);
        if (value !== null) {
          // 存储时移除前缀，恢复时再添加
          const businessKey = this.removePrefix(key, `${env}:${project}:${service}:`);
          serviceData.set(businessKey, value);
        }
      }

      return serviceData;
    }
  }

  /**
   * 获取服务对应的数据库编号
   */
  private getServiceDatabase(service: string): number | undefined {
    // 使用共享常量，避免硬编码
    const databaseMapping = {
      [MICROSERVICE_NAMES.GATEWAY_SERVICE]: 0,
      [MICROSERVICE_NAMES.AUTH_SERVICE]: 1,
      [MICROSERVICE_NAMES.CHARACTER_SERVICE]: 2,
      [MICROSERVICE_NAMES.GAME_SERVICE]: 3,
      [MICROSERVICE_NAMES.CLUB_SERVICE]: 4,
      [MICROSERVICE_NAMES.MATCH_SERVICE]: 5,
      [MICROSERVICE_NAMES.HERO_SERVICE]: 6,
      [MICROSERVICE_NAMES.CARD_SERVICE]: 7,
      // 为未来的服务预留数据库
      'transfer': 8,
      'achievement': 9,
      [MICROSERVICE_NAMES.NOTIFICATION_SERVICE]: 10,
    };
    return databaseMapping[service];
  }

  /**
   * 移除键前缀
   */
  private removePrefix(key: string, prefix: string): string {
    return key.startsWith(prefix) ? key.substring(prefix.length) : key;
  }
}
