import { Injectable, Logger } from '@nestjs/common';
import { ConfigManager } from './config-manager.service';
import { CacheManager } from './cache-manager.service';
import { EventManager } from './event-manager.service';

/**
 * 配置监控服务
 * 负责监控配置使用情况、性能指标和健康状态
 */
@Injectable()
export class ConfigMonitor {
  private readonly logger = new Logger(ConfigMonitor.name);

  // 使用统计
  private usageStats = new Map<string, {
    accessCount: number;
    lastAccessed: Date;
    errorCount: number;
    avgResponseTime: number;
    totalResponseTime: number;
  }>();

  // 性能指标
  private performanceMetrics = {
    totalRequests: 0,
    totalErrors: 0,
    avgResponseTime: 0,
    peakResponseTime: 0,
    startTime: new Date(),
  };

  // 监控间隔（毫秒）
  private readonly monitorInterval = 60000; // 1分钟
  private monitorTimer: NodeJS.Timeout;

  constructor(
    private readonly configManager: ConfigManager,
    private readonly cacheManager: CacheManager,
    private readonly eventManager: EventManager,
  ) {
    this.startMonitoring();
  }

  /**
   * 开始监控
   */
  private startMonitoring(): void {
    this.logger.log('Starting config monitoring...');
    
    this.monitorTimer = setInterval(() => {
      // 收集指标，暂时注释掉
      // this.collectMetrics();
    }, this.monitorInterval);
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
      this.logger.log('Config monitoring stopped');
    }
  }

  /**
   * 记录配置访问
   */
  recordAccess(tableName: string, responseTime: number, isError: boolean = false): void {
    // 更新表级统计
    const tableStats = this.usageStats.get(tableName) || {
      accessCount: 0,
      lastAccessed: new Date(),
      errorCount: 0,
      avgResponseTime: 0,
      totalResponseTime: 0,
    };

    tableStats.accessCount++;
    tableStats.lastAccessed = new Date();
    tableStats.totalResponseTime += responseTime;
    tableStats.avgResponseTime = tableStats.totalResponseTime / tableStats.accessCount;

    if (isError) {
      tableStats.errorCount++;
    }

    this.usageStats.set(tableName, tableStats);

    // 更新全局统计
    this.performanceMetrics.totalRequests++;
    this.performanceMetrics.avgResponseTime = 
      (this.performanceMetrics.avgResponseTime * (this.performanceMetrics.totalRequests - 1) + responseTime) / 
      this.performanceMetrics.totalRequests;

    if (responseTime > this.performanceMetrics.peakResponseTime) {
      this.performanceMetrics.peakResponseTime = responseTime;
    }

    if (isError) {
      this.performanceMetrics.totalErrors++;
    }

    // 记录慢查询
    if (responseTime > 100) { // 超过100ms认为是慢查询
      this.logger.warn(`Slow config query: ${tableName}, ${responseTime}ms`);
    }
  }

  /**
   * 收集指标
   */
  private async collectMetrics(): Promise<void> {
    try {
      // 收集缓存指标
      const cacheStats = this.cacheManager.getStats();
      
      // 收集事件指标
      const eventStats = this.eventManager.getEventStats();
      
      // 收集健康状态
      const healthStatus = await this.configManager.getHealthStatus();

      // 记录指标日志
      this.logger.debug('Config metrics collected', {
        cache: cacheStats,
        events: eventStats,
        health: healthStatus,
        usage: this.getTopUsedTables(5),
      });

      // 检查异常情况
      await this.checkAnomalies(cacheStats);

    } catch (error) {
      this.logger.error('Failed to collect metrics', error.stack);
    }
  }

  /**
   * 检查异常情况
   */
  private async checkAnomalies(cacheStats: any): Promise<void> {
    const issues: string[] = [];

    // 检查缓存命中率
    const hitRatio = parseFloat(cacheStats.hitRatio.replace('%', ''));
    if (hitRatio < 60) {
      issues.push(`Low cache hit ratio: ${cacheStats.hitRatio}`);
    }

    // 检查错误率
    const errorRate = this.performanceMetrics.totalRequests > 0 ? 
      (this.performanceMetrics.totalErrors / this.performanceMetrics.totalRequests) * 100 : 0;
    
    if (errorRate > 5) {
      issues.push(`High error rate: ${errorRate.toFixed(2)}%`);
    }

    // 检查响应时间
    if (this.performanceMetrics.avgResponseTime > 50) {
      issues.push(`High average response time: ${this.performanceMetrics.avgResponseTime.toFixed(2)}ms`);
    }

    // 检查内存使用
    if (cacheStats.memory.size > cacheStats.memory.max * 0.9) {
      issues.push('Memory cache near capacity');
    }

    // 记录异常
    if (issues.length > 0) {
      this.logger.warn('Config anomalies detected:', issues);
    }
  }

  /**
   * 获取使用统计
   */
  getUsageStats(): {
    tables: Array<{
      tableName: string;
      accessCount: number;
      lastAccessed: Date;
      errorCount: number;
      avgResponseTime: number;
      errorRate: number;
    }>;
    performance: typeof this.performanceMetrics;
  } {
    const tables = Array.from(this.usageStats.entries()).map(([tableName, stats]) => ({
      tableName,
      accessCount: stats.accessCount,
      lastAccessed: stats.lastAccessed,
      errorCount: stats.errorCount,
      avgResponseTime: Math.round(stats.avgResponseTime * 100) / 100,
      errorRate: stats.accessCount > 0 ? 
        Math.round((stats.errorCount / stats.accessCount) * 10000) / 100 : 0,
    }));

    // 按访问次数排序
    tables.sort((a, b) => b.accessCount - a.accessCount);

    return {
      tables,
      performance: { ...this.performanceMetrics },
    };
  }

  /**
   * 获取最常用的表
   */
  getTopUsedTables(limit: number = 10): Array<{
    tableName: string;
    accessCount: number;
    avgResponseTime: number;
  }> {
    return Array.from(this.usageStats.entries())
      .map(([tableName, stats]) => ({
        tableName,
        accessCount: stats.accessCount,
        avgResponseTime: Math.round(stats.avgResponseTime * 100) / 100,
      }))
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, limit);
  }

  /**
   * 获取慢查询统计
   */
  getSlowQueries(threshold: number = 100): Array<{
    tableName: string;
    avgResponseTime: number;
    accessCount: number;
  }> {
    return Array.from(this.usageStats.entries())
      .filter(([, stats]) => stats.avgResponseTime > threshold)
      .map(([tableName, stats]) => ({
        tableName,
        avgResponseTime: Math.round(stats.avgResponseTime * 100) / 100,
        accessCount: stats.accessCount,
      }))
      .sort((a, b) => b.avgResponseTime - a.avgResponseTime);
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): Array<{
    tableName: string;
    errorCount: number;
    errorRate: number;
    lastAccessed: Date;
  }> {
    return Array.from(this.usageStats.entries())
      .filter(([, stats]) => stats.errorCount > 0)
      .map(([tableName, stats]) => ({
        tableName,
        errorCount: stats.errorCount,
        errorRate: Math.round((stats.errorCount / stats.accessCount) * 10000) / 100,
        lastAccessed: stats.lastAccessed,
      }))
      .sort((a, b) => b.errorRate - a.errorRate);
  }

  /**
   * 重置统计数据
   */
  resetStats(): void {
    this.usageStats.clear();
    this.performanceMetrics = {
      totalRequests: 0,
      totalErrors: 0,
      avgResponseTime: 0,
      peakResponseTime: 0,
      startTime: new Date(),
    };
    
    this.logger.log('Config monitoring stats reset');
  }

  /**
   * 获取监控报告
   */
  async getMonitoringReport(): Promise<{
    summary: {
      uptime: string;
      totalRequests: number;
      errorRate: string;
      avgResponseTime: string;
      cacheHitRatio: string;
    };
    topTables: any[];
    slowQueries: any[];
    errors: any[];
    cacheStats: any;
    healthStatus: any;
  }> {
    const uptime = Date.now() - this.performanceMetrics.startTime.getTime();
    const uptimeHours = Math.floor(uptime / (1000 * 60 * 60));
    const uptimeMinutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));

    const errorRate = this.performanceMetrics.totalRequests > 0 ? 
      (this.performanceMetrics.totalErrors / this.performanceMetrics.totalRequests) * 100 : 0;

    const cacheStats = this.cacheManager.getStats();
    const healthStatus = await this.configManager.getHealthStatus();

    return {
      summary: {
        uptime: `${uptimeHours}h ${uptimeMinutes}m`,
        totalRequests: this.performanceMetrics.totalRequests,
        errorRate: `${errorRate.toFixed(2)}%`,
        avgResponseTime: `${this.performanceMetrics.avgResponseTime.toFixed(2)}ms`,
        cacheHitRatio: cacheStats.hitRatio,
      },
      topTables: this.getTopUsedTables(10),
      slowQueries: this.getSlowQueries(50),
      errors: this.getErrorStats(),
      cacheStats,
      healthStatus,
    };
  }
}
