// 配置表访问器类型定义

/**
 * 配置表访问器接口
 * 提供统一的配置表访问方法，包括基础CRUD操作和高级筛选功能
 */
export interface ConfigTableAccessor<T> {
   /**
   * 根据ID获取单个配置项
   * @param id 配置项的唯一标识符
   * @returns 配置项对象，如果不存在则返回null
   * @example
   * ```typescript
   * const hero = await this.gameConfig.hero.get(123);
   * if (hero) {
   *   console.log(hero.name);
   * }
   * ```
   */
  get(id: number): Promise<T | null>;

  /**
   * 获取所有配置项
   * @returns 所有配置项的数组
   * @example
   * ```typescript
   * const allHeroes = await this.gameConfig.hero.getAll();
   * console.log(`总共有 ${allHeroes.length} 个英雄`);
   * ```
   */
  getAll(): Promise<T[]>;

  /**
   * 批量获取配置项
   * @param ids 配置项ID数组
   * @returns Map对象，键为ID，值为配置项
   * @example
   * ```typescript
   * const heroMap = await this.gameConfig.hero.getBatch([1, 2, 3]);
   * const hero1 = heroMap.get(1);
   * ```
   */
  getBatch(ids: number[]): Promise<Map<number, T>>;

  /**
   * 根据关键词搜索配置项
   * @param keyword 搜索关键词
   * @returns 匹配的配置项数组
   * @example
   * ```typescript
   * const heroes = await this.gameConfig.hero.search('法师');
   * ```
   */
  search(keyword: string): Promise<T[]>;

  /**
   * 根据自定义条件筛选配置项
   * @param predicate 筛选条件函数，返回true的项目会被包含在结果中
   * @returns 符合条件的配置项数组
   * @example
   * ```typescript
   * // 筛选等级大于10的英雄
   * const highLevelHeroes = await this.gameConfig.hero.filter(
   *   hero => hero.level > 10
   * );
   *
   * // 筛选特定位置和等级的英雄
   * const tankHeroes = await this.gameConfig.hero.filter(
   *   hero => hero.position === 'tank' && hero.level >= 5
   * );
   * ```
   */
  filter(predicate: (item: T) => boolean): Promise<T[]>;

  /**
   * 根据指定字段和值筛选配置项
   * @param field 要筛选的字段名
   * @param value 字段值
   * @returns 字段值匹配的配置项数组
   * @example
   * ```typescript
   * // 获取指定队伍的所有球员配置
   * const teamPlayers = await this.gameConfig.team.findBy('teamId', 90101);
   *
   * // 获取指定位置的所有英雄
   * const goalkeepers = await this.gameConfig.hero.findBy('position', 'GK');
   *
   * // 获取指定等级的所有物品
   * const level5Items = await this.gameConfig.item.findBy('level', 5);
   * ```
   */
  findBy(field: keyof T, value: any): Promise<T[]>;

  /**
   * 根据自定义条件查找第一个匹配的配置项
   * @param predicate 查找条件函数
   * @returns 第一个符合条件的配置项，如果没有找到则返回null
   * @example
   * ```typescript
   * // 查找第一个满足条件的英雄
   * const firstMage = await this.gameConfig.hero.findOne(
   *   hero => hero.type === 'mage' && hero.level > 10
   * );
   *
   * // 查找特定名称的配置
   * const specificHero = await this.gameConfig.hero.findOne(
   *   hero => hero.name === '亚瑟王'
   * );
   * ```
   */
  findOne(predicate: (item: T) => boolean): Promise<T | null>;

  /**
   * 根据指定字段和值查找第一个匹配的配置项
   * @param field 要查找的字段名
   * @param value 字段值
   * @returns 第一个字段值匹配的配置项，如果没有找到则返回null
   * @example
   * ```typescript
   * // 查找指定ID的配置（等同于get方法，但更灵活）
   * const hero = await this.gameConfig.hero.findOneBy('heroId', 90001);
   *
   * // 查找指定名称的配置
   * const hero = await this.gameConfig.hero.findOneBy('name', '梅西');
   *
   * // 查找指定类型的第一个配置
   * const firstShop = await this.gameConfig.shop.findOneBy('type', 'weapon');
   * ```
   */
  findOneBy(field: keyof T, value: any): Promise<T | null>;
}
