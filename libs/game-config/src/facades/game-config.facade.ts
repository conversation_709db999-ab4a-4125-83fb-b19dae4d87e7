import {
  AccountDefinition, ActionDefinition, ActiveControlDefinition, ActiveParamDefinition, ActiveShowControlDefinition,
  ActivityConfigDefinition, AssistantAdviseDefinition, AttackModeDefinition, AttrDefinition, BackWeekendDefinition,
  BagBookMarkDefinition, BattleTestDefinition, BeliefDefinition, BeliefExpDefinition, BeliefFieldDefinition,
  BeliefPointsRewardDefinition, BeliefPricingDefinition, BeliefRateDefinition, BeliefSkillDefinition, BeliefSkillExpendDefinition,
  BeliefSkillUpDefinition, BestFootballDefinition, BigFiveCupRewardDefinition, BusinessMatchRewardDefinition, BuyEnergyDefinition,
  ChampionShopDefinition, ChiefBattleRewardDefinition, ChiefBattleScheduleDefinition, CoachSkillDefinition, CoachUpgradeDefinition,
  CoachUpgradeExpendDefinition, CoachUpstarDefinition, CombinationHandbookDefinition, CommentDefinition, ConfrontationDefinition,
  CountryDefinition, CreateBallerDefinition, CreateTeamFormationDefinition, DailyGiftDefinition, DecomposeDefinition,
  DqCupScheduleDefinition, DropDefinition, EnergySupplyDefinition, EnergyTimesDefinition, ExchangeClubDefinition,
  ExchangeControlDefinition, ExchangeShopDefinition, FaithDefinition, FaithStoreDefinition, FeildTrainningDefinition,
  FieldDefinition, FieldHospitalDefinition, FieldWarScheduleDefinition, FootballLevelDefinition, FormationCoordinateDefinition,
  GiftCodeDefinition, GiftPackageDefinition, GroundMatchFieldDefinition, GroundMatchRewardDefinition, GuildDefinition,
  GulfCupDefinition, GulfCupPlayerDefinition, GulfCupStrengthDefinition, GulfCupTeamDefinition, GulfTeamDefinition,
  HallOfFameDefinition, HeroBreakDefinition, HeroDefinition, HeroEnergyDefinition, HeroFormDefinition,
  HeroFormPointDefinition, HeroFosterDefinition, HeroIconDefinition, HeroInjuryDefinition, HeroPoolDefinition,
  HeroPveDefinition, HeroSkillDefinition, HeroStarDefinition, HeroTrainDefinition, IconDefinition,
  ItemChannelDefinition, ItemDefinition, KingStoreDefinition, LeagueCopyDefinition, LeagueEntranceDefinition,
  LeagueRelayRewardDefinition, LeagueRewardDefinition, LeagueScheduleDefinition, LeagueShopDefinition, LeagueSwitchDefinition,
  LeagueTeamDefinition, LeagueTimeDefinition, LoginSignDefinition, MLSDefinition, MLSPlayerDefinition,
  MLSStrengthDefinition, MLSTeamDefinition, MVPDefinition, MailTextDefinition, MatchEntranceDefinition,
  MiddleEastCupDefinition, MiddleEastPlayerDefinition, MiddleEastStrengthDefinition, MiddleEastTeamDefinition, MustDropDefinition,
  MustDropItemDefinition, NPCHeroDefinition, NavigateDefinition, NewHandTaskDefinition, NewPositionMatchDefinition,
  NewbieGuideDefinition, PlayerHandbookDefinition, PositionMatchDefinition, PullerAwardDefinition, PushPackageDefinition,
  RechargeDefinition, RegressionDefinition, ScoutScopeDefinition, SeasonStoreDefinition, ShareDefinition,
  ShopDefinition, ShopTabDefinition, SkillUnlockDefinition, StateProbabilityDefinition, SystemOpenDefinition,
  SystemParamDefinition, TacticDefinition, TacticResetDefinition, TacticRestrainDefinition, TaskCostDefinition,
  TaskDefinition, TaskMarkDefinition, TeamDefinition, TeamFormationDefinition, TeamLogoDefinition,
  TextTableDefinition, TourMatchDefinition, TrainningCoachDefinition, TrophyDefinition, TrophyRewardDefinition,
  TrophyTeamDefinition, TurntableControlDefinition, TurntableRewardDefinition, UltimateMatchAwardDefinition, UltimateMatchTimeDefinition,
  UpexpDefinition, VersionBulletinDefinition, VipDefinition, WorldBossAwardDefinition, WorldBossDefinition,
  WorldBossValueDefinition, WorldCupDefinition, WorldCupPlayerDefinition, WorldCupRewardDefinition, WorldCupStrengthDefinition,
  WorldCupTeamDefinition, limitStoreDefinition
} from '../interfaces';
import { Injectable } from '@nestjs/common';
import { ConfigManager } from '../core/config-manager.service';
import { ConfigTableAccessor } from './business-types';

/**
 * 游戏配置统一访问门面
 *
 * 🔥 此文件由工具自动生成，请勿手动修改
 *
 * 功能：
 * - 提供157个配置表的统一访问接口
 * - 完整的TypeScript类型安全
 * - 统一的CRUD操作（get, getAll, getBatch, search）
 *
 * 使用方式：
 * ```typescript
 * const hero = await this.gameConfig.hero.get(123);
 * const items = await this.gameConfig.item.getBatch([1,2,3]);
 * ```
 *
 * 重新生成：npm run config:facade
 */
@Injectable()
export class GameConfigFacade {
  account: ConfigTableAccessor<AccountDefinition>;
  action: ConfigTableAccessor<ActionDefinition>;
  activeControl: ConfigTableAccessor<ActiveControlDefinition>;
  activeParam: ConfigTableAccessor<ActiveParamDefinition>;
  activeShowControl: ConfigTableAccessor<ActiveShowControlDefinition>;
  activityConfig: ConfigTableAccessor<ActivityConfigDefinition>;
  assistantAdvise: ConfigTableAccessor<AssistantAdviseDefinition>;
  attackMode: ConfigTableAccessor<AttackModeDefinition>;
  attr: ConfigTableAccessor<AttrDefinition>;
  backWeekend: ConfigTableAccessor<BackWeekendDefinition>;
  bagBookMark: ConfigTableAccessor<BagBookMarkDefinition>;
  battleTest: ConfigTableAccessor<BattleTestDefinition>;
  belief: ConfigTableAccessor<BeliefDefinition>;
  beliefExp: ConfigTableAccessor<BeliefExpDefinition>;
  beliefField: ConfigTableAccessor<BeliefFieldDefinition>;
  beliefPointsReward: ConfigTableAccessor<BeliefPointsRewardDefinition>;
  beliefPricing: ConfigTableAccessor<BeliefPricingDefinition>;
  beliefRate: ConfigTableAccessor<BeliefRateDefinition>;
  beliefSkill: ConfigTableAccessor<BeliefSkillDefinition>;
  beliefSkillExpend: ConfigTableAccessor<BeliefSkillExpendDefinition>;
  beliefSkillUp: ConfigTableAccessor<BeliefSkillUpDefinition>;
  bestFootball: ConfigTableAccessor<BestFootballDefinition>;
  bigFiveCupReward: ConfigTableAccessor<BigFiveCupRewardDefinition>;
  businessMatchReward: ConfigTableAccessor<BusinessMatchRewardDefinition>;
  buyEnergy: ConfigTableAccessor<BuyEnergyDefinition>;
  championShop: ConfigTableAccessor<ChampionShopDefinition>;
  chiefBattleReward: ConfigTableAccessor<ChiefBattleRewardDefinition>;
  chiefBattleSchedule: ConfigTableAccessor<ChiefBattleScheduleDefinition>;
  coachSkill: ConfigTableAccessor<CoachSkillDefinition>;
  coachUpgrade: ConfigTableAccessor<CoachUpgradeDefinition>;
  coachUpgradeExpend: ConfigTableAccessor<CoachUpgradeExpendDefinition>;
  coachUpstar: ConfigTableAccessor<CoachUpstarDefinition>;
  combinationHandbook: ConfigTableAccessor<CombinationHandbookDefinition>;
  comment: ConfigTableAccessor<CommentDefinition>;
  confrontation: ConfigTableAccessor<ConfrontationDefinition>;
  country: ConfigTableAccessor<CountryDefinition>;
  createBaller: ConfigTableAccessor<CreateBallerDefinition>;
  createTeamFormation: ConfigTableAccessor<CreateTeamFormationDefinition>;
  dailyGift: ConfigTableAccessor<DailyGiftDefinition>;
  decompose: ConfigTableAccessor<DecomposeDefinition>;
  dqCupSchedule: ConfigTableAccessor<DqCupScheduleDefinition>;
  drop: ConfigTableAccessor<DropDefinition>;
  energySupply: ConfigTableAccessor<EnergySupplyDefinition>;
  energyTimes: ConfigTableAccessor<EnergyTimesDefinition>;
  exchangeClub: ConfigTableAccessor<ExchangeClubDefinition>;
  exchangeControl: ConfigTableAccessor<ExchangeControlDefinition>;
  exchangeShop: ConfigTableAccessor<ExchangeShopDefinition>;
  faith: ConfigTableAccessor<FaithDefinition>;
  faithStore: ConfigTableAccessor<FaithStoreDefinition>;
  feildTrainning: ConfigTableAccessor<FeildTrainningDefinition>;
  field: ConfigTableAccessor<FieldDefinition>;
  fieldHospital: ConfigTableAccessor<FieldHospitalDefinition>;
  fieldWarSchedule: ConfigTableAccessor<FieldWarScheduleDefinition>;
  footballLevel: ConfigTableAccessor<FootballLevelDefinition>;
  formationCoordinate: ConfigTableAccessor<FormationCoordinateDefinition>;
  giftCode: ConfigTableAccessor<GiftCodeDefinition>;
  giftPackage: ConfigTableAccessor<GiftPackageDefinition>;
  groundMatchField: ConfigTableAccessor<GroundMatchFieldDefinition>;
  groundMatchReward: ConfigTableAccessor<GroundMatchRewardDefinition>;
  guild: ConfigTableAccessor<GuildDefinition>;
  gulfCup: ConfigTableAccessor<GulfCupDefinition>;
  gulfCupPlayer: ConfigTableAccessor<GulfCupPlayerDefinition>;
  gulfCupStrength: ConfigTableAccessor<GulfCupStrengthDefinition>;
  gulfCupTeam: ConfigTableAccessor<GulfCupTeamDefinition>;
  gulfTeam: ConfigTableAccessor<GulfTeamDefinition>;
  hallOfFame: ConfigTableAccessor<HallOfFameDefinition>;
  hero: ConfigTableAccessor<HeroDefinition>;
  heroBreak: ConfigTableAccessor<HeroBreakDefinition>;
  heroEnergy: ConfigTableAccessor<HeroEnergyDefinition>;
  heroForm: ConfigTableAccessor<HeroFormDefinition>;
  heroFormPoint: ConfigTableAccessor<HeroFormPointDefinition>;
  heroFoster: ConfigTableAccessor<HeroFosterDefinition>;
  heroIcon: ConfigTableAccessor<HeroIconDefinition>;
  heroInjury: ConfigTableAccessor<HeroInjuryDefinition>;
  heroPool: ConfigTableAccessor<HeroPoolDefinition>;
  heroPve: ConfigTableAccessor<HeroPveDefinition>;
  heroSkill: ConfigTableAccessor<HeroSkillDefinition>;
  heroStar: ConfigTableAccessor<HeroStarDefinition>;
  heroTrain: ConfigTableAccessor<HeroTrainDefinition>;
  icon: ConfigTableAccessor<IconDefinition>;
  item: ConfigTableAccessor<ItemDefinition>;
  itemChannel: ConfigTableAccessor<ItemChannelDefinition>;
  kingStore: ConfigTableAccessor<KingStoreDefinition>;
  leagueCopy: ConfigTableAccessor<LeagueCopyDefinition>;
  leagueEntrance: ConfigTableAccessor<LeagueEntranceDefinition>;
  leagueRelayReward: ConfigTableAccessor<LeagueRelayRewardDefinition>;
  leagueReward: ConfigTableAccessor<LeagueRewardDefinition>;
  leagueSchedule: ConfigTableAccessor<LeagueScheduleDefinition>;
  leagueShop: ConfigTableAccessor<LeagueShopDefinition>;
  leagueSwitch: ConfigTableAccessor<LeagueSwitchDefinition>;
  leagueTeam: ConfigTableAccessor<LeagueTeamDefinition>;
  leagueTime: ConfigTableAccessor<LeagueTimeDefinition>;
  limitStore: ConfigTableAccessor<limitStoreDefinition>;
  loginSign: ConfigTableAccessor<LoginSignDefinition>;
  mailText: ConfigTableAccessor<MailTextDefinition>;
  matchEntrance: ConfigTableAccessor<MatchEntranceDefinition>;
  middleEastCup: ConfigTableAccessor<MiddleEastCupDefinition>;
  middleEastPlayer: ConfigTableAccessor<MiddleEastPlayerDefinition>;
  middleEastStrength: ConfigTableAccessor<MiddleEastStrengthDefinition>;
  middleEastTeam: ConfigTableAccessor<MiddleEastTeamDefinition>;
  mLS: ConfigTableAccessor<MLSDefinition>;
  mLSPlayer: ConfigTableAccessor<MLSPlayerDefinition>;
  mLSStrength: ConfigTableAccessor<MLSStrengthDefinition>;
  mLSTeam: ConfigTableAccessor<MLSTeamDefinition>;
  mustDrop: ConfigTableAccessor<MustDropDefinition>;
  mustDropItem: ConfigTableAccessor<MustDropItemDefinition>;
  mVP: ConfigTableAccessor<MVPDefinition>;
  navigate: ConfigTableAccessor<NavigateDefinition>;
  newbieGuide: ConfigTableAccessor<NewbieGuideDefinition>;
  newHandTask: ConfigTableAccessor<NewHandTaskDefinition>;
  newPositionMatch: ConfigTableAccessor<NewPositionMatchDefinition>;
  nPCHero: ConfigTableAccessor<NPCHeroDefinition>;
  playerHandbook: ConfigTableAccessor<PlayerHandbookDefinition>;
  positionMatch: ConfigTableAccessor<PositionMatchDefinition>;
  pullerAward: ConfigTableAccessor<PullerAwardDefinition>;
  pushPackage: ConfigTableAccessor<PushPackageDefinition>;
  recharge: ConfigTableAccessor<RechargeDefinition>;
  regression: ConfigTableAccessor<RegressionDefinition>;
  scoutScope: ConfigTableAccessor<ScoutScopeDefinition>;
  seasonStore: ConfigTableAccessor<SeasonStoreDefinition>;
  share: ConfigTableAccessor<ShareDefinition>;
  shop: ConfigTableAccessor<ShopDefinition>;
  shopTab: ConfigTableAccessor<ShopTabDefinition>;
  skillUnlock: ConfigTableAccessor<SkillUnlockDefinition>;
  stateProbability: ConfigTableAccessor<StateProbabilityDefinition>;
  systemOpen: ConfigTableAccessor<SystemOpenDefinition>;
  systemParam: ConfigTableAccessor<SystemParamDefinition>;
  tactic: ConfigTableAccessor<TacticDefinition>;
  tacticReset: ConfigTableAccessor<TacticResetDefinition>;
  tacticRestrain: ConfigTableAccessor<TacticRestrainDefinition>;
  task: ConfigTableAccessor<TaskDefinition>;
  taskCost: ConfigTableAccessor<TaskCostDefinition>;
  taskMark: ConfigTableAccessor<TaskMarkDefinition>;
  team: ConfigTableAccessor<TeamDefinition>;
  teamFormation: ConfigTableAccessor<TeamFormationDefinition>;
  teamLogo: ConfigTableAccessor<TeamLogoDefinition>;
  textTable: ConfigTableAccessor<TextTableDefinition>;
  tourMatch: ConfigTableAccessor<TourMatchDefinition>;
  trainningCoach: ConfigTableAccessor<TrainningCoachDefinition>;
  trophy: ConfigTableAccessor<TrophyDefinition>;
  trophyReward: ConfigTableAccessor<TrophyRewardDefinition>;
  trophyTeam: ConfigTableAccessor<TrophyTeamDefinition>;
  turntableControl: ConfigTableAccessor<TurntableControlDefinition>;
  turntableReward: ConfigTableAccessor<TurntableRewardDefinition>;
  ultimateMatchAward: ConfigTableAccessor<UltimateMatchAwardDefinition>;
  ultimateMatchTime: ConfigTableAccessor<UltimateMatchTimeDefinition>;
  upexp: ConfigTableAccessor<UpexpDefinition>;
  versionBulletin: ConfigTableAccessor<VersionBulletinDefinition>;
  vip: ConfigTableAccessor<VipDefinition>;
  worldBoss: ConfigTableAccessor<WorldBossDefinition>;
  worldBossAward: ConfigTableAccessor<WorldBossAwardDefinition>;
  worldBossValue: ConfigTableAccessor<WorldBossValueDefinition>;
  worldCup: ConfigTableAccessor<WorldCupDefinition>;
  worldCupPlayer: ConfigTableAccessor<WorldCupPlayerDefinition>;
  worldCupReward: ConfigTableAccessor<WorldCupRewardDefinition>;
  worldCupStrength: ConfigTableAccessor<WorldCupStrengthDefinition>;
  worldCupTeam: ConfigTableAccessor<WorldCupTeamDefinition>;

  constructor(private readonly configManager: ConfigManager) {
    this.initializeTableAccessors();
  }

  /**
   * 初始化所有表访问器
   */
  private initializeTableAccessors(): void {
    this.account = {
      get: (id: number) => this.configManager.get<AccountDefinition>('Account', id),
      getAll: () => this.configManager.getAll<AccountDefinition>('Account'),
      getBatch: (ids: number[]) => this.configManager.getBatch<AccountDefinition>('Account', ids),
      search: (keyword: string) => this.configManager.search<AccountDefinition>('Account', keyword),
      filter: (predicate: (item: AccountDefinition) => boolean) => this.configManager.filter<AccountDefinition>('Account', predicate),
      findBy: (field: keyof AccountDefinition, value: any) => this.configManager.findBy<AccountDefinition>('Account', field, value),
      findOne: (predicate: (item: AccountDefinition) => boolean) => this.configManager.findOne<AccountDefinition>('Account', predicate),
      findOneBy: (field: keyof AccountDefinition, value: any) => this.configManager.findOneBy<AccountDefinition>('Account', field, value)
    };

    this.action = {
      get: (id: number) => this.configManager.get<ActionDefinition>('Action', id),
      getAll: () => this.configManager.getAll<ActionDefinition>('Action'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ActionDefinition>('Action', ids),
      search: (keyword: string) => this.configManager.search<ActionDefinition>('Action', keyword),
      filter: (predicate: (item: ActionDefinition) => boolean) => this.configManager.filter<ActionDefinition>('Action', predicate),
      findBy: (field: keyof ActionDefinition, value: any) => this.configManager.findBy<ActionDefinition>('Action', field, value),
      findOne: (predicate: (item: ActionDefinition) => boolean) => this.configManager.findOne<ActionDefinition>('Action', predicate),
      findOneBy: (field: keyof ActionDefinition, value: any) => this.configManager.findOneBy<ActionDefinition>('Action', field, value)
    };

    this.activeControl = {
      get: (id: number) => this.configManager.get<ActiveControlDefinition>('ActiveControl', id),
      getAll: () => this.configManager.getAll<ActiveControlDefinition>('ActiveControl'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ActiveControlDefinition>('ActiveControl', ids),
      search: (keyword: string) => this.configManager.search<ActiveControlDefinition>('ActiveControl', keyword),
      filter: (predicate: (item: ActiveControlDefinition) => boolean) => this.configManager.filter<ActiveControlDefinition>('ActiveControl', predicate),
      findBy: (field: keyof ActiveControlDefinition, value: any) => this.configManager.findBy<ActiveControlDefinition>('ActiveControl', field, value),
      findOne: (predicate: (item: ActiveControlDefinition) => boolean) => this.configManager.findOne<ActiveControlDefinition>('ActiveControl', predicate),
      findOneBy: (field: keyof ActiveControlDefinition, value: any) => this.configManager.findOneBy<ActiveControlDefinition>('ActiveControl', field, value)
    };

    this.activeParam = {
      get: (id: number) => this.configManager.get<ActiveParamDefinition>('ActiveParam', id),
      getAll: () => this.configManager.getAll<ActiveParamDefinition>('ActiveParam'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ActiveParamDefinition>('ActiveParam', ids),
      search: (keyword: string) => this.configManager.search<ActiveParamDefinition>('ActiveParam', keyword),
      filter: (predicate: (item: ActiveParamDefinition) => boolean) => this.configManager.filter<ActiveParamDefinition>('ActiveParam', predicate),
      findBy: (field: keyof ActiveParamDefinition, value: any) => this.configManager.findBy<ActiveParamDefinition>('ActiveParam', field, value),
      findOne: (predicate: (item: ActiveParamDefinition) => boolean) => this.configManager.findOne<ActiveParamDefinition>('ActiveParam', predicate),
      findOneBy: (field: keyof ActiveParamDefinition, value: any) => this.configManager.findOneBy<ActiveParamDefinition>('ActiveParam', field, value)
    };

    this.activeShowControl = {
      get: (id: number) => this.configManager.get<ActiveShowControlDefinition>('ActiveShowControl', id),
      getAll: () => this.configManager.getAll<ActiveShowControlDefinition>('ActiveShowControl'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ActiveShowControlDefinition>('ActiveShowControl', ids),
      search: (keyword: string) => this.configManager.search<ActiveShowControlDefinition>('ActiveShowControl', keyword),
      filter: (predicate: (item: ActiveShowControlDefinition) => boolean) => this.configManager.filter<ActiveShowControlDefinition>('ActiveShowControl', predicate),
      findBy: (field: keyof ActiveShowControlDefinition, value: any) => this.configManager.findBy<ActiveShowControlDefinition>('ActiveShowControl', field, value),
      findOne: (predicate: (item: ActiveShowControlDefinition) => boolean) => this.configManager.findOne<ActiveShowControlDefinition>('ActiveShowControl', predicate),
      findOneBy: (field: keyof ActiveShowControlDefinition, value: any) => this.configManager.findOneBy<ActiveShowControlDefinition>('ActiveShowControl', field, value)
    };

    this.activityConfig = {
      get: (id: number) => this.configManager.get<ActivityConfigDefinition>('ActivityConfig', id),
      getAll: () => this.configManager.getAll<ActivityConfigDefinition>('ActivityConfig'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ActivityConfigDefinition>('ActivityConfig', ids),
      search: (keyword: string) => this.configManager.search<ActivityConfigDefinition>('ActivityConfig', keyword),
      filter: (predicate: (item: ActivityConfigDefinition) => boolean) => this.configManager.filter<ActivityConfigDefinition>('ActivityConfig', predicate),
      findBy: (field: keyof ActivityConfigDefinition, value: any) => this.configManager.findBy<ActivityConfigDefinition>('ActivityConfig', field, value),
      findOne: (predicate: (item: ActivityConfigDefinition) => boolean) => this.configManager.findOne<ActivityConfigDefinition>('ActivityConfig', predicate),
      findOneBy: (field: keyof ActivityConfigDefinition, value: any) => this.configManager.findOneBy<ActivityConfigDefinition>('ActivityConfig', field, value)
    };

    this.assistantAdvise = {
      get: (id: number) => this.configManager.get<AssistantAdviseDefinition>('AssistantAdvise', id),
      getAll: () => this.configManager.getAll<AssistantAdviseDefinition>('AssistantAdvise'),
      getBatch: (ids: number[]) => this.configManager.getBatch<AssistantAdviseDefinition>('AssistantAdvise', ids),
      search: (keyword: string) => this.configManager.search<AssistantAdviseDefinition>('AssistantAdvise', keyword),
      filter: (predicate: (item: AssistantAdviseDefinition) => boolean) => this.configManager.filter<AssistantAdviseDefinition>('AssistantAdvise', predicate),
      findBy: (field: keyof AssistantAdviseDefinition, value: any) => this.configManager.findBy<AssistantAdviseDefinition>('AssistantAdvise', field, value),
      findOne: (predicate: (item: AssistantAdviseDefinition) => boolean) => this.configManager.findOne<AssistantAdviseDefinition>('AssistantAdvise', predicate),
      findOneBy: (field: keyof AssistantAdviseDefinition, value: any) => this.configManager.findOneBy<AssistantAdviseDefinition>('AssistantAdvise', field, value)
    };

    this.attackMode = {
      get: (id: number) => this.configManager.get<AttackModeDefinition>('AttackMode', id),
      getAll: () => this.configManager.getAll<AttackModeDefinition>('AttackMode'),
      getBatch: (ids: number[]) => this.configManager.getBatch<AttackModeDefinition>('AttackMode', ids),
      search: (keyword: string) => this.configManager.search<AttackModeDefinition>('AttackMode', keyword),
      filter: (predicate: (item: AttackModeDefinition) => boolean) => this.configManager.filter<AttackModeDefinition>('AttackMode', predicate),
      findBy: (field: keyof AttackModeDefinition, value: any) => this.configManager.findBy<AttackModeDefinition>('AttackMode', field, value),
      findOne: (predicate: (item: AttackModeDefinition) => boolean) => this.configManager.findOne<AttackModeDefinition>('AttackMode', predicate),
      findOneBy: (field: keyof AttackModeDefinition, value: any) => this.configManager.findOneBy<AttackModeDefinition>('AttackMode', field, value)
    };

    this.attr = {
      get: (id: number) => this.configManager.get<AttrDefinition>('Attr', id),
      getAll: () => this.configManager.getAll<AttrDefinition>('Attr'),
      getBatch: (ids: number[]) => this.configManager.getBatch<AttrDefinition>('Attr', ids),
      search: (keyword: string) => this.configManager.search<AttrDefinition>('Attr', keyword),
      filter: (predicate: (item: AttrDefinition) => boolean) => this.configManager.filter<AttrDefinition>('Attr', predicate),
      findBy: (field: keyof AttrDefinition, value: any) => this.configManager.findBy<AttrDefinition>('Attr', field, value),
      findOne: (predicate: (item: AttrDefinition) => boolean) => this.configManager.findOne<AttrDefinition>('Attr', predicate),
      findOneBy: (field: keyof AttrDefinition, value: any) => this.configManager.findOneBy<AttrDefinition>('Attr', field, value)
    };

    this.backWeekend = {
      get: (id: number) => this.configManager.get<BackWeekendDefinition>('BackWeekend', id),
      getAll: () => this.configManager.getAll<BackWeekendDefinition>('BackWeekend'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BackWeekendDefinition>('BackWeekend', ids),
      search: (keyword: string) => this.configManager.search<BackWeekendDefinition>('BackWeekend', keyword),
      filter: (predicate: (item: BackWeekendDefinition) => boolean) => this.configManager.filter<BackWeekendDefinition>('BackWeekend', predicate),
      findBy: (field: keyof BackWeekendDefinition, value: any) => this.configManager.findBy<BackWeekendDefinition>('BackWeekend', field, value),
      findOne: (predicate: (item: BackWeekendDefinition) => boolean) => this.configManager.findOne<BackWeekendDefinition>('BackWeekend', predicate),
      findOneBy: (field: keyof BackWeekendDefinition, value: any) => this.configManager.findOneBy<BackWeekendDefinition>('BackWeekend', field, value)
    };

    this.bagBookMark = {
      get: (id: number) => this.configManager.get<BagBookMarkDefinition>('BagBookMark', id),
      getAll: () => this.configManager.getAll<BagBookMarkDefinition>('BagBookMark'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BagBookMarkDefinition>('BagBookMark', ids),
      search: (keyword: string) => this.configManager.search<BagBookMarkDefinition>('BagBookMark', keyword),
      filter: (predicate: (item: BagBookMarkDefinition) => boolean) => this.configManager.filter<BagBookMarkDefinition>('BagBookMark', predicate),
      findBy: (field: keyof BagBookMarkDefinition, value: any) => this.configManager.findBy<BagBookMarkDefinition>('BagBookMark', field, value),
      findOne: (predicate: (item: BagBookMarkDefinition) => boolean) => this.configManager.findOne<BagBookMarkDefinition>('BagBookMark', predicate),
      findOneBy: (field: keyof BagBookMarkDefinition, value: any) => this.configManager.findOneBy<BagBookMarkDefinition>('BagBookMark', field, value)
    };

    this.battleTest = {
      get: (id: number) => this.configManager.get<BattleTestDefinition>('BattleTest', id),
      getAll: () => this.configManager.getAll<BattleTestDefinition>('BattleTest'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BattleTestDefinition>('BattleTest', ids),
      search: (keyword: string) => this.configManager.search<BattleTestDefinition>('BattleTest', keyword),
      filter: (predicate: (item: BattleTestDefinition) => boolean) => this.configManager.filter<BattleTestDefinition>('BattleTest', predicate),
      findBy: (field: keyof BattleTestDefinition, value: any) => this.configManager.findBy<BattleTestDefinition>('BattleTest', field, value),
      findOne: (predicate: (item: BattleTestDefinition) => boolean) => this.configManager.findOne<BattleTestDefinition>('BattleTest', predicate),
      findOneBy: (field: keyof BattleTestDefinition, value: any) => this.configManager.findOneBy<BattleTestDefinition>('BattleTest', field, value)
    };

    this.belief = {
      get: (id: number) => this.configManager.get<BeliefDefinition>('Belief', id),
      getAll: () => this.configManager.getAll<BeliefDefinition>('Belief'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BeliefDefinition>('Belief', ids),
      search: (keyword: string) => this.configManager.search<BeliefDefinition>('Belief', keyword),
      filter: (predicate: (item: BeliefDefinition) => boolean) => this.configManager.filter<BeliefDefinition>('Belief', predicate),
      findBy: (field: keyof BeliefDefinition, value: any) => this.configManager.findBy<BeliefDefinition>('Belief', field, value),
      findOne: (predicate: (item: BeliefDefinition) => boolean) => this.configManager.findOne<BeliefDefinition>('Belief', predicate),
      findOneBy: (field: keyof BeliefDefinition, value: any) => this.configManager.findOneBy<BeliefDefinition>('Belief', field, value)
    };

    this.beliefExp = {
      get: (id: number) => this.configManager.get<BeliefExpDefinition>('BeliefExp', id),
      getAll: () => this.configManager.getAll<BeliefExpDefinition>('BeliefExp'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BeliefExpDefinition>('BeliefExp', ids),
      search: (keyword: string) => this.configManager.search<BeliefExpDefinition>('BeliefExp', keyword),
      filter: (predicate: (item: BeliefExpDefinition) => boolean) => this.configManager.filter<BeliefExpDefinition>('BeliefExp', predicate),
      findBy: (field: keyof BeliefExpDefinition, value: any) => this.configManager.findBy<BeliefExpDefinition>('BeliefExp', field, value),
      findOne: (predicate: (item: BeliefExpDefinition) => boolean) => this.configManager.findOne<BeliefExpDefinition>('BeliefExp', predicate),
      findOneBy: (field: keyof BeliefExpDefinition, value: any) => this.configManager.findOneBy<BeliefExpDefinition>('BeliefExp', field, value)
    };

    this.beliefField = {
      get: (id: number) => this.configManager.get<BeliefFieldDefinition>('BeliefField', id),
      getAll: () => this.configManager.getAll<BeliefFieldDefinition>('BeliefField'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BeliefFieldDefinition>('BeliefField', ids),
      search: (keyword: string) => this.configManager.search<BeliefFieldDefinition>('BeliefField', keyword),
      filter: (predicate: (item: BeliefFieldDefinition) => boolean) => this.configManager.filter<BeliefFieldDefinition>('BeliefField', predicate),
      findBy: (field: keyof BeliefFieldDefinition, value: any) => this.configManager.findBy<BeliefFieldDefinition>('BeliefField', field, value),
      findOne: (predicate: (item: BeliefFieldDefinition) => boolean) => this.configManager.findOne<BeliefFieldDefinition>('BeliefField', predicate),
      findOneBy: (field: keyof BeliefFieldDefinition, value: any) => this.configManager.findOneBy<BeliefFieldDefinition>('BeliefField', field, value)
    };

    this.beliefPointsReward = {
      get: (id: number) => this.configManager.get<BeliefPointsRewardDefinition>('BeliefPointsReward', id),
      getAll: () => this.configManager.getAll<BeliefPointsRewardDefinition>('BeliefPointsReward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BeliefPointsRewardDefinition>('BeliefPointsReward', ids),
      search: (keyword: string) => this.configManager.search<BeliefPointsRewardDefinition>('BeliefPointsReward', keyword),
      filter: (predicate: (item: BeliefPointsRewardDefinition) => boolean) => this.configManager.filter<BeliefPointsRewardDefinition>('BeliefPointsReward', predicate),
      findBy: (field: keyof BeliefPointsRewardDefinition, value: any) => this.configManager.findBy<BeliefPointsRewardDefinition>('BeliefPointsReward', field, value),
      findOne: (predicate: (item: BeliefPointsRewardDefinition) => boolean) => this.configManager.findOne<BeliefPointsRewardDefinition>('BeliefPointsReward', predicate),
      findOneBy: (field: keyof BeliefPointsRewardDefinition, value: any) => this.configManager.findOneBy<BeliefPointsRewardDefinition>('BeliefPointsReward', field, value)
    };

    this.beliefPricing = {
      get: (id: number) => this.configManager.get<BeliefPricingDefinition>('BeliefPricing', id),
      getAll: () => this.configManager.getAll<BeliefPricingDefinition>('BeliefPricing'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BeliefPricingDefinition>('BeliefPricing', ids),
      search: (keyword: string) => this.configManager.search<BeliefPricingDefinition>('BeliefPricing', keyword),
      filter: (predicate: (item: BeliefPricingDefinition) => boolean) => this.configManager.filter<BeliefPricingDefinition>('BeliefPricing', predicate),
      findBy: (field: keyof BeliefPricingDefinition, value: any) => this.configManager.findBy<BeliefPricingDefinition>('BeliefPricing', field, value),
      findOne: (predicate: (item: BeliefPricingDefinition) => boolean) => this.configManager.findOne<BeliefPricingDefinition>('BeliefPricing', predicate),
      findOneBy: (field: keyof BeliefPricingDefinition, value: any) => this.configManager.findOneBy<BeliefPricingDefinition>('BeliefPricing', field, value)
    };

    this.beliefRate = {
      get: (id: number) => this.configManager.get<BeliefRateDefinition>('BeliefRate', id),
      getAll: () => this.configManager.getAll<BeliefRateDefinition>('BeliefRate'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BeliefRateDefinition>('BeliefRate', ids),
      search: (keyword: string) => this.configManager.search<BeliefRateDefinition>('BeliefRate', keyword),
      filter: (predicate: (item: BeliefRateDefinition) => boolean) => this.configManager.filter<BeliefRateDefinition>('BeliefRate', predicate),
      findBy: (field: keyof BeliefRateDefinition, value: any) => this.configManager.findBy<BeliefRateDefinition>('BeliefRate', field, value),
      findOne: (predicate: (item: BeliefRateDefinition) => boolean) => this.configManager.findOne<BeliefRateDefinition>('BeliefRate', predicate),
      findOneBy: (field: keyof BeliefRateDefinition, value: any) => this.configManager.findOneBy<BeliefRateDefinition>('BeliefRate', field, value)
    };

    this.beliefSkill = {
      get: (id: number) => this.configManager.get<BeliefSkillDefinition>('BeliefSkill', id),
      getAll: () => this.configManager.getAll<BeliefSkillDefinition>('BeliefSkill'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BeliefSkillDefinition>('BeliefSkill', ids),
      search: (keyword: string) => this.configManager.search<BeliefSkillDefinition>('BeliefSkill', keyword),
      filter: (predicate: (item: BeliefSkillDefinition) => boolean) => this.configManager.filter<BeliefSkillDefinition>('BeliefSkill', predicate),
      findBy: (field: keyof BeliefSkillDefinition, value: any) => this.configManager.findBy<BeliefSkillDefinition>('BeliefSkill', field, value),
      findOne: (predicate: (item: BeliefSkillDefinition) => boolean) => this.configManager.findOne<BeliefSkillDefinition>('BeliefSkill', predicate),
      findOneBy: (field: keyof BeliefSkillDefinition, value: any) => this.configManager.findOneBy<BeliefSkillDefinition>('BeliefSkill', field, value)
    };

    this.beliefSkillExpend = {
      get: (id: number) => this.configManager.get<BeliefSkillExpendDefinition>('BeliefSkillExpend', id),
      getAll: () => this.configManager.getAll<BeliefSkillExpendDefinition>('BeliefSkillExpend'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BeliefSkillExpendDefinition>('BeliefSkillExpend', ids),
      search: (keyword: string) => this.configManager.search<BeliefSkillExpendDefinition>('BeliefSkillExpend', keyword),
      filter: (predicate: (item: BeliefSkillExpendDefinition) => boolean) => this.configManager.filter<BeliefSkillExpendDefinition>('BeliefSkillExpend', predicate),
      findBy: (field: keyof BeliefSkillExpendDefinition, value: any) => this.configManager.findBy<BeliefSkillExpendDefinition>('BeliefSkillExpend', field, value),
      findOne: (predicate: (item: BeliefSkillExpendDefinition) => boolean) => this.configManager.findOne<BeliefSkillExpendDefinition>('BeliefSkillExpend', predicate),
      findOneBy: (field: keyof BeliefSkillExpendDefinition, value: any) => this.configManager.findOneBy<BeliefSkillExpendDefinition>('BeliefSkillExpend', field, value)
    };

    this.beliefSkillUp = {
      get: (id: number) => this.configManager.get<BeliefSkillUpDefinition>('BeliefSkillUp', id),
      getAll: () => this.configManager.getAll<BeliefSkillUpDefinition>('BeliefSkillUp'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BeliefSkillUpDefinition>('BeliefSkillUp', ids),
      search: (keyword: string) => this.configManager.search<BeliefSkillUpDefinition>('BeliefSkillUp', keyword),
      filter: (predicate: (item: BeliefSkillUpDefinition) => boolean) => this.configManager.filter<BeliefSkillUpDefinition>('BeliefSkillUp', predicate),
      findBy: (field: keyof BeliefSkillUpDefinition, value: any) => this.configManager.findBy<BeliefSkillUpDefinition>('BeliefSkillUp', field, value),
      findOne: (predicate: (item: BeliefSkillUpDefinition) => boolean) => this.configManager.findOne<BeliefSkillUpDefinition>('BeliefSkillUp', predicate),
      findOneBy: (field: keyof BeliefSkillUpDefinition, value: any) => this.configManager.findOneBy<BeliefSkillUpDefinition>('BeliefSkillUp', field, value)
    };

    this.bestFootball = {
      get: (id: number) => this.configManager.get<BestFootballDefinition>('BestFootball', id),
      getAll: () => this.configManager.getAll<BestFootballDefinition>('BestFootball'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BestFootballDefinition>('BestFootball', ids),
      search: (keyword: string) => this.configManager.search<BestFootballDefinition>('BestFootball', keyword),
      filter: (predicate: (item: BestFootballDefinition) => boolean) => this.configManager.filter<BestFootballDefinition>('BestFootball', predicate),
      findBy: (field: keyof BestFootballDefinition, value: any) => this.configManager.findBy<BestFootballDefinition>('BestFootball', field, value),
      findOne: (predicate: (item: BestFootballDefinition) => boolean) => this.configManager.findOne<BestFootballDefinition>('BestFootball', predicate),
      findOneBy: (field: keyof BestFootballDefinition, value: any) => this.configManager.findOneBy<BestFootballDefinition>('BestFootball', field, value)
    };

    this.bigFiveCupReward = {
      get: (id: number) => this.configManager.get<BigFiveCupRewardDefinition>('BigFiveCupReward', id),
      getAll: () => this.configManager.getAll<BigFiveCupRewardDefinition>('BigFiveCupReward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BigFiveCupRewardDefinition>('BigFiveCupReward', ids),
      search: (keyword: string) => this.configManager.search<BigFiveCupRewardDefinition>('BigFiveCupReward', keyword),
      filter: (predicate: (item: BigFiveCupRewardDefinition) => boolean) => this.configManager.filter<BigFiveCupRewardDefinition>('BigFiveCupReward', predicate),
      findBy: (field: keyof BigFiveCupRewardDefinition, value: any) => this.configManager.findBy<BigFiveCupRewardDefinition>('BigFiveCupReward', field, value),
      findOne: (predicate: (item: BigFiveCupRewardDefinition) => boolean) => this.configManager.findOne<BigFiveCupRewardDefinition>('BigFiveCupReward', predicate),
      findOneBy: (field: keyof BigFiveCupRewardDefinition, value: any) => this.configManager.findOneBy<BigFiveCupRewardDefinition>('BigFiveCupReward', field, value)
    };

    this.businessMatchReward = {
      get: (id: number) => this.configManager.get<BusinessMatchRewardDefinition>('BusinessMatchReward', id),
      getAll: () => this.configManager.getAll<BusinessMatchRewardDefinition>('BusinessMatchReward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BusinessMatchRewardDefinition>('BusinessMatchReward', ids),
      search: (keyword: string) => this.configManager.search<BusinessMatchRewardDefinition>('BusinessMatchReward', keyword),
      filter: (predicate: (item: BusinessMatchRewardDefinition) => boolean) => this.configManager.filter<BusinessMatchRewardDefinition>('BusinessMatchReward', predicate),
      findBy: (field: keyof BusinessMatchRewardDefinition, value: any) => this.configManager.findBy<BusinessMatchRewardDefinition>('BusinessMatchReward', field, value),
      findOne: (predicate: (item: BusinessMatchRewardDefinition) => boolean) => this.configManager.findOne<BusinessMatchRewardDefinition>('BusinessMatchReward', predicate),
      findOneBy: (field: keyof BusinessMatchRewardDefinition, value: any) => this.configManager.findOneBy<BusinessMatchRewardDefinition>('BusinessMatchReward', field, value)
    };

    this.buyEnergy = {
      get: (id: number) => this.configManager.get<BuyEnergyDefinition>('BuyEnergy', id),
      getAll: () => this.configManager.getAll<BuyEnergyDefinition>('BuyEnergy'),
      getBatch: (ids: number[]) => this.configManager.getBatch<BuyEnergyDefinition>('BuyEnergy', ids),
      search: (keyword: string) => this.configManager.search<BuyEnergyDefinition>('BuyEnergy', keyword),
      filter: (predicate: (item: BuyEnergyDefinition) => boolean) => this.configManager.filter<BuyEnergyDefinition>('BuyEnergy', predicate),
      findBy: (field: keyof BuyEnergyDefinition, value: any) => this.configManager.findBy<BuyEnergyDefinition>('BuyEnergy', field, value),
      findOne: (predicate: (item: BuyEnergyDefinition) => boolean) => this.configManager.findOne<BuyEnergyDefinition>('BuyEnergy', predicate),
      findOneBy: (field: keyof BuyEnergyDefinition, value: any) => this.configManager.findOneBy<BuyEnergyDefinition>('BuyEnergy', field, value)
    };

    this.championShop = {
      get: (id: number) => this.configManager.get<ChampionShopDefinition>('ChampionShop', id),
      getAll: () => this.configManager.getAll<ChampionShopDefinition>('ChampionShop'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ChampionShopDefinition>('ChampionShop', ids),
      search: (keyword: string) => this.configManager.search<ChampionShopDefinition>('ChampionShop', keyword),
      filter: (predicate: (item: ChampionShopDefinition) => boolean) => this.configManager.filter<ChampionShopDefinition>('ChampionShop', predicate),
      findBy: (field: keyof ChampionShopDefinition, value: any) => this.configManager.findBy<ChampionShopDefinition>('ChampionShop', field, value),
      findOne: (predicate: (item: ChampionShopDefinition) => boolean) => this.configManager.findOne<ChampionShopDefinition>('ChampionShop', predicate),
      findOneBy: (field: keyof ChampionShopDefinition, value: any) => this.configManager.findOneBy<ChampionShopDefinition>('ChampionShop', field, value)
    };

    this.chiefBattleReward = {
      get: (id: number) => this.configManager.get<ChiefBattleRewardDefinition>('ChiefBattleReward', id),
      getAll: () => this.configManager.getAll<ChiefBattleRewardDefinition>('ChiefBattleReward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ChiefBattleRewardDefinition>('ChiefBattleReward', ids),
      search: (keyword: string) => this.configManager.search<ChiefBattleRewardDefinition>('ChiefBattleReward', keyword),
      filter: (predicate: (item: ChiefBattleRewardDefinition) => boolean) => this.configManager.filter<ChiefBattleRewardDefinition>('ChiefBattleReward', predicate),
      findBy: (field: keyof ChiefBattleRewardDefinition, value: any) => this.configManager.findBy<ChiefBattleRewardDefinition>('ChiefBattleReward', field, value),
      findOne: (predicate: (item: ChiefBattleRewardDefinition) => boolean) => this.configManager.findOne<ChiefBattleRewardDefinition>('ChiefBattleReward', predicate),
      findOneBy: (field: keyof ChiefBattleRewardDefinition, value: any) => this.configManager.findOneBy<ChiefBattleRewardDefinition>('ChiefBattleReward', field, value)
    };

    this.chiefBattleSchedule = {
      get: (id: number) => this.configManager.get<ChiefBattleScheduleDefinition>('ChiefBattleSchedule', id),
      getAll: () => this.configManager.getAll<ChiefBattleScheduleDefinition>('ChiefBattleSchedule'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ChiefBattleScheduleDefinition>('ChiefBattleSchedule', ids),
      search: (keyword: string) => this.configManager.search<ChiefBattleScheduleDefinition>('ChiefBattleSchedule', keyword),
      filter: (predicate: (item: ChiefBattleScheduleDefinition) => boolean) => this.configManager.filter<ChiefBattleScheduleDefinition>('ChiefBattleSchedule', predicate),
      findBy: (field: keyof ChiefBattleScheduleDefinition, value: any) => this.configManager.findBy<ChiefBattleScheduleDefinition>('ChiefBattleSchedule', field, value),
      findOne: (predicate: (item: ChiefBattleScheduleDefinition) => boolean) => this.configManager.findOne<ChiefBattleScheduleDefinition>('ChiefBattleSchedule', predicate),
      findOneBy: (field: keyof ChiefBattleScheduleDefinition, value: any) => this.configManager.findOneBy<ChiefBattleScheduleDefinition>('ChiefBattleSchedule', field, value)
    };

    this.coachSkill = {
      get: (id: number) => this.configManager.get<CoachSkillDefinition>('CoachSkill', id),
      getAll: () => this.configManager.getAll<CoachSkillDefinition>('CoachSkill'),
      getBatch: (ids: number[]) => this.configManager.getBatch<CoachSkillDefinition>('CoachSkill', ids),
      search: (keyword: string) => this.configManager.search<CoachSkillDefinition>('CoachSkill', keyword),
      filter: (predicate: (item: CoachSkillDefinition) => boolean) => this.configManager.filter<CoachSkillDefinition>('CoachSkill', predicate),
      findBy: (field: keyof CoachSkillDefinition, value: any) => this.configManager.findBy<CoachSkillDefinition>('CoachSkill', field, value),
      findOne: (predicate: (item: CoachSkillDefinition) => boolean) => this.configManager.findOne<CoachSkillDefinition>('CoachSkill', predicate),
      findOneBy: (field: keyof CoachSkillDefinition, value: any) => this.configManager.findOneBy<CoachSkillDefinition>('CoachSkill', field, value)
    };

    this.coachUpgrade = {
      get: (id: number) => this.configManager.get<CoachUpgradeDefinition>('CoachUpgrade', id),
      getAll: () => this.configManager.getAll<CoachUpgradeDefinition>('CoachUpgrade'),
      getBatch: (ids: number[]) => this.configManager.getBatch<CoachUpgradeDefinition>('CoachUpgrade', ids),
      search: (keyword: string) => this.configManager.search<CoachUpgradeDefinition>('CoachUpgrade', keyword),
      filter: (predicate: (item: CoachUpgradeDefinition) => boolean) => this.configManager.filter<CoachUpgradeDefinition>('CoachUpgrade', predicate),
      findBy: (field: keyof CoachUpgradeDefinition, value: any) => this.configManager.findBy<CoachUpgradeDefinition>('CoachUpgrade', field, value),
      findOne: (predicate: (item: CoachUpgradeDefinition) => boolean) => this.configManager.findOne<CoachUpgradeDefinition>('CoachUpgrade', predicate),
      findOneBy: (field: keyof CoachUpgradeDefinition, value: any) => this.configManager.findOneBy<CoachUpgradeDefinition>('CoachUpgrade', field, value)
    };

    this.coachUpgradeExpend = {
      get: (id: number) => this.configManager.get<CoachUpgradeExpendDefinition>('CoachUpgradeExpend', id),
      getAll: () => this.configManager.getAll<CoachUpgradeExpendDefinition>('CoachUpgradeExpend'),
      getBatch: (ids: number[]) => this.configManager.getBatch<CoachUpgradeExpendDefinition>('CoachUpgradeExpend', ids),
      search: (keyword: string) => this.configManager.search<CoachUpgradeExpendDefinition>('CoachUpgradeExpend', keyword),
      filter: (predicate: (item: CoachUpgradeExpendDefinition) => boolean) => this.configManager.filter<CoachUpgradeExpendDefinition>('CoachUpgradeExpend', predicate),
      findBy: (field: keyof CoachUpgradeExpendDefinition, value: any) => this.configManager.findBy<CoachUpgradeExpendDefinition>('CoachUpgradeExpend', field, value),
      findOne: (predicate: (item: CoachUpgradeExpendDefinition) => boolean) => this.configManager.findOne<CoachUpgradeExpendDefinition>('CoachUpgradeExpend', predicate),
      findOneBy: (field: keyof CoachUpgradeExpendDefinition, value: any) => this.configManager.findOneBy<CoachUpgradeExpendDefinition>('CoachUpgradeExpend', field, value)
    };

    this.coachUpstar = {
      get: (id: number) => this.configManager.get<CoachUpstarDefinition>('CoachUpstar', id),
      getAll: () => this.configManager.getAll<CoachUpstarDefinition>('CoachUpstar'),
      getBatch: (ids: number[]) => this.configManager.getBatch<CoachUpstarDefinition>('CoachUpstar', ids),
      search: (keyword: string) => this.configManager.search<CoachUpstarDefinition>('CoachUpstar', keyword),
      filter: (predicate: (item: CoachUpstarDefinition) => boolean) => this.configManager.filter<CoachUpstarDefinition>('CoachUpstar', predicate),
      findBy: (field: keyof CoachUpstarDefinition, value: any) => this.configManager.findBy<CoachUpstarDefinition>('CoachUpstar', field, value),
      findOne: (predicate: (item: CoachUpstarDefinition) => boolean) => this.configManager.findOne<CoachUpstarDefinition>('CoachUpstar', predicate),
      findOneBy: (field: keyof CoachUpstarDefinition, value: any) => this.configManager.findOneBy<CoachUpstarDefinition>('CoachUpstar', field, value)
    };

    this.combinationHandbook = {
      get: (id: number) => this.configManager.get<CombinationHandbookDefinition>('CombinationHandbook', id),
      getAll: () => this.configManager.getAll<CombinationHandbookDefinition>('CombinationHandbook'),
      getBatch: (ids: number[]) => this.configManager.getBatch<CombinationHandbookDefinition>('CombinationHandbook', ids),
      search: (keyword: string) => this.configManager.search<CombinationHandbookDefinition>('CombinationHandbook', keyword),
      filter: (predicate: (item: CombinationHandbookDefinition) => boolean) => this.configManager.filter<CombinationHandbookDefinition>('CombinationHandbook', predicate),
      findBy: (field: keyof CombinationHandbookDefinition, value: any) => this.configManager.findBy<CombinationHandbookDefinition>('CombinationHandbook', field, value),
      findOne: (predicate: (item: CombinationHandbookDefinition) => boolean) => this.configManager.findOne<CombinationHandbookDefinition>('CombinationHandbook', predicate),
      findOneBy: (field: keyof CombinationHandbookDefinition, value: any) => this.configManager.findOneBy<CombinationHandbookDefinition>('CombinationHandbook', field, value)
    };

    this.comment = {
      get: (id: number) => this.configManager.get<CommentDefinition>('Comment', id),
      getAll: () => this.configManager.getAll<CommentDefinition>('Comment'),
      getBatch: (ids: number[]) => this.configManager.getBatch<CommentDefinition>('Comment', ids),
      search: (keyword: string) => this.configManager.search<CommentDefinition>('Comment', keyword),
      filter: (predicate: (item: CommentDefinition) => boolean) => this.configManager.filter<CommentDefinition>('Comment', predicate),
      findBy: (field: keyof CommentDefinition, value: any) => this.configManager.findBy<CommentDefinition>('Comment', field, value),
      findOne: (predicate: (item: CommentDefinition) => boolean) => this.configManager.findOne<CommentDefinition>('Comment', predicate),
      findOneBy: (field: keyof CommentDefinition, value: any) => this.configManager.findOneBy<CommentDefinition>('Comment', field, value)
    };

    this.confrontation = {
      get: (id: number) => this.configManager.get<ConfrontationDefinition>('Confrontation', id),
      getAll: () => this.configManager.getAll<ConfrontationDefinition>('Confrontation'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ConfrontationDefinition>('Confrontation', ids),
      search: (keyword: string) => this.configManager.search<ConfrontationDefinition>('Confrontation', keyword),
      filter: (predicate: (item: ConfrontationDefinition) => boolean) => this.configManager.filter<ConfrontationDefinition>('Confrontation', predicate),
      findBy: (field: keyof ConfrontationDefinition, value: any) => this.configManager.findBy<ConfrontationDefinition>('Confrontation', field, value),
      findOne: (predicate: (item: ConfrontationDefinition) => boolean) => this.configManager.findOne<ConfrontationDefinition>('Confrontation', predicate),
      findOneBy: (field: keyof ConfrontationDefinition, value: any) => this.configManager.findOneBy<ConfrontationDefinition>('Confrontation', field, value)
    };

    this.country = {
      get: (id: number) => this.configManager.get<CountryDefinition>('Country', id),
      getAll: () => this.configManager.getAll<CountryDefinition>('Country'),
      getBatch: (ids: number[]) => this.configManager.getBatch<CountryDefinition>('Country', ids),
      search: (keyword: string) => this.configManager.search<CountryDefinition>('Country', keyword),
      filter: (predicate: (item: CountryDefinition) => boolean) => this.configManager.filter<CountryDefinition>('Country', predicate),
      findBy: (field: keyof CountryDefinition, value: any) => this.configManager.findBy<CountryDefinition>('Country', field, value),
      findOne: (predicate: (item: CountryDefinition) => boolean) => this.configManager.findOne<CountryDefinition>('Country', predicate),
      findOneBy: (field: keyof CountryDefinition, value: any) => this.configManager.findOneBy<CountryDefinition>('Country', field, value)
    };

    this.createBaller = {
      get: (id: number) => this.configManager.get<CreateBallerDefinition>('CreateBaller', id),
      getAll: () => this.configManager.getAll<CreateBallerDefinition>('CreateBaller'),
      getBatch: (ids: number[]) => this.configManager.getBatch<CreateBallerDefinition>('CreateBaller', ids),
      search: (keyword: string) => this.configManager.search<CreateBallerDefinition>('CreateBaller', keyword),
      filter: (predicate: (item: CreateBallerDefinition) => boolean) => this.configManager.filter<CreateBallerDefinition>('CreateBaller', predicate),
      findBy: (field: keyof CreateBallerDefinition, value: any) => this.configManager.findBy<CreateBallerDefinition>('CreateBaller', field, value),
      findOne: (predicate: (item: CreateBallerDefinition) => boolean) => this.configManager.findOne<CreateBallerDefinition>('CreateBaller', predicate),
      findOneBy: (field: keyof CreateBallerDefinition, value: any) => this.configManager.findOneBy<CreateBallerDefinition>('CreateBaller', field, value)
    };

    this.createTeamFormation = {
      get: (id: number) => this.configManager.get<CreateTeamFormationDefinition>('CreateTeamFormation', id),
      getAll: () => this.configManager.getAll<CreateTeamFormationDefinition>('CreateTeamFormation'),
      getBatch: (ids: number[]) => this.configManager.getBatch<CreateTeamFormationDefinition>('CreateTeamFormation', ids),
      search: (keyword: string) => this.configManager.search<CreateTeamFormationDefinition>('CreateTeamFormation', keyword),
      filter: (predicate: (item: CreateTeamFormationDefinition) => boolean) => this.configManager.filter<CreateTeamFormationDefinition>('CreateTeamFormation', predicate),
      findBy: (field: keyof CreateTeamFormationDefinition, value: any) => this.configManager.findBy<CreateTeamFormationDefinition>('CreateTeamFormation', field, value),
      findOne: (predicate: (item: CreateTeamFormationDefinition) => boolean) => this.configManager.findOne<CreateTeamFormationDefinition>('CreateTeamFormation', predicate),
      findOneBy: (field: keyof CreateTeamFormationDefinition, value: any) => this.configManager.findOneBy<CreateTeamFormationDefinition>('CreateTeamFormation', field, value)
    };

    this.dailyGift = {
      get: (id: number) => this.configManager.get<DailyGiftDefinition>('DailyGift', id),
      getAll: () => this.configManager.getAll<DailyGiftDefinition>('DailyGift'),
      getBatch: (ids: number[]) => this.configManager.getBatch<DailyGiftDefinition>('DailyGift', ids),
      search: (keyword: string) => this.configManager.search<DailyGiftDefinition>('DailyGift', keyword),
      filter: (predicate: (item: DailyGiftDefinition) => boolean) => this.configManager.filter<DailyGiftDefinition>('DailyGift', predicate),
      findBy: (field: keyof DailyGiftDefinition, value: any) => this.configManager.findBy<DailyGiftDefinition>('DailyGift', field, value),
      findOne: (predicate: (item: DailyGiftDefinition) => boolean) => this.configManager.findOne<DailyGiftDefinition>('DailyGift', predicate),
      findOneBy: (field: keyof DailyGiftDefinition, value: any) => this.configManager.findOneBy<DailyGiftDefinition>('DailyGift', field, value)
    };

    this.decompose = {
      get: (id: number) => this.configManager.get<DecomposeDefinition>('Decompose', id),
      getAll: () => this.configManager.getAll<DecomposeDefinition>('Decompose'),
      getBatch: (ids: number[]) => this.configManager.getBatch<DecomposeDefinition>('Decompose', ids),
      search: (keyword: string) => this.configManager.search<DecomposeDefinition>('Decompose', keyword),
      filter: (predicate: (item: DecomposeDefinition) => boolean) => this.configManager.filter<DecomposeDefinition>('Decompose', predicate),
      findBy: (field: keyof DecomposeDefinition, value: any) => this.configManager.findBy<DecomposeDefinition>('Decompose', field, value),
      findOne: (predicate: (item: DecomposeDefinition) => boolean) => this.configManager.findOne<DecomposeDefinition>('Decompose', predicate),
      findOneBy: (field: keyof DecomposeDefinition, value: any) => this.configManager.findOneBy<DecomposeDefinition>('Decompose', field, value)
    };

    this.dqCupSchedule = {
      get: (id: number) => this.configManager.get<DqCupScheduleDefinition>('DqCupSchedule', id),
      getAll: () => this.configManager.getAll<DqCupScheduleDefinition>('DqCupSchedule'),
      getBatch: (ids: number[]) => this.configManager.getBatch<DqCupScheduleDefinition>('DqCupSchedule', ids),
      search: (keyword: string) => this.configManager.search<DqCupScheduleDefinition>('DqCupSchedule', keyword),
      filter: (predicate: (item: DqCupScheduleDefinition) => boolean) => this.configManager.filter<DqCupScheduleDefinition>('DqCupSchedule', predicate),
      findBy: (field: keyof DqCupScheduleDefinition, value: any) => this.configManager.findBy<DqCupScheduleDefinition>('DqCupSchedule', field, value),
      findOne: (predicate: (item: DqCupScheduleDefinition) => boolean) => this.configManager.findOne<DqCupScheduleDefinition>('DqCupSchedule', predicate),
      findOneBy: (field: keyof DqCupScheduleDefinition, value: any) => this.configManager.findOneBy<DqCupScheduleDefinition>('DqCupSchedule', field, value)
    };

    this.drop = {
      get: (id: number) => this.configManager.get<DropDefinition>('Drop', id),
      getAll: () => this.configManager.getAll<DropDefinition>('Drop'),
      getBatch: (ids: number[]) => this.configManager.getBatch<DropDefinition>('Drop', ids),
      search: (keyword: string) => this.configManager.search<DropDefinition>('Drop', keyword),
      filter: (predicate: (item: DropDefinition) => boolean) => this.configManager.filter<DropDefinition>('Drop', predicate),
      findBy: (field: keyof DropDefinition, value: any) => this.configManager.findBy<DropDefinition>('Drop', field, value),
      findOne: (predicate: (item: DropDefinition) => boolean) => this.configManager.findOne<DropDefinition>('Drop', predicate),
      findOneBy: (field: keyof DropDefinition, value: any) => this.configManager.findOneBy<DropDefinition>('Drop', field, value)
    };

    this.energySupply = {
      get: (id: number) => this.configManager.get<EnergySupplyDefinition>('EnergySupply', id),
      getAll: () => this.configManager.getAll<EnergySupplyDefinition>('EnergySupply'),
      getBatch: (ids: number[]) => this.configManager.getBatch<EnergySupplyDefinition>('EnergySupply', ids),
      search: (keyword: string) => this.configManager.search<EnergySupplyDefinition>('EnergySupply', keyword),
      filter: (predicate: (item: EnergySupplyDefinition) => boolean) => this.configManager.filter<EnergySupplyDefinition>('EnergySupply', predicate),
      findBy: (field: keyof EnergySupplyDefinition, value: any) => this.configManager.findBy<EnergySupplyDefinition>('EnergySupply', field, value),
      findOne: (predicate: (item: EnergySupplyDefinition) => boolean) => this.configManager.findOne<EnergySupplyDefinition>('EnergySupply', predicate),
      findOneBy: (field: keyof EnergySupplyDefinition, value: any) => this.configManager.findOneBy<EnergySupplyDefinition>('EnergySupply', field, value)
    };

    this.energyTimes = {
      get: (id: number) => this.configManager.get<EnergyTimesDefinition>('EnergyTimes', id),
      getAll: () => this.configManager.getAll<EnergyTimesDefinition>('EnergyTimes'),
      getBatch: (ids: number[]) => this.configManager.getBatch<EnergyTimesDefinition>('EnergyTimes', ids),
      search: (keyword: string) => this.configManager.search<EnergyTimesDefinition>('EnergyTimes', keyword),
      filter: (predicate: (item: EnergyTimesDefinition) => boolean) => this.configManager.filter<EnergyTimesDefinition>('EnergyTimes', predicate),
      findBy: (field: keyof EnergyTimesDefinition, value: any) => this.configManager.findBy<EnergyTimesDefinition>('EnergyTimes', field, value),
      findOne: (predicate: (item: EnergyTimesDefinition) => boolean) => this.configManager.findOne<EnergyTimesDefinition>('EnergyTimes', predicate),
      findOneBy: (field: keyof EnergyTimesDefinition, value: any) => this.configManager.findOneBy<EnergyTimesDefinition>('EnergyTimes', field, value)
    };

    this.exchangeClub = {
      get: (id: number) => this.configManager.get<ExchangeClubDefinition>('ExchangeClub', id),
      getAll: () => this.configManager.getAll<ExchangeClubDefinition>('ExchangeClub'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ExchangeClubDefinition>('ExchangeClub', ids),
      search: (keyword: string) => this.configManager.search<ExchangeClubDefinition>('ExchangeClub', keyword),
      filter: (predicate: (item: ExchangeClubDefinition) => boolean) => this.configManager.filter<ExchangeClubDefinition>('ExchangeClub', predicate),
      findBy: (field: keyof ExchangeClubDefinition, value: any) => this.configManager.findBy<ExchangeClubDefinition>('ExchangeClub', field, value),
      findOne: (predicate: (item: ExchangeClubDefinition) => boolean) => this.configManager.findOne<ExchangeClubDefinition>('ExchangeClub', predicate),
      findOneBy: (field: keyof ExchangeClubDefinition, value: any) => this.configManager.findOneBy<ExchangeClubDefinition>('ExchangeClub', field, value)
    };

    this.exchangeControl = {
      get: (id: number) => this.configManager.get<ExchangeControlDefinition>('ExchangeControl', id),
      getAll: () => this.configManager.getAll<ExchangeControlDefinition>('ExchangeControl'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ExchangeControlDefinition>('ExchangeControl', ids),
      search: (keyword: string) => this.configManager.search<ExchangeControlDefinition>('ExchangeControl', keyword),
      filter: (predicate: (item: ExchangeControlDefinition) => boolean) => this.configManager.filter<ExchangeControlDefinition>('ExchangeControl', predicate),
      findBy: (field: keyof ExchangeControlDefinition, value: any) => this.configManager.findBy<ExchangeControlDefinition>('ExchangeControl', field, value),
      findOne: (predicate: (item: ExchangeControlDefinition) => boolean) => this.configManager.findOne<ExchangeControlDefinition>('ExchangeControl', predicate),
      findOneBy: (field: keyof ExchangeControlDefinition, value: any) => this.configManager.findOneBy<ExchangeControlDefinition>('ExchangeControl', field, value)
    };

    this.exchangeShop = {
      get: (id: number) => this.configManager.get<ExchangeShopDefinition>('ExchangeShop', id),
      getAll: () => this.configManager.getAll<ExchangeShopDefinition>('ExchangeShop'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ExchangeShopDefinition>('ExchangeShop', ids),
      search: (keyword: string) => this.configManager.search<ExchangeShopDefinition>('ExchangeShop', keyword),
      filter: (predicate: (item: ExchangeShopDefinition) => boolean) => this.configManager.filter<ExchangeShopDefinition>('ExchangeShop', predicate),
      findBy: (field: keyof ExchangeShopDefinition, value: any) => this.configManager.findBy<ExchangeShopDefinition>('ExchangeShop', field, value),
      findOne: (predicate: (item: ExchangeShopDefinition) => boolean) => this.configManager.findOne<ExchangeShopDefinition>('ExchangeShop', predicate),
      findOneBy: (field: keyof ExchangeShopDefinition, value: any) => this.configManager.findOneBy<ExchangeShopDefinition>('ExchangeShop', field, value)
    };

    this.faith = {
      get: (id: number) => this.configManager.get<FaithDefinition>('Faith', id),
      getAll: () => this.configManager.getAll<FaithDefinition>('Faith'),
      getBatch: (ids: number[]) => this.configManager.getBatch<FaithDefinition>('Faith', ids),
      search: (keyword: string) => this.configManager.search<FaithDefinition>('Faith', keyword),
      filter: (predicate: (item: FaithDefinition) => boolean) => this.configManager.filter<FaithDefinition>('Faith', predicate),
      findBy: (field: keyof FaithDefinition, value: any) => this.configManager.findBy<FaithDefinition>('Faith', field, value),
      findOne: (predicate: (item: FaithDefinition) => boolean) => this.configManager.findOne<FaithDefinition>('Faith', predicate),
      findOneBy: (field: keyof FaithDefinition, value: any) => this.configManager.findOneBy<FaithDefinition>('Faith', field, value)
    };

    this.faithStore = {
      get: (id: number) => this.configManager.get<FaithStoreDefinition>('FaithStore', id),
      getAll: () => this.configManager.getAll<FaithStoreDefinition>('FaithStore'),
      getBatch: (ids: number[]) => this.configManager.getBatch<FaithStoreDefinition>('FaithStore', ids),
      search: (keyword: string) => this.configManager.search<FaithStoreDefinition>('FaithStore', keyword),
      filter: (predicate: (item: FaithStoreDefinition) => boolean) => this.configManager.filter<FaithStoreDefinition>('FaithStore', predicate),
      findBy: (field: keyof FaithStoreDefinition, value: any) => this.configManager.findBy<FaithStoreDefinition>('FaithStore', field, value),
      findOne: (predicate: (item: FaithStoreDefinition) => boolean) => this.configManager.findOne<FaithStoreDefinition>('FaithStore', predicate),
      findOneBy: (field: keyof FaithStoreDefinition, value: any) => this.configManager.findOneBy<FaithStoreDefinition>('FaithStore', field, value)
    };

    this.feildTrainning = {
      get: (id: number) => this.configManager.get<FeildTrainningDefinition>('FeildTrainning', id),
      getAll: () => this.configManager.getAll<FeildTrainningDefinition>('FeildTrainning'),
      getBatch: (ids: number[]) => this.configManager.getBatch<FeildTrainningDefinition>('FeildTrainning', ids),
      search: (keyword: string) => this.configManager.search<FeildTrainningDefinition>('FeildTrainning', keyword),
      filter: (predicate: (item: FeildTrainningDefinition) => boolean) => this.configManager.filter<FeildTrainningDefinition>('FeildTrainning', predicate),
      findBy: (field: keyof FeildTrainningDefinition, value: any) => this.configManager.findBy<FeildTrainningDefinition>('FeildTrainning', field, value),
      findOne: (predicate: (item: FeildTrainningDefinition) => boolean) => this.configManager.findOne<FeildTrainningDefinition>('FeildTrainning', predicate),
      findOneBy: (field: keyof FeildTrainningDefinition, value: any) => this.configManager.findOneBy<FeildTrainningDefinition>('FeildTrainning', field, value)
    };

    this.field = {
      get: (id: number) => this.configManager.get<FieldDefinition>('Field', id),
      getAll: () => this.configManager.getAll<FieldDefinition>('Field'),
      getBatch: (ids: number[]) => this.configManager.getBatch<FieldDefinition>('Field', ids),
      search: (keyword: string) => this.configManager.search<FieldDefinition>('Field', keyword),
      filter: (predicate: (item: FieldDefinition) => boolean) => this.configManager.filter<FieldDefinition>('Field', predicate),
      findBy: (field: keyof FieldDefinition, value: any) => this.configManager.findBy<FieldDefinition>('Field', field, value),
      findOne: (predicate: (item: FieldDefinition) => boolean) => this.configManager.findOne<FieldDefinition>('Field', predicate),
      findOneBy: (field: keyof FieldDefinition, value: any) => this.configManager.findOneBy<FieldDefinition>('Field', field, value)
    };

    this.fieldHospital = {
      get: (id: number) => this.configManager.get<FieldHospitalDefinition>('FieldHospital', id),
      getAll: () => this.configManager.getAll<FieldHospitalDefinition>('FieldHospital'),
      getBatch: (ids: number[]) => this.configManager.getBatch<FieldHospitalDefinition>('FieldHospital', ids),
      search: (keyword: string) => this.configManager.search<FieldHospitalDefinition>('FieldHospital', keyword),
      filter: (predicate: (item: FieldHospitalDefinition) => boolean) => this.configManager.filter<FieldHospitalDefinition>('FieldHospital', predicate),
      findBy: (field: keyof FieldHospitalDefinition, value: any) => this.configManager.findBy<FieldHospitalDefinition>('FieldHospital', field, value),
      findOne: (predicate: (item: FieldHospitalDefinition) => boolean) => this.configManager.findOne<FieldHospitalDefinition>('FieldHospital', predicate),
      findOneBy: (field: keyof FieldHospitalDefinition, value: any) => this.configManager.findOneBy<FieldHospitalDefinition>('FieldHospital', field, value)
    };

    this.fieldWarSchedule = {
      get: (id: number) => this.configManager.get<FieldWarScheduleDefinition>('FieldWarSchedule', id),
      getAll: () => this.configManager.getAll<FieldWarScheduleDefinition>('FieldWarSchedule'),
      getBatch: (ids: number[]) => this.configManager.getBatch<FieldWarScheduleDefinition>('FieldWarSchedule', ids),
      search: (keyword: string) => this.configManager.search<FieldWarScheduleDefinition>('FieldWarSchedule', keyword),
      filter: (predicate: (item: FieldWarScheduleDefinition) => boolean) => this.configManager.filter<FieldWarScheduleDefinition>('FieldWarSchedule', predicate),
      findBy: (field: keyof FieldWarScheduleDefinition, value: any) => this.configManager.findBy<FieldWarScheduleDefinition>('FieldWarSchedule', field, value),
      findOne: (predicate: (item: FieldWarScheduleDefinition) => boolean) => this.configManager.findOne<FieldWarScheduleDefinition>('FieldWarSchedule', predicate),
      findOneBy: (field: keyof FieldWarScheduleDefinition, value: any) => this.configManager.findOneBy<FieldWarScheduleDefinition>('FieldWarSchedule', field, value)
    };

    this.footballLevel = {
      get: (id: number) => this.configManager.get<FootballLevelDefinition>('FootballLevel', id),
      getAll: () => this.configManager.getAll<FootballLevelDefinition>('FootballLevel'),
      getBatch: (ids: number[]) => this.configManager.getBatch<FootballLevelDefinition>('FootballLevel', ids),
      search: (keyword: string) => this.configManager.search<FootballLevelDefinition>('FootballLevel', keyword),
      filter: (predicate: (item: FootballLevelDefinition) => boolean) => this.configManager.filter<FootballLevelDefinition>('FootballLevel', predicate),
      findBy: (field: keyof FootballLevelDefinition, value: any) => this.configManager.findBy<FootballLevelDefinition>('FootballLevel', field, value),
      findOne: (predicate: (item: FootballLevelDefinition) => boolean) => this.configManager.findOne<FootballLevelDefinition>('FootballLevel', predicate),
      findOneBy: (field: keyof FootballLevelDefinition, value: any) => this.configManager.findOneBy<FootballLevelDefinition>('FootballLevel', field, value)
    };

    this.formationCoordinate = {
      get: (id: number) => this.configManager.get<FormationCoordinateDefinition>('FormationCoordinate', id),
      getAll: () => this.configManager.getAll<FormationCoordinateDefinition>('FormationCoordinate'),
      getBatch: (ids: number[]) => this.configManager.getBatch<FormationCoordinateDefinition>('FormationCoordinate', ids),
      search: (keyword: string) => this.configManager.search<FormationCoordinateDefinition>('FormationCoordinate', keyword),
      filter: (predicate: (item: FormationCoordinateDefinition) => boolean) => this.configManager.filter<FormationCoordinateDefinition>('FormationCoordinate', predicate),
      findBy: (field: keyof FormationCoordinateDefinition, value: any) => this.configManager.findBy<FormationCoordinateDefinition>('FormationCoordinate', field, value),
      findOne: (predicate: (item: FormationCoordinateDefinition) => boolean) => this.configManager.findOne<FormationCoordinateDefinition>('FormationCoordinate', predicate),
      findOneBy: (field: keyof FormationCoordinateDefinition, value: any) => this.configManager.findOneBy<FormationCoordinateDefinition>('FormationCoordinate', field, value)
    };

    this.giftCode = {
      get: (id: number) => this.configManager.get<GiftCodeDefinition>('GiftCode', id),
      getAll: () => this.configManager.getAll<GiftCodeDefinition>('GiftCode'),
      getBatch: (ids: number[]) => this.configManager.getBatch<GiftCodeDefinition>('GiftCode', ids),
      search: (keyword: string) => this.configManager.search<GiftCodeDefinition>('GiftCode', keyword),
      filter: (predicate: (item: GiftCodeDefinition) => boolean) => this.configManager.filter<GiftCodeDefinition>('GiftCode', predicate),
      findBy: (field: keyof GiftCodeDefinition, value: any) => this.configManager.findBy<GiftCodeDefinition>('GiftCode', field, value),
      findOne: (predicate: (item: GiftCodeDefinition) => boolean) => this.configManager.findOne<GiftCodeDefinition>('GiftCode', predicate),
      findOneBy: (field: keyof GiftCodeDefinition, value: any) => this.configManager.findOneBy<GiftCodeDefinition>('GiftCode', field, value)
    };

    this.giftPackage = {
      get: (id: number) => this.configManager.get<GiftPackageDefinition>('GiftPackage', id),
      getAll: () => this.configManager.getAll<GiftPackageDefinition>('GiftPackage'),
      getBatch: (ids: number[]) => this.configManager.getBatch<GiftPackageDefinition>('GiftPackage', ids),
      search: (keyword: string) => this.configManager.search<GiftPackageDefinition>('GiftPackage', keyword),
      filter: (predicate: (item: GiftPackageDefinition) => boolean) => this.configManager.filter<GiftPackageDefinition>('GiftPackage', predicate),
      findBy: (field: keyof GiftPackageDefinition, value: any) => this.configManager.findBy<GiftPackageDefinition>('GiftPackage', field, value),
      findOne: (predicate: (item: GiftPackageDefinition) => boolean) => this.configManager.findOne<GiftPackageDefinition>('GiftPackage', predicate),
      findOneBy: (field: keyof GiftPackageDefinition, value: any) => this.configManager.findOneBy<GiftPackageDefinition>('GiftPackage', field, value)
    };

    this.groundMatchField = {
      get: (id: number) => this.configManager.get<GroundMatchFieldDefinition>('GroundMatchField', id),
      getAll: () => this.configManager.getAll<GroundMatchFieldDefinition>('GroundMatchField'),
      getBatch: (ids: number[]) => this.configManager.getBatch<GroundMatchFieldDefinition>('GroundMatchField', ids),
      search: (keyword: string) => this.configManager.search<GroundMatchFieldDefinition>('GroundMatchField', keyword),
      filter: (predicate: (item: GroundMatchFieldDefinition) => boolean) => this.configManager.filter<GroundMatchFieldDefinition>('GroundMatchField', predicate),
      findBy: (field: keyof GroundMatchFieldDefinition, value: any) => this.configManager.findBy<GroundMatchFieldDefinition>('GroundMatchField', field, value),
      findOne: (predicate: (item: GroundMatchFieldDefinition) => boolean) => this.configManager.findOne<GroundMatchFieldDefinition>('GroundMatchField', predicate),
      findOneBy: (field: keyof GroundMatchFieldDefinition, value: any) => this.configManager.findOneBy<GroundMatchFieldDefinition>('GroundMatchField', field, value)
    };

    this.groundMatchReward = {
      get: (id: number) => this.configManager.get<GroundMatchRewardDefinition>('GroundMatchReward', id),
      getAll: () => this.configManager.getAll<GroundMatchRewardDefinition>('GroundMatchReward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<GroundMatchRewardDefinition>('GroundMatchReward', ids),
      search: (keyword: string) => this.configManager.search<GroundMatchRewardDefinition>('GroundMatchReward', keyword),
      filter: (predicate: (item: GroundMatchRewardDefinition) => boolean) => this.configManager.filter<GroundMatchRewardDefinition>('GroundMatchReward', predicate),
      findBy: (field: keyof GroundMatchRewardDefinition, value: any) => this.configManager.findBy<GroundMatchRewardDefinition>('GroundMatchReward', field, value),
      findOne: (predicate: (item: GroundMatchRewardDefinition) => boolean) => this.configManager.findOne<GroundMatchRewardDefinition>('GroundMatchReward', predicate),
      findOneBy: (field: keyof GroundMatchRewardDefinition, value: any) => this.configManager.findOneBy<GroundMatchRewardDefinition>('GroundMatchReward', field, value)
    };

    this.guild = {
      get: (id: number) => this.configManager.get<GuildDefinition>('Guild', id),
      getAll: () => this.configManager.getAll<GuildDefinition>('Guild'),
      getBatch: (ids: number[]) => this.configManager.getBatch<GuildDefinition>('Guild', ids),
      search: (keyword: string) => this.configManager.search<GuildDefinition>('Guild', keyword),
      filter: (predicate: (item: GuildDefinition) => boolean) => this.configManager.filter<GuildDefinition>('Guild', predicate),
      findBy: (field: keyof GuildDefinition, value: any) => this.configManager.findBy<GuildDefinition>('Guild', field, value),
      findOne: (predicate: (item: GuildDefinition) => boolean) => this.configManager.findOne<GuildDefinition>('Guild', predicate),
      findOneBy: (field: keyof GuildDefinition, value: any) => this.configManager.findOneBy<GuildDefinition>('Guild', field, value)
    };

    this.gulfCup = {
      get: (id: number) => this.configManager.get<GulfCupDefinition>('GulfCup', id),
      getAll: () => this.configManager.getAll<GulfCupDefinition>('GulfCup'),
      getBatch: (ids: number[]) => this.configManager.getBatch<GulfCupDefinition>('GulfCup', ids),
      search: (keyword: string) => this.configManager.search<GulfCupDefinition>('GulfCup', keyword),
      filter: (predicate: (item: GulfCupDefinition) => boolean) => this.configManager.filter<GulfCupDefinition>('GulfCup', predicate),
      findBy: (field: keyof GulfCupDefinition, value: any) => this.configManager.findBy<GulfCupDefinition>('GulfCup', field, value),
      findOne: (predicate: (item: GulfCupDefinition) => boolean) => this.configManager.findOne<GulfCupDefinition>('GulfCup', predicate),
      findOneBy: (field: keyof GulfCupDefinition, value: any) => this.configManager.findOneBy<GulfCupDefinition>('GulfCup', field, value)
    };

    this.gulfCupPlayer = {
      get: (id: number) => this.configManager.get<GulfCupPlayerDefinition>('GulfCupPlayer', id),
      getAll: () => this.configManager.getAll<GulfCupPlayerDefinition>('GulfCupPlayer'),
      getBatch: (ids: number[]) => this.configManager.getBatch<GulfCupPlayerDefinition>('GulfCupPlayer', ids),
      search: (keyword: string) => this.configManager.search<GulfCupPlayerDefinition>('GulfCupPlayer', keyword),
      filter: (predicate: (item: GulfCupPlayerDefinition) => boolean) => this.configManager.filter<GulfCupPlayerDefinition>('GulfCupPlayer', predicate),
      findBy: (field: keyof GulfCupPlayerDefinition, value: any) => this.configManager.findBy<GulfCupPlayerDefinition>('GulfCupPlayer', field, value),
      findOne: (predicate: (item: GulfCupPlayerDefinition) => boolean) => this.configManager.findOne<GulfCupPlayerDefinition>('GulfCupPlayer', predicate),
      findOneBy: (field: keyof GulfCupPlayerDefinition, value: any) => this.configManager.findOneBy<GulfCupPlayerDefinition>('GulfCupPlayer', field, value)
    };

    this.gulfCupStrength = {
      get: (id: number) => this.configManager.get<GulfCupStrengthDefinition>('GulfCupStrength', id),
      getAll: () => this.configManager.getAll<GulfCupStrengthDefinition>('GulfCupStrength'),
      getBatch: (ids: number[]) => this.configManager.getBatch<GulfCupStrengthDefinition>('GulfCupStrength', ids),
      search: (keyword: string) => this.configManager.search<GulfCupStrengthDefinition>('GulfCupStrength', keyword),
      filter: (predicate: (item: GulfCupStrengthDefinition) => boolean) => this.configManager.filter<GulfCupStrengthDefinition>('GulfCupStrength', predicate),
      findBy: (field: keyof GulfCupStrengthDefinition, value: any) => this.configManager.findBy<GulfCupStrengthDefinition>('GulfCupStrength', field, value),
      findOne: (predicate: (item: GulfCupStrengthDefinition) => boolean) => this.configManager.findOne<GulfCupStrengthDefinition>('GulfCupStrength', predicate),
      findOneBy: (field: keyof GulfCupStrengthDefinition, value: any) => this.configManager.findOneBy<GulfCupStrengthDefinition>('GulfCupStrength', field, value)
    };

    this.gulfCupTeam = {
      get: (id: number) => this.configManager.get<GulfCupTeamDefinition>('GulfCupTeam', id),
      getAll: () => this.configManager.getAll<GulfCupTeamDefinition>('GulfCupTeam'),
      getBatch: (ids: number[]) => this.configManager.getBatch<GulfCupTeamDefinition>('GulfCupTeam', ids),
      search: (keyword: string) => this.configManager.search<GulfCupTeamDefinition>('GulfCupTeam', keyword),
      filter: (predicate: (item: GulfCupTeamDefinition) => boolean) => this.configManager.filter<GulfCupTeamDefinition>('GulfCupTeam', predicate),
      findBy: (field: keyof GulfCupTeamDefinition, value: any) => this.configManager.findBy<GulfCupTeamDefinition>('GulfCupTeam', field, value),
      findOne: (predicate: (item: GulfCupTeamDefinition) => boolean) => this.configManager.findOne<GulfCupTeamDefinition>('GulfCupTeam', predicate),
      findOneBy: (field: keyof GulfCupTeamDefinition, value: any) => this.configManager.findOneBy<GulfCupTeamDefinition>('GulfCupTeam', field, value)
    };

    this.gulfTeam = {
      get: (id: number) => this.configManager.get<GulfTeamDefinition>('GulfTeam', id),
      getAll: () => this.configManager.getAll<GulfTeamDefinition>('GulfTeam'),
      getBatch: (ids: number[]) => this.configManager.getBatch<GulfTeamDefinition>('GulfTeam', ids),
      search: (keyword: string) => this.configManager.search<GulfTeamDefinition>('GulfTeam', keyword),
      filter: (predicate: (item: GulfTeamDefinition) => boolean) => this.configManager.filter<GulfTeamDefinition>('GulfTeam', predicate),
      findBy: (field: keyof GulfTeamDefinition, value: any) => this.configManager.findBy<GulfTeamDefinition>('GulfTeam', field, value),
      findOne: (predicate: (item: GulfTeamDefinition) => boolean) => this.configManager.findOne<GulfTeamDefinition>('GulfTeam', predicate),
      findOneBy: (field: keyof GulfTeamDefinition, value: any) => this.configManager.findOneBy<GulfTeamDefinition>('GulfTeam', field, value)
    };

    this.hallOfFame = {
      get: (id: number) => this.configManager.get<HallOfFameDefinition>('HallOfFame', id),
      getAll: () => this.configManager.getAll<HallOfFameDefinition>('HallOfFame'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HallOfFameDefinition>('HallOfFame', ids),
      search: (keyword: string) => this.configManager.search<HallOfFameDefinition>('HallOfFame', keyword),
      filter: (predicate: (item: HallOfFameDefinition) => boolean) => this.configManager.filter<HallOfFameDefinition>('HallOfFame', predicate),
      findBy: (field: keyof HallOfFameDefinition, value: any) => this.configManager.findBy<HallOfFameDefinition>('HallOfFame', field, value),
      findOne: (predicate: (item: HallOfFameDefinition) => boolean) => this.configManager.findOne<HallOfFameDefinition>('HallOfFame', predicate),
      findOneBy: (field: keyof HallOfFameDefinition, value: any) => this.configManager.findOneBy<HallOfFameDefinition>('HallOfFame', field, value)
    };

    this.hero = {
      get: (id: number) => this.configManager.get<HeroDefinition>('Hero', id),
      getAll: () => this.configManager.getAll<HeroDefinition>('Hero'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroDefinition>('Hero', ids),
      search: (keyword: string) => this.configManager.search<HeroDefinition>('Hero', keyword),
      filter: (predicate: (item: HeroDefinition) => boolean) => this.configManager.filter<HeroDefinition>('Hero', predicate),
      findBy: (field: keyof HeroDefinition, value: any) => this.configManager.findBy<HeroDefinition>('Hero', field, value),
      findOne: (predicate: (item: HeroDefinition) => boolean) => this.configManager.findOne<HeroDefinition>('Hero', predicate),
      findOneBy: (field: keyof HeroDefinition, value: any) => this.configManager.findOneBy<HeroDefinition>('Hero', field, value)
    };

    this.heroBreak = {
      get: (id: number) => this.configManager.get<HeroBreakDefinition>('HeroBreak', id),
      getAll: () => this.configManager.getAll<HeroBreakDefinition>('HeroBreak'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroBreakDefinition>('HeroBreak', ids),
      search: (keyword: string) => this.configManager.search<HeroBreakDefinition>('HeroBreak', keyword),
      filter: (predicate: (item: HeroBreakDefinition) => boolean) => this.configManager.filter<HeroBreakDefinition>('HeroBreak', predicate),
      findBy: (field: keyof HeroBreakDefinition, value: any) => this.configManager.findBy<HeroBreakDefinition>('HeroBreak', field, value),
      findOne: (predicate: (item: HeroBreakDefinition) => boolean) => this.configManager.findOne<HeroBreakDefinition>('HeroBreak', predicate),
      findOneBy: (field: keyof HeroBreakDefinition, value: any) => this.configManager.findOneBy<HeroBreakDefinition>('HeroBreak', field, value)
    };

    this.heroEnergy = {
      get: (id: number) => this.configManager.get<HeroEnergyDefinition>('HeroEnergy', id),
      getAll: () => this.configManager.getAll<HeroEnergyDefinition>('HeroEnergy'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroEnergyDefinition>('HeroEnergy', ids),
      search: (keyword: string) => this.configManager.search<HeroEnergyDefinition>('HeroEnergy', keyword),
      filter: (predicate: (item: HeroEnergyDefinition) => boolean) => this.configManager.filter<HeroEnergyDefinition>('HeroEnergy', predicate),
      findBy: (field: keyof HeroEnergyDefinition, value: any) => this.configManager.findBy<HeroEnergyDefinition>('HeroEnergy', field, value),
      findOne: (predicate: (item: HeroEnergyDefinition) => boolean) => this.configManager.findOne<HeroEnergyDefinition>('HeroEnergy', predicate),
      findOneBy: (field: keyof HeroEnergyDefinition, value: any) => this.configManager.findOneBy<HeroEnergyDefinition>('HeroEnergy', field, value)
    };

    this.heroForm = {
      get: (id: number) => this.configManager.get<HeroFormDefinition>('HeroForm', id),
      getAll: () => this.configManager.getAll<HeroFormDefinition>('HeroForm'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroFormDefinition>('HeroForm', ids),
      search: (keyword: string) => this.configManager.search<HeroFormDefinition>('HeroForm', keyword),
      filter: (predicate: (item: HeroFormDefinition) => boolean) => this.configManager.filter<HeroFormDefinition>('HeroForm', predicate),
      findBy: (field: keyof HeroFormDefinition, value: any) => this.configManager.findBy<HeroFormDefinition>('HeroForm', field, value),
      findOne: (predicate: (item: HeroFormDefinition) => boolean) => this.configManager.findOne<HeroFormDefinition>('HeroForm', predicate),
      findOneBy: (field: keyof HeroFormDefinition, value: any) => this.configManager.findOneBy<HeroFormDefinition>('HeroForm', field, value)
    };

    this.heroFormPoint = {
      get: (id: number) => this.configManager.get<HeroFormPointDefinition>('HeroFormPoint', id),
      getAll: () => this.configManager.getAll<HeroFormPointDefinition>('HeroFormPoint'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroFormPointDefinition>('HeroFormPoint', ids),
      search: (keyword: string) => this.configManager.search<HeroFormPointDefinition>('HeroFormPoint', keyword),
      filter: (predicate: (item: HeroFormPointDefinition) => boolean) => this.configManager.filter<HeroFormPointDefinition>('HeroFormPoint', predicate),
      findBy: (field: keyof HeroFormPointDefinition, value: any) => this.configManager.findBy<HeroFormPointDefinition>('HeroFormPoint', field, value),
      findOne: (predicate: (item: HeroFormPointDefinition) => boolean) => this.configManager.findOne<HeroFormPointDefinition>('HeroFormPoint', predicate),
      findOneBy: (field: keyof HeroFormPointDefinition, value: any) => this.configManager.findOneBy<HeroFormPointDefinition>('HeroFormPoint', field, value)
    };

    this.heroFoster = {
      get: (id: number) => this.configManager.get<HeroFosterDefinition>('HeroFoster', id),
      getAll: () => this.configManager.getAll<HeroFosterDefinition>('HeroFoster'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroFosterDefinition>('HeroFoster', ids),
      search: (keyword: string) => this.configManager.search<HeroFosterDefinition>('HeroFoster', keyword),
      filter: (predicate: (item: HeroFosterDefinition) => boolean) => this.configManager.filter<HeroFosterDefinition>('HeroFoster', predicate),
      findBy: (field: keyof HeroFosterDefinition, value: any) => this.configManager.findBy<HeroFosterDefinition>('HeroFoster', field, value),
      findOne: (predicate: (item: HeroFosterDefinition) => boolean) => this.configManager.findOne<HeroFosterDefinition>('HeroFoster', predicate),
      findOneBy: (field: keyof HeroFosterDefinition, value: any) => this.configManager.findOneBy<HeroFosterDefinition>('HeroFoster', field, value)
    };

    this.heroIcon = {
      get: (id: number) => this.configManager.get<HeroIconDefinition>('HeroIcon', id),
      getAll: () => this.configManager.getAll<HeroIconDefinition>('HeroIcon'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroIconDefinition>('HeroIcon', ids),
      search: (keyword: string) => this.configManager.search<HeroIconDefinition>('HeroIcon', keyword),
      filter: (predicate: (item: HeroIconDefinition) => boolean) => this.configManager.filter<HeroIconDefinition>('HeroIcon', predicate),
      findBy: (field: keyof HeroIconDefinition, value: any) => this.configManager.findBy<HeroIconDefinition>('HeroIcon', field, value),
      findOne: (predicate: (item: HeroIconDefinition) => boolean) => this.configManager.findOne<HeroIconDefinition>('HeroIcon', predicate),
      findOneBy: (field: keyof HeroIconDefinition, value: any) => this.configManager.findOneBy<HeroIconDefinition>('HeroIcon', field, value)
    };

    this.heroInjury = {
      get: (id: number) => this.configManager.get<HeroInjuryDefinition>('HeroInjury', id),
      getAll: () => this.configManager.getAll<HeroInjuryDefinition>('HeroInjury'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroInjuryDefinition>('HeroInjury', ids),
      search: (keyword: string) => this.configManager.search<HeroInjuryDefinition>('HeroInjury', keyword),
      filter: (predicate: (item: HeroInjuryDefinition) => boolean) => this.configManager.filter<HeroInjuryDefinition>('HeroInjury', predicate),
      findBy: (field: keyof HeroInjuryDefinition, value: any) => this.configManager.findBy<HeroInjuryDefinition>('HeroInjury', field, value),
      findOne: (predicate: (item: HeroInjuryDefinition) => boolean) => this.configManager.findOne<HeroInjuryDefinition>('HeroInjury', predicate),
      findOneBy: (field: keyof HeroInjuryDefinition, value: any) => this.configManager.findOneBy<HeroInjuryDefinition>('HeroInjury', field, value)
    };

    this.heroPool = {
      get: (id: number) => this.configManager.get<HeroPoolDefinition>('HeroPool', id),
      getAll: () => this.configManager.getAll<HeroPoolDefinition>('HeroPool'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroPoolDefinition>('HeroPool', ids),
      search: (keyword: string) => this.configManager.search<HeroPoolDefinition>('HeroPool', keyword),
      filter: (predicate: (item: HeroPoolDefinition) => boolean) => this.configManager.filter<HeroPoolDefinition>('HeroPool', predicate),
      findBy: (field: keyof HeroPoolDefinition, value: any) => this.configManager.findBy<HeroPoolDefinition>('HeroPool', field, value),
      findOne: (predicate: (item: HeroPoolDefinition) => boolean) => this.configManager.findOne<HeroPoolDefinition>('HeroPool', predicate),
      findOneBy: (field: keyof HeroPoolDefinition, value: any) => this.configManager.findOneBy<HeroPoolDefinition>('HeroPool', field, value)
    };

    this.heroPve = {
      get: (id: number) => this.configManager.get<HeroPveDefinition>('HeroPve', id),
      getAll: () => this.configManager.getAll<HeroPveDefinition>('HeroPve'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroPveDefinition>('HeroPve', ids),
      search: (keyword: string) => this.configManager.search<HeroPveDefinition>('HeroPve', keyword),
      filter: (predicate: (item: HeroPveDefinition) => boolean) => this.configManager.filter<HeroPveDefinition>('HeroPve', predicate),
      findBy: (field: keyof HeroPveDefinition, value: any) => this.configManager.findBy<HeroPveDefinition>('HeroPve', field, value),
      findOne: (predicate: (item: HeroPveDefinition) => boolean) => this.configManager.findOne<HeroPveDefinition>('HeroPve', predicate),
      findOneBy: (field: keyof HeroPveDefinition, value: any) => this.configManager.findOneBy<HeroPveDefinition>('HeroPve', field, value)
    };

    this.heroSkill = {
      get: (id: number) => this.configManager.get<HeroSkillDefinition>('HeroSkill', id),
      getAll: () => this.configManager.getAll<HeroSkillDefinition>('HeroSkill'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroSkillDefinition>('HeroSkill', ids),
      search: (keyword: string) => this.configManager.search<HeroSkillDefinition>('HeroSkill', keyword),
      filter: (predicate: (item: HeroSkillDefinition) => boolean) => this.configManager.filter<HeroSkillDefinition>('HeroSkill', predicate),
      findBy: (field: keyof HeroSkillDefinition, value: any) => this.configManager.findBy<HeroSkillDefinition>('HeroSkill', field, value),
      findOne: (predicate: (item: HeroSkillDefinition) => boolean) => this.configManager.findOne<HeroSkillDefinition>('HeroSkill', predicate),
      findOneBy: (field: keyof HeroSkillDefinition, value: any) => this.configManager.findOneBy<HeroSkillDefinition>('HeroSkill', field, value)
    };

    this.heroStar = {
      get: (id: number) => this.configManager.get<HeroStarDefinition>('HeroStar', id),
      getAll: () => this.configManager.getAll<HeroStarDefinition>('HeroStar'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroStarDefinition>('HeroStar', ids),
      search: (keyword: string) => this.configManager.search<HeroStarDefinition>('HeroStar', keyword),
      filter: (predicate: (item: HeroStarDefinition) => boolean) => this.configManager.filter<HeroStarDefinition>('HeroStar', predicate),
      findBy: (field: keyof HeroStarDefinition, value: any) => this.configManager.findBy<HeroStarDefinition>('HeroStar', field, value),
      findOne: (predicate: (item: HeroStarDefinition) => boolean) => this.configManager.findOne<HeroStarDefinition>('HeroStar', predicate),
      findOneBy: (field: keyof HeroStarDefinition, value: any) => this.configManager.findOneBy<HeroStarDefinition>('HeroStar', field, value)
    };

    this.heroTrain = {
      get: (id: number) => this.configManager.get<HeroTrainDefinition>('HeroTrain', id),
      getAll: () => this.configManager.getAll<HeroTrainDefinition>('HeroTrain'),
      getBatch: (ids: number[]) => this.configManager.getBatch<HeroTrainDefinition>('HeroTrain', ids),
      search: (keyword: string) => this.configManager.search<HeroTrainDefinition>('HeroTrain', keyword),
      filter: (predicate: (item: HeroTrainDefinition) => boolean) => this.configManager.filter<HeroTrainDefinition>('HeroTrain', predicate),
      findBy: (field: keyof HeroTrainDefinition, value: any) => this.configManager.findBy<HeroTrainDefinition>('HeroTrain', field, value),
      findOne: (predicate: (item: HeroTrainDefinition) => boolean) => this.configManager.findOne<HeroTrainDefinition>('HeroTrain', predicate),
      findOneBy: (field: keyof HeroTrainDefinition, value: any) => this.configManager.findOneBy<HeroTrainDefinition>('HeroTrain', field, value)
    };

    this.icon = {
      get: (id: number) => this.configManager.get<IconDefinition>('Icon', id),
      getAll: () => this.configManager.getAll<IconDefinition>('Icon'),
      getBatch: (ids: number[]) => this.configManager.getBatch<IconDefinition>('Icon', ids),
      search: (keyword: string) => this.configManager.search<IconDefinition>('Icon', keyword),
      filter: (predicate: (item: IconDefinition) => boolean) => this.configManager.filter<IconDefinition>('Icon', predicate),
      findBy: (field: keyof IconDefinition, value: any) => this.configManager.findBy<IconDefinition>('Icon', field, value),
      findOne: (predicate: (item: IconDefinition) => boolean) => this.configManager.findOne<IconDefinition>('Icon', predicate),
      findOneBy: (field: keyof IconDefinition, value: any) => this.configManager.findOneBy<IconDefinition>('Icon', field, value)
    };

    this.item = {
      get: (id: number) => this.configManager.get<ItemDefinition>('Item', id),
      getAll: () => this.configManager.getAll<ItemDefinition>('Item'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ItemDefinition>('Item', ids),
      search: (keyword: string) => this.configManager.search<ItemDefinition>('Item', keyword),
      filter: (predicate: (item: ItemDefinition) => boolean) => this.configManager.filter<ItemDefinition>('Item', predicate),
      findBy: (field: keyof ItemDefinition, value: any) => this.configManager.findBy<ItemDefinition>('Item', field, value),
      findOne: (predicate: (item: ItemDefinition) => boolean) => this.configManager.findOne<ItemDefinition>('Item', predicate),
      findOneBy: (field: keyof ItemDefinition, value: any) => this.configManager.findOneBy<ItemDefinition>('Item', field, value)
    };

    this.itemChannel = {
      get: (id: number) => this.configManager.get<ItemChannelDefinition>('ItemChannel', id),
      getAll: () => this.configManager.getAll<ItemChannelDefinition>('ItemChannel'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ItemChannelDefinition>('ItemChannel', ids),
      search: (keyword: string) => this.configManager.search<ItemChannelDefinition>('ItemChannel', keyword),
      filter: (predicate: (item: ItemChannelDefinition) => boolean) => this.configManager.filter<ItemChannelDefinition>('ItemChannel', predicate),
      findBy: (field: keyof ItemChannelDefinition, value: any) => this.configManager.findBy<ItemChannelDefinition>('ItemChannel', field, value),
      findOne: (predicate: (item: ItemChannelDefinition) => boolean) => this.configManager.findOne<ItemChannelDefinition>('ItemChannel', predicate),
      findOneBy: (field: keyof ItemChannelDefinition, value: any) => this.configManager.findOneBy<ItemChannelDefinition>('ItemChannel', field, value)
    };

    this.kingStore = {
      get: (id: number) => this.configManager.get<KingStoreDefinition>('KingStore', id),
      getAll: () => this.configManager.getAll<KingStoreDefinition>('KingStore'),
      getBatch: (ids: number[]) => this.configManager.getBatch<KingStoreDefinition>('KingStore', ids),
      search: (keyword: string) => this.configManager.search<KingStoreDefinition>('KingStore', keyword),
      filter: (predicate: (item: KingStoreDefinition) => boolean) => this.configManager.filter<KingStoreDefinition>('KingStore', predicate),
      findBy: (field: keyof KingStoreDefinition, value: any) => this.configManager.findBy<KingStoreDefinition>('KingStore', field, value),
      findOne: (predicate: (item: KingStoreDefinition) => boolean) => this.configManager.findOne<KingStoreDefinition>('KingStore', predicate),
      findOneBy: (field: keyof KingStoreDefinition, value: any) => this.configManager.findOneBy<KingStoreDefinition>('KingStore', field, value)
    };

    this.leagueCopy = {
      get: (id: number) => this.configManager.get<LeagueCopyDefinition>('LeagueCopy', id),
      getAll: () => this.configManager.getAll<LeagueCopyDefinition>('LeagueCopy'),
      getBatch: (ids: number[]) => this.configManager.getBatch<LeagueCopyDefinition>('LeagueCopy', ids),
      search: (keyword: string) => this.configManager.search<LeagueCopyDefinition>('LeagueCopy', keyword),
      filter: (predicate: (item: LeagueCopyDefinition) => boolean) => this.configManager.filter<LeagueCopyDefinition>('LeagueCopy', predicate),
      findBy: (field: keyof LeagueCopyDefinition, value: any) => this.configManager.findBy<LeagueCopyDefinition>('LeagueCopy', field, value),
      findOne: (predicate: (item: LeagueCopyDefinition) => boolean) => this.configManager.findOne<LeagueCopyDefinition>('LeagueCopy', predicate),
      findOneBy: (field: keyof LeagueCopyDefinition, value: any) => this.configManager.findOneBy<LeagueCopyDefinition>('LeagueCopy', field, value)
    };

    this.leagueEntrance = {
      get: (id: number) => this.configManager.get<LeagueEntranceDefinition>('LeagueEntrance', id),
      getAll: () => this.configManager.getAll<LeagueEntranceDefinition>('LeagueEntrance'),
      getBatch: (ids: number[]) => this.configManager.getBatch<LeagueEntranceDefinition>('LeagueEntrance', ids),
      search: (keyword: string) => this.configManager.search<LeagueEntranceDefinition>('LeagueEntrance', keyword),
      filter: (predicate: (item: LeagueEntranceDefinition) => boolean) => this.configManager.filter<LeagueEntranceDefinition>('LeagueEntrance', predicate),
      findBy: (field: keyof LeagueEntranceDefinition, value: any) => this.configManager.findBy<LeagueEntranceDefinition>('LeagueEntrance', field, value),
      findOne: (predicate: (item: LeagueEntranceDefinition) => boolean) => this.configManager.findOne<LeagueEntranceDefinition>('LeagueEntrance', predicate),
      findOneBy: (field: keyof LeagueEntranceDefinition, value: any) => this.configManager.findOneBy<LeagueEntranceDefinition>('LeagueEntrance', field, value)
    };

    this.leagueRelayReward = {
      get: (id: number) => this.configManager.get<LeagueRelayRewardDefinition>('LeagueRelayReward', id),
      getAll: () => this.configManager.getAll<LeagueRelayRewardDefinition>('LeagueRelayReward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<LeagueRelayRewardDefinition>('LeagueRelayReward', ids),
      search: (keyword: string) => this.configManager.search<LeagueRelayRewardDefinition>('LeagueRelayReward', keyword),
      filter: (predicate: (item: LeagueRelayRewardDefinition) => boolean) => this.configManager.filter<LeagueRelayRewardDefinition>('LeagueRelayReward', predicate),
      findBy: (field: keyof LeagueRelayRewardDefinition, value: any) => this.configManager.findBy<LeagueRelayRewardDefinition>('LeagueRelayReward', field, value),
      findOne: (predicate: (item: LeagueRelayRewardDefinition) => boolean) => this.configManager.findOne<LeagueRelayRewardDefinition>('LeagueRelayReward', predicate),
      findOneBy: (field: keyof LeagueRelayRewardDefinition, value: any) => this.configManager.findOneBy<LeagueRelayRewardDefinition>('LeagueRelayReward', field, value)
    };

    this.leagueReward = {
      get: (id: number) => this.configManager.get<LeagueRewardDefinition>('LeagueReward', id),
      getAll: () => this.configManager.getAll<LeagueRewardDefinition>('LeagueReward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<LeagueRewardDefinition>('LeagueReward', ids),
      search: (keyword: string) => this.configManager.search<LeagueRewardDefinition>('LeagueReward', keyword),
      filter: (predicate: (item: LeagueRewardDefinition) => boolean) => this.configManager.filter<LeagueRewardDefinition>('LeagueReward', predicate),
      findBy: (field: keyof LeagueRewardDefinition, value: any) => this.configManager.findBy<LeagueRewardDefinition>('LeagueReward', field, value),
      findOne: (predicate: (item: LeagueRewardDefinition) => boolean) => this.configManager.findOne<LeagueRewardDefinition>('LeagueReward', predicate),
      findOneBy: (field: keyof LeagueRewardDefinition, value: any) => this.configManager.findOneBy<LeagueRewardDefinition>('LeagueReward', field, value)
    };

    this.leagueSchedule = {
      get: (id: number) => this.configManager.get<LeagueScheduleDefinition>('LeagueSchedule', id),
      getAll: () => this.configManager.getAll<LeagueScheduleDefinition>('LeagueSchedule'),
      getBatch: (ids: number[]) => this.configManager.getBatch<LeagueScheduleDefinition>('LeagueSchedule', ids),
      search: (keyword: string) => this.configManager.search<LeagueScheduleDefinition>('LeagueSchedule', keyword),
      filter: (predicate: (item: LeagueScheduleDefinition) => boolean) => this.configManager.filter<LeagueScheduleDefinition>('LeagueSchedule', predicate),
      findBy: (field: keyof LeagueScheduleDefinition, value: any) => this.configManager.findBy<LeagueScheduleDefinition>('LeagueSchedule', field, value),
      findOne: (predicate: (item: LeagueScheduleDefinition) => boolean) => this.configManager.findOne<LeagueScheduleDefinition>('LeagueSchedule', predicate),
      findOneBy: (field: keyof LeagueScheduleDefinition, value: any) => this.configManager.findOneBy<LeagueScheduleDefinition>('LeagueSchedule', field, value)
    };

    this.leagueShop = {
      get: (id: number) => this.configManager.get<LeagueShopDefinition>('LeagueShop', id),
      getAll: () => this.configManager.getAll<LeagueShopDefinition>('LeagueShop'),
      getBatch: (ids: number[]) => this.configManager.getBatch<LeagueShopDefinition>('LeagueShop', ids),
      search: (keyword: string) => this.configManager.search<LeagueShopDefinition>('LeagueShop', keyword),
      filter: (predicate: (item: LeagueShopDefinition) => boolean) => this.configManager.filter<LeagueShopDefinition>('LeagueShop', predicate),
      findBy: (field: keyof LeagueShopDefinition, value: any) => this.configManager.findBy<LeagueShopDefinition>('LeagueShop', field, value),
      findOne: (predicate: (item: LeagueShopDefinition) => boolean) => this.configManager.findOne<LeagueShopDefinition>('LeagueShop', predicate),
      findOneBy: (field: keyof LeagueShopDefinition, value: any) => this.configManager.findOneBy<LeagueShopDefinition>('LeagueShop', field, value)
    };

    this.leagueSwitch = {
      get: (id: number) => this.configManager.get<LeagueSwitchDefinition>('LeagueSwitch', id),
      getAll: () => this.configManager.getAll<LeagueSwitchDefinition>('LeagueSwitch'),
      getBatch: (ids: number[]) => this.configManager.getBatch<LeagueSwitchDefinition>('LeagueSwitch', ids),
      search: (keyword: string) => this.configManager.search<LeagueSwitchDefinition>('LeagueSwitch', keyword),
      filter: (predicate: (item: LeagueSwitchDefinition) => boolean) => this.configManager.filter<LeagueSwitchDefinition>('LeagueSwitch', predicate),
      findBy: (field: keyof LeagueSwitchDefinition, value: any) => this.configManager.findBy<LeagueSwitchDefinition>('LeagueSwitch', field, value),
      findOne: (predicate: (item: LeagueSwitchDefinition) => boolean) => this.configManager.findOne<LeagueSwitchDefinition>('LeagueSwitch', predicate),
      findOneBy: (field: keyof LeagueSwitchDefinition, value: any) => this.configManager.findOneBy<LeagueSwitchDefinition>('LeagueSwitch', field, value)
    };

    this.leagueTeam = {
      get: (id: number) => this.configManager.get<LeagueTeamDefinition>('LeagueTeam', id),
      getAll: () => this.configManager.getAll<LeagueTeamDefinition>('LeagueTeam'),
      getBatch: (ids: number[]) => this.configManager.getBatch<LeagueTeamDefinition>('LeagueTeam', ids),
      search: (keyword: string) => this.configManager.search<LeagueTeamDefinition>('LeagueTeam', keyword),
      filter: (predicate: (item: LeagueTeamDefinition) => boolean) => this.configManager.filter<LeagueTeamDefinition>('LeagueTeam', predicate),
      findBy: (field: keyof LeagueTeamDefinition, value: any) => this.configManager.findBy<LeagueTeamDefinition>('LeagueTeam', field, value),
      findOne: (predicate: (item: LeagueTeamDefinition) => boolean) => this.configManager.findOne<LeagueTeamDefinition>('LeagueTeam', predicate),
      findOneBy: (field: keyof LeagueTeamDefinition, value: any) => this.configManager.findOneBy<LeagueTeamDefinition>('LeagueTeam', field, value)
    };

    this.leagueTime = {
      get: (id: number) => this.configManager.get<LeagueTimeDefinition>('LeagueTime', id),
      getAll: () => this.configManager.getAll<LeagueTimeDefinition>('LeagueTime'),
      getBatch: (ids: number[]) => this.configManager.getBatch<LeagueTimeDefinition>('LeagueTime', ids),
      search: (keyword: string) => this.configManager.search<LeagueTimeDefinition>('LeagueTime', keyword),
      filter: (predicate: (item: LeagueTimeDefinition) => boolean) => this.configManager.filter<LeagueTimeDefinition>('LeagueTime', predicate),
      findBy: (field: keyof LeagueTimeDefinition, value: any) => this.configManager.findBy<LeagueTimeDefinition>('LeagueTime', field, value),
      findOne: (predicate: (item: LeagueTimeDefinition) => boolean) => this.configManager.findOne<LeagueTimeDefinition>('LeagueTime', predicate),
      findOneBy: (field: keyof LeagueTimeDefinition, value: any) => this.configManager.findOneBy<LeagueTimeDefinition>('LeagueTime', field, value)
    };

    this.limitStore = {
      get: (id: number) => this.configManager.get<limitStoreDefinition>('limitStore', id),
      getAll: () => this.configManager.getAll<limitStoreDefinition>('limitStore'),
      getBatch: (ids: number[]) => this.configManager.getBatch<limitStoreDefinition>('limitStore', ids),
      search: (keyword: string) => this.configManager.search<limitStoreDefinition>('limitStore', keyword),
      filter: (predicate: (item: limitStoreDefinition) => boolean) => this.configManager.filter<limitStoreDefinition>('limitStore', predicate),
      findBy: (field: keyof limitStoreDefinition, value: any) => this.configManager.findBy<limitStoreDefinition>('limitStore', field, value),
      findOne: (predicate: (item: limitStoreDefinition) => boolean) => this.configManager.findOne<limitStoreDefinition>('limitStore', predicate),
      findOneBy: (field: keyof limitStoreDefinition, value: any) => this.configManager.findOneBy<limitStoreDefinition>('limitStore', field, value)
    };

    this.loginSign = {
      get: (id: number) => this.configManager.get<LoginSignDefinition>('LoginSign', id),
      getAll: () => this.configManager.getAll<LoginSignDefinition>('LoginSign'),
      getBatch: (ids: number[]) => this.configManager.getBatch<LoginSignDefinition>('LoginSign', ids),
      search: (keyword: string) => this.configManager.search<LoginSignDefinition>('LoginSign', keyword),
      filter: (predicate: (item: LoginSignDefinition) => boolean) => this.configManager.filter<LoginSignDefinition>('LoginSign', predicate),
      findBy: (field: keyof LoginSignDefinition, value: any) => this.configManager.findBy<LoginSignDefinition>('LoginSign', field, value),
      findOne: (predicate: (item: LoginSignDefinition) => boolean) => this.configManager.findOne<LoginSignDefinition>('LoginSign', predicate),
      findOneBy: (field: keyof LoginSignDefinition, value: any) => this.configManager.findOneBy<LoginSignDefinition>('LoginSign', field, value)
    };

    this.mailText = {
      get: (id: number) => this.configManager.get<MailTextDefinition>('MailText', id),
      getAll: () => this.configManager.getAll<MailTextDefinition>('MailText'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MailTextDefinition>('MailText', ids),
      search: (keyword: string) => this.configManager.search<MailTextDefinition>('MailText', keyword),
      filter: (predicate: (item: MailTextDefinition) => boolean) => this.configManager.filter<MailTextDefinition>('MailText', predicate),
      findBy: (field: keyof MailTextDefinition, value: any) => this.configManager.findBy<MailTextDefinition>('MailText', field, value),
      findOne: (predicate: (item: MailTextDefinition) => boolean) => this.configManager.findOne<MailTextDefinition>('MailText', predicate),
      findOneBy: (field: keyof MailTextDefinition, value: any) => this.configManager.findOneBy<MailTextDefinition>('MailText', field, value)
    };

    this.matchEntrance = {
      get: (id: number) => this.configManager.get<MatchEntranceDefinition>('MatchEntrance', id),
      getAll: () => this.configManager.getAll<MatchEntranceDefinition>('MatchEntrance'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MatchEntranceDefinition>('MatchEntrance', ids),
      search: (keyword: string) => this.configManager.search<MatchEntranceDefinition>('MatchEntrance', keyword),
      filter: (predicate: (item: MatchEntranceDefinition) => boolean) => this.configManager.filter<MatchEntranceDefinition>('MatchEntrance', predicate),
      findBy: (field: keyof MatchEntranceDefinition, value: any) => this.configManager.findBy<MatchEntranceDefinition>('MatchEntrance', field, value),
      findOne: (predicate: (item: MatchEntranceDefinition) => boolean) => this.configManager.findOne<MatchEntranceDefinition>('MatchEntrance', predicate),
      findOneBy: (field: keyof MatchEntranceDefinition, value: any) => this.configManager.findOneBy<MatchEntranceDefinition>('MatchEntrance', field, value)
    };

    this.middleEastCup = {
      get: (id: number) => this.configManager.get<MiddleEastCupDefinition>('MiddleEastCup', id),
      getAll: () => this.configManager.getAll<MiddleEastCupDefinition>('MiddleEastCup'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MiddleEastCupDefinition>('MiddleEastCup', ids),
      search: (keyword: string) => this.configManager.search<MiddleEastCupDefinition>('MiddleEastCup', keyword),
      filter: (predicate: (item: MiddleEastCupDefinition) => boolean) => this.configManager.filter<MiddleEastCupDefinition>('MiddleEastCup', predicate),
      findBy: (field: keyof MiddleEastCupDefinition, value: any) => this.configManager.findBy<MiddleEastCupDefinition>('MiddleEastCup', field, value),
      findOne: (predicate: (item: MiddleEastCupDefinition) => boolean) => this.configManager.findOne<MiddleEastCupDefinition>('MiddleEastCup', predicate),
      findOneBy: (field: keyof MiddleEastCupDefinition, value: any) => this.configManager.findOneBy<MiddleEastCupDefinition>('MiddleEastCup', field, value)
    };

    this.middleEastPlayer = {
      get: (id: number) => this.configManager.get<MiddleEastPlayerDefinition>('MiddleEastPlayer', id),
      getAll: () => this.configManager.getAll<MiddleEastPlayerDefinition>('MiddleEastPlayer'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MiddleEastPlayerDefinition>('MiddleEastPlayer', ids),
      search: (keyword: string) => this.configManager.search<MiddleEastPlayerDefinition>('MiddleEastPlayer', keyword),
      filter: (predicate: (item: MiddleEastPlayerDefinition) => boolean) => this.configManager.filter<MiddleEastPlayerDefinition>('MiddleEastPlayer', predicate),
      findBy: (field: keyof MiddleEastPlayerDefinition, value: any) => this.configManager.findBy<MiddleEastPlayerDefinition>('MiddleEastPlayer', field, value),
      findOne: (predicate: (item: MiddleEastPlayerDefinition) => boolean) => this.configManager.findOne<MiddleEastPlayerDefinition>('MiddleEastPlayer', predicate),
      findOneBy: (field: keyof MiddleEastPlayerDefinition, value: any) => this.configManager.findOneBy<MiddleEastPlayerDefinition>('MiddleEastPlayer', field, value)
    };

    this.middleEastStrength = {
      get: (id: number) => this.configManager.get<MiddleEastStrengthDefinition>('MiddleEastStrength', id),
      getAll: () => this.configManager.getAll<MiddleEastStrengthDefinition>('MiddleEastStrength'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MiddleEastStrengthDefinition>('MiddleEastStrength', ids),
      search: (keyword: string) => this.configManager.search<MiddleEastStrengthDefinition>('MiddleEastStrength', keyword),
      filter: (predicate: (item: MiddleEastStrengthDefinition) => boolean) => this.configManager.filter<MiddleEastStrengthDefinition>('MiddleEastStrength', predicate),
      findBy: (field: keyof MiddleEastStrengthDefinition, value: any) => this.configManager.findBy<MiddleEastStrengthDefinition>('MiddleEastStrength', field, value),
      findOne: (predicate: (item: MiddleEastStrengthDefinition) => boolean) => this.configManager.findOne<MiddleEastStrengthDefinition>('MiddleEastStrength', predicate),
      findOneBy: (field: keyof MiddleEastStrengthDefinition, value: any) => this.configManager.findOneBy<MiddleEastStrengthDefinition>('MiddleEastStrength', field, value)
    };

    this.middleEastTeam = {
      get: (id: number) => this.configManager.get<MiddleEastTeamDefinition>('MiddleEastTeam', id),
      getAll: () => this.configManager.getAll<MiddleEastTeamDefinition>('MiddleEastTeam'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MiddleEastTeamDefinition>('MiddleEastTeam', ids),
      search: (keyword: string) => this.configManager.search<MiddleEastTeamDefinition>('MiddleEastTeam', keyword),
      filter: (predicate: (item: MiddleEastTeamDefinition) => boolean) => this.configManager.filter<MiddleEastTeamDefinition>('MiddleEastTeam', predicate),
      findBy: (field: keyof MiddleEastTeamDefinition, value: any) => this.configManager.findBy<MiddleEastTeamDefinition>('MiddleEastTeam', field, value),
      findOne: (predicate: (item: MiddleEastTeamDefinition) => boolean) => this.configManager.findOne<MiddleEastTeamDefinition>('MiddleEastTeam', predicate),
      findOneBy: (field: keyof MiddleEastTeamDefinition, value: any) => this.configManager.findOneBy<MiddleEastTeamDefinition>('MiddleEastTeam', field, value)
    };

    this.mLS = {
      get: (id: number) => this.configManager.get<MLSDefinition>('MLS', id),
      getAll: () => this.configManager.getAll<MLSDefinition>('MLS'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MLSDefinition>('MLS', ids),
      search: (keyword: string) => this.configManager.search<MLSDefinition>('MLS', keyword),
      filter: (predicate: (item: MLSDefinition) => boolean) => this.configManager.filter<MLSDefinition>('MLS', predicate),
      findBy: (field: keyof MLSDefinition, value: any) => this.configManager.findBy<MLSDefinition>('MLS', field, value),
      findOne: (predicate: (item: MLSDefinition) => boolean) => this.configManager.findOne<MLSDefinition>('MLS', predicate),
      findOneBy: (field: keyof MLSDefinition, value: any) => this.configManager.findOneBy<MLSDefinition>('MLS', field, value)
    };

    this.mLSPlayer = {
      get: (id: number) => this.configManager.get<MLSPlayerDefinition>('MLSPlayer', id),
      getAll: () => this.configManager.getAll<MLSPlayerDefinition>('MLSPlayer'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MLSPlayerDefinition>('MLSPlayer', ids),
      search: (keyword: string) => this.configManager.search<MLSPlayerDefinition>('MLSPlayer', keyword),
      filter: (predicate: (item: MLSPlayerDefinition) => boolean) => this.configManager.filter<MLSPlayerDefinition>('MLSPlayer', predicate),
      findBy: (field: keyof MLSPlayerDefinition, value: any) => this.configManager.findBy<MLSPlayerDefinition>('MLSPlayer', field, value),
      findOne: (predicate: (item: MLSPlayerDefinition) => boolean) => this.configManager.findOne<MLSPlayerDefinition>('MLSPlayer', predicate),
      findOneBy: (field: keyof MLSPlayerDefinition, value: any) => this.configManager.findOneBy<MLSPlayerDefinition>('MLSPlayer', field, value)
    };

    this.mLSStrength = {
      get: (id: number) => this.configManager.get<MLSStrengthDefinition>('MLSStrength', id),
      getAll: () => this.configManager.getAll<MLSStrengthDefinition>('MLSStrength'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MLSStrengthDefinition>('MLSStrength', ids),
      search: (keyword: string) => this.configManager.search<MLSStrengthDefinition>('MLSStrength', keyword),
      filter: (predicate: (item: MLSStrengthDefinition) => boolean) => this.configManager.filter<MLSStrengthDefinition>('MLSStrength', predicate),
      findBy: (field: keyof MLSStrengthDefinition, value: any) => this.configManager.findBy<MLSStrengthDefinition>('MLSStrength', field, value),
      findOne: (predicate: (item: MLSStrengthDefinition) => boolean) => this.configManager.findOne<MLSStrengthDefinition>('MLSStrength', predicate),
      findOneBy: (field: keyof MLSStrengthDefinition, value: any) => this.configManager.findOneBy<MLSStrengthDefinition>('MLSStrength', field, value)
    };

    this.mLSTeam = {
      get: (id: number) => this.configManager.get<MLSTeamDefinition>('MLSTeam', id),
      getAll: () => this.configManager.getAll<MLSTeamDefinition>('MLSTeam'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MLSTeamDefinition>('MLSTeam', ids),
      search: (keyword: string) => this.configManager.search<MLSTeamDefinition>('MLSTeam', keyword),
      filter: (predicate: (item: MLSTeamDefinition) => boolean) => this.configManager.filter<MLSTeamDefinition>('MLSTeam', predicate),
      findBy: (field: keyof MLSTeamDefinition, value: any) => this.configManager.findBy<MLSTeamDefinition>('MLSTeam', field, value),
      findOne: (predicate: (item: MLSTeamDefinition) => boolean) => this.configManager.findOne<MLSTeamDefinition>('MLSTeam', predicate),
      findOneBy: (field: keyof MLSTeamDefinition, value: any) => this.configManager.findOneBy<MLSTeamDefinition>('MLSTeam', field, value)
    };

    this.mustDrop = {
      get: (id: number) => this.configManager.get<MustDropDefinition>('MustDrop', id),
      getAll: () => this.configManager.getAll<MustDropDefinition>('MustDrop'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MustDropDefinition>('MustDrop', ids),
      search: (keyword: string) => this.configManager.search<MustDropDefinition>('MustDrop', keyword),
      filter: (predicate: (item: MustDropDefinition) => boolean) => this.configManager.filter<MustDropDefinition>('MustDrop', predicate),
      findBy: (field: keyof MustDropDefinition, value: any) => this.configManager.findBy<MustDropDefinition>('MustDrop', field, value),
      findOne: (predicate: (item: MustDropDefinition) => boolean) => this.configManager.findOne<MustDropDefinition>('MustDrop', predicate),
      findOneBy: (field: keyof MustDropDefinition, value: any) => this.configManager.findOneBy<MustDropDefinition>('MustDrop', field, value)
    };

    this.mustDropItem = {
      get: (id: number) => this.configManager.get<MustDropItemDefinition>('MustDropItem', id),
      getAll: () => this.configManager.getAll<MustDropItemDefinition>('MustDropItem'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MustDropItemDefinition>('MustDropItem', ids),
      search: (keyword: string) => this.configManager.search<MustDropItemDefinition>('MustDropItem', keyword),
      filter: (predicate: (item: MustDropItemDefinition) => boolean) => this.configManager.filter<MustDropItemDefinition>('MustDropItem', predicate),
      findBy: (field: keyof MustDropItemDefinition, value: any) => this.configManager.findBy<MustDropItemDefinition>('MustDropItem', field, value),
      findOne: (predicate: (item: MustDropItemDefinition) => boolean) => this.configManager.findOne<MustDropItemDefinition>('MustDropItem', predicate),
      findOneBy: (field: keyof MustDropItemDefinition, value: any) => this.configManager.findOneBy<MustDropItemDefinition>('MustDropItem', field, value)
    };

    this.mVP = {
      get: (id: number) => this.configManager.get<MVPDefinition>('MVP', id),
      getAll: () => this.configManager.getAll<MVPDefinition>('MVP'),
      getBatch: (ids: number[]) => this.configManager.getBatch<MVPDefinition>('MVP', ids),
      search: (keyword: string) => this.configManager.search<MVPDefinition>('MVP', keyword),
      filter: (predicate: (item: MVPDefinition) => boolean) => this.configManager.filter<MVPDefinition>('MVP', predicate),
      findBy: (field: keyof MVPDefinition, value: any) => this.configManager.findBy<MVPDefinition>('MVP', field, value),
      findOne: (predicate: (item: MVPDefinition) => boolean) => this.configManager.findOne<MVPDefinition>('MVP', predicate),
      findOneBy: (field: keyof MVPDefinition, value: any) => this.configManager.findOneBy<MVPDefinition>('MVP', field, value)
    };

    this.navigate = {
      get: (id: number) => this.configManager.get<NavigateDefinition>('Navigate', id),
      getAll: () => this.configManager.getAll<NavigateDefinition>('Navigate'),
      getBatch: (ids: number[]) => this.configManager.getBatch<NavigateDefinition>('Navigate', ids),
      search: (keyword: string) => this.configManager.search<NavigateDefinition>('Navigate', keyword),
      filter: (predicate: (item: NavigateDefinition) => boolean) => this.configManager.filter<NavigateDefinition>('Navigate', predicate),
      findBy: (field: keyof NavigateDefinition, value: any) => this.configManager.findBy<NavigateDefinition>('Navigate', field, value),
      findOne: (predicate: (item: NavigateDefinition) => boolean) => this.configManager.findOne<NavigateDefinition>('Navigate', predicate),
      findOneBy: (field: keyof NavigateDefinition, value: any) => this.configManager.findOneBy<NavigateDefinition>('Navigate', field, value)
    };

    this.newbieGuide = {
      get: (id: number) => this.configManager.get<NewbieGuideDefinition>('NewbieGuide', id),
      getAll: () => this.configManager.getAll<NewbieGuideDefinition>('NewbieGuide'),
      getBatch: (ids: number[]) => this.configManager.getBatch<NewbieGuideDefinition>('NewbieGuide', ids),
      search: (keyword: string) => this.configManager.search<NewbieGuideDefinition>('NewbieGuide', keyword),
      filter: (predicate: (item: NewbieGuideDefinition) => boolean) => this.configManager.filter<NewbieGuideDefinition>('NewbieGuide', predicate),
      findBy: (field: keyof NewbieGuideDefinition, value: any) => this.configManager.findBy<NewbieGuideDefinition>('NewbieGuide', field, value),
      findOne: (predicate: (item: NewbieGuideDefinition) => boolean) => this.configManager.findOne<NewbieGuideDefinition>('NewbieGuide', predicate),
      findOneBy: (field: keyof NewbieGuideDefinition, value: any) => this.configManager.findOneBy<NewbieGuideDefinition>('NewbieGuide', field, value)
    };

    this.newHandTask = {
      get: (id: number) => this.configManager.get<NewHandTaskDefinition>('NewHandTask', id),
      getAll: () => this.configManager.getAll<NewHandTaskDefinition>('NewHandTask'),
      getBatch: (ids: number[]) => this.configManager.getBatch<NewHandTaskDefinition>('NewHandTask', ids),
      search: (keyword: string) => this.configManager.search<NewHandTaskDefinition>('NewHandTask', keyword),
      filter: (predicate: (item: NewHandTaskDefinition) => boolean) => this.configManager.filter<NewHandTaskDefinition>('NewHandTask', predicate),
      findBy: (field: keyof NewHandTaskDefinition, value: any) => this.configManager.findBy<NewHandTaskDefinition>('NewHandTask', field, value),
      findOne: (predicate: (item: NewHandTaskDefinition) => boolean) => this.configManager.findOne<NewHandTaskDefinition>('NewHandTask', predicate),
      findOneBy: (field: keyof NewHandTaskDefinition, value: any) => this.configManager.findOneBy<NewHandTaskDefinition>('NewHandTask', field, value)
    };

    this.newPositionMatch = {
      get: (id: number) => this.configManager.get<NewPositionMatchDefinition>('NewPositionMatch', id),
      getAll: () => this.configManager.getAll<NewPositionMatchDefinition>('NewPositionMatch'),
      getBatch: (ids: number[]) => this.configManager.getBatch<NewPositionMatchDefinition>('NewPositionMatch', ids),
      search: (keyword: string) => this.configManager.search<NewPositionMatchDefinition>('NewPositionMatch', keyword),
      filter: (predicate: (item: NewPositionMatchDefinition) => boolean) => this.configManager.filter<NewPositionMatchDefinition>('NewPositionMatch', predicate),
      findBy: (field: keyof NewPositionMatchDefinition, value: any) => this.configManager.findBy<NewPositionMatchDefinition>('NewPositionMatch', field, value),
      findOne: (predicate: (item: NewPositionMatchDefinition) => boolean) => this.configManager.findOne<NewPositionMatchDefinition>('NewPositionMatch', predicate),
      findOneBy: (field: keyof NewPositionMatchDefinition, value: any) => this.configManager.findOneBy<NewPositionMatchDefinition>('NewPositionMatch', field, value)
    };

    this.nPCHero = {
      get: (id: number) => this.configManager.get<NPCHeroDefinition>('NPCHero', id),
      getAll: () => this.configManager.getAll<NPCHeroDefinition>('NPCHero'),
      getBatch: (ids: number[]) => this.configManager.getBatch<NPCHeroDefinition>('NPCHero', ids),
      search: (keyword: string) => this.configManager.search<NPCHeroDefinition>('NPCHero', keyword),
      filter: (predicate: (item: NPCHeroDefinition) => boolean) => this.configManager.filter<NPCHeroDefinition>('NPCHero', predicate),
      findBy: (field: keyof NPCHeroDefinition, value: any) => this.configManager.findBy<NPCHeroDefinition>('NPCHero', field, value),
      findOne: (predicate: (item: NPCHeroDefinition) => boolean) => this.configManager.findOne<NPCHeroDefinition>('NPCHero', predicate),
      findOneBy: (field: keyof NPCHeroDefinition, value: any) => this.configManager.findOneBy<NPCHeroDefinition>('NPCHero', field, value)
    };

    this.playerHandbook = {
      get: (id: number) => this.configManager.get<PlayerHandbookDefinition>('PlayerHandbook', id),
      getAll: () => this.configManager.getAll<PlayerHandbookDefinition>('PlayerHandbook'),
      getBatch: (ids: number[]) => this.configManager.getBatch<PlayerHandbookDefinition>('PlayerHandbook', ids),
      search: (keyword: string) => this.configManager.search<PlayerHandbookDefinition>('PlayerHandbook', keyword),
      filter: (predicate: (item: PlayerHandbookDefinition) => boolean) => this.configManager.filter<PlayerHandbookDefinition>('PlayerHandbook', predicate),
      findBy: (field: keyof PlayerHandbookDefinition, value: any) => this.configManager.findBy<PlayerHandbookDefinition>('PlayerHandbook', field, value),
      findOne: (predicate: (item: PlayerHandbookDefinition) => boolean) => this.configManager.findOne<PlayerHandbookDefinition>('PlayerHandbook', predicate),
      findOneBy: (field: keyof PlayerHandbookDefinition, value: any) => this.configManager.findOneBy<PlayerHandbookDefinition>('PlayerHandbook', field, value)
    };

    this.positionMatch = {
      get: (id: number) => this.configManager.get<PositionMatchDefinition>('PositionMatch', id),
      getAll: () => this.configManager.getAll<PositionMatchDefinition>('PositionMatch'),
      getBatch: (ids: number[]) => this.configManager.getBatch<PositionMatchDefinition>('PositionMatch', ids),
      search: (keyword: string) => this.configManager.search<PositionMatchDefinition>('PositionMatch', keyword),
      filter: (predicate: (item: PositionMatchDefinition) => boolean) => this.configManager.filter<PositionMatchDefinition>('PositionMatch', predicate),
      findBy: (field: keyof PositionMatchDefinition, value: any) => this.configManager.findBy<PositionMatchDefinition>('PositionMatch', field, value),
      findOne: (predicate: (item: PositionMatchDefinition) => boolean) => this.configManager.findOne<PositionMatchDefinition>('PositionMatch', predicate),
      findOneBy: (field: keyof PositionMatchDefinition, value: any) => this.configManager.findOneBy<PositionMatchDefinition>('PositionMatch', field, value)
    };

    this.pullerAward = {
      get: (id: number) => this.configManager.get<PullerAwardDefinition>('PullerAward', id),
      getAll: () => this.configManager.getAll<PullerAwardDefinition>('PullerAward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<PullerAwardDefinition>('PullerAward', ids),
      search: (keyword: string) => this.configManager.search<PullerAwardDefinition>('PullerAward', keyword),
      filter: (predicate: (item: PullerAwardDefinition) => boolean) => this.configManager.filter<PullerAwardDefinition>('PullerAward', predicate),
      findBy: (field: keyof PullerAwardDefinition, value: any) => this.configManager.findBy<PullerAwardDefinition>('PullerAward', field, value),
      findOne: (predicate: (item: PullerAwardDefinition) => boolean) => this.configManager.findOne<PullerAwardDefinition>('PullerAward', predicate),
      findOneBy: (field: keyof PullerAwardDefinition, value: any) => this.configManager.findOneBy<PullerAwardDefinition>('PullerAward', field, value)
    };

    this.pushPackage = {
      get: (id: number) => this.configManager.get<PushPackageDefinition>('PushPackage', id),
      getAll: () => this.configManager.getAll<PushPackageDefinition>('PushPackage'),
      getBatch: (ids: number[]) => this.configManager.getBatch<PushPackageDefinition>('PushPackage', ids),
      search: (keyword: string) => this.configManager.search<PushPackageDefinition>('PushPackage', keyword),
      filter: (predicate: (item: PushPackageDefinition) => boolean) => this.configManager.filter<PushPackageDefinition>('PushPackage', predicate),
      findBy: (field: keyof PushPackageDefinition, value: any) => this.configManager.findBy<PushPackageDefinition>('PushPackage', field, value),
      findOne: (predicate: (item: PushPackageDefinition) => boolean) => this.configManager.findOne<PushPackageDefinition>('PushPackage', predicate),
      findOneBy: (field: keyof PushPackageDefinition, value: any) => this.configManager.findOneBy<PushPackageDefinition>('PushPackage', field, value)
    };

    this.recharge = {
      get: (id: number) => this.configManager.get<RechargeDefinition>('Recharge', id),
      getAll: () => this.configManager.getAll<RechargeDefinition>('Recharge'),
      getBatch: (ids: number[]) => this.configManager.getBatch<RechargeDefinition>('Recharge', ids),
      search: (keyword: string) => this.configManager.search<RechargeDefinition>('Recharge', keyword),
      filter: (predicate: (item: RechargeDefinition) => boolean) => this.configManager.filter<RechargeDefinition>('Recharge', predicate),
      findBy: (field: keyof RechargeDefinition, value: any) => this.configManager.findBy<RechargeDefinition>('Recharge', field, value),
      findOne: (predicate: (item: RechargeDefinition) => boolean) => this.configManager.findOne<RechargeDefinition>('Recharge', predicate),
      findOneBy: (field: keyof RechargeDefinition, value: any) => this.configManager.findOneBy<RechargeDefinition>('Recharge', field, value)
    };

    this.regression = {
      get: (id: number) => this.configManager.get<RegressionDefinition>('Regression', id),
      getAll: () => this.configManager.getAll<RegressionDefinition>('Regression'),
      getBatch: (ids: number[]) => this.configManager.getBatch<RegressionDefinition>('Regression', ids),
      search: (keyword: string) => this.configManager.search<RegressionDefinition>('Regression', keyword),
      filter: (predicate: (item: RegressionDefinition) => boolean) => this.configManager.filter<RegressionDefinition>('Regression', predicate),
      findBy: (field: keyof RegressionDefinition, value: any) => this.configManager.findBy<RegressionDefinition>('Regression', field, value),
      findOne: (predicate: (item: RegressionDefinition) => boolean) => this.configManager.findOne<RegressionDefinition>('Regression', predicate),
      findOneBy: (field: keyof RegressionDefinition, value: any) => this.configManager.findOneBy<RegressionDefinition>('Regression', field, value)
    };

    this.scoutScope = {
      get: (id: number) => this.configManager.get<ScoutScopeDefinition>('ScoutScope', id),
      getAll: () => this.configManager.getAll<ScoutScopeDefinition>('ScoutScope'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ScoutScopeDefinition>('ScoutScope', ids),
      search: (keyword: string) => this.configManager.search<ScoutScopeDefinition>('ScoutScope', keyword),
      filter: (predicate: (item: ScoutScopeDefinition) => boolean) => this.configManager.filter<ScoutScopeDefinition>('ScoutScope', predicate),
      findBy: (field: keyof ScoutScopeDefinition, value: any) => this.configManager.findBy<ScoutScopeDefinition>('ScoutScope', field, value),
      findOne: (predicate: (item: ScoutScopeDefinition) => boolean) => this.configManager.findOne<ScoutScopeDefinition>('ScoutScope', predicate),
      findOneBy: (field: keyof ScoutScopeDefinition, value: any) => this.configManager.findOneBy<ScoutScopeDefinition>('ScoutScope', field, value)
    };

    this.seasonStore = {
      get: (id: number) => this.configManager.get<SeasonStoreDefinition>('SeasonStore', id),
      getAll: () => this.configManager.getAll<SeasonStoreDefinition>('SeasonStore'),
      getBatch: (ids: number[]) => this.configManager.getBatch<SeasonStoreDefinition>('SeasonStore', ids),
      search: (keyword: string) => this.configManager.search<SeasonStoreDefinition>('SeasonStore', keyword),
      filter: (predicate: (item: SeasonStoreDefinition) => boolean) => this.configManager.filter<SeasonStoreDefinition>('SeasonStore', predicate),
      findBy: (field: keyof SeasonStoreDefinition, value: any) => this.configManager.findBy<SeasonStoreDefinition>('SeasonStore', field, value),
      findOne: (predicate: (item: SeasonStoreDefinition) => boolean) => this.configManager.findOne<SeasonStoreDefinition>('SeasonStore', predicate),
      findOneBy: (field: keyof SeasonStoreDefinition, value: any) => this.configManager.findOneBy<SeasonStoreDefinition>('SeasonStore', field, value)
    };

    this.share = {
      get: (id: number) => this.configManager.get<ShareDefinition>('Share', id),
      getAll: () => this.configManager.getAll<ShareDefinition>('Share'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ShareDefinition>('Share', ids),
      search: (keyword: string) => this.configManager.search<ShareDefinition>('Share', keyword),
      filter: (predicate: (item: ShareDefinition) => boolean) => this.configManager.filter<ShareDefinition>('Share', predicate),
      findBy: (field: keyof ShareDefinition, value: any) => this.configManager.findBy<ShareDefinition>('Share', field, value),
      findOne: (predicate: (item: ShareDefinition) => boolean) => this.configManager.findOne<ShareDefinition>('Share', predicate),
      findOneBy: (field: keyof ShareDefinition, value: any) => this.configManager.findOneBy<ShareDefinition>('Share', field, value)
    };

    this.shop = {
      get: (id: number) => this.configManager.get<ShopDefinition>('Shop', id),
      getAll: () => this.configManager.getAll<ShopDefinition>('Shop'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ShopDefinition>('Shop', ids),
      search: (keyword: string) => this.configManager.search<ShopDefinition>('Shop', keyword),
      filter: (predicate: (item: ShopDefinition) => boolean) => this.configManager.filter<ShopDefinition>('Shop', predicate),
      findBy: (field: keyof ShopDefinition, value: any) => this.configManager.findBy<ShopDefinition>('Shop', field, value),
      findOne: (predicate: (item: ShopDefinition) => boolean) => this.configManager.findOne<ShopDefinition>('Shop', predicate),
      findOneBy: (field: keyof ShopDefinition, value: any) => this.configManager.findOneBy<ShopDefinition>('Shop', field, value)
    };

    this.shopTab = {
      get: (id: number) => this.configManager.get<ShopTabDefinition>('ShopTab', id),
      getAll: () => this.configManager.getAll<ShopTabDefinition>('ShopTab'),
      getBatch: (ids: number[]) => this.configManager.getBatch<ShopTabDefinition>('ShopTab', ids),
      search: (keyword: string) => this.configManager.search<ShopTabDefinition>('ShopTab', keyword),
      filter: (predicate: (item: ShopTabDefinition) => boolean) => this.configManager.filter<ShopTabDefinition>('ShopTab', predicate),
      findBy: (field: keyof ShopTabDefinition, value: any) => this.configManager.findBy<ShopTabDefinition>('ShopTab', field, value),
      findOne: (predicate: (item: ShopTabDefinition) => boolean) => this.configManager.findOne<ShopTabDefinition>('ShopTab', predicate),
      findOneBy: (field: keyof ShopTabDefinition, value: any) => this.configManager.findOneBy<ShopTabDefinition>('ShopTab', field, value)
    };

    this.skillUnlock = {
      get: (id: number) => this.configManager.get<SkillUnlockDefinition>('SkillUnlock', id),
      getAll: () => this.configManager.getAll<SkillUnlockDefinition>('SkillUnlock'),
      getBatch: (ids: number[]) => this.configManager.getBatch<SkillUnlockDefinition>('SkillUnlock', ids),
      search: (keyword: string) => this.configManager.search<SkillUnlockDefinition>('SkillUnlock', keyword),
      filter: (predicate: (item: SkillUnlockDefinition) => boolean) => this.configManager.filter<SkillUnlockDefinition>('SkillUnlock', predicate),
      findBy: (field: keyof SkillUnlockDefinition, value: any) => this.configManager.findBy<SkillUnlockDefinition>('SkillUnlock', field, value),
      findOne: (predicate: (item: SkillUnlockDefinition) => boolean) => this.configManager.findOne<SkillUnlockDefinition>('SkillUnlock', predicate),
      findOneBy: (field: keyof SkillUnlockDefinition, value: any) => this.configManager.findOneBy<SkillUnlockDefinition>('SkillUnlock', field, value)
    };

    this.stateProbability = {
      get: (id: number) => this.configManager.get<StateProbabilityDefinition>('StateProbability', id),
      getAll: () => this.configManager.getAll<StateProbabilityDefinition>('StateProbability'),
      getBatch: (ids: number[]) => this.configManager.getBatch<StateProbabilityDefinition>('StateProbability', ids),
      search: (keyword: string) => this.configManager.search<StateProbabilityDefinition>('StateProbability', keyword),
      filter: (predicate: (item: StateProbabilityDefinition) => boolean) => this.configManager.filter<StateProbabilityDefinition>('StateProbability', predicate),
      findBy: (field: keyof StateProbabilityDefinition, value: any) => this.configManager.findBy<StateProbabilityDefinition>('StateProbability', field, value),
      findOne: (predicate: (item: StateProbabilityDefinition) => boolean) => this.configManager.findOne<StateProbabilityDefinition>('StateProbability', predicate),
      findOneBy: (field: keyof StateProbabilityDefinition, value: any) => this.configManager.findOneBy<StateProbabilityDefinition>('StateProbability', field, value)
    };

    this.systemOpen = {
      get: (id: number) => this.configManager.get<SystemOpenDefinition>('SystemOpen', id),
      getAll: () => this.configManager.getAll<SystemOpenDefinition>('SystemOpen'),
      getBatch: (ids: number[]) => this.configManager.getBatch<SystemOpenDefinition>('SystemOpen', ids),
      search: (keyword: string) => this.configManager.search<SystemOpenDefinition>('SystemOpen', keyword),
      filter: (predicate: (item: SystemOpenDefinition) => boolean) => this.configManager.filter<SystemOpenDefinition>('SystemOpen', predicate),
      findBy: (field: keyof SystemOpenDefinition, value: any) => this.configManager.findBy<SystemOpenDefinition>('SystemOpen', field, value),
      findOne: (predicate: (item: SystemOpenDefinition) => boolean) => this.configManager.findOne<SystemOpenDefinition>('SystemOpen', predicate),
      findOneBy: (field: keyof SystemOpenDefinition, value: any) => this.configManager.findOneBy<SystemOpenDefinition>('SystemOpen', field, value)
    };

    this.systemParam = {
      get: (id: number) => this.configManager.get<SystemParamDefinition>('SystemParam', id),
      getAll: () => this.configManager.getAll<SystemParamDefinition>('SystemParam'),
      getBatch: (ids: number[]) => this.configManager.getBatch<SystemParamDefinition>('SystemParam', ids),
      search: (keyword: string) => this.configManager.search<SystemParamDefinition>('SystemParam', keyword),
      filter: (predicate: (item: SystemParamDefinition) => boolean) => this.configManager.filter<SystemParamDefinition>('SystemParam', predicate),
      findBy: (field: keyof SystemParamDefinition, value: any) => this.configManager.findBy<SystemParamDefinition>('SystemParam', field, value),
      findOne: (predicate: (item: SystemParamDefinition) => boolean) => this.configManager.findOne<SystemParamDefinition>('SystemParam', predicate),
      findOneBy: (field: keyof SystemParamDefinition, value: any) => this.configManager.findOneBy<SystemParamDefinition>('SystemParam', field, value)
    };

    this.tactic = {
      get: (id: number) => this.configManager.get<TacticDefinition>('Tactic', id),
      getAll: () => this.configManager.getAll<TacticDefinition>('Tactic'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TacticDefinition>('Tactic', ids),
      search: (keyword: string) => this.configManager.search<TacticDefinition>('Tactic', keyword),
      filter: (predicate: (item: TacticDefinition) => boolean) => this.configManager.filter<TacticDefinition>('Tactic', predicate),
      findBy: (field: keyof TacticDefinition, value: any) => this.configManager.findBy<TacticDefinition>('Tactic', field, value),
      findOne: (predicate: (item: TacticDefinition) => boolean) => this.configManager.findOne<TacticDefinition>('Tactic', predicate),
      findOneBy: (field: keyof TacticDefinition, value: any) => this.configManager.findOneBy<TacticDefinition>('Tactic', field, value)
    };

    this.tacticReset = {
      get: (id: number) => this.configManager.get<TacticResetDefinition>('TacticReset', id),
      getAll: () => this.configManager.getAll<TacticResetDefinition>('TacticReset'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TacticResetDefinition>('TacticReset', ids),
      search: (keyword: string) => this.configManager.search<TacticResetDefinition>('TacticReset', keyword),
      filter: (predicate: (item: TacticResetDefinition) => boolean) => this.configManager.filter<TacticResetDefinition>('TacticReset', predicate),
      findBy: (field: keyof TacticResetDefinition, value: any) => this.configManager.findBy<TacticResetDefinition>('TacticReset', field, value),
      findOne: (predicate: (item: TacticResetDefinition) => boolean) => this.configManager.findOne<TacticResetDefinition>('TacticReset', predicate),
      findOneBy: (field: keyof TacticResetDefinition, value: any) => this.configManager.findOneBy<TacticResetDefinition>('TacticReset', field, value)
    };

    this.tacticRestrain = {
      get: (id: number) => this.configManager.get<TacticRestrainDefinition>('TacticRestrain', id),
      getAll: () => this.configManager.getAll<TacticRestrainDefinition>('TacticRestrain'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TacticRestrainDefinition>('TacticRestrain', ids),
      search: (keyword: string) => this.configManager.search<TacticRestrainDefinition>('TacticRestrain', keyword),
      filter: (predicate: (item: TacticRestrainDefinition) => boolean) => this.configManager.filter<TacticRestrainDefinition>('TacticRestrain', predicate),
      findBy: (field: keyof TacticRestrainDefinition, value: any) => this.configManager.findBy<TacticRestrainDefinition>('TacticRestrain', field, value),
      findOne: (predicate: (item: TacticRestrainDefinition) => boolean) => this.configManager.findOne<TacticRestrainDefinition>('TacticRestrain', predicate),
      findOneBy: (field: keyof TacticRestrainDefinition, value: any) => this.configManager.findOneBy<TacticRestrainDefinition>('TacticRestrain', field, value)
    };

    this.task = {
      get: (id: number) => this.configManager.get<TaskDefinition>('Task', id),
      getAll: () => this.configManager.getAll<TaskDefinition>('Task'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TaskDefinition>('Task', ids),
      search: (keyword: string) => this.configManager.search<TaskDefinition>('Task', keyword),
      filter: (predicate: (item: TaskDefinition) => boolean) => this.configManager.filter<TaskDefinition>('Task', predicate),
      findBy: (field: keyof TaskDefinition, value: any) => this.configManager.findBy<TaskDefinition>('Task', field, value),
      findOne: (predicate: (item: TaskDefinition) => boolean) => this.configManager.findOne<TaskDefinition>('Task', predicate),
      findOneBy: (field: keyof TaskDefinition, value: any) => this.configManager.findOneBy<TaskDefinition>('Task', field, value)
    };

    this.taskCost = {
      get: (id: number) => this.configManager.get<TaskCostDefinition>('TaskCost', id),
      getAll: () => this.configManager.getAll<TaskCostDefinition>('TaskCost'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TaskCostDefinition>('TaskCost', ids),
      search: (keyword: string) => this.configManager.search<TaskCostDefinition>('TaskCost', keyword),
      filter: (predicate: (item: TaskCostDefinition) => boolean) => this.configManager.filter<TaskCostDefinition>('TaskCost', predicate),
      findBy: (field: keyof TaskCostDefinition, value: any) => this.configManager.findBy<TaskCostDefinition>('TaskCost', field, value),
      findOne: (predicate: (item: TaskCostDefinition) => boolean) => this.configManager.findOne<TaskCostDefinition>('TaskCost', predicate),
      findOneBy: (field: keyof TaskCostDefinition, value: any) => this.configManager.findOneBy<TaskCostDefinition>('TaskCost', field, value)
    };

    this.taskMark = {
      get: (id: number) => this.configManager.get<TaskMarkDefinition>('TaskMark', id),
      getAll: () => this.configManager.getAll<TaskMarkDefinition>('TaskMark'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TaskMarkDefinition>('TaskMark', ids),
      search: (keyword: string) => this.configManager.search<TaskMarkDefinition>('TaskMark', keyword),
      filter: (predicate: (item: TaskMarkDefinition) => boolean) => this.configManager.filter<TaskMarkDefinition>('TaskMark', predicate),
      findBy: (field: keyof TaskMarkDefinition, value: any) => this.configManager.findBy<TaskMarkDefinition>('TaskMark', field, value),
      findOne: (predicate: (item: TaskMarkDefinition) => boolean) => this.configManager.findOne<TaskMarkDefinition>('TaskMark', predicate),
      findOneBy: (field: keyof TaskMarkDefinition, value: any) => this.configManager.findOneBy<TaskMarkDefinition>('TaskMark', field, value)
    };

    this.team = {
      get: (id: number) => this.configManager.get<TeamDefinition>('Team', id),
      getAll: () => this.configManager.getAll<TeamDefinition>('Team'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TeamDefinition>('Team', ids),
      search: (keyword: string) => this.configManager.search<TeamDefinition>('Team', keyword),
      filter: (predicate: (item: TeamDefinition) => boolean) => this.configManager.filter<TeamDefinition>('Team', predicate),
      findBy: (field: keyof TeamDefinition, value: any) => this.configManager.findBy<TeamDefinition>('Team', field, value),
      findOne: (predicate: (item: TeamDefinition) => boolean) => this.configManager.findOne<TeamDefinition>('Team', predicate),
      findOneBy: (field: keyof TeamDefinition, value: any) => this.configManager.findOneBy<TeamDefinition>('Team', field, value)
    };

    this.teamFormation = {
      get: (id: number) => this.configManager.get<TeamFormationDefinition>('TeamFormation', id),
      getAll: () => this.configManager.getAll<TeamFormationDefinition>('TeamFormation'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TeamFormationDefinition>('TeamFormation', ids),
      search: (keyword: string) => this.configManager.search<TeamFormationDefinition>('TeamFormation', keyword),
      filter: (predicate: (item: TeamFormationDefinition) => boolean) => this.configManager.filter<TeamFormationDefinition>('TeamFormation', predicate),
      findBy: (field: keyof TeamFormationDefinition, value: any) => this.configManager.findBy<TeamFormationDefinition>('TeamFormation', field, value),
      findOne: (predicate: (item: TeamFormationDefinition) => boolean) => this.configManager.findOne<TeamFormationDefinition>('TeamFormation', predicate),
      findOneBy: (field: keyof TeamFormationDefinition, value: any) => this.configManager.findOneBy<TeamFormationDefinition>('TeamFormation', field, value)
    };

    this.teamLogo = {
      get: (id: number) => this.configManager.get<TeamLogoDefinition>('TeamLogo', id),
      getAll: () => this.configManager.getAll<TeamLogoDefinition>('TeamLogo'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TeamLogoDefinition>('TeamLogo', ids),
      search: (keyword: string) => this.configManager.search<TeamLogoDefinition>('TeamLogo', keyword),
      filter: (predicate: (item: TeamLogoDefinition) => boolean) => this.configManager.filter<TeamLogoDefinition>('TeamLogo', predicate),
      findBy: (field: keyof TeamLogoDefinition, value: any) => this.configManager.findBy<TeamLogoDefinition>('TeamLogo', field, value),
      findOne: (predicate: (item: TeamLogoDefinition) => boolean) => this.configManager.findOne<TeamLogoDefinition>('TeamLogo', predicate),
      findOneBy: (field: keyof TeamLogoDefinition, value: any) => this.configManager.findOneBy<TeamLogoDefinition>('TeamLogo', field, value)
    };

    this.textTable = {
      get: (id: number) => this.configManager.get<TextTableDefinition>('TextTable', id),
      getAll: () => this.configManager.getAll<TextTableDefinition>('TextTable'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TextTableDefinition>('TextTable', ids),
      search: (keyword: string) => this.configManager.search<TextTableDefinition>('TextTable', keyword),
      filter: (predicate: (item: TextTableDefinition) => boolean) => this.configManager.filter<TextTableDefinition>('TextTable', predicate),
      findBy: (field: keyof TextTableDefinition, value: any) => this.configManager.findBy<TextTableDefinition>('TextTable', field, value),
      findOne: (predicate: (item: TextTableDefinition) => boolean) => this.configManager.findOne<TextTableDefinition>('TextTable', predicate),
      findOneBy: (field: keyof TextTableDefinition, value: any) => this.configManager.findOneBy<TextTableDefinition>('TextTable', field, value)
    };

    this.tourMatch = {
      get: (id: number) => this.configManager.get<TourMatchDefinition>('TourMatch', id),
      getAll: () => this.configManager.getAll<TourMatchDefinition>('TourMatch'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TourMatchDefinition>('TourMatch', ids),
      search: (keyword: string) => this.configManager.search<TourMatchDefinition>('TourMatch', keyword),
      filter: (predicate: (item: TourMatchDefinition) => boolean) => this.configManager.filter<TourMatchDefinition>('TourMatch', predicate),
      findBy: (field: keyof TourMatchDefinition, value: any) => this.configManager.findBy<TourMatchDefinition>('TourMatch', field, value),
      findOne: (predicate: (item: TourMatchDefinition) => boolean) => this.configManager.findOne<TourMatchDefinition>('TourMatch', predicate),
      findOneBy: (field: keyof TourMatchDefinition, value: any) => this.configManager.findOneBy<TourMatchDefinition>('TourMatch', field, value)
    };

    this.trainningCoach = {
      get: (id: number) => this.configManager.get<TrainningCoachDefinition>('TrainningCoach', id),
      getAll: () => this.configManager.getAll<TrainningCoachDefinition>('TrainningCoach'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TrainningCoachDefinition>('TrainningCoach', ids),
      search: (keyword: string) => this.configManager.search<TrainningCoachDefinition>('TrainningCoach', keyword),
      filter: (predicate: (item: TrainningCoachDefinition) => boolean) => this.configManager.filter<TrainningCoachDefinition>('TrainningCoach', predicate),
      findBy: (field: keyof TrainningCoachDefinition, value: any) => this.configManager.findBy<TrainningCoachDefinition>('TrainningCoach', field, value),
      findOne: (predicate: (item: TrainningCoachDefinition) => boolean) => this.configManager.findOne<TrainningCoachDefinition>('TrainningCoach', predicate),
      findOneBy: (field: keyof TrainningCoachDefinition, value: any) => this.configManager.findOneBy<TrainningCoachDefinition>('TrainningCoach', field, value)
    };

    this.trophy = {
      get: (id: number) => this.configManager.get<TrophyDefinition>('Trophy', id),
      getAll: () => this.configManager.getAll<TrophyDefinition>('Trophy'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TrophyDefinition>('Trophy', ids),
      search: (keyword: string) => this.configManager.search<TrophyDefinition>('Trophy', keyword),
      filter: (predicate: (item: TrophyDefinition) => boolean) => this.configManager.filter<TrophyDefinition>('Trophy', predicate),
      findBy: (field: keyof TrophyDefinition, value: any) => this.configManager.findBy<TrophyDefinition>('Trophy', field, value),
      findOne: (predicate: (item: TrophyDefinition) => boolean) => this.configManager.findOne<TrophyDefinition>('Trophy', predicate),
      findOneBy: (field: keyof TrophyDefinition, value: any) => this.configManager.findOneBy<TrophyDefinition>('Trophy', field, value)
    };

    this.trophyReward = {
      get: (id: number) => this.configManager.get<TrophyRewardDefinition>('TrophyReward', id),
      getAll: () => this.configManager.getAll<TrophyRewardDefinition>('TrophyReward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TrophyRewardDefinition>('TrophyReward', ids),
      search: (keyword: string) => this.configManager.search<TrophyRewardDefinition>('TrophyReward', keyword),
      filter: (predicate: (item: TrophyRewardDefinition) => boolean) => this.configManager.filter<TrophyRewardDefinition>('TrophyReward', predicate),
      findBy: (field: keyof TrophyRewardDefinition, value: any) => this.configManager.findBy<TrophyRewardDefinition>('TrophyReward', field, value),
      findOne: (predicate: (item: TrophyRewardDefinition) => boolean) => this.configManager.findOne<TrophyRewardDefinition>('TrophyReward', predicate),
      findOneBy: (field: keyof TrophyRewardDefinition, value: any) => this.configManager.findOneBy<TrophyRewardDefinition>('TrophyReward', field, value)
    };

    this.trophyTeam = {
      get: (id: number) => this.configManager.get<TrophyTeamDefinition>('TrophyTeam', id),
      getAll: () => this.configManager.getAll<TrophyTeamDefinition>('TrophyTeam'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TrophyTeamDefinition>('TrophyTeam', ids),
      search: (keyword: string) => this.configManager.search<TrophyTeamDefinition>('TrophyTeam', keyword),
      filter: (predicate: (item: TrophyTeamDefinition) => boolean) => this.configManager.filter<TrophyTeamDefinition>('TrophyTeam', predicate),
      findBy: (field: keyof TrophyTeamDefinition, value: any) => this.configManager.findBy<TrophyTeamDefinition>('TrophyTeam', field, value),
      findOne: (predicate: (item: TrophyTeamDefinition) => boolean) => this.configManager.findOne<TrophyTeamDefinition>('TrophyTeam', predicate),
      findOneBy: (field: keyof TrophyTeamDefinition, value: any) => this.configManager.findOneBy<TrophyTeamDefinition>('TrophyTeam', field, value)
    };

    this.turntableControl = {
      get: (id: number) => this.configManager.get<TurntableControlDefinition>('TurntableControl', id),
      getAll: () => this.configManager.getAll<TurntableControlDefinition>('TurntableControl'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TurntableControlDefinition>('TurntableControl', ids),
      search: (keyword: string) => this.configManager.search<TurntableControlDefinition>('TurntableControl', keyword),
      filter: (predicate: (item: TurntableControlDefinition) => boolean) => this.configManager.filter<TurntableControlDefinition>('TurntableControl', predicate),
      findBy: (field: keyof TurntableControlDefinition, value: any) => this.configManager.findBy<TurntableControlDefinition>('TurntableControl', field, value),
      findOne: (predicate: (item: TurntableControlDefinition) => boolean) => this.configManager.findOne<TurntableControlDefinition>('TurntableControl', predicate),
      findOneBy: (field: keyof TurntableControlDefinition, value: any) => this.configManager.findOneBy<TurntableControlDefinition>('TurntableControl', field, value)
    };

    this.turntableReward = {
      get: (id: number) => this.configManager.get<TurntableRewardDefinition>('TurntableReward', id),
      getAll: () => this.configManager.getAll<TurntableRewardDefinition>('TurntableReward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<TurntableRewardDefinition>('TurntableReward', ids),
      search: (keyword: string) => this.configManager.search<TurntableRewardDefinition>('TurntableReward', keyword),
      filter: (predicate: (item: TurntableRewardDefinition) => boolean) => this.configManager.filter<TurntableRewardDefinition>('TurntableReward', predicate),
      findBy: (field: keyof TurntableRewardDefinition, value: any) => this.configManager.findBy<TurntableRewardDefinition>('TurntableReward', field, value),
      findOne: (predicate: (item: TurntableRewardDefinition) => boolean) => this.configManager.findOne<TurntableRewardDefinition>('TurntableReward', predicate),
      findOneBy: (field: keyof TurntableRewardDefinition, value: any) => this.configManager.findOneBy<TurntableRewardDefinition>('TurntableReward', field, value)
    };

    this.ultimateMatchAward = {
      get: (id: number) => this.configManager.get<UltimateMatchAwardDefinition>('UltimateMatchAward', id),
      getAll: () => this.configManager.getAll<UltimateMatchAwardDefinition>('UltimateMatchAward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<UltimateMatchAwardDefinition>('UltimateMatchAward', ids),
      search: (keyword: string) => this.configManager.search<UltimateMatchAwardDefinition>('UltimateMatchAward', keyword),
      filter: (predicate: (item: UltimateMatchAwardDefinition) => boolean) => this.configManager.filter<UltimateMatchAwardDefinition>('UltimateMatchAward', predicate),
      findBy: (field: keyof UltimateMatchAwardDefinition, value: any) => this.configManager.findBy<UltimateMatchAwardDefinition>('UltimateMatchAward', field, value),
      findOne: (predicate: (item: UltimateMatchAwardDefinition) => boolean) => this.configManager.findOne<UltimateMatchAwardDefinition>('UltimateMatchAward', predicate),
      findOneBy: (field: keyof UltimateMatchAwardDefinition, value: any) => this.configManager.findOneBy<UltimateMatchAwardDefinition>('UltimateMatchAward', field, value)
    };

    this.ultimateMatchTime = {
      get: (id: number) => this.configManager.get<UltimateMatchTimeDefinition>('UltimateMatchTime', id),
      getAll: () => this.configManager.getAll<UltimateMatchTimeDefinition>('UltimateMatchTime'),
      getBatch: (ids: number[]) => this.configManager.getBatch<UltimateMatchTimeDefinition>('UltimateMatchTime', ids),
      search: (keyword: string) => this.configManager.search<UltimateMatchTimeDefinition>('UltimateMatchTime', keyword),
      filter: (predicate: (item: UltimateMatchTimeDefinition) => boolean) => this.configManager.filter<UltimateMatchTimeDefinition>('UltimateMatchTime', predicate),
      findBy: (field: keyof UltimateMatchTimeDefinition, value: any) => this.configManager.findBy<UltimateMatchTimeDefinition>('UltimateMatchTime', field, value),
      findOne: (predicate: (item: UltimateMatchTimeDefinition) => boolean) => this.configManager.findOne<UltimateMatchTimeDefinition>('UltimateMatchTime', predicate),
      findOneBy: (field: keyof UltimateMatchTimeDefinition, value: any) => this.configManager.findOneBy<UltimateMatchTimeDefinition>('UltimateMatchTime', field, value)
    };

    this.upexp = {
      get: (id: number) => this.configManager.get<UpexpDefinition>('Upexp', id),
      getAll: () => this.configManager.getAll<UpexpDefinition>('Upexp'),
      getBatch: (ids: number[]) => this.configManager.getBatch<UpexpDefinition>('Upexp', ids),
      search: (keyword: string) => this.configManager.search<UpexpDefinition>('Upexp', keyword),
      filter: (predicate: (item: UpexpDefinition) => boolean) => this.configManager.filter<UpexpDefinition>('Upexp', predicate),
      findBy: (field: keyof UpexpDefinition, value: any) => this.configManager.findBy<UpexpDefinition>('Upexp', field, value),
      findOne: (predicate: (item: UpexpDefinition) => boolean) => this.configManager.findOne<UpexpDefinition>('Upexp', predicate),
      findOneBy: (field: keyof UpexpDefinition, value: any) => this.configManager.findOneBy<UpexpDefinition>('Upexp', field, value)
    };

    this.versionBulletin = {
      get: (id: number) => this.configManager.get<VersionBulletinDefinition>('VersionBulletin', id),
      getAll: () => this.configManager.getAll<VersionBulletinDefinition>('VersionBulletin'),
      getBatch: (ids: number[]) => this.configManager.getBatch<VersionBulletinDefinition>('VersionBulletin', ids),
      search: (keyword: string) => this.configManager.search<VersionBulletinDefinition>('VersionBulletin', keyword),
      filter: (predicate: (item: VersionBulletinDefinition) => boolean) => this.configManager.filter<VersionBulletinDefinition>('VersionBulletin', predicate),
      findBy: (field: keyof VersionBulletinDefinition, value: any) => this.configManager.findBy<VersionBulletinDefinition>('VersionBulletin', field, value),
      findOne: (predicate: (item: VersionBulletinDefinition) => boolean) => this.configManager.findOne<VersionBulletinDefinition>('VersionBulletin', predicate),
      findOneBy: (field: keyof VersionBulletinDefinition, value: any) => this.configManager.findOneBy<VersionBulletinDefinition>('VersionBulletin', field, value)
    };

    this.vip = {
      get: (id: number) => this.configManager.get<VipDefinition>('Vip', id),
      getAll: () => this.configManager.getAll<VipDefinition>('Vip'),
      getBatch: (ids: number[]) => this.configManager.getBatch<VipDefinition>('Vip', ids),
      search: (keyword: string) => this.configManager.search<VipDefinition>('Vip', keyword),
      filter: (predicate: (item: VipDefinition) => boolean) => this.configManager.filter<VipDefinition>('Vip', predicate),
      findBy: (field: keyof VipDefinition, value: any) => this.configManager.findBy<VipDefinition>('Vip', field, value),
      findOne: (predicate: (item: VipDefinition) => boolean) => this.configManager.findOne<VipDefinition>('Vip', predicate),
      findOneBy: (field: keyof VipDefinition, value: any) => this.configManager.findOneBy<VipDefinition>('Vip', field, value)
    };

    this.worldBoss = {
      get: (id: number) => this.configManager.get<WorldBossDefinition>('WorldBoss', id),
      getAll: () => this.configManager.getAll<WorldBossDefinition>('WorldBoss'),
      getBatch: (ids: number[]) => this.configManager.getBatch<WorldBossDefinition>('WorldBoss', ids),
      search: (keyword: string) => this.configManager.search<WorldBossDefinition>('WorldBoss', keyword),
      filter: (predicate: (item: WorldBossDefinition) => boolean) => this.configManager.filter<WorldBossDefinition>('WorldBoss', predicate),
      findBy: (field: keyof WorldBossDefinition, value: any) => this.configManager.findBy<WorldBossDefinition>('WorldBoss', field, value),
      findOne: (predicate: (item: WorldBossDefinition) => boolean) => this.configManager.findOne<WorldBossDefinition>('WorldBoss', predicate),
      findOneBy: (field: keyof WorldBossDefinition, value: any) => this.configManager.findOneBy<WorldBossDefinition>('WorldBoss', field, value)
    };

    this.worldBossAward = {
      get: (id: number) => this.configManager.get<WorldBossAwardDefinition>('WorldBossAward', id),
      getAll: () => this.configManager.getAll<WorldBossAwardDefinition>('WorldBossAward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<WorldBossAwardDefinition>('WorldBossAward', ids),
      search: (keyword: string) => this.configManager.search<WorldBossAwardDefinition>('WorldBossAward', keyword),
      filter: (predicate: (item: WorldBossAwardDefinition) => boolean) => this.configManager.filter<WorldBossAwardDefinition>('WorldBossAward', predicate),
      findBy: (field: keyof WorldBossAwardDefinition, value: any) => this.configManager.findBy<WorldBossAwardDefinition>('WorldBossAward', field, value),
      findOne: (predicate: (item: WorldBossAwardDefinition) => boolean) => this.configManager.findOne<WorldBossAwardDefinition>('WorldBossAward', predicate),
      findOneBy: (field: keyof WorldBossAwardDefinition, value: any) => this.configManager.findOneBy<WorldBossAwardDefinition>('WorldBossAward', field, value)
    };

    this.worldBossValue = {
      get: (id: number) => this.configManager.get<WorldBossValueDefinition>('WorldBossValue', id),
      getAll: () => this.configManager.getAll<WorldBossValueDefinition>('WorldBossValue'),
      getBatch: (ids: number[]) => this.configManager.getBatch<WorldBossValueDefinition>('WorldBossValue', ids),
      search: (keyword: string) => this.configManager.search<WorldBossValueDefinition>('WorldBossValue', keyword),
      filter: (predicate: (item: WorldBossValueDefinition) => boolean) => this.configManager.filter<WorldBossValueDefinition>('WorldBossValue', predicate),
      findBy: (field: keyof WorldBossValueDefinition, value: any) => this.configManager.findBy<WorldBossValueDefinition>('WorldBossValue', field, value),
      findOne: (predicate: (item: WorldBossValueDefinition) => boolean) => this.configManager.findOne<WorldBossValueDefinition>('WorldBossValue', predicate),
      findOneBy: (field: keyof WorldBossValueDefinition, value: any) => this.configManager.findOneBy<WorldBossValueDefinition>('WorldBossValue', field, value)
    };

    this.worldCup = {
      get: (id: number) => this.configManager.get<WorldCupDefinition>('WorldCup', id),
      getAll: () => this.configManager.getAll<WorldCupDefinition>('WorldCup'),
      getBatch: (ids: number[]) => this.configManager.getBatch<WorldCupDefinition>('WorldCup', ids),
      search: (keyword: string) => this.configManager.search<WorldCupDefinition>('WorldCup', keyword),
      filter: (predicate: (item: WorldCupDefinition) => boolean) => this.configManager.filter<WorldCupDefinition>('WorldCup', predicate),
      findBy: (field: keyof WorldCupDefinition, value: any) => this.configManager.findBy<WorldCupDefinition>('WorldCup', field, value),
      findOne: (predicate: (item: WorldCupDefinition) => boolean) => this.configManager.findOne<WorldCupDefinition>('WorldCup', predicate),
      findOneBy: (field: keyof WorldCupDefinition, value: any) => this.configManager.findOneBy<WorldCupDefinition>('WorldCup', field, value)
    };

    this.worldCupPlayer = {
      get: (id: number) => this.configManager.get<WorldCupPlayerDefinition>('WorldCupPlayer', id),
      getAll: () => this.configManager.getAll<WorldCupPlayerDefinition>('WorldCupPlayer'),
      getBatch: (ids: number[]) => this.configManager.getBatch<WorldCupPlayerDefinition>('WorldCupPlayer', ids),
      search: (keyword: string) => this.configManager.search<WorldCupPlayerDefinition>('WorldCupPlayer', keyword),
      filter: (predicate: (item: WorldCupPlayerDefinition) => boolean) => this.configManager.filter<WorldCupPlayerDefinition>('WorldCupPlayer', predicate),
      findBy: (field: keyof WorldCupPlayerDefinition, value: any) => this.configManager.findBy<WorldCupPlayerDefinition>('WorldCupPlayer', field, value),
      findOne: (predicate: (item: WorldCupPlayerDefinition) => boolean) => this.configManager.findOne<WorldCupPlayerDefinition>('WorldCupPlayer', predicate),
      findOneBy: (field: keyof WorldCupPlayerDefinition, value: any) => this.configManager.findOneBy<WorldCupPlayerDefinition>('WorldCupPlayer', field, value)
    };

    this.worldCupReward = {
      get: (id: number) => this.configManager.get<WorldCupRewardDefinition>('WorldCupReward', id),
      getAll: () => this.configManager.getAll<WorldCupRewardDefinition>('WorldCupReward'),
      getBatch: (ids: number[]) => this.configManager.getBatch<WorldCupRewardDefinition>('WorldCupReward', ids),
      search: (keyword: string) => this.configManager.search<WorldCupRewardDefinition>('WorldCupReward', keyword),
      filter: (predicate: (item: WorldCupRewardDefinition) => boolean) => this.configManager.filter<WorldCupRewardDefinition>('WorldCupReward', predicate),
      findBy: (field: keyof WorldCupRewardDefinition, value: any) => this.configManager.findBy<WorldCupRewardDefinition>('WorldCupReward', field, value),
      findOne: (predicate: (item: WorldCupRewardDefinition) => boolean) => this.configManager.findOne<WorldCupRewardDefinition>('WorldCupReward', predicate),
      findOneBy: (field: keyof WorldCupRewardDefinition, value: any) => this.configManager.findOneBy<WorldCupRewardDefinition>('WorldCupReward', field, value)
    };

    this.worldCupStrength = {
      get: (id: number) => this.configManager.get<WorldCupStrengthDefinition>('WorldCupStrength', id),
      getAll: () => this.configManager.getAll<WorldCupStrengthDefinition>('WorldCupStrength'),
      getBatch: (ids: number[]) => this.configManager.getBatch<WorldCupStrengthDefinition>('WorldCupStrength', ids),
      search: (keyword: string) => this.configManager.search<WorldCupStrengthDefinition>('WorldCupStrength', keyword),
      filter: (predicate: (item: WorldCupStrengthDefinition) => boolean) => this.configManager.filter<WorldCupStrengthDefinition>('WorldCupStrength', predicate),
      findBy: (field: keyof WorldCupStrengthDefinition, value: any) => this.configManager.findBy<WorldCupStrengthDefinition>('WorldCupStrength', field, value),
      findOne: (predicate: (item: WorldCupStrengthDefinition) => boolean) => this.configManager.findOne<WorldCupStrengthDefinition>('WorldCupStrength', predicate),
      findOneBy: (field: keyof WorldCupStrengthDefinition, value: any) => this.configManager.findOneBy<WorldCupStrengthDefinition>('WorldCupStrength', field, value)
    };

    this.worldCupTeam = {
      get: (id: number) => this.configManager.get<WorldCupTeamDefinition>('WorldCupTeam', id),
      getAll: () => this.configManager.getAll<WorldCupTeamDefinition>('WorldCupTeam'),
      getBatch: (ids: number[]) => this.configManager.getBatch<WorldCupTeamDefinition>('WorldCupTeam', ids),
      search: (keyword: string) => this.configManager.search<WorldCupTeamDefinition>('WorldCupTeam', keyword),
      filter: (predicate: (item: WorldCupTeamDefinition) => boolean) => this.configManager.filter<WorldCupTeamDefinition>('WorldCupTeam', predicate),
      findBy: (field: keyof WorldCupTeamDefinition, value: any) => this.configManager.findBy<WorldCupTeamDefinition>('WorldCupTeam', field, value),
      findOne: (predicate: (item: WorldCupTeamDefinition) => boolean) => this.configManager.findOne<WorldCupTeamDefinition>('WorldCupTeam', predicate),
      findOneBy: (field: keyof WorldCupTeamDefinition, value: any) => this.configManager.findOneBy<WorldCupTeamDefinition>('WorldCupTeam', field, value)
    };
  }
}
