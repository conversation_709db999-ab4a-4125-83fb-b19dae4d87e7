// Auto-generated from GulfCupTeam.json
// Generated at: 2025-07-20T12:56:03.634Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface GulfCupTeamDefinition {
  defenseId: number; // 唯一标识符 例: 1101, 1201 (原: DefenseID)
  offensiveId: number; // 唯一标识符 例: 101, 201 (原: OffensiveID)
  iconId: number; // 唯一标识符 例: 11190, 11191 (原: IconID)
  id: number; // 唯一标识符 例: 101, 102
  name: string; // 名称 例: 沙特阿拉伯, 阿联酋
  formation: number; // 数值 例: 442401 (原: Formation)
  num1: number; // 数值 例: 1 (原: Num1)
  num2: number; // 数值 例: 1 (原: Num2)
  num3: number; // 数值 例: 0 (原: Num3)
  probability1: number; // 数值 例: 1000 (原: Probability1)
  probability2: number; // 数值 例: 1000 (原: Probability2)
  probability3: number; // 数值 例: 0 (原: Probability3)
  reward1: number; // 奖励 例: 90079 (原: Reward1)
  reward2: number; // 奖励 例: 90080 (原: Reward2)
  reward3: number; // 奖励 例: 0 (原: Reward3)
  rewardType1: number; // 类型 例: 0 (原: RewardType1)
  rewardType2: number; // 类型 例: 0 (原: RewardType2)
  rewardType3: number; // 类型 例: 0 (原: RewardType3)
}

// 字段映射：新字段名 -> 原始字段名
export const GulfCupTeamFieldMappings = {
  defenseId: 'DefenseID',
  offensiveId: 'OffensiveID',
  iconId: 'IconID',
  formation: 'Formation',
  num1: 'Num1',
  num2: 'Num2',
  num3: 'Num3',
  probability1: 'Probability1',
  probability2: 'Probability2',
  probability3: 'Probability3',
  reward1: 'Reward1',
  reward2: 'Reward2',
  reward3: 'Reward3',
  rewardType1: 'RewardType1',
  rewardType2: 'RewardType2',
  rewardType3: 'RewardType3',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const GulfCupTeamReverseFieldMappings = {
  'DefenseID': 'defenseId',
  'OffensiveID': 'offensiveId',
  'IconID': 'iconId',
  'Formation': 'formation',
  'Num1': 'num1',
  'Num2': 'num2',
  'Num3': 'num3',
  'Probability1': 'probability1',
  'Probability2': 'probability2',
  'Probability3': 'probability3',
  'Reward1': 'reward1',
  'Reward2': 'reward2',
  'Reward3': 'reward3',
  'RewardType1': 'rewardType1',
  'RewardType2': 'rewardType2',
  'RewardType3': 'rewardType3',
} as const;

export const GulfCupTeamMeta = {
  tableName: 'GulfCupTeam',
  dataFileName: 'GulfCupTeam.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 18,
  requiredFields: ['defenseId', 'offensiveId', 'iconId', 'id', 'name', 'formation', 'num1', 'num2', 'num3', 'probability1', 'probability2', 'probability3', 'reward1', 'reward2', 'reward3', 'rewardType1', 'rewardType2', 'rewardType3'],
  optionalFields: [],
  renamedFieldsCount: 16,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: GulfCupTeamFieldMappings,
  reverseFieldMappings: GulfCupTeamReverseFieldMappings,
} as const;

export type GulfCupTeamConfigMeta = typeof GulfCupTeamMeta;
export type GulfCupTeamFieldMapping = typeof GulfCupTeamFieldMappings;
export type GulfCupTeamReverseFieldMapping = typeof GulfCupTeamReverseFieldMappings;
