// Auto-generated from FootballerSkill.json
// Generated at: 2025-07-20T12:56:03.472Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据
// 表名已规范化: FootballerSkill → HeroSkill

export interface HeroSkillDefinition {
  id: number; // 唯一标识符 例: 10001, 10002
  skillName: string; // 技能名称 例: 球场旋风, 弹簧人 (原: SkillName)
  abilityValue: number; // 数值 例: 0, 280 (原: AbilityValue)
  duration: number; // 持续时间 例: 0, 30 (原: Duration)
  icon: number; // 图标 例: 50322, 50323 (原: Icon)
  iconBg: number; // 图标 例: 50021, 50022 (原: IconBg)
  pattern: number; // 数值 例: 0, 1 (原: Pattern)
  potentialValue: number; // 数值 例: 90, 70 (原: PotentialValue)
  probability: number; // 数值 例: 0, 30 (原: Probability)
  skillCaption: string; // 技能 例: 速度属性增加#0#%, 弹跳属性增加#0#% (原: SkillCaption)
  skillRank: string; // 技能 例: S, A (原: SkillRank)
  starValue: number; // 数值 例: 9, 6 (原: StarValue)
  typeA: number; // 类型 例: 11, 12 (原: TypeA)
  typeAOpportunity: number; // 类型 例: 1, 21 (原: TypeAOpportunity)
  typeAValue: number; // 类型 例: 15, 12 (原: TypeAValue)
  typeB: number; // 类型 例: 0, 13 (原: TypeB)
  typeBOpportunity: number; // 类型 例: 0, 1 (原: TypeBOpportunity)
  typeBValue: number; // 类型 例: 0, 10 (原: TypeBValue)
  typeC: number; // 类型 例: 0, 16 (原: TypeC)
  typeCOpportunity: number; // 类型 例: 0, 2 (原: TypeCOpportunity)
  typeCValue: number; // 类型 例: 0, 11 (原: TypeCValue)
  typeD: number; // 类型 例: 0, 22 (原: TypeD)
  typeDOpportunity: number; // 类型 例: 0, 1 (原: TypeDOpportunity)
  typeDValue: number; // 类型 例: 0, 5 (原: TypeDValue)
}

// 字段映射：新字段名 -> 原始字段名
export const HeroSkillFieldMappings = {
  skillName: 'SkillName',
  abilityValue: 'AbilityValue',
  duration: 'Duration',
  icon: 'Icon',
  iconBg: 'IconBg',
  pattern: 'Pattern',
  potentialValue: 'PotentialValue',
  probability: 'Probability',
  skillCaption: 'SkillCaption',
  skillRank: 'SkillRank',
  starValue: 'StarValue',
  typeA: 'TypeA',
  typeAOpportunity: 'TypeAOpportunity',
  typeAValue: 'TypeAValue',
  typeB: 'TypeB',
  typeBOpportunity: 'TypeBOpportunity',
  typeBValue: 'TypeBValue',
  typeC: 'TypeC',
  typeCOpportunity: 'TypeCOpportunity',
  typeCValue: 'TypeCValue',
  typeD: 'TypeD',
  typeDOpportunity: 'TypeDOpportunity',
  typeDValue: 'TypeDValue',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const HeroSkillReverseFieldMappings = {
  'SkillName': 'skillName',
  'AbilityValue': 'abilityValue',
  'Duration': 'duration',
  'Icon': 'icon',
  'IconBg': 'iconBg',
  'Pattern': 'pattern',
  'PotentialValue': 'potentialValue',
  'Probability': 'probability',
  'SkillCaption': 'skillCaption',
  'SkillRank': 'skillRank',
  'StarValue': 'starValue',
  'TypeA': 'typeA',
  'TypeAOpportunity': 'typeAOpportunity',
  'TypeAValue': 'typeAValue',
  'TypeB': 'typeB',
  'TypeBOpportunity': 'typeBOpportunity',
  'TypeBValue': 'typeBValue',
  'TypeC': 'typeC',
  'TypeCOpportunity': 'typeCOpportunity',
  'TypeCValue': 'typeCValue',
  'TypeD': 'typeD',
  'TypeDOpportunity': 'typeDOpportunity',
  'TypeDValue': 'typeDValue',
} as const;

export const HeroSkillMeta = {
  tableName: 'HeroSkill',
  originalTableName: 'FootballerSkill',
  dataFileName: 'FootballerSkill.json',
  primaryKey: 'id',
  searchFields: ['skillName'],
  fieldsCount: 24,
  requiredFields: ['id', 'skillName', 'abilityValue', 'duration', 'icon', 'iconBg', 'pattern', 'potentialValue', 'probability', 'skillCaption', 'skillRank', 'starValue', 'typeA', 'typeAOpportunity', 'typeAValue', 'typeB', 'typeBOpportunity', 'typeBValue', 'typeC', 'typeCOpportunity', 'typeCValue', 'typeD', 'typeDOpportunity', 'typeDValue'],
  optionalFields: [],
  renamedFieldsCount: 23,
  hasFieldMappings: true,
  isTableRenamed: true,
  fieldMappings: HeroSkillFieldMappings,
  reverseFieldMappings: HeroSkillReverseFieldMappings,
} as const;

export type HeroSkillConfigMeta = typeof HeroSkillMeta;
export type HeroSkillFieldMapping = typeof HeroSkillFieldMappings;
export type HeroSkillReverseFieldMapping = typeof HeroSkillReverseFieldMappings;
