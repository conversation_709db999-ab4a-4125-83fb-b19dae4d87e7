// Auto-generated from FootballerTrain.json
// Generated at: 2025-07-20T12:56:03.500Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据
// 表名已规范化: FootballerTrain → HeroTrain

export interface HeroTrainDefinition {
  id: number; // 唯一标识符 例: 1, 2
  playerType: string; // 类型 例: GK, DC (原: PlayerType)
  propertyRange: number[]; // 数组 例: 1,2,3,6,7,9,10,11,13,14,15,..., 1,2,3,7,11,16,17,18,19,0,0,... (原: PropertyRange)
  random: number[]; // 数组 例: 1,50, 15,50 (原: Random)
  type: number; // 类型 例: 1, 2 (原: Type)
}

// 字段映射：新字段名 -> 原始字段名
export const HeroTrainFieldMappings = {
  playerType: 'PlayerType',
  propertyRange: 'PropertyRange',
  random: 'Random',
  type: 'Type',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const HeroTrainReverseFieldMappings = {
  'PlayerType': 'playerType',
  'PropertyRange': 'propertyRange',
  'Random': 'random',
  'Type': 'type',
} as const;

export const HeroTrainMeta = {
  tableName: 'HeroTrain',
  originalTableName: 'FootballerTrain',
  dataFileName: 'FootballerTrain.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 5,
  requiredFields: ['id', 'playerType', 'propertyRange', 'random', 'type'],
  optionalFields: [],
  renamedFieldsCount: 4,
  hasFieldMappings: true,
  isTableRenamed: true,
  fieldMappings: HeroTrainFieldMappings,
  reverseFieldMappings: HeroTrainReverseFieldMappings,
} as const;

export type HeroTrainConfigMeta = typeof HeroTrainMeta;
export type HeroTrainFieldMapping = typeof HeroTrainFieldMappings;
export type HeroTrainReverseFieldMapping = typeof HeroTrainReverseFieldMappings;
