// Auto-generated from TacticReset.json
// Generated at: 2025-07-20T12:56:06.145Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface TacticResetDefinition {
  resetItemId: number; // 唯一标识符 例: 0, 11 (原: ResetItemID)
  id: number; // 唯一标识符 例: 1, 2
  resetCoinNumber: number; // 金币 例: 0, 10 (原: ResetCoinNumber)
}

// 字段映射：新字段名 -> 原始字段名
export const TacticResetFieldMappings = {
  resetItemId: 'ResetItemID',
  resetCoinNumber: 'ResetCoinNumber',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const TacticResetReverseFieldMappings = {
  'ResetItemID': 'resetItemId',
  'ResetCoinNumber': 'resetCoinNumber',
} as const;

export const TacticResetMeta = {
  tableName: 'TacticReset',
  dataFileName: 'TacticReset.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 3,
  requiredFields: ['resetItemId', 'id', 'resetCoinNumber'],
  optionalFields: [],
  renamedFieldsCount: 2,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: TacticResetFieldMappings,
  reverseFieldMappings: TacticResetReverseFieldMappings,
} as const;

export type TacticResetConfigMeta = typeof TacticResetMeta;
export type TacticResetFieldMapping = typeof TacticResetFieldMappings;
export type TacticResetReverseFieldMapping = typeof TacticResetReverseFieldMappings;
