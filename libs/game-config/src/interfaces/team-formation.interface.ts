// Auto-generated from TeamFormation.json
// Generated at: 2025-07-20T12:56:06.877Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface TeamFormationDefinition {
  itemId: number; // 物品ID 例: 10500 (原: ItemID)
  picId: number; // 唯一标识符 例: 0 (原: PicID)
  id: number; // 唯一标识符 例: 442101, 442102
  name: string; // 名称 例: 442A, 442B
  attackArg1: number; // 攻击力 例: 100 (原: AttackArg1)
  attackArg10: number; // 攻击力 例: 800, 810 (原: AttackArg10)
  attackArg11: number; // 攻击力 例: 700, 710 (原: AttackArg11)
  attackArg12: number; // 攻击力 例: 700, 710 (原: AttackArg12)
  attackArg2: number; // 攻击力 例: 300 (原: AttackArg2)
  attackArg3: number; // 攻击力 例: 400 (原: AttackArg3)
  attackArg4: number; // 攻击力 例: 400 (原: AttackArg4)
  attackArg5: number; // 攻击力 例: 450 (原: AttackArg5)
  attackArg6: number; // 攻击力 例: 500, 505 (原: AttackArg6)
  attackArg7: number; // 攻击力 例: 550, 560 (原: AttackArg7)
  attackArg8: number; // 攻击力 例: 550, 560 (原: AttackArg8)
  attackArg9: number; // 攻击力 例: 600, 610 (原: AttackArg9)
  defendArg1: number; // 数值 例: 900, 910 (原: DefendArg1)
  defendArg10: number; // 数值 例: 200 (原: DefendArg10)
  defendArg11: number; // 数值 例: 300 (原: DefendArg11)
  defendArg12: number; // 数值 例: 300 (原: DefendArg12)
  defendArg2: number; // 数值 例: 700, 710 (原: DefendArg2)
  defendArg3: number; // 数值 例: 600, 610 (原: DefendArg3)
  defendArg4: number; // 数值 例: 600, 610 (原: DefendArg4)
  defendArg5: number; // 数值 例: 550, 560 (原: DefendArg5)
  defendArg6: number; // 数值 例: 500, 505 (原: DefendArg6)
  defendArg7: number; // 数值 例: 450 (原: DefendArg7)
  defendArg8: number; // 数值 例: 450 (原: DefendArg8)
  defendArg9: number; // 数值 例: 400 (原: DefendArg9)
  itemNumber: number; // 数值 例: 10, 20 (原: ItemNumber)
  level: number; // 等级 例: 1, 2 (原: Level)
  needLevel: number; // 等级 例: 0, 10 (原: NeedLevel)
  needPF: number; // 数值 例: 0, 600 (原: NeedPF)
  num1: number; // 数值 例: 1 (原: Num1)
  num10: number; // 数值 例: 2, 1 (原: Num10)
  num11: number; // 数值 例: 0, 1 (原: Num11)
  num12: number; // 数值 例: 0, 1 (原: Num12)
  num2: number; // 数值 例: 2, 3 (原: Num2)
  num3: number; // 数值 例: 1, 0 (原: Num3)
  num4: number; // 数值 例: 1, 0 (原: Num4)
  num5: number; // 数值 例: 0, 1 (原: Num5)
  num6: number; // 数值 例: 2, 0 (原: Num6)
  num7: number; // 数值 例: 1, 0 (原: Num7)
  num8: number; // 数值 例: 1, 0 (原: Num8)
  num9: number; // 数值 例: 0, 1 (原: Num9)
  position1: string; // 位置 例: 门将 (原: Position1)
  position10: string; // 位置 例: 中锋 (原: Position10)
  position11: string; // 位置 例: 左边锋 (原: Position11)
  position12: string; // 位置 例: 右边锋 (原: Position12)
  position2: string; // 位置 例: 中后卫 (原: Position2)
  position3: string; // 位置 例: 左后卫 (原: Position3)
  position4: string; // 位置 例: 右后卫 (原: Position4)
  position5: string; // 位置 例: 后腰 (原: Position5)
  position6: string; // 位置 例: 中前卫 (原: Position6)
  position7: string; // 位置 例: 左前卫 (原: Position7)
  position8: string; // 位置 例: 右前卫 (原: Position8)
  position9: string; // 位置 例: 前腰 (原: Position9)
  tactic1: number; // 数值 例: 101, 701 (原: Tactic1)
  tactic2: number; // 数值 例: 301, 801 (原: Tactic2)
  tactic3: number; // 数值 例: 501, 401 (原: Tactic3)
}

// 字段映射：新字段名 -> 原始字段名
export const TeamFormationFieldMappings = {
  itemId: 'ItemID',
  picId: 'PicID',
  attackArg1: 'AttackArg1',
  attackArg10: 'AttackArg10',
  attackArg11: 'AttackArg11',
  attackArg12: 'AttackArg12',
  attackArg2: 'AttackArg2',
  attackArg3: 'AttackArg3',
  attackArg4: 'AttackArg4',
  attackArg5: 'AttackArg5',
  attackArg6: 'AttackArg6',
  attackArg7: 'AttackArg7',
  attackArg8: 'AttackArg8',
  attackArg9: 'AttackArg9',
  defendArg1: 'DefendArg1',
  defendArg10: 'DefendArg10',
  defendArg11: 'DefendArg11',
  defendArg12: 'DefendArg12',
  defendArg2: 'DefendArg2',
  defendArg3: 'DefendArg3',
  defendArg4: 'DefendArg4',
  defendArg5: 'DefendArg5',
  defendArg6: 'DefendArg6',
  defendArg7: 'DefendArg7',
  defendArg8: 'DefendArg8',
  defendArg9: 'DefendArg9',
  itemNumber: 'ItemNumber',
  level: 'Level',
  needLevel: 'NeedLevel',
  needPF: 'NeedPF',
  num1: 'Num1',
  num10: 'Num10',
  num11: 'Num11',
  num12: 'Num12',
  num2: 'Num2',
  num3: 'Num3',
  num4: 'Num4',
  num5: 'Num5',
  num6: 'Num6',
  num7: 'Num7',
  num8: 'Num8',
  num9: 'Num9',
  position1: 'Position1',
  position10: 'Position10',
  position11: 'Position11',
  position12: 'Position12',
  position2: 'Position2',
  position3: 'Position3',
  position4: 'Position4',
  position5: 'Position5',
  position6: 'Position6',
  position7: 'Position7',
  position8: 'Position8',
  position9: 'Position9',
  tactic1: 'Tactic1',
  tactic2: 'Tactic2',
  tactic3: 'Tactic3',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const TeamFormationReverseFieldMappings = {
  'ItemID': 'itemId',
  'PicID': 'picId',
  'AttackArg1': 'attackArg1',
  'AttackArg10': 'attackArg10',
  'AttackArg11': 'attackArg11',
  'AttackArg12': 'attackArg12',
  'AttackArg2': 'attackArg2',
  'AttackArg3': 'attackArg3',
  'AttackArg4': 'attackArg4',
  'AttackArg5': 'attackArg5',
  'AttackArg6': 'attackArg6',
  'AttackArg7': 'attackArg7',
  'AttackArg8': 'attackArg8',
  'AttackArg9': 'attackArg9',
  'DefendArg1': 'defendArg1',
  'DefendArg10': 'defendArg10',
  'DefendArg11': 'defendArg11',
  'DefendArg12': 'defendArg12',
  'DefendArg2': 'defendArg2',
  'DefendArg3': 'defendArg3',
  'DefendArg4': 'defendArg4',
  'DefendArg5': 'defendArg5',
  'DefendArg6': 'defendArg6',
  'DefendArg7': 'defendArg7',
  'DefendArg8': 'defendArg8',
  'DefendArg9': 'defendArg9',
  'ItemNumber': 'itemNumber',
  'Level': 'level',
  'NeedLevel': 'needLevel',
  'NeedPF': 'needPF',
  'Num1': 'num1',
  'Num10': 'num10',
  'Num11': 'num11',
  'Num12': 'num12',
  'Num2': 'num2',
  'Num3': 'num3',
  'Num4': 'num4',
  'Num5': 'num5',
  'Num6': 'num6',
  'Num7': 'num7',
  'Num8': 'num8',
  'Num9': 'num9',
  'Position1': 'position1',
  'Position10': 'position10',
  'Position11': 'position11',
  'Position12': 'position12',
  'Position2': 'position2',
  'Position3': 'position3',
  'Position4': 'position4',
  'Position5': 'position5',
  'Position6': 'position6',
  'Position7': 'position7',
  'Position8': 'position8',
  'Position9': 'position9',
  'Tactic1': 'tactic1',
  'Tactic2': 'tactic2',
  'Tactic3': 'tactic3',
} as const;

export const TeamFormationMeta = {
  tableName: 'TeamFormation',
  dataFileName: 'TeamFormation.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 59,
  requiredFields: ['itemId', 'picId', 'id', 'name', 'attackArg1', 'attackArg10', 'attackArg11', 'attackArg12', 'attackArg2', 'attackArg3', 'attackArg4', 'attackArg5', 'attackArg6', 'attackArg7', 'attackArg8', 'attackArg9', 'defendArg1', 'defendArg10', 'defendArg11', 'defendArg12', 'defendArg2', 'defendArg3', 'defendArg4', 'defendArg5', 'defendArg6', 'defendArg7', 'defendArg8', 'defendArg9', 'itemNumber', 'level', 'needLevel', 'needPF', 'num1', 'num10', 'num11', 'num12', 'num2', 'num3', 'num4', 'num5', 'num6', 'num7', 'num8', 'num9', 'position1', 'position10', 'position11', 'position12', 'position2', 'position3', 'position4', 'position5', 'position6', 'position7', 'position8', 'position9', 'tactic1', 'tactic2', 'tactic3'],
  optionalFields: [],
  renamedFieldsCount: 57,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: TeamFormationFieldMappings,
  reverseFieldMappings: TeamFormationReverseFieldMappings,
} as const;

export type TeamFormationConfigMeta = typeof TeamFormationMeta;
export type TeamFormationFieldMapping = typeof TeamFormationFieldMappings;
export type TeamFormationReverseFieldMapping = typeof TeamFormationReverseFieldMappings;
