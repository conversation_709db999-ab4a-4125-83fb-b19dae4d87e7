/**
 * 共享常量定义
 * 只包含所有服务都需要的真正共享的常量
 */

// 微服务名称常量 - 所有服务都需要引用
export const MICROSERVICE_NAMES = {
  AUTH_SERVICE: 'auth',
  CHARACTER_SERVICE: 'character',
  GAME_SERVICE: 'game',
  CLUB_SERVICE: 'club',
  MATCH_SERVICE: 'match',
  CARD_SERVICE: 'card',
  HERO_SERVICE: 'hero',
  ECONOMY_SERVICE: 'economy',
  SOCIAL_SERVICE: 'social',
  ACTIVITY_SERVICE: 'activity',
  NOTIFICATION_SERVICE: 'notification',
  GATEWAY_SERVICE: 'gateway',
} as const;

// 微服务名称类型
export type MicroserviceName = typeof MICROSERVICE_NAMES[keyof typeof MICROSERVICE_NAMES];

// 微服务端口获取函数 - 从环境变量读取
export const getMicroservicePort = (serviceName: MicroserviceName): number => {
  const portMap: Record<MicroserviceName, string> = {
    [MICROSERVICE_NAMES.GATEWAY_SERVICE]: 'GATEWAY_PORT',
    [MICROSERVICE_NAMES.AUTH_SERVICE]: 'AUTH_PORT',
    [MICROSERVICE_NAMES.CHARACTER_SERVICE]: 'CHARACTER_PORT',
    [MICROSERVICE_NAMES.GAME_SERVICE]: 'GAME_PORT',
    [MICROSERVICE_NAMES.CLUB_SERVICE]: 'CLUB_PORT',
    [MICROSERVICE_NAMES.MATCH_SERVICE]: 'MATCH_PORT',
    [MICROSERVICE_NAMES.CARD_SERVICE]: 'CARD_PORT',
    [MICROSERVICE_NAMES.HERO_SERVICE]: 'HERO_PORT',
    [MICROSERVICE_NAMES.ECONOMY_SERVICE]: 'ECONOMY_PORT',
    [MICROSERVICE_NAMES.SOCIAL_SERVICE]: 'SOCIAL_PORT',
    [MICROSERVICE_NAMES.ACTIVITY_SERVICE]: 'ACTIVITY_PORT',
    [MICROSERVICE_NAMES.NOTIFICATION_SERVICE]: 'NOTIFICATION_PORT',
  };

  const envVar = portMap[serviceName];
  const port = process.env[envVar];

  if (!port) {
    throw new Error(`Environment variable ${envVar} is not defined for service ${serviceName}`);
  }

  const portNumber = parseInt(port, 10);
  if (isNaN(portNumber)) {
    throw new Error(`Invalid port number in ${envVar}: ${port}`);
  }

  return portNumber;
};
