/**
 * 端口配置工具
 * 
 * 统一管理从环境变量读取端口配置，避免硬编码
 */

import { MICROSERVICE_NAMES, MicroserviceName, getMicroservicePort } from '../constants';

/**
 * 获取所有微服务的端口配置
 * 
 * @returns 端口配置对象
 */
export const getAllMicroservicePorts = (): Record<MicroserviceName, number> => {
  const ports: Record<string, number> = {};
  
  Object.values(MICROSERVICE_NAMES).forEach(serviceName => {
    ports[serviceName] = getMicroservicePort(serviceName);
  });
  
  return ports as Record<MicroserviceName, number>;
};

/**
 * 验证所有必需的端口环境变量是否已设置
 * 
 * @returns 验证结果
 */
export const validatePortConfiguration = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  try {
    Object.values(MICROSERVICE_NAMES).forEach(serviceName => {
      try {
        getMicroservicePort(serviceName);
      } catch (error) {
        errors.push(`${serviceName}: ${error.message}`);
      }
    });
  } catch (error) {
    errors.push(`Validation failed: ${error.message}`);
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * 获取服务的完整URL
 * 
 * @param serviceName 服务名
 * @param protocol 协议 (默认: http)
 * @param host 主机 (默认: localhost)
 * @returns 完整的服务URL
 */
export const getServiceUrl = (
  serviceName: MicroserviceName, 
  protocol: 'http' | 'https' | 'ws' | 'wss' = 'http',
  host: string = 'localhost'
): string => {
  const port = getMicroservicePort(serviceName);
  return `${protocol}://${host}:${port}`;
};

/**
 * 打印所有服务的端口配置 (用于调试)
 */
export const printPortConfiguration = (): void => {
  console.log('🔌 微服务端口配置:');
  console.log('='.repeat(40));
  
  try {
    const ports = getAllMicroservicePorts();
    Object.entries(ports).forEach(([serviceName, port]) => {
      console.log(`  ${serviceName.padEnd(20)} → ${port}`);
    });
  } catch (error) {
    console.error('❌ 端口配置错误:', error.message);
  }
  
  console.log('='.repeat(40));
};
