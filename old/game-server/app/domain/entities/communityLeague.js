var logger = require("pomelo-logger").getLogger('pomelo', __filename);
var EventEmitter = require("events").EventEmitter;
var util = require("util");
var dataApi = require("../../util/dataApi");
var utils = require("../../util/utils");
var commonEnum = require("../../../../shared/enum");
var Code = require("../../../../shared/code");
var Constant = require("../../../../shared/constant");
var TimeUtils = require("../../util/timeUtils");

//社区联赛
var CommunityLeague = function(seasonId, league) 
{
  this.uid = seasonId; //赛季Id
  this.league = league;

  this.groupPlayerMap = new Map(); //groupId -> [{ uid1}, {uid2}] //1-16 [uid1,uid2] //参赛人数据
  this.playerGroupMap = new Map(); //Uid->groupId //反向映射

  this.promotionMap   = new Map();      //groupId -> [uid, uid] //晋级玩家列表
  this.eliminateMap   = new Map();      //round_groupId -> [uid, uid] //淘汰玩家列表
  this.roundBattleMap = new Map();      //round_groupId -> [battleId, battleId2] //没人对打的情况不记录 ,轮空也要记录
  this.roundRank      = new Map();      //round_groupId -> [rankObj, rankObj] //因为是过程数据也是最终数据,没有办法,只能维护一个队列了
  this.battleScheduleMap = new Map();   //round_groupId = [ {uid, uid}]; //战斗赛程

  this.battleMap = new Map();           //[battleId]-> [battleObj];  //big data 所有轮次的数据都存在上面
  this.playerMap = new Map();           // uid->obj //玩家自己信息数据 进球率
  this.finalRank = new Map();           //总榜排名 // groupId -> [rankObjList]

  this.finalPromotionMap = new Map(); //uid => groupId //第一次升级到专业联赛的玩家存储
  this.cropEnrollList = [];     //删除不符合要求的玩家
  //系统支持数据
  this.joinerList = []; // uid  //玩家比赛玩家列表
  this.currRound = 0;   //当前轮次

  this.privateConfig = [];  //私有配置

  //辅助数据(不保存数据)
  this.roundConfig = []; //轮次配置表
  this.groupMaxCount = 0;
  this.typeId = commonEnum.LEAGUE_RUN_TYPE_ID.COMMUNITY;
  this.awaitTime = 0;   //战斗服返回时间不用存DB
  this.MAXTimeOut = 60 * 30;  //战斗服返回超时最大时间  30分钟
  //logger.debug("seasonId", this.uid);
  
  //初始化配置表
  this.initByConfig();
  this.initPrivateConfig();
};

util.inherits(CommunityLeague, EventEmitter);
module.exports = CommunityLeague;

//初始化配置数据
CommunityLeague.prototype.initByConfig = function() {
  //round 轮次 name: 轮次名称 required_num 每小组最大保存最大人数
  var roundConfig = [
    { round: 0, name: "预选赛", required_num: 64},
    // { round: 0, name: "audition", required_num: 64},
    // { round: 1, name: "32强赛",    required_num: 32},
    // { round: 2, name: "16强赛",    required_num: 16},
    // { round: 3, name: "8强赛",     required_num: 8},
    // { round: 4, name: "4强赛",     required_num: 4},
  ];
  this.groupMaxCount = 16;//一个信仰一组
  this.groupUpMax = 20; //每组晋级20个
  this.roundConfig = roundConfig;
};

//初始化私有配置
CommunityLeague.prototype.initPrivateConfig = function()
{
  /*isRunning: 某个函数某些地方只运行一次,
   canAction: 当前函数是否全部把事情完成(等回调), 
   battleStatus: battleStatus,
   sendWaitStartNtf: 发送等待比赛开始通知
   false, sendStartNtf: 发送比赛开始通知
  */
  let battleStatus = commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN;
  var privateConfig = 
  [
    { round: 0, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false, wait: false}, //sendWaitStartNtf 发送等待比赛通知 sendStartNtf 发送比赛开始通知
    { round: 1, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false, wait: false},
    { round: 2, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false, wait: false},
    { round: 3, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false, wait: false},
    { round: 4, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false, wait: false},
  ];
  this.privateConfig = privateConfig;
};

CommunityLeague.prototype.toJSONforDB = function()
{
    var community = {
    uid: this.uid,
    joinerList: this.joinerList,
    currRound: this.currRound,
    groupPlayerMap: this.league.groupPlayerMapToDB(this.groupPlayerMap),
    playerGroupMap: this.league.playerGroupMapToDB(this.playerGroupMap),

    battleMap: this.league.battleMapToDB(this.battleMap),
    playerMap: this.league.playerMapToDB(this.playerMap),
    finalRank: this.league.finalRankToDB(this.finalRank),

    promotionMap: this.league.roundGroupIdToDB(this.promotionMap),
    eliminateMap: this.league.roundGroupIdToDB(this.eliminateMap),
    roundBattleMap: this.league.roundGroupIdToDB(this.roundBattleMap),
    roundRank: this.league.roundGroupIdToDB(this.roundRank),
    battleScheduleMap: this.league.roundGroupIdToDB(this.battleScheduleMap),

    finalPromotionMap: this.league.finalProMapToDB(this.finalPromotionMap),
    cropEnrollList: this.cropEnrollList,

    privateConfig: this.privateConfig,
  };
  return community;
};

CommunityLeague.prototype.initByDB = function(doc) 
{
  this.uid = doc.uid;
  this.groupPlayerMap = this.league.groupPlayerMapLoadFromDB(doc.groupPlayerMap) || new Map();
  this.playerGroupMap = this.league.playerGroupMapLoadFromDB(doc.playerGroupMap) || new Map();

  this.battleMap = this.league.battleMapLoadFromDB(doc.battleMap) || new Map();
  this.playerMap = this.league.playerMapLoadFromDB(doc.playerMap) || new Map();
  this.finalRank = this.league.finalRankLoadFromDB(doc.finalRank) || new Map();

  this.promotionMap = this.league.roundGroupIdLoadFromDB(doc.promotionMap) || new Map();
  this.eliminateMap = this.league.roundGroupIdLoadFromDB(doc.eliminateMap) || new Map();
  this.roundBattleMap = this.league.roundGroupIdLoadFromDB(doc.roundBattleMap) || new Map();
  this.roundRank = this.league.roundGroupIdLoadFromDB(doc.roundRank) || new Map();
  this.battleScheduleMap = this.league.roundGroupIdLoadFromDB(doc.battleScheduleMap) || new Map();
  this.finalPromotionMap = this.league.finalProMapLoadFromDB(doc.finalPromotionMap) || new Map();
  this.cropEnrollList = doc.cropEnrollList || [];
  this.privateConfig = this.league.checkPrivateConfigAbnormal(doc.privateConfig);
  
  if (!this.privateConfig)
  {
    logger.error("CommunityLeague.initByDB: not privateConfig! doc.privateConfig", doc.privateConfig);
    this.initPrivateConfig();
  }

  //系统支持数据
  this.joinerList = doc.joinerList || []; // uid  //玩家比赛玩家列表
  this.currRound = doc.currRound || 0;    //当前轮次
  //logger.info("CommunityLeague initByDB: doc.uid, this.uid, currRound", doc.uid,  this.uid, this.currRound);
};

CommunityLeague.prototype.getRoundConfig = function(round)
{
  if(round > this.roundConfig.length - 1)
  {
    this.roundConfig.push({ round: round, name: "预选赛", required_num: 64});
  }
  return this.league.comGetRoundConfig(this.roundConfig, round);
};

CommunityLeague.prototype.getLastRound = function()
{
  return this.league.comGetLastRound(this.roundConfig);
};

CommunityLeague.prototype.getFinalRound = function(isScoreRank)
{
  let finalRound = this.getLastRound();
  if (isScoreRank)
  {
    finalRound = finalRound + 1;
  }
  return finalRound;
};

//当前最终轮次(显示)
CommunityLeague.prototype.getCurrFinalRound = function(isScoreRank)
{
  let currRound = this.getCurrRound();
  let lastRound = this.getLastRound();
  let finalRound = 0;
  let battleStatus =this.getConfigBattleStatus(currRound);
  if (isScoreRank && currRound > 0)
  {
    //查看当前轮数的状态
    if (lastRound === currRound &&  battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END ) //给最终排行榜
    {
      finalRound = currRound + 1;
    }
    else if (battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END) //当前轮结束了、给当前排行榜
    {
        finalRound = currRound + 1;
    }else
    {
      finalRound = currRound - 1; //还没有结束,给上一轮排行榜
    }
  }
  else
  {
    finalRound = currRound;
    if (battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END && currRound < this.getMaxRound() - 1)//最后一轮结束不加
    {
        finalRound = currRound + 1;
    }
  }
  return finalRound;
};

CommunityLeague.prototype.canRunNextAction = function(round) 
{
  return this.league.comCheckRunNextAction(this.privateConfig, round);
};

CommunityLeague.prototype.setCanRunNextAction = function(round) 
{
   this.league.comSetCanRunNextAction(this.privateConfig, round);
};

CommunityLeague.prototype.setBattleStatus = function(round, battleStatus)
{
  this.league.comSetBattleStatus(this.privateConfig, round, battleStatus);
};

CommunityLeague.prototype.getConfigBattleStatus = function(round)
{
  return this.league.comGetConfigBattleStatus(this.privateConfig, round);
};

CommunityLeague.prototype.setSendWaitStartNtf = function(round)
{
  this.league.comSetSendWaitStartNtf(this.privateConfig, round);
};

CommunityLeague.prototype.checkSendWaitStartNtf = function(round)
{
  return this.league.comCheckSendWaitStartNtf(this.privateConfig, round);
};

CommunityLeague.prototype.setSendStartNtf = function(round)
{
  this.league.comSetSendStartNtf(this.privateConfig, round);
};

CommunityLeague.prototype.checkSendStartNtf = function(round)
{
  return this.league.comCheckSendStartNtf(this.privateConfig, round);
};

CommunityLeague.prototype.checkRunning = function(round) 
{
  return this.league.comCheckRunning(this.privateConfig, round);
};

CommunityLeague.prototype.checkWait = function(round)
{
  return this.league.comCheckWait(this.privateConfig, round);
};

CommunityLeague.prototype.setIsRunning = function(round)
{
  this.league.comSetIsRunning(this.privateConfig, round);
};

CommunityLeague.prototype.setIsWait = function(round, flag)
{
  this.league.comSetIsWait(this.privateConfig, round, flag);
};

CommunityLeague.prototype.checkIsEnroll = function(uid)
{
  return utils.hasUidInList(this.joinerList, uid);
};
//报名           根据信仰放到不同的赛区 simplePlayerObj.beliefId 信仰
CommunityLeague.prototype.Enroll = function(simplePlayerObj) 
{
  if (!simplePlayerObj) {
    return Code.FAIL;
  }

  let uid = simplePlayerObj.uid;
  if (this.checkIsEnroll(uid))
  {
    logger.error("CommunityLeague.Enroll:  repeat enroll", uid);
    return Code.LEAGUE.ENROLL_REPEAT;
  }

  if (this.league.isProfessionJoiner(uid))
  {
    let groupId = this.league.getProfessionGroupIdByUid(uid);
    logger.error("CommunityLeague.Enroll: this player is profession player", uid, groupId);
    return Code.LEAGUE.ENROLL_PROFESSION_NOT_ALLOW;
  }

  var playerObj = this.league.newPlayerObj(uid, false);
  playerObj.power = simplePlayerObj.actualStrength;
  let enrollTime = TimeUtils.now();
  playerObj.enrollTime = enrollTime;
  playerObj.beliefId = simplePlayerObj.beliefId;//信仰id用于分组
  playerObj.roomUidList = [];//保存预选赛战斗记录

  this.setPlayerMap(uid, playerObj);
  this.joinerList.push(uid);
  this.league.leagueFsm.enrollTime.updateEnrollTime(uid, enrollTime);
  return Code.OK;
};
//第一次分组          按信仰分组
CommunityLeague.prototype.splitGroup = function()
{
  //随机抽取
  let joiner_num = this.joinerList.length;
  let idx = 0;
  // let beliefConfig = dataApi.allData.data["Belief"];
  for(let i in this.joinerList)
  {
      let groupId = this.getPlayerMap(this.joinerList[i]).beliefId;
      if(!groupId)//找不到组默认无信仰
      {
        groupId = 16;
      }
      let uid = this.joinerList[i];
      if (!this.hasGroupPlayerMap(groupId)) {
        this.setGroupPlayerMap(groupId, []);
      }
      let uidList = this.getGroupPlayerMap(groupId);
      uidList.push(uid);
      this.setPlayerGroupMap(uid, groupId);
      //初始化玩家数据
      if (!this.hasPlayerMap(uid)) {
        logger.error("CommunityLeague splitGroup: not playerMap", uid);
      }
  }

  logger.info("splitGroup:  joiner_num, joiner_num, idx", joiner_num, idx);
};
//得到该组淘汰数量
CommunityLeague.prototype.getGroupEliminateNum = function(groupId)
{
  let list = this.groupPlayerMap.get(groupId);
  let num = 0;
  for(let i in list)
  {
    if(this.hasEliminateMap(groupId))
    {
      let eliminate = this.getEliminateMap(groupId);
      if(utils.hasUidInList(eliminate, list[i]))
        num++;
    }
  }
  return num;
};
//淘汰
CommunityLeague.prototype.reGroup = function()
{
    for(let [k, v] of this.finalRank)
    {
      for(let i = v.length - 1; i >= 0; i--)
      {
        if(v[i].lossCount >= 2)//输两场淘汰
        {
          let list = this.groupPlayerMap.get(k);
          if((list.length - this.getGroupEliminateNum(k)) > this.groupUpMax)
          {
            this.addPlayerToEliminate(this.currRound, k, v[i].playerUid);
          }
        }
      }
    }
};

CommunityLeague.prototype.makeBattleObjList = function( randomUidList, totalNum) 
{
  //把随机后玩家放入到队列表中
  let battleObjList = []; //{uid, uid2}
  let idx = 0;
  let length = randomUidList.length;
  for (let index = 0; index < totalNum / 2; index++) {
    let uid1 = "robot_" + idx,
      uid2 = "robot_" + idx;
    if (idx < length) {
      //说明玩家已经分配完成，那么就是用"" 代替
      uid1 = randomUidList[idx];
    } else {
      uid2 = "robot_" + idx;
    }

    idx++;

    if (idx < length) {
      uid2 = randomUidList[idx];
    } else {
      uid2 = "robot_" + idx;
    }
    idx++;

    if (!uid1 && "" !== uid1) {
      logger.error("makeBattleObjList", idx, uid1);
      break;
    }

    if (!uid2 && "" !== uid2) {
      logger.error("makeBattleObjList", idx, uid2);
      break;
    }

    var battleObj = {
      Host: uid1,
      Gust: uid2
    };

    battleObjList[index] = battleObj;
  }

  return battleObjList;
};

//积分排序
function __score_compare_func(rankObj1, rankObj2) 
{
  let totalScore1 = rankObj1.totalScore;
  let goalCount1  = rankObj1.goalCount;
  let missCount1  = rankObj1.missCount;
  let power1 = rankObj1.power;
  let enrollTime1    = rankObj1.enrollTime;
  let diffGoalCount1 = goalCount1 - missCount1;

  let totalScore2 = rankObj2.totalScore;
  let goalCount2  = rankObj2.goalCount;
  let missCount2  = rankObj2.missCount;
  let power2 = rankObj2.power;
  let enrollTime2    = rankObj2.enrollTime;
  let diffGoalCount2 = goalCount2 - missCount2;
  let total1 = rankObj1.lossCount + rankObj1.winCount + rankObj1.drawCount;
  let total2 = rankObj2.lossCount + rankObj2.winCount + rankObj2.drawCount;
  //负
  if(rankObj1.lossCount !== rankObj2.lossCount)
  {
    //降序
    if (rankObj1.lossCount < rankObj2.lossCount) {
      return -1;
    } else if (rankObj1.lossCount > rankObj2.lossCount) {
      return 1;
    }
  }
  //总场次多的排前面
  if(total1 !== total2)
  {
    //降序
    if (total1 < total2) {
      return 1;
    } else if (total1 > total2) {
      return -1;
    }
  }
  //胜场
  if(rankObj1.winCount !== rankObj2.winCount)
  {
    //降序
    if (rankObj1.winCount < rankObj2.winCount) {
      return 1;
    } else if (rankObj1.winCount > rankObj2.winCount) {
      return -1;
    }
  }
  //平
  if(rankObj1.drawCount !== rankObj2.drawCount)
  {
    //降序
    if (rankObj1.drawCount < rankObj2.drawCount) {
      return 1;
    } else if (rankObj1.drawCount > rankObj2.drawCount) {
      return -1;
    }
  }
  //积分
  if (totalScore1 !== totalScore2) {
    //降序
    if (totalScore1 < totalScore2) {
      return 1;
    } else if (totalScore1 > totalScore2) {
      return -1;
    }
  }

  //进球数
  if (diffGoalCount1 !== diffGoalCount2) {
    //降序
    if (diffGoalCount1 < diffGoalCount2) {
      return 1;
    } else if (diffGoalCount1 > diffGoalCount2) {
      return -1;
    }
  }

  //进球数
  if (goalCount1 !== goalCount2) {
    //降序
    if (goalCount1 < goalCount2) {
      return 1;
    } else if (goalCount1 > goalCount2) {
      return -1;
    }
  }

  //战力
  if (power1 !== power2) {
    //降序
    if (power1 < power2) {
      return 1;
    } else if (power1 > power2) {
      return -1;
    }
  }

  //报名时间 靠前的人胜利
  //升序
  if (enrollTime1 > enrollTime2) {
    return 1;
  } else if (enrollTime1 < enrollTime2) {
    return -1;
  }
  return 0;
};

//首次联赛排序
function __first_compare_func(rankObj1, rankObj2) 
{
  let power1 = rankObj1.power;
  let enrollTime1 = rankObj1.enrollTime;
  let power2 = rankObj2.power;
  let enrollTime2 = rankObj2.enrollTime;

  //战力
  if (power1 != power2) {
    //降序
    if (power1 < power2) {
      return 1;
    } else if (power1 > power2) {
      return -1;
    }
  }

  //报名时间 靠前的人胜利
  //升序
  if (enrollTime1 > enrollTime2) {
    return 1;
  } else if (enrollTime1 < enrollTime2) {
    return -1;
  }
  return 0;
}

//note: 这两个map里面的数据结构不一样，所以分开写(人数刚好，或者小于规定的人数 <= 128*64)
CommunityLeague.prototype.directPromotion = function(round)
{
  for (let [k, v] of this.groupPlayerMap) 
  {
      let round_key = this.league.getRoundKey(round, k);
      //晋级玩家
      this.setPromotionMap(k, v);
  }
};

CommunityLeague.prototype.printDirectPromotion = function(round)
{
  for (let [k, v] of this.groupPlayerMap) 
  {
    //logger.info("directPromotion: round, groupId", round, k, v.length);
    //if (k === 8);
    //logger.info("directPromotion", v);
  }
};

CommunityLeague.prototype.printPromotion = function(round)
{
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
    let round_key = this.league.getRoundKey(round, groupId);
    let uidList = this.getPromotionMap(groupId);
    if (!uidList) continue;
      logger.info("printPromotion", round, groupId, uidList.length, uidList);
  }
};

CommunityLeague.prototype.getPromotionPlayer = function(round) {
  let groupPlayerMap = new Map();
  //从第一组开始进行分人
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
    let round_key = this.league.getRoundKey(round, groupId);
    if (!this.hasPromotionMap(groupId))
    {
      continue;
    }

    let uidList = this.getPromotionMap(groupId);
    if (!uidList || uidList.length <= 0) 
    {
      continue;
    }

    groupPlayerMap.set(round_key, uidList);
  }
  return groupPlayerMap;
};

CommunityLeague.prototype.getPromotionUidList = function(round) 
{
  let proUidList = [];
  //从第一组开始进行分人
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
      let round_key = this.league.getRoundKey(round, groupId);
      //logger.info("getPromotionUidList:", round, groupId, round_key);
      if (!this.hasPromotionMap(groupId))
      {
        continue;
      }

      //logger.info("getPromotionUidList", groupId, round_key);
      let uidList = this.getPromotionMap(groupId);
      if (!uidList || uidList.length <= 0) 
      { 
        continue;
      }

      //logger.info("getPromotionUidList", groupId, round_key, uidList);
      for (let i in uidList) {
        const uid = uidList[i];
        if (!uid || "" === uid) 
        {
          continue;
        }
        proUidList.push(uid);
      }
  }
  //logger.info("getPromotionPlayer: getPromotionUidList", proUidList);
  return proUidList;
};

CommunityLeague.prototype.getPromotionUidListByGroupId = function(round, groupId) 
{
  let proUidList = [];
  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasPromotionMap(groupId))
  {
    return proUidList;
  }

  let uidList = this.getPromotionMap(groupId);
  if (!uidList || uidList.length <= 0) 
  { 
    return proUidList;
  }

  for (let i in uidList) {
    const uid = uidList[i];
    if (!uid || "" === uid) 
    {
      continue;
    }

    proUidList.push(uid);
  }
  return proUidList;
};

CommunityLeague.prototype.getEliminateUidListByGroupId = function(round, groupId) 
{
  let proUidList = [];
  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasEliminateMap(round_key))
  {
    return proUidList;
  }

  let uidList = this.getEliminateMap(round_key);
  if (uidList.length <= 0) 
  { 
    return proUidList;
  }

  for (let i in uidList) {
    const uid = uidList[i];
    if (!uid || "" === uid) 
    {
      continue;
    }

    proUidList.push(uid);
  }
  return proUidList;
};

CommunityLeague.prototype.getEliminatePlayer = function(round) {
  let groupPlayerMap = new Map();
  //从第一组开始进行分人
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
    let round_key = this.league.getRoundKey(round, groupId);
    if (!this.hasEliminateMap(round_key))
    {
      continue;
    }

    let uidList = this.getEliminateMap(round_key);
    if (!uidList || uidList.length <= 0) 
    {
      continue;
    }

    groupPlayerMap.set(round_key, uidList);
  }
  return groupPlayerMap;
};

CommunityLeague.prototype.getEliminateUidList = function(round) {
  let proUidList = [];
  //从第一组开始进行分人
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
      let round_key = this.league.getRoundKey(round, groupId);
      if (!this.hasEliminateMap(round_key))
      {
        continue;
      }

      let uidList = this.getEliminateMap(round_key);
      if (!uidList || uidList.length <= 0) 
      {
        continue;
      }

      for (let i in uidList) {
        const uid = uidList[i];
        if (!uid || "" === uid) 
        {
          continue;
        }
        proUidList.push(uid);
      }
  }

  //logger.info("getPromotionPlayer: getEliminateUidList", proUidList);
  return proUidList;
};

//生成下一轮比赛战斗队列
//由于不存在超人的情况,只考虑少人情况
CommunityLeague.prototype.genNextRoundBattleQueue = function(nextRound) 
{
  let lastRound = nextRound - 1;
  var config = this.getRoundConfig(nextRound);
  let required_num = config.required_num;
  let beginTime = this.league.leagueFsm.getRoundBattleTime(this.typeId, nextRound);

  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
    let last_round_key = this.league.getRoundKey(lastRound, groupId);
    let round_key = this.league.getRoundKey(nextRound, groupId);

    // let uidList = this.getPromotionMap(groupId);
    let uidList = utils.cloneArray(this.getGroupPlayerMap(groupId));
    if (!uidList || uidList.length <= 0) continue;
    if(uidList.length <= this.groupUpMax)
    {
      for(let i in uidList)
      {
        let list = this.getEliminateMap(groupId);
        if(!utils.hasUidInList(list, uidList[i]))
        {
          this.addPlayerToPromotion(this.currRound, groupId, uidList[i]);
        }
      }
      continue;
    }
    //已经输两场的就不要再加入对战列表了
    for(let i = uidList.length - 1; i >= 0; i--)
    {
      let len = uidList.length;
      if(len > this.groupUpMax)
      {
        let lossCount = this.getPlayerMap(uidList[i]).lossCount;
        if(lossCount >= 2)
          uidList.splice(i, 1);
      }
    }
    //先随机下uidlist
    let randomUidList = utils.arrayIndexRandom(uidList);
    //先看有多人个人，看下需要少人,少的不robot进去
    let length = randomUidList.length;
    let robot_num = length % 2;
    let robot_idx = 1; //机器人的索引为 20-robot_num ~ 20-1
    for (let i = 0; i < length + robot_num; i += 2) {
      let idx1 = i;
      let idx2 = i + 1;
      let uid1 = "";
      let uid2 = "";

      if (!randomUidList[idx1]) {
        uid1 = "robot_" + robot_idx  + "_"+ utils.syncCreateUid();
        robot_idx++;
      } else {
        uid1 = randomUidList[idx1];
      }

      if (!randomUidList[idx2]) {
        uid2 = "robot_" + robot_idx + "_"+ utils.syncCreateUid();
        robot_idx++;
      } else {
        uid2 = randomUidList[idx2];
      }

      let obj = {
        Host: uid1, //客场
        Gust: uid2,
        BeginTime: beginTime,
      };

      if (!this.hasBattleScheduleMap(round_key)) {
        this.setBattleScheduleMap(round_key, []);
      }
    
      let objList = this.getBattleScheduleMap(round_key);
      objList.push(obj);
      this.setBattleScheduleMap(round_key, objList);
    }

    let battleList = this.getBattleScheduleMap(round_key);
    if (this.league.checkBattleListHostGuest(battleList))
    {
      logger.error("genNextRoundBattleQueue checkBattleListHostGuest", groupId, battleList);
    }
  }
};

CommunityLeague.prototype.showCommunity = function(status_obj) 
{
  //logger.info("show community");
  let round = this.currRound;
  if (round === commonEnum.LEAGUE_RUN_ROUND.CommunityAudition && !this.checkRunning(round))
  {   
      this.comAudition(status_obj);
      this.commonSettle(status_obj);
  }
  return true;
};

CommunityLeague.prototype.comAudition = function(status_obj)
{
  let round = this.currRound;
  let localRound = commonEnum.LEAGUE_RUN_ROUND.CommunityAudition;
  let check = this.league.checkRound(round, localRound, "Audition");
  if (!check)
  { 
    return false;
  }

  let ret = this.checkRunning(round);
  if (ret)
  {
    ret = this.canRunNextAction(round);
    logger.info("_comAudition: canRunNextAction", ret);
    if (ret) return true;

    return false;
  }
  
  logger.info( "_comAudition: joiner_num", this.joinerList.length);

  //分组
  this.splitGroup(round);

  //直接晋级到下一轮
  // this.directPromotion(round);
  this.setIsRunning(round);
  this.setCanRunNextAction(round);
  this.setSendWaitStartNtf(round);
  this.setSendStartNtf(round);
  this.setBattleStatus(round, commonEnum.LEAGUE_BATTLE_STATUS.END);
  return false;  //这里返回false原因是不让状态机第一次运行就跑到下一个状态
};
//战斗服返回超时重新重设状态
CommunityLeague.prototype.battleTimeOutResetPrivateConfig = function()
{
  let atConfig;
  let k;
  //得到当前轮的状态
  for(let i in this.privateConfig)
  {
    if(this.privateConfig[i].round === this.currRound)
    {
      k = i;
      atConfig = this.privateConfig[i];
    }
  }
  //进入等待时间，可以开始计时
  if(atConfig.isRunning === true && atConfig.canAction === false)
  {
    this.awaitTime += 1;//因为updateFsmState函数是一秒一次所以每次加1为1秒
  }
  //超时了重置
  if((this.awaitTime >= this.MAXTimeOut) && (this.privateConfig[k].isRunning === true && this.privateConfig[k].canAction === false))
  {
    logger.warn("battle TimeOut PrivateConfig:", this.typeId, this.awaitTime, this.privateConfig[k]);
    this.privateConfig[k].isRunning = false;
    this.privateConfig[k].wait = false;
    this.awaitTime = 0;
    logger.warn("battle TimeOut ResetPrivateConfig:", this.typeId, this.awaitTime, this.privateConfig[k]);
  }
};
//预选赛淘汰流程
CommunityLeague.prototype.comGroup64to32 = function(status_obj) 
{
  let round = this.currRound;
  //检查战斗服返回是否超时
  this.battleTimeOutResetPrivateConfig();

  if(this.checkWait(round))//战斗是否返回
  {
    return false;
  }
  //检查该部分运行过没有
  let ret = this.checkRunning(round);
  if (ret)
  {
    //检查是否能够到达下一转
    ret = this.canRunNextAction(round);
    if (ret) return true; //事情做完了，但是还没有到达下一伦次开始的时间
    // return false; //还没有做完，继续等待
  }

  this.setIsWait(this.currRound, true);
  this.commProcessFlow(status_obj, this.currRound);
  return false;
};

CommunityLeague.prototype.comGroup32to16 = function(status_obj) 
{
  let round = this.currRound;
  let localRound = commonEnum.LEAGUE_RUN_ROUND.CommunityGroup32to16;
  let check = this.league.checkRound(round, localRound, "Group32to16");
  if (!check)
  { 
    return false;
  }
  //检查战斗服返回是否超时
  this.battleTimeOutResetPrivateConfig();
  //检查该部分运行过没有
  let ret = this.checkRunning(round);
  if (ret)
  {
    //检查是否能够到达下一转
    ret = this.canRunNextAction(round);
    if (ret) return true; //事情做完了，但是还没有到达下一伦次开始的时间
    return false; //还没有做完，继续等待
  }

  //当前轮次淘汰
  this.commProcessFlow(status_obj, round);
  return false;
};

CommunityLeague.prototype.comGroup16to8 = function(status_obj)
{
  let round = this.currRound;
  let localRound = commonEnum.LEAGUE_RUN_ROUND.CommunityGroup16to8;
  let check = this.league.checkRound(round, localRound, "Group16to8");
  if (!check)
  {
    return false;
  }
  //检查战斗服返回是否超时
  this.battleTimeOutResetPrivateConfig();
  //检查该部分运行过没有
  let ret = this.checkRunning(round);
  if (ret)
  {
    //检查是否能够到达下一转
    ret = this.canRunNextAction(round);
    if (ret) return true; //事情做完了，但是还没有到达下一伦次开始的时间
    return false; //还没有做完，继续等待
  }

  //当前轮次淘汰
  this.commProcessFlow(status_obj, round);
  return false;
};

CommunityLeague.prototype.comGroup8to4 = function(status_obj) 
{
  let round = this.currRound;
  let localRound = commonEnum.LEAGUE_RUN_ROUND.CommunityGroup8to4;
  let check = this.league.checkRound(round, localRound, "Group8to4");
  if (!check)
  { 
    return false;
  }
  //检查战斗服返回是否超时
  this.battleTimeOutResetPrivateConfig();
  //检查该部分运行过没有
  let ret = this.checkRunning(round);
  if (ret)
  {
    //检查是否能够到达下一转
    ret = this.canRunNextAction(round);
    if (ret) return true; //事情做完了，但是还没有到达下一伦次开始的时间
    return false; //还没有做完，继续等待
  }

  //当前轮次淘汰
  this.commProcessFlow(status_obj, round);
  return false;
};

//发送消息通知客户端比赛即将开始
//找到该轮次所有的人,打包出来
CommunityLeague.prototype.commonWait = function() 
{
  let waitStartData = [];
  let round = this.currRound;
  if (!this.checkSendWaitStartNtf(round))
  {
    logger.info("commonWait nextRound", this.currRound);
    waitStartData = this.getRoundNtfPlayerData(round);
    this.setSendWaitStartNtf(round);
  }
  
  return waitStartData;
};

//发送的玩家数据已经生成好了，需要从记录里面去拿
CommunityLeague.prototype.getRoundNtfPlayerData = function(round)
{
  let roundNtfPlayerStartData = [];
  if (round > this.currRound)
  {
    return roundNtfPlayerStartData;
  }

  let leagueName = commonEnum.LEAGUE_NAME.COMMUNITY + "联赛";
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) 
  {
      let round_key = this.league.getRoundKey(round, groupId);
      let battleUidList = this.getRoundBattleMap(round_key);
      if (!battleUidList || battleUidList.length <= 0) continue;
      for (let idx in battleUidList) {
       let battleId = battleUidList[idx];
       let battleObj = this.getBattleMap(battleId);
       if (!battleObj) continue;
       
       let home = battleObj.home;
       let away = battleObj.away;
       let roomUid = battleObj.roomUid;
       //过滤有机器人的情况
       if (this.league.checkRobot(home) || this.league.checkRobot(away)) continue;

       let teamA = this.league.getSimpleTeamInfo(home);
       let teamB = this.league.getSimpleTeamInfo(away);
       let homeSimpleRolePrepareInfo = {
          uid: home,
          roomUid: roomUid,
          competitorUid: away,
          teamA: teamA,
          teamB: teamB,
          beginTime: battleObj.beginTime,
          leagueName: leagueName,
          groupId: groupId,
          round: round,
        };

       let awaySimpleRolePrepareInfo = {
          uid: away,
          roomUid: roomUid,
          competitorUid: home,
          teamA: teamA,
          teamB: teamB,
          beginTime: battleObj.beginTime,
          leagueName: leagueName,
          groupId: groupId,
          round: round,
         };

         //logger.info("getRoundNtfPlayerData: homeSimpleRolePrepareInfo", homeSimpleRolePrepareInfo, battleObj);
         //logger.info("getRoundNtfPlayerData: awaySimpleRolePrepareInfo", awaySimpleRolePrepareInfo);
       roundNtfPlayerStartData.push(homeSimpleRolePrepareInfo);
       roundNtfPlayerStartData.push(awaySimpleRolePrepareInfo);
      }
    }

    logger.info("getRoundNtfPlayerData", round, roundNtfPlayerStartData.length);
    return roundNtfPlayerStartData;
};

CommunityLeague.prototype.commonPlayRecord = function()
{
  let round = this.currRound;
  let leagueName = commonEnum.LEAGUE_NAME.COMMUNITY + "联赛";
  let data = {
    typeId: this.getTypeId(),
    leagueName: leagueName,
    round: round,
  };

  let isSend = false;
  if (!this.checkSendStartNtf(round))
  {
    logger.info("commonPlay nextRound", round);
    this.setSendStartNtf(round);
    isSend = true;
  }

  let battleStatus = this.getConfigBattleStatus(round)
  if(commonEnum.LEAGUE_BATTLE_STATUS.RUNNING !== battleStatus)
  {
    this.setBattleStatus(round, commonEnum.LEAGUE_BATTLE_STATUS.RUNNING);
  }

  return {isNeedSend: isSend, playRecord: data};
};

//结算，并生成下一轮比赛 ,每个轮次只能跑一次
CommunityLeague.prototype.commonSettle = function() 
{
  this.setBattleStatus(this.currRound, commonEnum.LEAGUE_BATTLE_STATUS.END);
  this.currRound += 1;
  let nextRound = this.currRound;
  if(nextRound > this.privateConfig.length - 1)
  {
    this.privateConfig.push({ round: nextRound, isRunning: false, canAction: false, battleStatus: commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN, sendWaitStartNtf: false, sendStartNtf: false, wait: false});
  }
  logger.info("commonSettle nextRound", nextRound);
  this.genNextRoundBattleQueue(nextRound);
  this.league.saveCommunity();
  return true;
};

CommunityLeague.prototype.getComNotifyList = function(round, isPromotion)
{
  let notifyMailList = [];
  let leagueName = commonEnum.LEAGUE_NAME.COMMUNITY + "联赛";
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) 
  {
      let notifyType = 0;
      let uidList = [];
      if (isPromotion)
      {      
        uidList = this.getPromotionUidListByGroupId(round, groupId);
        notifyType = commonEnum.FINAL_MAIL_NOTIFY_TYPE.PROMOTION;
      }else
      {
        uidList = this.getEliminateUidListByGroupId(round, groupId);
        notifyType = commonEnum.FINAL_MAIL_NOTIFY_TYPE.ELIMINATE;
        //logger.info("getComNotifyList round, groupId, uidList",round, groupId, uidList.length);
      }

      for(let idx in uidList)
      { 
        let pUid = uidList[idx];
        if (this.league.checkRobot(pUid)) //机器人过滤
        {
           continue;
        }

        let pBattleId = this.getBattleIdByRoundGroupId(round, groupId, pUid);
        //logger.info("getNotifyMailPromotionList: pBattleId", pBattleId);
        if (!pBattleId)
        {
          logger.info("getComNotifyList: not found pBattleId", round, groupId, pUid, pBattleId);
          continue;
        }

        let battleObj =  this.getBattleMap(pBattleId);
        if (!battleObj)
        {
          logger.info("getComNotifyList: not found battleObj", round, groupId, pUid, pBattleId);
          continue;
        }

        let pObj = this.league.comGetNotifyMailObj(pUid, battleObj, this.getTypeId(), notifyType, leagueName, round);
        notifyMailList.push(pObj);
      } 
    }

    logger.info("getComNotifyList: notifyMailList", notifyMailList.length);
    return notifyMailList;
};

CommunityLeague.prototype.getNotifyMailPromotionList = function(round)
{
  return this.getComNotifyList(round, true);
};

CommunityLeague.prototype.getNotifyMailEliminateList = function(round)
{
  return this.getComNotifyList(round, false);
};

CommunityLeague.prototype.commonNextRound = function()
{
  this.currRound = this.currRound + 1;
  logger.info("commonNextRound currRound", this.currRound);
  return true;
};

//最终结算
//将晋级玩家的数据发送给下一个联赛
CommunityLeague.prototype.finalSettle = function() 
{
  //处理最终排名
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) 
  {
      this.makeFinalGroupRank(groupId);
  }

  //把社区联赛晋级的队伍自动加入到常规来
  let uidList = this.getFinalPrUidList();
  for (let i in uidList) 
  {
    const uid = uidList[i];
    if (!uid) continue;
    let playerObj = this.league.newPlayerObj(uid, true);
    playerObj.beliefId = this.playerGroupMap.get(uid);
    this.league.communitySendPlayerToNormal(playerObj);
  }

  //logger.error("finalSettle", this.currRound, commonEnum.LEAGUE_BATTLE_STATUS.END);
  this.setBattleStatus(this.currRound, commonEnum.LEAGUE_BATTLE_STATUS.END);
  this.league.saveCommunity();
  return true;
};

//通知最终晋级的玩家
CommunityLeague.prototype.getNotifyMailFinalPromotion = function()
{
  let notifyMailList = [];
  let uidList = this.getFinalPrUidList();
  for (let i in uidList) 
  {
    const uid = uidList[i];
    if (!uid) continue;

    if (this.league.checkRobot(uid)) //机器人过滤
    {
       continue;
    }

    let pObj = {
      uid: uid,
      name: commonEnum.LEAGUE_NAME.COMMUNITY,
      typeId: this.getTypeId(),
      notifyType: commonEnum.FINAL_MAIL_NOTIFY_TYPE.PROMOTION,
    };
    notifyMailList.push(pObj);
  }

  return notifyMailList;
};

//通用处理流程
CommunityLeague.prototype.commProcessFlow = function(status_obj, round) {
  //取当前比赛各个战斗队列
  let groupPlayerMap = this.getRoundBattleQueue(round);
  //当前轮次淘汰
  let self = this;
  this.comGetBattleFlow(round, groupPlayerMap, function(err){
    if (!!err)
    {
      logger.error("comGetBattleFlow: error", err);
      return;
    }
    //每轮结束计算排名
    for (let groupId = 1; groupId <= self.groupMaxCount; groupId++)
    {
      self.makeFinalGroupRank(groupId);
    }
    self.setIsWait(self.currRound, false);
    self.reGroup();//重新分组，把输两场的淘汰了
    //如果每组都只剩20人以内在设置
    if(self.checkIsFinish())
    {
      self.setCanRunNextAction(round);
      self.setIsWait(self.currRound, false);
      return;
    }
    self.commonSettle();//这里会增加轮次并且生成下轮对战列表
    self.awaitTime = 0;
  });

  this.setIsRunning(round);
};
//通用处理流程
CommunityLeague.prototype.checkIsFinish = function() {
    let i = 0;
    for(let [k,v] of this.groupPlayerMap)
    {
      if((v.length - this.getGroupEliminateNum(k)) <= this.groupUpMax)
      {
        for(let n in v)
        {
          let list = this.getEliminateMap(k);
          if(!utils.hasUidInList(list, v[n]))
          {
            this.addPlayerToPromotion(this.currRound, k, v[n]);
          }
        }
        i++;
      }
    }
    if(i === this.groupPlayerMap.size)
    {
      return true;
    }
    return false;
};

//获取某一轮次战斗队列
CommunityLeague.prototype.getRoundBattleQueue = function(round) {
  var map = new Map(); //round_groupId = > [Host: uid1, Gust:uid2];
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
    let round_key = this.league.getRoundKey(round, groupId);
    let objList = this.getBattleScheduleMap(round_key);
    if (!objList) continue;
    let cpObjList = utils.cloneArray(objList);
    map.set(round_key, cpObjList);
  }
  return map;
};

CommunityLeague.prototype.comGetBattleFlow = function(round, groupPlayerMap, cb)
{
  let self = this;
  //1.拉取玩家数据
  this.league.commonGetBattleData(groupPlayerMap, function(err){
    if (!!err)
    {
      logger.error("comGetBattleData: callback error", err);
      return cb(err);
    }

    //通用战斗流程
    self.comEliminationGroup(round, groupPlayerMap, function(err) {
      if (!!err)
      {
        logger.error("comEliminationGroup: catch a error!", err);
        cb(err);
        return;
      }

      logger.info("commProcessFlow .............", round);
      self.printDirectPromotion(round);
      for (let groupId = 1; groupId <= self.groupMaxCount; groupId++) 
      {
        self.printRank(round, groupId);
      }

      cb(null);
      return;
    });
  });
};

CommunityLeague.prototype.comEliminationGroup = function(round, groupPlayerMap, cb) 
{
  for (let [k, v] of groupPlayerMap) 
  {
    let groupId = this.league.getGroupId(k);
    //logger.info("comEliminationGroup k,v", k,v.length)
    this.updateBattleQueueProcess(round, parseInt(groupId), v, v.length);
  }

  //全部入队列后，一起发送请求,一起处理
  this.league.battleQueue.sendBattleToBattleSvr(function(err, code){
    //logger.info("comEliminationGroup", err, code);
    if (!!err)
    {
      logger.error("sendBattleToBattleSvr failed! err", err);
      cb(err);
      return;
    }

    if (code !== Code.OK)
    {
      logger.error("comEliminationGroup: ret error! err", code);
      cb("ret error! err");
      return;
    }

    cb(null);
    return;
  });
};

//处理战斗队列
CommunityLeague.prototype.updateBattleQueueProcess = function( round, groupId, battleObjList, length)
{
  //logger.info("updateBattleQueueProcess: round, groupId, length", round, groupId, battleObjList.length, length);
  var config = this.getRoundConfig(round);
  let required_every_group_num = config.required_num;

  let lastRound = round - 1;
  let historyRankData = this.makeTotalLastRoundGroupRank(groupId, lastRound);

  //把随机后玩家放入到队列表中
  let battleList = this.league.makeBattleList(this.typeId, round, groupId, battleObjList, length, historyRankData);
  //logger.info("updateBattleQueueProcess 0: round, groupId, length", round, groupId, battleList.length);

  //处理轮空情况
  //联赛没有位置为空的情况，就有算，也上上一轮留下的机器人，位置不可能留空
  this.league.preProcessBattleList(battleList, required_every_group_num);
  //logger.error("updateBattleQueueProcess", round, groupId, battleList);
  
  let needSendBattleList = this.league.getAllPlayerBattleList(battleList);
  //logger.info("updateBattleQueueProcess 1: round, groupId, length", round, groupId, needSendBattleList.length, battleList.length);
  if (needSendBattleList.length > 0) {
    //logger.info("updateBattleQueueProcess 2: round, groupId, robot_length, length", round, groupId, battleList.length - needSendBattleList.length, needSendBattleList.length);
    this.league.sendBattleQueue(needSendBattleList);
  }

  //这里直接处理不需要战斗的情况 比如玩家轮空，两个机器人对打，直接处理
  this.proRobotBattleResult(round, groupId, battleList);
};

//处理机器人战斗结果
//Note: 这里没有对战分数
CommunityLeague.prototype.proRobotBattleResult = function(round, groupId, battleResultList) 
{
  //logger.info("proRobotBattleResult", battleResultList);
  for (let index in battleResultList) 
  {
    let winTeam = "";
    let lostTeam = "";
    let data = battleResultList[index];
    let robotA = "robot_" === data.home.substr(0, 6); //home是否为机器人
    let robotB = "robot_" === data.away.substr(0, 6); //away是否为机器人
    if (!robotA && !robotB) continue;
    let newHomePlayerObj = this.league.newPlayerObj(data.home, true);
    let newAwayPlayerObj = this.league.newPlayerObj(data.away, true);
    //都为机器人或者人
    if (robotA && robotB) {
      //都是机器人 算主场赢
      winTeam = data.home;
      lostTeam = data.away;
      newHomePlayerObj.battleCount += 1;
      newHomePlayerObj.winCount += 1;
      newHomePlayerObj.goalCount += 0;
      newHomePlayerObj.missCount += 0;
      newHomePlayerObj.totalScore += 3;

      newAwayPlayerObj.battleCount += 1;
      newAwayPlayerObj.lossCount += 1;
      newAwayPlayerObj.goalCount += 0;
      newAwayPlayerObj.missCount += 0;
      newAwayPlayerObj.totalScore += 0;
    }
    else if (!robotA && robotB) 
    {      //这里的情况是人对机器人，是轮空直接判定为人赢
      //A为真人 B为机器人
      winTeam = data.home;
      lostTeam = data.away;
      newHomePlayerObj.battleCount += 1;
      newHomePlayerObj.winCount += 1;
      newHomePlayerObj.totalScore += 3;
  
      newAwayPlayerObj.battleCount += 1;
      newAwayPlayerObj.lossCount += 1;
      newAwayPlayerObj.totalScore += 0;
    } //A为机器人 B为真人
    else {
      winTeam = data.away;
      lostTeam = data.home;
      newHomePlayerObj.battleCount += 1;
      newHomePlayerObj.lossCount += 1;
      newHomePlayerObj.totalScore += 0;

      newAwayPlayerObj.battleCount += 1;
      newAwayPlayerObj.winCount += 1;
      newAwayPlayerObj.totalScore += 3;
    }

    data.winTeam = winTeam;
    //增加胜场
    if(!robotA && winTeam === data.home)
    {
      let obj = this.playerMap.get(data.home);
      obj.battleCount += newHomePlayerObj.battleCount;
      obj.winCount += newHomePlayerObj.winCount;
      obj.lossCount += newHomePlayerObj.lossCount;
      obj.totalScore += newHomePlayerObj.totalScore;
    }
    else if(!robotB && winTeam === data.away)
    {
      let obj = this.playerMap.get(data.away);
      obj.battleCount += newAwayPlayerObj.battleCount;
      obj.winCount += newAwayPlayerObj.winCount;
      obj.lossCount += newAwayPlayerObj.lossCount;
      obj.totalScore += newAwayPlayerObj.totalScore;
    }

    this.addPlayerToRoundGroupRank(round, groupId, newHomePlayerObj);
    this.addPlayerToRoundGroupRank(round, groupId, newAwayPlayerObj);
    this.addRoundBattle(round, groupId, this.league.comGetBattleResultObj(data, this.typeId));   //战斗结果保存
  }
};

//处理战斗服返回来的结果
//Note: 对战双方都是真人
CommunityLeague.prototype.proPlayerBattleResult = function(result) 
{
  if (!result) {
    logger.error("proPlayerBattleResult: not result", result);
    return;
  }

  let winTeam = "";
  let lostTeam = "";
  let data = result;
  let homeScore = data.homeScore;
  let awayScore = data.awayScore;
  let round = data.round;
  let groupId = data.groupId;

  let robotA = "robot_" === data.home.substr(0, 6); //home是否为机器人
  let robotB = "robot_" === data.away.substr(0, 6); //away是否为机器人
  if (robotA || robotB) {
    logger.error("proPlayerBattleResult: home and away is robot", result);
    return;
  }
  
  let newHomePlayerObj = this.league.newPlayerObj(data.home, true);
  let newAwayPlayerObj = this.league.newPlayerObj(data.away, true);
  if (homeScore > awayScore) 
  {
    //A赢
    winTeam = data.home;
    lostTeam = data.away;
    newHomePlayerObj.battleCount += 1;
    newHomePlayerObj.winCount += 1;
    newHomePlayerObj.goalCount += data.homeScore;
    newHomePlayerObj.missCount += data.awayScore;
    newHomePlayerObj.totalScore += 3;

    newAwayPlayerObj.battleCount += 1;
    newAwayPlayerObj.lossCount += 1;
    newAwayPlayerObj.goalCount += data.awayScore;
    newAwayPlayerObj.missCount += data.homeScore;
    newAwayPlayerObj.totalScore += 0; //失败加0分
  } else if (homeScore < awayScore) 
  {
    //B赢
    winTeam = data.away;
    lostTeam = data.home;
    newHomePlayerObj.battleCount += 1;
    newHomePlayerObj.lossCount += 1;
    newHomePlayerObj.goalCount += data.homeScore;
    newHomePlayerObj.missCount += data.awayScore;

    newAwayPlayerObj.battleCount += 1;
    newAwayPlayerObj.winCount += 1;
    newAwayPlayerObj.goalCount += data.awayScore;
    newAwayPlayerObj.missCount += data.homeScore;
    newAwayPlayerObj.totalScore += 3; //失败加0分
    //logger.info("player 2 data.home, playerObj1", data.home, playerObj1);
  } //A平
  else {
    newHomePlayerObj.totalScore += 1;
    newAwayPlayerObj.totalScore += 1;

    //平局加一分
    newHomePlayerObj.battleCount += 1;
    newHomePlayerObj.drawCount += 1;
    newHomePlayerObj.goalCount += data.homeScore;
    newHomePlayerObj.missCount += data.awayScore;

    newAwayPlayerObj.battleCount += 1;
    newAwayPlayerObj.drawCount += 1;
    newAwayPlayerObj.goalCount += data.awayScore;
    newAwayPlayerObj.missCount += data.homeScore;
  }

  data.winTeam = winTeam;

  let list = [];
  list.push(data.roomUid);
  let objA = this.playerMap.get(data.home);
  objA.winCount += newHomePlayerObj.winCount;
  objA.drawCount += newHomePlayerObj.drawCount;
  objA.lossCount += newHomePlayerObj.lossCount;
  objA.totalScore += newHomePlayerObj.totalScore;
  if(objA.roomUidList)
  {
    objA.roomUidList.push(data.roomUid);
  }
  else
  {
    objA.roomUidList = list
  }

  let objB = this.playerMap.get(data.away);
  objB.winCount += newAwayPlayerObj.winCount;
  objB.drawCount += newAwayPlayerObj.drawCount;
  objB.lossCount += newAwayPlayerObj.lossCount;
  objB.totalScore += newAwayPlayerObj.totalScore;
  if(objB.roomUidList)
  {
    objB.roomUidList.push(data.roomUid);
  }
  else
  {
    objB.roomUidList = list
  }

  this.addPlayerToRoundGroupRank(round, groupId, newHomePlayerObj);
  this.addPlayerToRoundGroupRank(round, groupId, newAwayPlayerObj);
  this.addRoundBattle(round, groupId, data);   //战斗结果保存
  //logger.info("proPlayerBattleResult: data", data);
};
//添加到晋级
CommunityLeague.prototype.addPlayerToPromotion = function(round, groupId, uid) 
{
  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasPromotionMap(groupId)) {
    this.setPromotionMap(groupId, []);
  }

  let uidList = this.getPromotionMap(groupId);
  //检查排名中是否存在uid，如果有就不用添加
  if (utils.hasUidInList(uidList, uid)) 
  {
    logger.error("_addPlayerToPromotion: repeat add promotion uid", uid, round_key);
    return;
  }
  uidList.push(uid);
  //logger.info("addPlayerToPromotion", round, groupId, uid);
};

CommunityLeague.prototype.addPlayerToEliminate = function(round, groupId, uid)
{
  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasEliminateMap(groupId)) {
    this.setEliminateMap(groupId, []);
  }

  let uidList = this.getEliminateMap(groupId);
  //检查排名中是否存在uid，如果有就不用添加
  if (utils.hasUidInList(uidList, uid)) 
  {
    logger.error("_addPlayerToPromotion: repeat add promotion uid", uid, round_key);
    return;
  }

  //logger.info("addPlayerToEliminate", uidList);
  uidList.push(uid);
  //logger.info("addPlayerToEliminate", round, groupId, uid);
};

CommunityLeague.prototype.addRoundBattle = function(round, groupId, data)
{
    this.setBattleMap(data.battleId, data);
    let round_key =  this.league.getRoundKey(round, groupId);
    if (!this.hasRoundBattleMap(round_key))
    {
      this.setRoundBattleMap(round_key, []);
    }
    let battleUidList = this.getRoundBattleMap(round_key);
    battleUidList.push(data.battleId);
};

CommunityLeague.prototype.printRank = function(round, groupId)
{  
  let round_key = this.league.getRoundKey(round, groupId);
  var rankObjList = this.getRoundRank(round_key);
  let uidList = [];
  for (let i in rankObjList) {
    const playerObj = rankObjList[i];
    if (!playerObj) continue;
    // if (groupId === 8 )
    //   logger.info("rankList: playerUid, totalScore, diff, goalCount, power1", 
    //   playerObj.playerUid, playerObj.totalScore, playerObj.goalCount, playerObj.power, playerObj.enrollTime);
    uidList.push(playerObj.playerUid);
  }
};

CommunityLeague.prototype.addPlayerToRoundGroupRank = function( round, groupId, playerObj)
{
  if (!playerObj) {
    logger.error("addPlayerToRoundGroupRank: not playerObj uid", playerObj);
    return;
  }

  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasRoundRank(round_key)) {
    this.setRoundRank(round_key, []);
  }

  var rankObjList = this.getRoundRank(round_key);
  rankObjList.push(utils.clone(playerObj));
  rankObjList.sort(__score_compare_func);
  this.setRoundRank(round_key, rankObjList);
};

CommunityLeague.prototype.getPlayerRoundRank = function(round_key, uid)
{
  if (!this.hasRoundRank(round_key))
  {
    return null;
  }

  var rankObjList = this.getRoundRank(round_key);
  for (let idx in rankObjList) {
    const obj = rankObjList[idx];
    if (!obj) continue;
    let playerUid = obj.playerUid;
    if (uid === playerUid)
    {
      return obj;
    }
  }

  return null;
};

//验证最终小组排名
CommunityLeague.prototype.makeFinalGroupRank = function(groupId)
{
    let map = new Map(); //uid => [uid,Obj]
    let rankObjList = [];
    let uidList =  this.getGroupPlayerMap(parseInt(groupId));
    if (!uidList || uidList.length <= 0)
    {
      return;
    }
    
    for (let i = 0; i < uidList.length; i++) {
        const uid = uidList[i];
        if (!uid) continue;
        let obj = this.getPlayerMap(uid);
        map.set(uid, obj)
    }

    for (let [k,v ] of map) 
    {
        rankObjList.push(v);
    }

    //排序
    rankObjList.sort(__score_compare_func);

  this.finalRank.set(groupId, rankObjList);
};

CommunityLeague.prototype.makeTotalLastRoundGroupRank = function(groupId, lastRound)
{
    let historyRankObjMap = this.makeLastRoundGroupRank(groupId, lastRound);
    let leagueName = commonEnum.LEAGUE_NAME.COMMUNITY + "联赛";
    let config = this.getRoundConfig(lastRound + 1);
    let roundName = config.name;
    let rankObjList = 
    {
        historyRankObjMap: historyRankObjMap,
        leagueName: leagueName,
        roundName: roundName,
    };

  return rankObjList;
};

CommunityLeague.prototype.makeLastRoundGroupRank = function(groupId, finalRound)
{
  let map = new Map(); //uid => [uid,Obj]
  let historyRankObjMap = new Map();
  let rankObjList = [];
  let uidList =  this.getGroupPlayerMap(parseInt(groupId));
  if (!uidList || uidList.length <= 0)
  {
    logger.error("makeLastRoundGroupRank: not found groupId player", groupId, finalRound);
    return historyRankObjMap; 
  }
  
  for (let i = 0; i < uidList.length; i++) {
      const uid = uidList[i];
      if (!uid) continue;
      let obj = this.league.newPlayerObj(uid, false);
      map.set(uid, obj)
  }

  for (let round = 1; round <= finalRound; round++) 
  {
      let round_key = this.league.getRoundKey(round, groupId);
      var rankList = this.getRoundRank(round_key);
      for (let i in rankList) 
      {
          const data = rankList[i];
          if(!map.has(data.playerUid)) continue;
          let playerObj = map.get(data.playerUid);
          playerObj.battleCount +=  data.battleCount;
          playerObj.winCount += data.winCount;
          playerObj.drawCount += data.drawCount;
          playerObj.lossCount += data.lossCount;
          playerObj.goalCount += data.goalCount;
          playerObj.missCount +=  data.missCount;
          playerObj.totalScore += data.totalScore;
          playerObj.power = data.power; //使用最近一场的战力
          if (this.league.checkRobot(data.playerUid)) //过滤掉机器人
          {
            playerObj.power = 0;
          }
      }
  }

  for (let [k,v ] of map) 
  {
      rankObjList.push(v);
  }

  //排序
  rankObjList.sort(__score_compare_func);

  let rank = 1;
  for (let i in rankObjList) {
      const data = rankObjList[i];
      let uid = data.playerUid;
      let winCount = data.winCount;
      let lossCount  = data.lossCount;
      let drawCount  = data.drawCount;
      let totalValue = 0;
      if (!this.league.checkRobot(uid))
      {
        totalValue = this.league.getTeamTotalValue(uid);
      }
      
      let obj = {
        winCount: winCount,
        lossCount: lossCount,
        drawCount: drawCount,
        rank: rank,
        totalValue: totalValue,
      };

      historyRankObjMap.set(uid, obj);
      //logger.info("makeFinalGroupRank: groupId, playerUid, ", finalRound, groupId, data.playerUid, obj);
      rank++;
  }

  return historyRankObjMap;
};

CommunityLeague.prototype.getFinalGroupIdRank = function() 
{
  let ctyRank = [];
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) 
  {
      let rankObjList = this.finalRank.get(groupId);
      if (!rankObjList)
      {
        //logger.warn("getFinalGroupIdRank: groupId not rank data", groupId);
        continue;
      }
  
      let rank = 1;
      for (let i in rankObjList) 
      {
        const data = rankObjList[i];
        let uid = data.playerUid;
        if (this.league.checkRobot(uid)) continue;  //过滤机器人
        let simpleRankObj = {
          uid: uid,
          groupId: groupId,
          rank: rank,
          typeId: this.getTypeId(),
        };
    
        ctyRank.push(simpleRankObj);
        rank++;
      }
  }
  return ctyRank;
};

//最近晋级玩家列表
CommunityLeague.prototype.getFinalPrUidList = function() 
{
  let uidList = [];
  let last_round = this.getLastRound();
  if (!last_round) return uidList;
  uidList = this.getPromotionUidList(last_round);
  logger.info("getFinalPrUidList uidList, length", uidList.length);
  return uidList;
};

//第一次联赛挑选前100名玩家到专业联赛
CommunityLeague.prototype.playerPromotionToProfession = function() 
{
  logger.info("playerPromotionToProfession joinerList", this.joinerList.length);
  let rankObjList = [];
  for (let i in this.joinerList) 
  {
    const uid = this.joinerList[i];
    let playerObj = this.getPlayerMap(uid);
    if (!playerObj) continue;
    
    rankObjList.push(playerObj);
  }

  //排序
  rankObjList.sort(__first_compare_func);

  let groupIdRankUidMap = new Map(); //groupId => [uid, uid]
  let groupId = 5;
  let count = 1;
  for (let i in rankObjList) 
  {
    const playerObj = rankObjList[i];
    if (!playerObj) continue;
    const uid = playerObj.playerUid;

    if (!groupIdRankUidMap.has(groupId)) 
    {
      groupIdRankUidMap.set(groupId, []);
    }

    let uidList = groupIdRankUidMap.get(groupId);
    uidList.push(playerObj);

    //删除该Uid
    utils.removeElement(this.joinerList, uid);
    this.delPlayerMap(uid);
    this.delPlayerGroupMap(uid);
    this.delGroupPlayerMap(this.getPlayerGroupMap(uid), uid);

    //logger.info("uid", uid);
    this.finalPromotionMap.set(uid, groupId);
    if (count !== 0 && count % 20 === 0) {
      count = 0;
      groupId--;
    }

    if (groupId <= 0) {
      break;
    }

    count++;
  }

  //把这些玩家发送专业联赛
  this.league.profession.insertProfessionRank(groupIdRankUidMap);
};

//第一次晋级到专业联赛的玩家
CommunityLeague.prototype.getFirstNotifyMailFinalPromotion = function()
{
  let notifyMailList = [];
  for (let [uid, groupId] of this.finalPromotionMap) 
  {
    if (this.league.checkRobot(uid)) continue;
    let pObj = {
      uid: uid,
      name: this.league.getProfessionLeagueName(groupId),
      typeId: this.league.profession.getTypeIdByGroupId(groupId),
      notifyType: commonEnum.FINAL_MAIL_NOTIFY_TYPE.PROMOTION,
    };

    //logger.info("getFirstNotifyMailFinalPromotion uid", pObj);
    notifyMailList.push(pObj);
  }

  logger.info("getFirstNotifyMailFinalPromotion notifyMailList", notifyMailList.length);
  return notifyMailList;
};

CommunityLeague.prototype.CropEnroll = function(maxEnrollCount) 
{
  let rankObjList = [];
  for (let i in this.joinerList) {
    const uid = this.joinerList[i];
    let playerObj = this.getPlayerMap(uid);
    if (!playerObj) {
      continue;
    }
    rankObjList.push(playerObj);
  }

  //排序
  rankObjList.sort(__first_compare_func);

  let count = 1;
  for (let i in rankObjList) {
    const playerObj = rankObjList[i];
    if (!playerObj) 
    {
      continue;
    }

    if(count > maxEnrollCount)
    {
      let uid = playerObj.playerUid;
      this.cropEnrollList.push(uid);
      //删除该Uid
      utils.removeElement(this.joinerList, uid);
      this.delPlayerMap(uid);
      let groupId = this.getPlayerGroupMap(uid);
      this.delPlayerGroupMap(uid);
      this.delGroupPlayerMap(groupId, uid);
      logger.info("CropEnroll: uid", uid);
    }
    
    count++;
  }
};

//报名成功的玩家
CommunityLeague.prototype.getEnrollSuccess = function()
{
  let notifyMailList = [];
  for (let i in this.joinerList) {
    const uid = this.joinerList[i];
    let obj = {
      uid: uid,
      success: commonEnum.ENROLL_NOTIFY.SUCCESS,
      profession: 0,
    };
    notifyMailList.push(obj);
  }
  return notifyMailList;
};

//报名失败的玩家
CommunityLeague.prototype.getEnrollFailed = function()
{
  let notifyMailList = [];
  for (let i in this.cropEnrollList) {
    const uid = this.joinerList[i];
    let obj = {
      uid: uid,
      success: commonEnum.ENROLL_NOTIFY.FAILED,
      profession: 0,
    };
    notifyMailList.push(obj);
  }
  return notifyMailList;
};

//************************************************************消息************************** */

CommunityLeague.prototype.getCurrRound = function()
{
  return this.currRound;
};

//获取修正后轮次
CommunityLeague.prototype.getFixCurrRound = function()
{
  let realCurrRound = this.getCurrRound();
  let fixCurrRound = realCurrRound;
  let battleStatus = this.getConfigBattleStatus(realCurrRound);
  if(realCurrRound > 0 && commonEnum.LEAGUE_BATTLE_STATUS.END !== battleStatus) //比赛未开始、取上一轮
  {
    fixCurrRound = realCurrRound - 1;
    logger.info("getFixCurrRound: realCurrRound, fixCurrRound", realCurrRound, fixCurrRound);
  }

  return fixCurrRound;
};

CommunityLeague.prototype.getBattleIdByRoundGroupId = function(round, groupId, uid)
{
    let battleId = "";
    let round_key = this.league.getRoundKey(round, groupId);
    let battleUidList =  this.getRoundBattleMap(round_key);
    if (!battleUidList)
    {
        logger.info("getBattleIdByRoundGroupId: not found battleUidList! round_key", round_key);
        return battleId;
    }

    for(let idx in battleUidList)
    {
      let __battleId = battleUidList[idx];
      let battleObj =  this.getBattleMap(__battleId);
      if (uid === battleObj.home || uid === battleObj.away )
      {
        battleId = __battleId;
        break;
      }
    }
    return battleId;
};

//检查玩家在某一小组，某一轮次是否
CommunityLeague.prototype.checkBattleScheduleByUid = function(round, groupId, uid)
{
  let battleId = this.getBattleIdByRoundGroupId(round, groupId, uid);
  if ( "" !== battleId)
  {
    return true;
  }

  return false;
}

//获取赛程榜
CommunityLeague.prototype.getRoundScheduleInfo = function(round, groupId)
{
  let roundScheduleData = 
  {
    typeId: this.getTypeId(),
    roundId: round,
    name: "",
    groupId: groupId,
    finalRound: this.getCurrFinalRound(false),
    maxGroupCount: this.groupMaxCount,
    scheduleTime: this.league.getBattleTime(this.getTypeId()),
    scheduleList: [],
  };

  if (round <= 0)
  {
    logger.error("getRoundScheduleInfo: round less 0!", round);
    return roundScheduleData;
  }

  let config= this.getRoundConfig(round);
  if (!config)
  {
    logger.error("getRoundScheduleInfo: round error!", round);
    return roundScheduleData;
  }

  roundScheduleData.name = config.name;
  if (!groupId || groupId <= 0)
  {
    logger.error("getRoundScheduleInfo: groupId error!", round, groupId);
    return roundScheduleData;
  }

  logger.info("getRoundScheduleInfo: round, groupId", round, groupId);
  let currRoundBattleStatus = this.getConfigBattleStatus(this.currRound);
  //不能超过当前轮次，因为当期轮次还没有完成，无法获取下一轮的赛程
  if (round > this.currRound && currRoundBattleStatus === commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN )
  {
    logger.error("getRoundScheduleInfo: round more than currRound!", round, this.currRound);
    return roundScheduleData;
  }

  // if (currRoundBattleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END)//当前轮结束就显示下一轮赛程
  // {
  //   if(this.currRound < this.getMaxRound())
  //   {
  //     roundScheduleData.finalRound = this.currRound + 1;
  //   }
  // }
  let required_every_group_num = config.required_num;

  let scheduleList = [];
  let roundGroupId = this.league.getRoundKey(round, groupId);
  let battleObjList = this.getBattleScheduleMap(roundGroupId);
  let battleUidList = this.getRoundBattleMap(roundGroupId);
  if(battleObjList)
  {
    this.league.preProcessBattleList(battleObjList, required_every_group_num);//人数不够打乱对战列表
  }

  if (!battleObjList) 
  {
    let ScheduleGroupInfo = {
      battleId: "",
      teamA: "",
      teamB: "",
      teamAScore: 0,
      teamBScore: 0,
      beginTime: 0,
      status: 0,
      teamAName: "",
      teamBName: "",
      teamAfaceUrl: "",
      teamBfaceUrl: "",
      winTeam: "",
    };
    scheduleList.push(ScheduleGroupInfo);
    roundScheduleData.scheduleList = scheduleList;
    logger.error("getRoundScheduleInfo: battleObjList error!", round, groupId);
    return roundScheduleData;
  }
  
  let battleStatus = this.getConfigBattleStatus(round);
  for (const idx in battleObjList) 
  {
    let obj = battleObjList[idx];
    let winTeam = "";
    if(battleUidList)
    {
      for(let i in battleUidList)
      {
        let battleObj =this.getBattleMap(battleUidList[i]);
        if(battleObj.home === obj.Host)
        {
          //logger.error("：：：：：：：：：：：：：：：：", obj, battleObj);
          winTeam = battleObj.winTeam;
        }
      }
    }

    if (!obj) continue;
    let uid1 = obj.Host;
    let uid2 = obj.Gust;
    let beginTime = obj.BeginTime;
    let robotA = "robot_" === uid1.substr(0, 6); //home是否为机器人
    let robotB = "robot_" === uid2.substr(0, 6); //away是否为机器人
    //if (robotA && robotB) continue;

    let roomUid = "";
    let teamAScore = 0;
    let teamBScore = 0;
    if (battleStatus !== commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN) {
        let battleId = this.getBattleIdByRoundGroupId(round, groupId, uid1);
        let battleObj =  this.getBattleMap(battleId);
        roomUid = battleObj.roomUid;
        teamAScore = battleObj.homeScore;
        teamBScore = battleObj.awayScore;
    }

    if (robotA)
    {
      uid1 = "";
    }

    if (robotB)
    {
      uid2 = "";
    }

    let ScheduleGroupInfo = {
      battleId: roomUid,
      teamA: uid1,
      teamB: uid2,
      teamAScore: teamAScore,
      teamBScore: teamBScore,
      beginTime: beginTime,
      status: battleStatus,
      teamAName: "",
      teamBName: "",
      teamAfaceUrl: "",
      teamBfaceUrl: "",
      winTeam: winTeam,
    };

    scheduleList.push(ScheduleGroupInfo);
  }

  roundScheduleData.scheduleList = scheduleList;
  logger.info("roundScheduleData", roundScheduleData.scheduleList.length);
  return roundScheduleData;
};

CommunityLeague.prototype.getRoundScoreRankInfo = function(round, groupId)
{
  let roundScoreRankData = 
  {
    typeId: this.getTypeId(),
    roundId: round,
    name: "",
    groupId: groupId,
    finalRound: this.getCurrFinalRound(true),
    maxGroupCount: this.groupMaxCount,
    scheduleTime: this.league.getBattleTime(this.getTypeId()),
    maxRound: this.getMaxRound(),
    teamInfoList: [],
  };
  
  if (round <= 0)
  {
    logger.error("getRoundScoreRankInfo: round less 0!", round);
    return roundScoreRankData;
  }

  logger.info("getRoundScoreRankInfo", round, this.getCurrFinalRound(true), this.getFinalRound(true));
  if (round === this.getFinalRound(true))
  {
    return this.getFinalScoreRankInfo(groupId);
  }

  let config= this.getRoundConfig(round);
  if (!config)
  {
    logger.error("getRoundScoreRankInfo: round error!", round);
    return roundScoreRankData;
  }

  roundScoreRankData.name = config.name;
  if (!groupId || groupId <= 0)
  {
    logger.error("getRoundScoreRankInfo: groupId error!", round, groupId);
    return roundScoreRankData;
  }

  logger.info("getRoundScoreRankInfo: round, groupId", round, groupId);
  //大于当前轮次就返回总榜
  if (round > this.currRound)
  {
    return this.getFinalScoreRankInfo(groupId);
    // logger.error("getRoundScoreRankInfo: round more than currRound!", round, this.currRound);
    // return roundScoreRankData;
  }
  
  let battleStatus = this.getConfigBattleStatus(round);
  if (battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN)
  {
    logger.error("getRoundScoreRankInfo: battleStatus error! round, groupId, battleStatus", round, groupId, battleStatus);
    return roundScoreRankData;
  }

  let teamInfoList = [];
  if (commonEnum.LEAGUE_BATTLE_STATUS.END === battleStatus)
  {
    let roundGroupId = this.league.getRoundKey(round, groupId);
    let rankObjList = this.getRoundRank(roundGroupId);
    if (!rankObjList) 
    {
      logger.error("getRoundScoreRankInfo: rankObjList error!", round, groupId);
      return roundScoreRankData;
    }

    for(let idx in rankObjList)
    {
      let rank = rankObjList[idx];
      //logger.info("rank.playerUid 1", rank.playerUid);
      if ("" === rank.playerUid) continue;
      if (this.league.checkRobot(rank.playerUid)) continue;
    
      let playerObj = {
        playerUid: rank.playerUid,
        name: "",
        faceIcon: 0,
        battleCount: rank.battleCount,
        winCount: rank.winCount,
        drawCount: rank.drawCount,
        lossCount: rank.lossCount,
        goalCount: rank.goalCount,
        missCount: rank.missCount,
        totalScore: rank.totalScore,
        knockoutStatus: commonEnum.KNOCOUT_LEVEL.DOWNGRADE,
      }
      //logger.info("rank.playerUid 2", rank.playerUid);
      teamInfoList.push(playerObj);
    }
  }

  roundScoreRankData.teamInfoList = teamInfoList;
  //logger.info("roundScoreRankData", roundScoreRankData);
  return roundScoreRankData;
};

//最终排名
CommunityLeague.prototype.getFinalScoreRankInfo = function(groupId)
{
  let roundScoreRankData = 
  {
    typeId: this.getTypeId(),
    roundId: this.currRound + 1,//this.getLastRound() + 1,
    name: "总榜",
    groupId: groupId,
    finalRound: this.currRound + 1,//this.getFinalRound(true),
    maxGroupCount: this.groupMaxCount,
    scheduleTime: this.league.getBattleTime(this.getTypeId()),
    maxRound: this.getMaxRound(),
    teamInfoList: [],
  };

   let lastRound = this.getLastRound();
  // if (lastRound != this.getCurrRound()) //还没有到最后一轮
  // {
  //   logger.error("getRoundScoreRankInfo: lastRound error! round, groupId, battleStatus", lastRound, groupId);
  //   return roundScoreRankData;
  // }
  //
   let battleStatus = this.getConfigBattleStatus(lastRound);
  // if (battleStatus !== commonEnum.LEAGUE_BATTLE_STATUS.END) //还没有结束
  // {
  //   logger.error("getRoundScoreRankInfo: battleStatus error! round, groupId, battleStatus", lastRound, groupId, battleStatus);
  //   return roundScoreRankData;
  // }

  let rankObjList = this.finalRank.get(groupId);
  if (!rankObjList)
  {
    logger.error("getRoundScoreRankInfo: rankObjList error! round, groupId, battleStatus", lastRound, groupId, battleStatus);
    return roundScoreRankData;
  }

  let teamInfoList = [];
  for (let i in rankObjList) 
  {
    const rank = rankObjList[i];
    let uid = rank.playerUid;
    if ("" === rank.playerUid) continue;
    if (this.league.checkRobot(uid)) continue;
    
    let playerObj = {
      playerUid: rank.playerUid,
      name: "",
      faceIcon: 0,
      battleCount: rank.battleCount,
      winCount: rank.winCount,
      drawCount: rank.drawCount,
      lossCount: rank.lossCount,
      goalCount: rank.goalCount,
      missCount: rank.missCount,
      totalScore: rank.totalScore,
      knockoutStatus: commonEnum.KNOCOUT_LEVEL.DOWNGRADE,
    }

    teamInfoList.push(playerObj);
  }
  
  roundScoreRankData.teamInfoList = teamInfoList;
  return roundScoreRankData;
};

//检查最终轮是否结束
CommunityLeague.prototype.checkLastRoundEnd = function()
{
  let lastRound = this.getLastRound();
  let currRound = this.getCurrRound();
  let battleStatus = this.getConfigBattleStatus(currRound);
  if (lastRound === currRound && battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END) //还没有到最后一轮
  {
    return true;
  }

  logger.info("checkLastRoundEnd: lastRound, currRound, battleStatus", lastRound, currRound, battleStatus);
  return false;
};

CommunityLeague.prototype.getCurrScheduleInfo =  function(groupId)
{
  let round = this.getFixCurrRound();
  let roundScheduleData = this.getRoundScheduleInfo(round, groupId);
  return roundScheduleData;
};

CommunityLeague.prototype.getCurrScoreRankInfo = function(groupId)
{
  let round = this.getFixCurrRound();
  let roundScoreRankData = this.getRoundScoreRankInfo(round, groupId);
  return roundScoreRankData;
};

CommunityLeague.prototype.getGroupIdByUid = function(uid)
{
  let groupId = this.getPlayerGroupMap(uid);
  if (!groupId)
  {
    return 0;
  }
  return groupId;
};

CommunityLeague.prototype.getBattleObjByRoundGroupId = function(round, groupId, uid)
{
    let round_key = this.league.getRoundKey(round, groupId)
    let battleUidList = this.getRoundBattleMap(round_key);
    if (!battleUidList)
    {
      logger.error("getBattleObjByRoundGroupId: get battleUidList failed!", round, groupId);
      return null;
    }
    
    for (let idx in battleUidList) 
    {
      let battleId = battleUidList[idx];
      let data = this.getBattleMap(battleId);
      if (!data)
      {
        continue;
      }

      let home = data.home;
      let away = data.away;
      if (home === uid || away === uid)
      {
        return data;
      }
    }
    return null;
};

//已经结束比赛的要上积分
CommunityLeague.prototype.addHistory = function(groupId, round, battleObj)
{
  let uidA = battleObj.Host;
  let uidB = battleObj.Gust;
  let beginTime = battleObj.BeginTime;
  let teamA = this.league.getSimpleTeamInfo(uidA);
  let teamB = this.league.getSimpleTeamInfo(uidB);
  let battleStatus = this.getConfigBattleStatus(round);
  if (battleStatus < 0)
  {
    logger.error("addHistory: battleStatus error! round, groupId, battleStatus", round, groupId, battleStatus);
    return null;
  }

  let teamAScore = 0;
  let teamBScore = 0;
  let roomUid = "";
  //只有打完了才有分数
  if (battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END)
  {
    let data = this.getBattleObjByRoundGroupId(round, groupId, uidA);
    if (!battleObj)
    {
      logger.error("addHistory: getBattleObjByRoundGroupId failed", round, groupId, uidA);
      return null;
    }
    teamAScore = data.homeScore;
    teamBScore = data.awayScore;
    roomUid = data.roomUid || "";
  }

  let isNull = 0;
  if (this.league.checkRobot(uidA))
  {
    teamA.playerUid = "";
    isNull = 1;
  }

  if (this.league.checkRobot(uidB))
  {
    teamB.playerUid = "";
    isNull = 1;
  }

  let history = {
    typeId: this.typeId,
    groupId: groupId,
    roundId: round,
    beginTime: beginTime,
    teamA: teamA,
    teamB: teamB,
    teamAScore: teamAScore,
    teamBScore: teamBScore,
    status: battleStatus,
    isNull: isNull,
    roomUid: roomUid
  };

  return history;
};

CommunityLeague.prototype.getHistory = function(uid)
{
  if (!uid)
  {
    logger.error("getHistory: not uid");
    return;
  }

  let personalHistoryInfo = [];
  if(!this.checkIsEnroll(uid)) //未参赛
  {
    logger.warn("getHistory: player not join here", uid);
    return personalHistoryInfo;
  }

  let groupId = this.getPlayerGroupMap(uid);
  if (!groupId || groupId === 0)
  {
    logger.error("getHistory: not found groupId", uid);
    return personalHistoryInfo;
  }

  logger.info("getHistory", groupId, uid, this.currRound);
  if (this.currRound <= 0)
  {
    return personalHistoryInfo;
  }

    //把赛程拿出来
  for (let round = 1; round <= this.currRound; round++) 
  {
    let round_key = this.league.getRoundKey(round, groupId)
    let objList = this.getBattleScheduleMap(round_key);
    //logger.error("getHistory: ", round_key, objList);
    if (!objList) continue;
    for(let idx in objList)
    {
      let battleObj = objList[idx];
      if (!battleObj) continue;
      if (uid === battleObj.Host || uid === battleObj.Gust)
      {
        let history = this.addHistory(groupId, round, battleObj);
        if (!history)
        {
          continue;
        }
        personalHistoryInfo.push(history);
      }
    }
  }
  return personalHistoryInfo;
};
//个人赛程
CommunityLeague.prototype.getAllSchedule = function(uid)
{
  if (!uid)
  {
    logger.error("getAllSchedule: not uid");
    return;
  }

  let personalHistoryInfo = [];
  if(!this.checkIsEnroll(uid)) //未参赛
  {
    logger.warn("getAllSchedule: player not join here", uid);
    return personalHistoryInfo;
  }

  let groupId = this.getPlayerGroupMap(uid);
  if (!groupId || groupId === 0)
  {
    logger.error("getAllSchedule: not found groupId", uid);
    return personalHistoryInfo;
  }

  logger.info("getAllSchedule", groupId, uid, this.currRound);
  if (this.currRound <= 0)
  {
    return personalHistoryInfo;
  }

  //把赛程拿出来
  for (let round = 1; round <= this.currRound; round++)
  {
    let round_key = this.league.getRoundKey(round, groupId)
    let objList = this.getBattleScheduleMap(round_key);
    //logger.error("getHistory: ", round_key, objList);
    if (!objList) continue;
    for(let idx in objList)
    {
      let battleObj = objList[idx];
      if (!battleObj) continue;
      if (uid === battleObj.Host || uid === battleObj.Gust)
      {
        let history = this.addHistory(groupId, round, battleObj);
        if (!history)
        {
          continue;
        }
        history.seasonId = this.uid;
        personalHistoryInfo.push(history);
      }
    }
  }
  return personalHistoryInfo;
};

CommunityLeague.prototype.getMaxGroupCount = function()
{
  return this.groupMaxCount;
};

//总榜
CommunityLeague.prototype.getMaxRound = function()
{
  let lastRound = this.getLastRound();
  return lastRound + 1;
};

//*************************************辅助函数*************************************************** */
//battleScheduleMap
CommunityLeague.prototype.setBattleScheduleMap = function(round_key, battleUidList)
{
  this.battleScheduleMap.set(round_key, battleUidList);
};

CommunityLeague.prototype.getBattleScheduleMap = function(round_key)
{
  return this.battleScheduleMap.get(round_key);
};

CommunityLeague.prototype.hasBattleScheduleMap = function(round_key)
{
  return this.battleScheduleMap.has(round_key);
};

//playerMap
CommunityLeague.prototype.setPlayerMap = function(uid, playerObj) 
{
  this.playerMap.set(uid, playerObj);
};

CommunityLeague.prototype.getPlayerMap = function(uid) 
{
  return this.playerMap.get(uid);
};

CommunityLeague.prototype.hasPlayerMap = function(uid) 
{
  return this.playerMap.has(uid);
};

CommunityLeague.prototype.delPlayerMap = function(uid) 
{
  return this.playerMap.delete(uid);
};

CommunityLeague.prototype.playerCount =  function()
{
  let count = 0;
  for (let [k, v] of this.playerMap) 
  {
    count++;
  }
  return count;
};

//playerGroupMap
CommunityLeague.prototype.setPlayerGroupMap = function(uid, groupId) {
  this.playerGroupMap.set(uid, groupId);
};

CommunityLeague.prototype.getPlayerGroupMap = function(uid) {
  return this.playerGroupMap.get(uid);
};

CommunityLeague.prototype.hasPlayerGroupMap = function(uid) {
  return this.playerGroupMap.has(uid);
};

CommunityLeague.prototype.delPlayerGroupMap = function(uid) {
  return this.playerGroupMap.delete(uid);
};

//groupPlayerMap
CommunityLeague.prototype.setGroupPlayerMap = function(groupId, uidList) {
  this.groupPlayerMap.set(groupId, uidList);
};

CommunityLeague.prototype.getGroupPlayerMap = function(groupId) {
  return this.groupPlayerMap.get(groupId);
};

CommunityLeague.prototype.hasGroupPlayerMap = function(groupId) {
  return this.groupPlayerMap.has(groupId);
};

CommunityLeague.prototype.delGroupPlayerMap = function(groupId, uid)
{
  let uidList = this.groupPlayerMap.get(groupId);
  if (!uidList)
  {
    return;
  }
  utils.removeElement(uidList, uid);
};

//PromotionMap
CommunityLeague.prototype.setPromotionMap = function( groupId, promotionUidList) {
  this.promotionMap.set(groupId, promotionUidList);
};

CommunityLeague.prototype.getPromotionMap = function(groupId) {
  return this.promotionMap.get(groupId);
};

CommunityLeague.prototype.hasPromotionMap = function(groupId) {
  return this.promotionMap.has(groupId);
};

//EliminateMap
CommunityLeague.prototype.setEliminateMap = function( round_key, uidList) {
  this.eliminateMap.set(round_key, uidList);
};

CommunityLeague.prototype.getEliminateMap = function(round_key) {
  return this.eliminateMap.get(round_key);
};

CommunityLeague.prototype.hasEliminateMap = function(round_key) {
  return this.eliminateMap.has(round_key);
};

//RoundBattleMap
CommunityLeague.prototype.setRoundBattleMap = function(round_key, battleUidList) 
{
  this.roundBattleMap.set(round_key, battleUidList);
};

CommunityLeague.prototype.getRoundBattleMap = function(round_key) 
{
  return this.roundBattleMap.get(round_key);
};

CommunityLeague.prototype.hasRoundBattleMap = function(round_key) 
{
  return this.roundBattleMap.has(round_key);
};

//battleMap
CommunityLeague.prototype.setBattleMap = function(battleUid, battleObj) {
  this.battleMap.set(battleUid, battleObj);
};

CommunityLeague.prototype.getBattleMap = function(battleUid) {
  return this.battleMap.get(battleUid);
};

CommunityLeague.prototype.hasBattleMap = function(battleUid) {
  return this.battleMap.has(battleUid);
};

//roundRank
CommunityLeague.prototype.setRoundRank = function(round_key, rankList) {
  this.roundRank.set(round_key, rankList);
};

CommunityLeague.prototype.getRoundRank = function(round_key) {
  return this.roundRank.get(round_key);
};

CommunityLeague.prototype.hasRoundRank = function(round_key) {
  return this.roundRank.has(round_key);
};

CommunityLeague.prototype.getTypeId = function()
{
  return this.typeId;
}
//*************************************辅助函数*************************************************** */
