/**
 * Created by aaa on 2015/5/27.
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var dataApi = require('../../util/dataApi');
var utils = require('../../util/utils');
var commonEnum = require('../../../../shared/enum');
var Calc = require('../../util/calc');
var Code = require('../../../../shared/code');
var TimeUtils = require('../../util/timeUtils');
let debugCfg = require('../../../config/debugConfig');

var Heros = function(player) {
    this.player = player;
    this.uid = player.playerId;
    this.allheros = new Map(); // key => value
    this.heroTrain = new Map(); // key => value  //球员特训
    this.heroStar = new Map();  //球员升星
    this.heroSkill = new Map()  //球员技能
    this.seriousInjuryTime = TimeUtils.now();  //重伤时间
    this.firstGetHero = [];         //记录获得球员
    this.fixId = 0;
};

util.inherits(Heros, EventEmitter);

module.exports = Heros;

Heros.prototype.initByDB = function(doc) {
    this.uid = doc.uid;
    this.allheros = utils.toMap(doc.heros) || new Map();
    this.heroTrain = utils.toMap(doc.heroTrain) || new Map();
    this.heroStar = utils.toMap(doc.heroStar) || new Map();
    this.heroSkill = utils.toMap(doc.heroSkill) || new Map();
    this.seriousInjuryTime = doc.seriousInjuryTime || TimeUtils.now();
    this.firstGetHero = doc.firstGetHero || [];
    this.fixId = doc.fixId || 0;
};

//属性修复调用口详见 player.checkFix
Heros.prototype.fixAttr = function()
{
    for (let [k, v] of this.allheros) {
        this.fixHeroAttr(k);
        this.fixHeroSkillData(k);
        this.onceFixHeroSkill(k);
        this.fixHeroStarData(k);
        this.checkHeroTrainData(k);
        this.onceFixHeroSkillAct(k);
        this.checkHeroSkillAct(k);
        this.fixInitProperty(k, v.ResID);
        //最后计算球员属性
        this.reCalcAttrRevision(k);
        // this.reCalcAttr(k);
    }
    //计算一下阵容数据
    this.player.teamFormations.recalcAllTeamFormationAttr();
    if(this.fixId === 0) {
        this.fixId = 1;
    }
};

Heros.prototype.onceFixHeroSkillAct = function(uid)
{
    //清技能加成
    let hero = this.getHero(uid);
    let oneLevelAttr = hero["oneLevelAttr"];
    for (let key in oneLevelAttr) {
        let attrInfo = oneLevelAttr[key];
        if (key !== "ResistanceDamage") {
            if (!attrInfo.SkillAct) {
                attrInfo.SkillAct = 0;
                attrInfo.Skill = 0;
            }else {
                attrInfo.SkillAct = 0;
                attrInfo.Skill = 0;
            }
        }
    }
};

Heros.prototype.fixHeroAttr = function(uid)
{
    var hero = this.getHero(uid);
    var oneLevelAttr = hero["oneLevelAttr"];
    for (var key in oneLevelAttr) {
        const attrInfo = oneLevelAttr[key];
        if (key !== "ResistanceDamage") {
            if (!attrInfo.Trainer)
            {
                attrInfo.Trainer = 0;
            }

            if (!attrInfo.Skill)
            {
                attrInfo.Skill = 0;
            }

            if (!attrInfo.Cur)
            {
                attrInfo.Cur = 0;
            }

            if (!attrInfo.SkillAct)
            {
                attrInfo.SkillAct = 0;
            }

            if(!attrInfo.GroundTrain) {
                attrInfo.GroundTrain = 0;
            }

            if(!attrInfo.AttackTactics) {
                attrInfo.AttackTactics = 0;
            }

            if(!attrInfo.DefTactics) {
                attrInfo.DefTactics = 0;
            }

            if(typeof (attrInfo.TrainerSkillAtt) === "undefined") {
                attrInfo.TrainerSkillAtt = 0;
            }

            if(typeof(attrInfo.BeliefSkillAtt) === "undefined") {
                attrInfo.BeliefSkillAtt = 0;
            }
        }
    }

    if(!hero.BattleNum) {
        hero.BattleNum = 0;
    }
    
    if(!hero.Status) {
        hero.Status= commonEnum.HERO_STATUS.COMMONLY;      //球员状态
    }

    if(hero.TreatyDay === undefined) {
        let treatyDay = dataApi.allData.data["SystemParam"][commonEnum.HERO_TREATY.TREATY_DAY].Param;
        hero.TreatyDay = treatyDay;
    }

    if(!hero.TreatyReTime) {
        hero.TreatyReTime = TimeUtils.now();
    }

    if (!hero.Fatigue) {
        hero.Fatigue = 0;
    }

    if(!hero.Health) {
        hero.Health = commonEnum.HERO_HEALTH_STATUS.HEALTH;
    }

    if(!hero.FatigueRatio) {
        hero.FatigueRatio = 1;
    }

    if(!hero.ReTimeFatigue) {
        hero.ReTimeFatigue = TimeUtils.now();
    }

    if(!hero.isTreat) {
        hero.isTreat = 0;
    }

    if(!hero.isTrain) {
        hero.isTrain = 0;
    }

    if(!hero.TrainCount) {
        hero.TrainCount = 0;
    }

    if(!hero.isLockTrain) {
        hero.isLockTrain = 0;
    }

    if(!hero.isStage) {
        let arr = [1, 1, 1, 1, 1, 1];
        hero.isStage = arr;
    }

    if(!hero.OldBreakOut) {
        let sum = 30;
        for(let i in hero.Breakthrough) {
            sum += hero.Breakthrough[i];
        }
        hero.OldBreakOut = sum;
    }

    if(typeof(hero.isDie) === "undefined") {
        hero.isDie = 0;  //0 未退役  1已退役
    }

    if(typeof(hero.leftTime) === "undefined") {
        let initLeftTime = Math.floor(hero.IsInitial * dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.LeagueSeason].Param * 86400000);
        hero.leftTime = TimeUtils.beginningOfTodayByTime(hero.CreateTime) + initLeftTime;
    }

    //状态连续降级3次
    if(typeof(hero.ThreeDropNum) === "undefined") {
        hero.ThreeDropNum = 0;
    }

    //10次状态未到巅峰
    if(typeof(hero.TenDropNum) === "undefined") {
        hero.TenDropNum = 0;
    }

    if(typeof(hero.LifeNum) === "undefined") {
        hero.LifeNum = 0;
    }
};

Heros.prototype.fixHeroSkillData = function(uid)
{
    let heroSkill = this.getOneHeroSkill(uid);
    if(!heroSkill) {
        //logger.error("yyyyyyyyyyyyyyyyyyyyyyyyyyy", uid);
        return;
    }
    if(!heroSkill.AttributeValue) {
        heroSkill.AttributeValue = 1;
    }
};

Heros.prototype.initByConfig = function() 
{

};

Heros.prototype.toJSONforDB = function() {
    var heros = {
        uid: this.uid,
        heros: utils.toArray(this.allheros),
        heroTrain: utils.toArray(this.heroTrain),
        heroStar: utils.toArray(this.heroStar),
        heroSkill: utils.toArray(this.heroSkill),
        seriousInjuryTime: this.seriousInjuryTime,
        fixId: this.fixId
    };
    //logger.info("toJSONforDB", this.uid);
    return heros;
};

Heros.prototype.toJSONforClient = function() {
    return this.makeClientHeroList();
};


/** //创建一个球员
 * @param  {resId} 球员resId
 * @returns newHero 新球员对象
 */
Heros.prototype.addHero = function(resId, isNewer) {
    var hero = this.newHero(resId);
    var uid = hero.Uid;
    this.allheros.set(uid, hero);
    //特训初始化
    let train = this.initTrain(uid);
    this.heroTrain.set(uid, train);

    //升星初始化
    let star = this.initHeroStar(uid);
    this.heroStar.set(uid, star);

    //球员技能
    let skill = this.initHeroSkill(uid);
    this.heroSkill.set(uid, skill);

    //记录首次获得球员
    if(this.firstGetHero.indexOf(resId) === -1) {
        this.firstGetHero.push(resId);
    }

    //重新计算属性
    this.reCalcAttrRevision(uid);
    // this.reCalcAttr(uid);
    //除去系统送的球员
    if(isNewer !== 1 || !isNewer) {
        //触发任务
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.ELEVEN, resId);
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.SIX);
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.THREE);
    }

    //记录新增球员
    this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.ADD_HERO, [resId], {});

    return this.allheros.get(uid);
};

/** //创建一个新手球员(登陆创角流程使用)
 * @param  {} uid 球员uid
 * @returns {} newHero 新球员对象
 */
Heros.prototype.addHeroNewer = function(resId, isNewer) {
    var newHero = this.addHero(resId, isNewer);
    newHero.IsNewer = 1;     //设置新手球员标志
    return newHero;
};

/** //获取球员属性数据
 * @param  {} config 球员表配置
 * @returns {} base 球员属性对象
 */
function getInitProperty(config){
    var base = {}
    for (var i in commonEnum.ONE_LEVEL_ATTR_NAMES) {
        let key = i;
        let attrInfo = {
            Init: 0,         //初始值
        };
        if (key !== "ResistanceDamage") {
            attrInfo.Init = config[key];
        }
        base[key] = attrInfo;
    }
    //添加技能ID
    var skillList = [];
    for(let index =0; index < config.SkillList.length; index++)
    {
        var skillID =  config.SkillList[index];
        skillList.push(skillID);
    }

    base["SkillList"] =  skillList;
    return base;
};

Heros.prototype.fixInitProperty = function(uid, resId) {
    let bead = resId / 10000;
    let config;
    if(bead < 9) {
        config = dataApi.allData.data["Footballer"][resId];
    } else {
        config = dataApi.allData.data["FootballerPve"][resId];
    }

    let hero = this.getHero(uid)
    let oneLevelAttr = hero.oneLevelAttr
    // let heroStage = hero.isStage;

    for(let i = 1; i < 7; ++i) {
        let initProperty = this.getInitPropertyByType(i, resId, hero.OldBreakOut);
        let stage = this.getCultivateStage(oneLevelAttr, initProperty);
        // logger.error("rrrrrrrrrrrrrrrrr", stage, i);
        if(stage > 9) {
            stage = 9;
        }
        for (let key in initProperty) {
            if (key !== "ResistanceDamage") {
                //如果有已经满了的
                let stageMax = initProperty[key].init * (stage + 1);
                // if (heroStage[i - 1] !== 1 && heroStage[i - 1]) {
                //     heroStage[i - 1] = stage;
                // }
                if (oneLevelAttr[key].Base >= stageMax) {
                    oneLevelAttr[key].Base = stageMax;
                    if (oneLevelAttr[key].Base > initProperty[key].max) {
                        oneLevelAttr[key].Base = initProperty[key].max;
                    }
                }
            }
        }
    }

    for (let i in commonEnum.ONE_LEVEL_ATTR_NAMES) {
        if (i !== "ResistanceDamage") {
            if(config[i] > oneLevelAttr[i].Base) {
                oneLevelAttr[i].Base = config[i];
            }
        }
    }
}

/** //创建一个球员
 * @param  {} resId 球员resId
 * @returns {} newHero 新球员对象
 */
Heros.prototype.newHero = function(resId) {
    var hero = {};
    //通过utils工具生成一个唯一的UID
    hero["Uid"] = utils.syncCreateUid();
    //球员Id
    hero["ResID"] = resId;
    hero["Breakthrough"] = [];          //记录球员突破
    hero["Star"] = 0;                   //球员星级
    hero["TrainCount"] = 0;                //训练次数
    //var config = dataApi.allData.data["Footballer"][resId];
    var config;
    let bead = resId / 10000;
    if(bead < 9)
    {
        config = dataApi.allData.data["Footballer"][resId];
    }
    else
    {
        config = dataApi.allData.data["FootballerPve"][resId];
    }
    let treatyDay = dataApi.allData.data["SystemParam"][commonEnum.HERO_TREATY.TREATY_DAY].Param;
    hero["IsNewer"] = 0;                        //是否为新手球员 默认为0
    hero["IsInitial"] = config.IsInitial;       //是否会退役  0:不会   n:退役的剩余赛季数
    hero["BattleNum"] = 0;                      //战斗场数
    hero["Fatigue"] = 0;                     //疲劳值
    hero["ReTimeFatigue"] = TimeUtils.now();               //恢复疲劳值时间
    hero["FatigueRatio"] = 1;                //疲劳衰减系数
    // let rand = Math.floor(Math.random() * 4 + 1);
    // hero["Health"] = rand;    //健康状态
    hero["Health"] = commonEnum.HERO_HEALTH_STATUS.HEALTH;    //健康状态
    hero["Status"] = commonEnum.HERO_STATUS.COMMONLY;      //球员状态
    // hero["TreatyDay"] = 0;                         //合约天数
    hero["TreatyDay"] = treatyDay;                         //合约天数
    hero["TreatyReTime"] = TimeUtils.now();                //合约刷新时间
    hero["isUseCareerMedicine"] = 0;                       //是否使用过青春永驻药剂，每个球员只限使用一次
    hero["isTreat"] = 0;                       //是否在治疗中   0不在  1在
    hero["isTrain"] = 0;                       //是否在训练中   0不在  1在
    hero["isLockTrain"] = 0;                   //是否锁定训练   0没有  1锁定
    hero["isStage"] = [1, 1, 1, 1, 1, 1];
    hero["OldBreakOut"] = 30;               //突破最大值
    hero["LifeNum"] = 0;                    //续约生涯次数
    //属性
    var oneLevelAttr = {};   //一级属性
    var twoLevelAttr = {};   //二级属性
    for (var i in commonEnum.ONE_LEVEL_ATTR_NAMES) {
        if (i === "ResistanceDamage") {
            continue;
        }
        let key = i;
        const value = commonEnum.ONE_LEVEL_ATTR_NAMES[i];
        let attrInfo = {
            Type: 0,         //类型
            Cur: 0,          //当前值
            //Init: 0,         //初始值
            Base: config[key],         //基础值
            Skill: 0,        //技能附加值
            HellTrain: 0,    //培养值
            League: 0,       //联盟附加值
            Qualificate: 0,  //特训
            UpgradeStar: 0,  //升星
            Inspire: 0,       //鼓舞
            Handbook: 0,      //图鉴加成
            SkillAct: 0,      //技能加成系数

            Trainer: 0,       //教练属性加成

            //必须存储的字段
            OldBase: 0,       //最大值基础值
            SkillBuffRate: 0,   //技能buffer
            GroundTrain: 0,     //球场泡澡加成值
            AttackTactics: 0,   //进攻战术加成
            DefTactics: 0,      //防守战术加成
            TrainerSkillAtt: 0,  //教练技能加成
            BeliefSkillAtt: 0     //信仰技能加成值
        };

        attrInfo.Type = value;
        //logger.error("newHero******************* index ", key, value, attrInfo);
        oneLevelAttr[key] = attrInfo;
    }

    for (var i in commonEnum.TWO_LEVEL_ATTR_NAMES) {
        const key =  i;
        const value = commonEnum.TWO_LEVEL_ATTR_NAMES[i];
        let attrInfo = {
            Type: 0,         //类型
            Cur: 0,          //当前值
            Init: 0,         //初始值
            Base: 0,         //基础值
            Skill: 0,        //技能附加值
            HellTrain: 0,    //培养值
            League: 0,       //联盟附加值
            Qualificate: 0,  //特训值
            UpgradeStar: 0,  //升星
            Inspire: 0,      //鼓舞
            
            //必须存储的字段
            OldBase: 0,       //最大值基础值
            SkillBuffRate: 0,   //技能buffer
        };
        attrInfo.Type = value;
        //logger.error("newHero******************* index ", key, value, attrInfo);
        twoLevelAttr[key] = attrInfo;
    }

    let initLeftTime = Math.floor(hero.IsInitial * dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.LeagueSeason].Param * 86400000);
    hero["CreateTime"] = TimeUtils.now(); //获得球员时间
    hero["leftTime"] = TimeUtils.beginningOfToday() + initLeftTime;  //退役时间;
    hero["oneLevelAttr"] = oneLevelAttr;
    hero["twoLevelAttr"] = twoLevelAttr;
    hero["AttackAndDefend"] = { position: "", attack: 0, defend: 0 }; //teamFormationId => { position: "GK", attack: xx, defend: xxx};
    hero["isDie"] = 0;    //0 未退役  1已退役
    hero["ThreeDropNum"] = 0;  //状态3连降级次数
    hero["TenDropNum"] = 0;    //状态10连未巅峰次数
    //logger.error("newHero******************* hero ", hero);
    return hero;
};

/** 删除英雄
 * @param  {} uid 球员uid
 * @returns 无
 */
Heros.prototype.delHero = function (uid) {
    //删除对象
    this.allheros.delete(uid);
    logger.info("delete hero *******Uid", uid);
    return Code.OK;
};

/**
 * 解雇球员
 * @param  {} uid 球员uid
 */
Heros.prototype.checkHeroStatus = function (heroUidList) {
    let ret = Code.OK;
    for(let i = 0; i < heroUidList.length; ++i) {
        let uid = heroUidList[i];
        //检查是否有这个球员
        let hero = this.allheros.get(uid);
        if(!hero) {
            ret = Code.HERO_CODE.NOT_HAVE;
            break; 
        }

        //检查是否在阵容中
        let teamList = this.player.teamFormations.checkHeroInFormation(uid);
        if(teamList.length > 0) {
            ret = Code.HERO_CODE.IN_TEAM;
            break;
        }

        //检查是否在治疗中
        if(hero.isTreat) {
            ret = Code.HERO_CODE.IN_TREAT;
            break;
        }

        //检查是否在训练中
        if(hero.isTrain) {
            ret = Code.HERO_CODE.IN_TRAIN; 
            break;
        }
    }

    return ret;
}

//删除球员数据
Heros.prototype.delHeroData = function (uid) { 
    this.allheros.delete(uid);
    this.heroTrain.delete(uid);
    this.heroStar.delete(uid);
    this.heroSkill.delete(uid);
    logger.info("delHeroData: uid", uid);
}


/**
 * 批量解雇球员
 * @param  {} uid 球员uid
 * @param  {} type 解雇类型   1免费解约  2懂币解约  3解雇
 * @param  index  1获取解约信息  2解约  3解雇
 */
Heros.prototype.batchDismissHero = function (heroUidList, type, index) {
    let ret = {code: Code.FAIL, itemInfo: []};
    //没有选中球员
    if(!heroUidList || heroUidList.length < 1) {
        ret.code = Code.HERO_CODE.NOT_CHOOSE;
        return ret;
    }

    //参数检查
    if(!type || type < 1 || type > 3 || !index || index < 1 || index > 3) {
        ret.code = Code.PARAM_FAIL;
        return ret;
    }

    //先检查
    let retCode = this.checkHeroStatus(heroUidList);
    if(retCode !== Code.OK) {
        ret.code = retCode;
        return ret;
    }

    if(type === 2 && index !== 1) {
        let needGold = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.GOLD_NUM].Param;
        //检查钱是否足够
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, needGold)){
            ret.code = Code.GOLD_FALL;
            return ret;
        }
        //扣钱
        this.player.deductMoney(commonEnum.PLAY_INFO.gold, needGold);
    }

    let allItem = [];
    for(let i = 0; i < heroUidList.length; ++i) {
        let uid = heroUidList[i];
        let hero = this.allheros.get(uid);
        if(!hero) {
            continue;
        }
        let resId = hero.ResID;
        let oneLevelAttr = hero.oneLevelAttr;
        let breakOutNum = hero.OldBreakOut;
        let star = hero.Star;
        //返还球探体力
        let scoutInfo = this.restitutionScoutEnergy(resId, type, star, index);
        allItem.push.apply(allItem,scoutInfo);
        //返还培养消耗
        if(index !== 3) {
            let itemList = this.restitutionTrainCost(oneLevelAttr, resId, type, breakOutNum);
            allItem.push.apply(allItem,itemList);
        }
        //删除球员数据
        if(index === 2 || index === 3) {
            this.player.footballGround.delRetirementListAndHofHero(uid);
            this.delHeroData(uid);
            //记录解雇球员
            this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.DEL_HERO, [resId], {});
            logger.info("dismissHero: hero Uid", uid);
        }

    }

    let addItemList = this.removeRepetition(allItem);
    if(index === 2) {
        for(let i in addItemList) {
            //过滤球探体力
            if(addItemList[i].id !== 6) {
                if(addItemList[i].num >= 1) {
                    this.player.bag.addItem(addItemList[i].id, addItemList[i].num);
                }
            }
        }
        this.player.updateBag();
    }

    ret.code = Code.OK;
    ret.itemInfo = addItemList;
    return ret;
}

//合并同类
Heros.prototype.removeRepetition = function(arr) {
    let toClient = new Map();
    for(let i in arr) {
        if(toClient.has(arr[i].id)) {
            let obj = toClient.get(arr[i].id);
            obj.num += arr[i].num;
        }else {
            toClient.set(arr[i].id, {id:arr[i].id, num:arr[i].num});
        }
    }

    let toArr = [];
    let index = 0;
    for (let [k, v] of toClient) {
        toArr[index] = {};
        toArr[index].id = v.id;
        toArr[index].num = v.num;
        index++;
    }

    return toArr;
}

Heros.prototype.restitutionScoutEnergy = function(resId, type, star, index) {
    let config = dataApi.allData.data["Footballer"][resId];
    if(!config) {
        return [];
    }

    if(!config.Return) {
        config.Return = 0;
    }

    let cash = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.NORMAL_STAR_TERMINATION].Param / 100;
    let gold = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.GOLD_STAR_TERMINATION].Param / 100;
    let addNum = 0;
    if(type === 1) {
        addNum = Math.floor(config.Return * star * cash) + config.Return;
    }else if(type === 2){
        addNum = Math.floor(config.Return * star * gold) + config.Return;
    }else  {
        addNum = config.Return;
    }

    if(index === 2 || index === 3) {
        this.player.scout.addScoutEnergyByPlayer(addNum);
    }

    let item = [{id: 6, num: addNum}];
    return item;
}

/*
* 返回培养消耗
*  resId:  球员id
*  type: 解雇类型 1免费解雇  2懂币解雇
*
* */
Heros.prototype.restitutionTrainCost = function(oneLevelAttr, resId, type, breakOutNum) {
    let cashType = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.NORMAL_TERMINATION].Param / 100;
    let goldType = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.GOLD_TERMINATION].Param / 100;
    let costList = [];
    //i 代表大类
    //大属性
    for(let i = 1; i < 7; ++i) {
        let initProperty = this.getInitPropertyByType(i, resId, breakOutNum);
        let stage = this.getCultivateStage(oneLevelAttr, initProperty);
        let index = 1;
        //小属性阶段s
        for(let j = stage; j >= 1; j--) {
            //阶段总属性
            let sum = 0;
            //培养上下限平均值
            let randAve = 0;
            let config = {};
            //返还数量
            let trainNum = 0;
            //阶段消耗
            let stageNum = 0;
            for(let m in initProperty) {
                let nowStageMax = initProperty[m].init * (j + 1);
                let beforeStageMax = initProperty[m].init * j;
                if (m !== "ResistanceDamage") {
                    config = this.getCultivateConfigByStage(j);
                    if (JSON.stringify(config) === "{}") {
                        continue;
                    }

                    //培养上下限平均值
                    randAve = Math.ceil((config.lowerLimit + config.upperLimit) / 2);

                    if(type === 1) {
                        stageNum = config.itemNum;
                    } else  {
                        stageNum = config.costMoney;
                    }

                    //首次
                    if (index === 1) {
                        sum += oneLevelAttr[m].Base - beforeStageMax;
                    } else {
                        sum += nowStageMax - beforeStageMax;
                    }
                }
            }

            if (JSON.stringify(config) === "{}") {
                index++;
                continue;
            }

            let obj = {};
            //免费
            if(type === 1) {
                trainNum = Math.floor((sum / 3 / randAve) * cashType) * stageNum;
                obj.id = config.itemId;
                obj.num = trainNum;
                costList.push(obj);
            }else {
                trainNum = Math.floor((sum / 3 / randAve) * goldType ) * stageNum;
                obj.id = 1;  //欧元
                obj.num = trainNum;
                costList.push(obj);
            }
            index ++;
        }
    }

    return costList;
};


/** 获取一个球员
 * @param  {} uid 球员uid
 * @returns 无
 */
Heros.prototype.getHero = function(uid) {
    return this.allheros.get(uid);
};

/** 获取球员列表
 * @returns 无
 */
Heros.prototype.getHeroList = function() {
    return utils.toArray(this.allheros);
};

/** 获取首次获得球员列表
 * @returns 无
 */
Heros.prototype.getFirstGetHeroList = function() {
    for(let [uid, v] of this.allheros) {
        //记录首次获得球员
        if(this.firstGetHero.indexOf(v.ResID) === -1) {
            this.firstGetHero.push(v.ResID);
        }
    }
    return this.firstGetHero;
};

//球员数量
Heros.prototype.getHeroListCount = function() {
    let count = 0;
    for(let [ uid, obj ] of this.allheros)
    {
        if (!uid) continue;
        count++;
    }

    return count;
};

function __rating_compare_func(rankObj1, rankObj2) {
	let rating1 = rankObj1.rating;
	let rating2 = rankObj2.rating;
	if (rating1 !== rating2 ) { 
		 //降序
		if (rating1 < rating2) {
            return 1;
        }else if (rating1 > rating2) {
            return -1;
        }
        else {
            return 0;
        }
	}
	return 0;
}

//球员UID List
Heros.prototype.getHeroUidRatingList= function() {
    var sortList = [];
    var uidList = [];
    for(let [ uid, obj ] of this.allheros)
    {
        if (!uid || "" === uid) continue;
        var rankObj = {
            uid: uid,
            rating: this.getRating(uid)
        };

        sortList.push(rankObj);
    }

    //排序
     sortList.sort(__rating_compare_func);

	//打印
	for(let key in sortList) {
        const rankObj =  sortList[key];
        uidList.push(rankObj.uid);
		logger.info("getHeroUidRatingList after", rankObj);
    }

    logger.info("getHeroUidRatingList uidList", uidList);

    return uidList;
};

//计算球员教练属性
Heros.prototype.calcCoachAttr = function(uid)
{   
    // this.player.footballGround.calcHeroTrainerAttr(uid);
    this.player.footballGround.checkHeroHandbookAttr(uid);
};

/** 计算一级属性
 * @param  {} uid 球员uid
 * @returns 无
 */
Heros.prototype.calcOneLevelAttr = function(uid) {
    var hero = this.allheros.get(uid);
    var oneLevelAttr = hero["oneLevelAttr"];
    var resID = hero["ResID"];
    //var config = dataApi.allData.data["Footballer"][resID];
    var config;
    let bead = resID / 10000;
    if(bead < 9)
    {
        config = dataApi.allData.data["Footballer"][resID];
    }
    else
    {
        config = dataApi.allData.data["FootballerPve"][resID];
    }

    //logger.error("addHero object****************************************************", this.allheros.get(uid));
    //logger.debug("trainCount:", trainCount);
    let heroStar = this.getOneHeroStar(uid);
    if(!heroStar) {
        logger.error("hero Start fail"+ uid + "playerId:" + this.player.playerId);
        return;
    }
    let statusConfig = dataApi.allData.data["FootballerForm"][hero.Status];
    if(!statusConfig) {
        return;
    }
    //状态加成系数
    let addCoefficient = statusConfig.Ratio / 10000;  

    //合约加成
    let treatyRatio = 1;
    if (hero.TreatyDay < 1) {
       let treatyConfig = dataApi.allData.data["SystemParam"][commonEnum.HERO_TREATY.DECAY_RATIO].Param;
       if(!treatyRatio) {
           return;
       }
       treatyRatio = treatyConfig / 100;
    }

    //健康状态加成
    let fatigueRatio = 1;
    if(hero.Health !== commonEnum.HERO_HEALTH_STATUS.HEALTH) {
        fatigueRatio = (100 - hero.FatigueRatio) / 100;
    }

    //退役衰减系数
    let outRatio = 1;
    if(hero.LifeNum < 3) {     //未满3次需要算生涯衰减
        if(hero.isDie === 1) {   //已经在退役列表
            outRatio = 1;
        }else {
            //检查球员是否在任意阵容中
            let teamList = this.player.teamFormations.checkHeroInArbitrarilyFormation(uid);
            if(teamList.length > 0) {
                if(TimeUtils.futureDayInterval(hero.leftTime) <= 0) {
                    let ratio = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.RETIREMENT].Param;
                    outRatio = (100 - ratio) / 100;
                }else {
                    outRatio = 1;
                }
            }else{
                outRatio = 1;
            }
        }
    }


    //计算一级属性
    for (var key in oneLevelAttr) {
        const attrInfo = oneLevelAttr[key];
        if (key !== "ResistanceDamage") {
            //1.计算基础数值  attrInfo.Base  初始值+培养值
            //attrInfo.Base = this._InnerCalcBaseValue(initProperty[key].Init, attrInfo.HellTrain);
            // attrInfo.HellTrain = this._InnerCalcHellTrainValue(attrInfo.HellTrain);

            //logger.info("calcOneLevelAttr: attrInfo 1", attrInfo);

            attrInfo.League = this._InnerCalcLeagueValue(config, 0);
            attrInfo.Qualificate = this._InnerCalcQualificateValue(attrInfo.Qualificate);
            if(heroStar.Property[key] === undefined) {
                logger.error("heroStar is fail" + this.player.playerId + heroStar.Property + key);
                continue;
            }
            attrInfo.UpgradeStar = this._InnerCalcUpgradeStarValue(heroStar.Property[key]);
            attrInfo.Inspire = this._InnerCalcInspireValue(config, 0);
            if(attrInfo.SkillAct !== 0) 
            {
                attrInfo.Skill = utils.MathRound((attrInfo.Base * attrInfo.SkillAct) / 100);
            }
            // logger.error("战术加成--------------------------------", attrInfo.AttackTactics + attrInfo.DefTactics)
           // logger.info("calcOneLevelAttr: attrInfo 2 ", attrInfo);
            //(即属性的基础值+地狱集训附加值+联盟技能附加值+球员升星附加值 + 特训值 +图鉴激活值 + 技能附加值 + 球场泡澡加成+ 进攻战术加成+防守战术加成 + 教练技能加成+信仰技能) * 合约加成 * 伤病系数 * 退役衰减系数
            //状态加成
            let statusAdd = utils.MathRound(attrInfo.Base * addCoefficient - attrInfo.Base);
            // logger.error("状态加成--------------", statusAdd, addCoefficient);
            attrInfo.Cur = utils.MathRound((attrInfo.Base + attrInfo.Qualificate + attrInfo.League
                + attrInfo.UpgradeStar + attrInfo.Handbook + attrInfo.Skill + attrInfo.Trainer + attrInfo.GroundTrain + attrInfo.AttackTactics + attrInfo.DefTactics + statusAdd + attrInfo.TrainerSkillAtt + attrInfo.BeliefSkillAtt) * treatyRatio * fatigueRatio * outRatio);
            //logger.info("calcOneLevelAttr: attrInfo.Base", attrInfo.Cur);
            //logger.info("attrInfo.Base, attrInfo.Qualificate , attrInfo.League , attrInfo.UpgradeStar , attrInfo.Handbook , attrInfo.Skill , attrInfo.Trainer,  addCoefficient , treatyRatio , fatigueRatio , outRatio");
            //logger.info(attrInfo.Base, attrInfo.Qualificate , attrInfo.League , attrInfo.UpgradeStar , attrInfo.Handbook , attrInfo.Skill , attrInfo.Trainer,  addCoefficient , treatyRatio , fatigueRatio , outRatio);
            //logger.info("calcOneLevelAttr: key, Base, Cur", key, attrInfo.Base, attrInfo.Cur);
        }
    }
};

/** 计算上场球员实力
 * @param heroUid 球员uid
 * @param currPostion 位置
 * @param uid  阵容uid
 * @returns 无
 */
Heros.prototype.calcHeroInFormationRate = function(heroUid, currPostion, uid) {
    let hero = this.allheros.get(heroUid);
    //let copyHero = utils.deepCopy(hero);
    //let oneLevelAttr = copyHero.oneLevelAttr;
    let oneLevelAttr = {};
    for(let key in hero.oneLevelAttr) {
        oneLevelAttr[key] = hero.oneLevelAttr[key].Cur
    }
    let resId = hero.ResID;
    //let config = dataApi.allData.data["Footballer"][resId];
    let config;
    let bead = resId / 10000;
    if(bead < 9)
    {
        config = dataApi.allData.data["Footballer"][resId];
    }
    else
    {
        config = dataApi.allData.data["FootballerPve"][resId];
    }

    if(!config){
        logger.error("calcHeroInFormationRate config is not found", heroUid);
        return 0;
    }

    let position1 =  config.Position1;
    let position2 =  config.Position2;
    if ("" === position1 && "" === position2) {
        logger.error("calcHeroInFormationRate position1 and position2 not data!", uid);
        return 0;
    }

    //1.先查看当前位置是否为球员擅长位置
    let positionMatchRate = 0;
    if (currPostion === position1 || currPostion === position2) {
        positionMatchRate = 1;
    }else {
        let positionMatchId = config.PositionMatch;
        //2.不属于擅长位置，查表
        if ( "" === position1 ) {
            positionMatchRate = 0;
        } else if(positionMatchId > 0) {
            positionMatchRate = this.player.heros.getPositionMatchRateById(positionMatchId, currPostion)
        } else {
            positionMatchRate = this.player.heros.getPositionMatchRate(position1, currPostion)
        }
    
        if (0 === positionMatchRate || NaN === positionMatchRate) {
            //走到这里应该是配置出错了
            logger.error("calcHeroInFormationRate config is fail");
            return 0;
        }
    }

    //计算属性
    let currTotal = 0;
    let count = 0;
    for (let i in oneLevelAttr) {
        let cur = oneLevelAttr[i];
        if (i !== "ResistanceDamage") 
        {
            //logger.info("calcHeroInFormationRate: heroUid, pos, rate, Cur", heroUid, i, positionMatchRate, attrInfo.Cur);
            cur *= positionMatchRate;
            currTotal += cur;
            //logger.info("calcHeroInFormationRate: heroUid, Cur, currTotal", heroUid, attrInfo.Cur, currTotal);
            count ++;
        }
    }
    //计算球员实力值
    // let strength =  utils.MathRound(currTotal / count);
    let strength =  Math.floor(currTotal / count);
    return strength;
};


/**
 * 计算阵容战术加成
 * @param heroUid 球员uid
 * @param currPostion 位置
 * @param uid  阵容uid
 * @param tacticType 战术类型   1 进攻战术加成   2防守战术加成
 */
Heros.prototype.calcTacticsAttrByType = function(heroUid, currPostion, uid, tacticType) {
    let hero = this.getHero(heroUid);
    if(!hero) {
        return;
    }
    let oneLevelAttr = hero.oneLevelAttr;
    let formation = this.player.teamFormations.getOneTeamFormation(uid);
    let str = "";
    let config, useTactics;
    if(tacticType === 1) {
        useTactics = formation.UseTactics;
        str = "AttackTactics";
        config = dataApi.allData.data["Tactic"][useTactics];
    }else if(tacticType === 2) {
        useTactics = formation.UseDefTactics;
        str = "DefTactics";
        config = dataApi.allData.data["Tactic"][useTactics];
    }else {
        return;
    }

    if(!config) {
        return;
    }

    //加成值
    let attrValue = [];
    //加成位置
    let attrPos = [];
    //加成类型
    let attrType = [];
    for(let j = 1; j < 7; ++j) {
        attrType.push(config["AddType" + j]);
        attrValue.push(config["AddValue" + j]);
        attrPos.push(config["AddPosition" + j]);
    }

    for(let key in commonEnum.ONE_LEVEL_ATTR_NAMES) {
        if(key !== "ResistanceDamage") {
            let attrInfo = oneLevelAttr[key];
            //先清一次
            if (!!attrInfo[str]) {
                attrInfo[str] = 0;
            }
        }
    }

    //检查这个位置是否有加成
    if(attrPos.indexOf(currPostion) === -1) {
        return;
    }

    for(let key in commonEnum.ONE_LEVEL_ATTR_NAMES) {
        if(key !== "ResistanceDamage") {
            let val = commonEnum.ONE_LEVEL_ATTR_NAMES[key];
            let attrInfo = oneLevelAttr[key];
            for(let n = 0; n < attrType.length; ++n) {
                if(attrType[n] === val) {
                    attrInfo[str] = attrInfo.Base * attrValue[n] / 10000;
                }
            }
        }
    }
};

/** 计算球员综合实力
 * @param  {} uid 球员uid
 * @returns 无
 */
Heros.prototype.calcRating = function(uid) 
{
    var hero = this.allheros.get(uid);
    var oneLevelAttr = hero["oneLevelAttr"];
    var twoLevelAttr = hero["twoLevelAttr"];

    var initTotal = 0;
    var baseTotal = 0;
    var skillTotal = 0;
    var hellTrainTotal = 0;
    var leagueTotal = 0;
    var qualificateTotal = 0;
    var upgradeStarTotal = 0;
    var currTotal = 0;
    
    //var config = dataApi.allData.data["Footballer"][hero.ResID];
    let config;
    let bead = hero.ResID / 10000;
    if(bead < 9)
    {
        config = dataApi.allData.data["Footballer"][hero.ResID];
    }
    else
    {
        config = dataApi.allData.data["FootballerPve"][hero.ResID];
    }

    var initProperty = getInitProperty(config)

    //计算一级属性
    var count = 0;
    for (var key in oneLevelAttr) {
        const attrInfo = oneLevelAttr[key];
        if (key !== "ResistanceDamage") {
            initTotal        = initTotal        + initProperty[key].Init;
            baseTotal        = baseTotal        + attrInfo.Base;
            skillTotal       = skillTotal       + attrInfo.Skill;
            hellTrainTotal   = hellTrainTotal   + attrInfo.HellTrain;
            leagueTotal      = leagueTotal      + attrInfo.League;
            qualificateTotal = qualificateTotal + attrInfo.Qualificate;
            upgradeStarTotal = upgradeStarTotal + attrInfo.UpgradeStar;
            currTotal        = currTotal        + attrInfo.Cur;
            count++;
        }
    }

    initAvg = utils.MathRound( initTotal / count);
    baseAvg = utils.MathRound(baseTotal / count);
    skillAvg = utils.MathRound(skillTotal / count);
    hellTrainAvg = utils.MathRound(hellTrainTotal / count);
    leagueAvg = utils.MathRound(leagueTotal/ count);
    qualificateAvg =utils.MathRound(qualificateTotal/ count);
    upgradeStarAvg = utils.MathRound(upgradeStarTotal / count);
    // currAvg =  utils.MathRound(currTotal/ count);
    currAvg =  Math.floor(currTotal/ count);
    let attr = twoLevelAttr["BallerRating"];
    attr.Cur  = currAvg;
    attr.Init = initAvg;
    attr.Base = baseAvg;
    attr.Skill = skillAvg;
    attr.HellTrain = hellTrainAvg;
    attr.League = leagueAvg;
    attr.Qualificate = qualificateAvg;
    attr.UpgradeStar = upgradeStarAvg;
    attr.Inspire =  0;
    //更新技能状态
    this.updateSkillStatus(uid, 1, attr.Cur);
};

/**
 * 获取球员实力
 * @param  {uid} uid 球员Uid
 * @returns 实力
 */
Heros.prototype.getRating = function(uid) {
    var hero = this.allheros.get(uid);
    var rating = 0;
    if (!hero) {
        return rating;
    }

    rating =  hero["twoLevelAttr"]["BallerRating"].Cur;
    return rating;
};

/**
 * 获取球员属性配置中的对应列 进攻系数
 * @param  {} key 球员位置
 * @returns 位置参数
 */
Heros.prototype.getAttackRate = function (key){
    var ballerAttackRateMap = {};
    ballerAttackRateMap['GK']="GKATRate";	
    ballerAttackRateMap['DC']="DCATRate";	
    ballerAttackRateMap['DL']="DLATRate";	
    ballerAttackRateMap['DR']="DRATRate";	
    ballerAttackRateMap['DM']="DMATRate";	
    ballerAttackRateMap['MC']="MCATRate";	
    ballerAttackRateMap['ML']="MLATRate";	
    ballerAttackRateMap['MR']="MRATRate";	
    ballerAttackRateMap['AM']="AMATRate";	
    ballerAttackRateMap['ST']="STATRate";	
    ballerAttackRateMap['WL']="WLATRate";	
    ballerAttackRateMap['WR']="WRATRate";	
    var val = ballerAttackRateMap[key];
    return val?val:"";
};

/**
 * 获取球员属性配置中的对应列 防守系数
 * @param  {key} 球员位置
 * @returns 位置参数
 */
Heros.prototype.getDefineRate = function(key) {
    var ballerDefendRateMap = {};
    ballerDefendRateMap['GK']="GKDFRate";	
    ballerDefendRateMap['DC']="DCDFRate";	
    ballerDefendRateMap['DL']="DLDFRate";	
    ballerDefendRateMap['DR']="DRDFRate";	
    ballerDefendRateMap['DM']="DMDFRate";	
    ballerDefendRateMap['MC']="MCDFRate";	
    ballerDefendRateMap['ML']="MLDFRate";	
    ballerDefendRateMap['MR']="MRDFRate";	
    ballerDefendRateMap['AM']="AMDFRate";	
    ballerDefendRateMap['ST']="STDFRate";	
    ballerDefendRateMap['WL']="WLDFRate";	
    ballerDefendRateMap['WR']="WRDFRate";
    var val = ballerDefendRateMap[key];
    return val?val:"";
};

/** 获取球员匹配中的匹配系数
 * @param  {} postionColumn 配置列  擅长位置
 * @param  {} postionRow    位置行  所在位置
 */
Heros.prototype.getPositionMatchRate = function (postionColumn, postionRow) {
    var id = commonEnum.TEAM_FORMATION_CONFIG_POSITION_TO_ID[postionColumn];
    var config = dataApi.allData.data["PositionMatch"][id];
    var rate = config[postionRow];
    return rate;
};

/** 获取球员匹配中的匹配系数
 * @param  {} postionColumn 配置列  擅长位置
 * @param  {} postionRow    位置行  所在位置
 */
Heros.prototype.getPositionMatchRateById = function (id, postionRow) {
    let config = dataApi.allData.data["NewPositionMatch"][id];
    let rate = config[postionRow];
    return rate;
};

/** 计算二级属性
 * @param  {} uid 球员uid
 * @returns 无
 */
Heros.prototype.calcTwoLevelAttr = function(uid) {
    //计算综合实力
    this.calcRating(uid);
};

/** 计算全部属性 (除球员进攻和防守属性)
 * @param  {} uid 球员uid
 * @returns 无
 */
// Heros.prototype.reCalcAttr = function(uid) {
//     this.calcCoachAttr(uid);  //这个计算函数必须在计算一级属性的前面
//     this.player.teamFormations.calcHeroTrainerAttr(uid);      //计算教练属性加成
//     this.player.teamFormations.calcHeroTacticsAttr(2, uid);  //计算战术加成
//     this.player.teamFormations.calcTrainerSkillAttr(uid);  //教练技能加成
//     this.player.beliefSkill.reCalcBeliefSkillAttr(uid);             //信仰技能加成
//     this.calcOneLevelAttr(uid);
//     this.calcTwoLevelAttr(uid);
// };

/** 计算全部属性 (除球员进攻和防守属性)
 * @param  {} uid 球员uid
 * @param teamUid   队伍uid
 * @returns 无
 */
Heros.prototype.reCalcAttr = function(uid, teamUid) {
    //图鉴加成
    this.calcCoachAttr(uid);  //这个计算函数必须在计算一级属性的前面
    this.player.teamFormations.calcHeroTrainerAttr(uid, teamUid);                 //计算教练属性加成
    this.player.teamFormations.calcHeroTacticsAttr(2, uid, teamUid);              //计算战术加成
    this.player.teamFormations.calcTrainerSkillAttr(uid, teamUid);                //教练技能加成
    this.player.beliefSkill.reCalcBeliefSkillAttr(uid);             //信仰技能加成
    this.calcOneLevelAttr(uid);
    this.calcTwoLevelAttr(uid);
};


/** 计算全部属性 (除球员进攻和防守属性)(改版后)
 * @param  {} uid 球员uid
 * @returns 无
 */
Heros.prototype.reCalcAttrRevision = function(uid) {
    //图鉴加成
    this.calcCoachAttr(uid);  //这个计算函数必须在计算一级属性的前面
    //获取球员所在当前阵容的队伍Uid
    let teamUid = this.player.teamFormations.getHeroInMainTeamUid(uid);
    //检查球员是否在主力阵容
    if(!!teamUid) {
        this.player.teamFormations.calcHeroTrainerAttr(uid, teamUid);                 //计算教练属性加成
        this.player.teamFormations.calcHeroTacticsAttr(2, uid, teamUid);              //计算战术加成
        this.player.teamFormations.calcTrainerSkillAttr(uid, teamUid);                //教练技能加成
    }else {
        //不在主力阵容清除教练与战术相关加成
        this.clearHeroTrainerAttrAndTacticsAttr(uid);
    }
    this.player.beliefSkill.reCalcBeliefSkillAttr(uid);             //信仰技能加成
    this.calcOneLevelAttr(uid);
    this.calcTwoLevelAttr(uid);
};

//清除教练与战术相关加成
Heros.prototype.clearHeroTrainerAttrAndTacticsAttr = function(uid) {
    let hero = this.allheros.get(uid);
    let oneLevelAttr = hero["oneLevelAttr"];
    for (let key in oneLevelAttr) {
        const attrInfo = oneLevelAttr[key];
        if (key !== "ResistanceDamage") {
            if(attrInfo.Trainer !== 0){
                attrInfo.Trainer = 0;
            }

            if(attrInfo.Trainer !== 0){
                attrInfo.Trainer = 0;
            }

            if(attrInfo.AttackTactics !== 0) {
                attrInfo.AttackTactics = 0;
            }

            if(attrInfo.DefTactics !== 0) {
                attrInfo.DefTactics = 0;
            }

            if(attrInfo.TrainerSkillAtt !== 0) {
                attrInfo.TrainerSkillAtt = 0;
            }
        }
    }
}

/** 计算球员进攻值和防守值
 * @param  {} uid    
 * @param  {} teamFormationId 阵容Uid
 * @param  {} currPostion     球员在阵容的位置
 * @returns {} 无
 */
Heros.prototype.calcBallerAttackAndDefend = function(uid, teamFormationId, currPostion) {
    var hero = this.allheros.get(uid);
    //let copyHero = utils.deepCopy(hero);
    let oneLevelAttr = hero.oneLevelAttr;
    if (!hero) {
        logger.error("calcBallerAttackAndDefend hero not found!", uid);
        return;
    }

    if ( "" === teamFormationId)
    {
        logger.error("calcBallerAttackAndDefend teamFormationId is null!", uid);
        return;
    }

    // var oneLevelAttr = hero.oneLevelAttr;
    var attrConfig = dataApi.allData.data["Attr"];
    if (!attrConfig) {
        logger.error("calcBallerAttackAndDefend attrConfig not found!", uid);
        return;
    }

    //计算该球员位置
    //var config = dataApi.allData.data["Footballer"][hero.ResID];
    var config;
    let bead = hero.ResID / 10000;
    if(bead < 9)
    {
        config = dataApi.allData.data["Footballer"][hero.ResID];
    }
    else
    {
        config = dataApi.allData.data["FootballerPve"][hero.ResID];
    }

    var position1 =  config.Position1;
    var position2 =  config.Position2;
    if ("" === position1 && "" === position2) {
        logger.error("calcBallerAttackAndDefend position1 and position2 not data!", uid);
        return;
    }

    //1.先查看当前位置是否为球员擅长位置
    var positionMatchRate = 0;
    if (currPostion === position1 || currPostion === position2) {
        positionMatchRate = 1;
    }else {
        let positionMatchId = config.PositionMatch;
        //2.不属于擅长位置，查表
        if ( "" === position1 ) {
            positionMatchRate = 0;
        } else if(positionMatchId > 0) {
            positionMatchRate = this.player.heros.getPositionMatchRateById(positionMatchId, currPostion)
        } else {
            positionMatchRate = this.player.heros.getPositionMatchRate(position1, currPostion)
        }
    
        if (0 === positionMatchRate || NaN === positionMatchRate)
        {
            //走到这里应该是配置出错了
            logger.error('------------ calc_baller_attack search position error! ---------------', position1, position2, hero.Uid);
            return;
        }
        //logger.info('------------ calcBallerAttackAndDefend: positionMatchRate=', positionMatchRate);
    }

    let currAttackTotal = 0;
    let currDefendTotal = 0;
    let attackRateName = this.getAttackRate(currPostion);
    let defendRateName = this.getDefineRate(currPostion);
    let currValue = 0;
    let attrStr = "";

    for (let id in attrConfig) {
         const element = attrConfig[id];
         let attrCurValue = 0;
         switch (element["ID"]) {
            case commonEnum.ONE_LEVEL_ATTR_NAMES.Speed:
                var attr = oneLevelAttr["Speed"];
                attrCurValue = attr.Cur;
                attrStr = "Speed";
            break;
             
            case commonEnum.ONE_LEVEL_ATTR_NAMES.Jumping:
                var attr = oneLevelAttr["Jumping"];
                attrCurValue = attr.Cur;
                attrStr = "Jumping";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.Strength:
                var attr = oneLevelAttr["Strength"];
                attrCurValue = attr.Cur;
                attrStr = "Strength";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.Stamina:
                var attr = oneLevelAttr["Stamina"];
                attrCurValue = attr.Cur;
                attrStr = "Stamina";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.Finishing:
                var attr = oneLevelAttr["Finishing"];
                attrCurValue = attr.Cur;
                attrStr = "Finishing";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.Dribbling:
                var attr = oneLevelAttr["Dribbling"];
                attrCurValue = attr.Cur;
                attrStr = "Dribbling";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.Passing:
                var attr = oneLevelAttr["Passing"];
                attrCurValue = attr.Cur;
                attrStr = "Passing";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.Heading:
                var attr = oneLevelAttr["Heading"];
                attrCurValue = attr.Cur;
                attrStr = "Heading";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.StandingTackle:
                var attr = oneLevelAttr["StandingTackle"];
                attrCurValue = attr.Cur;
                attrStr = "StandingTackle";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.SlidingTackle:
                var attr = oneLevelAttr["SlidingTackle"];
                attrCurValue = attr.Cur;
                attrStr = "SlidingTackle";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.LongPassing:
                var attr = oneLevelAttr["LongPassing"];
                attrCurValue = attr.Cur;
                attrStr = "LongPassing";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.LongShots:
                var attr = oneLevelAttr["LongShots"];
                attrCurValue = attr.Cur;
                attrStr = "LongShots";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.Penalties:
                var attr = oneLevelAttr["Penalties"];
                attrCurValue = attr.Cur;
                attrStr = "Penalties";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.CornerKick:
                var attr = oneLevelAttr["CornerKick"];
                attrCurValue = attr.Cur;
                attrStr = "CornerKick";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.FreeKick:
                var attr = oneLevelAttr["FreeKick"];
                attrCurValue = attr.Cur;
                attrStr = "FreeKick";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.ExplosiveForce:
                var attr = oneLevelAttr["ExplosiveForce"];
                attrCurValue = attr.Cur;
                attrStr = "ExplosiveForce";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.Attack:
                var attr = oneLevelAttr["Attack"];
                attrCurValue = attr.Cur;
                attrStr = "Attack";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.Volleys:
                var attr = oneLevelAttr["Volleys"];
                attrCurValue = attr.Cur;
                attrStr = "Volleys";
            break;

            case commonEnum.ONE_LEVEL_ATTR_NAMES.Save:
                var attr = oneLevelAttr["Save"];
                attrCurValue = attr.Cur;
                attrStr = "Save";
            break;
             default:
                 attrCurValue = 0;
                 break;
         }

         currValue = attrCurValue * 100;
         var attackRate = Math.floor(element[attackRateName] * 100);
         var defendRate = Math.floor(element[defendRateName] * 100);
         let attackValue = utils.Math5Round(attackRate * currValue / 10000);
         let defendValue = utils.Math5Round(defendRate * currValue / 10000); 
         currAttackTotal = currAttackTotal + attackValue;
         currDefendTotal = currDefendTotal + defendValue;
         //logger.info("ResID: ", hero.ResID, attrStr, attackRate, defendRate, currValue, attackValue, defendValue, currAttackTotal, currDefendTotal);
        }
        
    var AttackAndDefend = hero.AttackAndDefend;
    var attack = utils.MathRound(positionMatchRate * currAttackTotal );  //球员进攻值
    var defend = utils.MathRound(positionMatchRate * currDefendTotal );  //球员防守值
    
    //logger.error("calcBallerAttackAndDefend____________________________", hero.ResID, attack, defend, positionMatchRate, currAttackTotal, currDefendTotal);
    var object = {
        position: currPostion, //位置
        attack: attack,
        defend: defend
    };
 
    AttackAndDefend[teamFormationId] = object;
};

/** 删除球员身上存储的阵容属性
 * @param  {} uid    
 * @param  {} teamFormationId 阵容Uid
 * @returns {} 无
 */
Heros.prototype.DelHeroAttackAndDefend = function(uid, teamFormationId) {
    var hero = this.allheros.get(uid);
    if (!hero) {
        logger.error("DelHeroAttackAndDefend: hero not found!", uid);
        return;
    }

    if (!teamFormationId)
    {
        logger.error("DelHeroAttackAndDefend: teamFormationId is null!", uid);
        return;
    }

    var attackAndDefend = hero.AttackAndDefend;
    for(var id in attackAndDefend) {
        if (id === teamFormationId) {
            delete attackAndDefend[id];
            break;
        }
    }

    //logger.info("DelHeroAttackAndDefend: attackAndDefend ", uid, attackAndDefend);
    return Code.OK;
};

Heros.prototype.checkHeroResIdIsExist = function(resId)
{
    let exist = false;
    for (let [uid, hero] of this.allheros) {
        if (hero.ResID === resId) {
            if(hero.isDie === 0) {
                exist = true;
                break;
            }
            break;
        }
    }
    return exist;
};

Heros.prototype.getHeroUidByResId = function(resId)
{
    let heroUid = "";
    for (let [uid, hero] of this.allheros)
    {
        if (hero.ResID === resId)
        {
            heroUid = uid;
            break;
        }
    }

    return heroUid;
};

/**************************************球员培养及突破***begin**************************************************/
/** 培养球员
 * @param  {}  UID     球员UID
 * @param  {}  type    属性类型
 * @param  {}  index   培养类型   1.欧元培养   2.道具培养
 */
Heros.prototype.cultivateHero = function(uid, type, index) {
    let hero = this.allheros.get(uid);
    if (!hero) {
        logger.error("cultivateHero: hero not found!", uid);
        return [Code.FAIL];
    }
    //logger.error("cultivateHero--------start----")
    let random = [];  //随机值
    let initProperty = {};   //属性类型
    let resID = hero["ResID"];
    let oneLevelAttr = hero["oneLevelAttr"];
    let heroStage = hero.isStage;
    //是否突破过
    initProperty = this.getInitPropertyByType(type, resID, hero.OldBreakOut);
    random = this.cultivateHeroByIndex(type,index, oneLevelAttr, initProperty, heroStage, resID);
    if(random[0] < Code.FAIL){
        //重新计算球员实力值
        this.reCalcAttrRevision(hero.Uid);
        // this.reCalcAttr(hero.Uid);
        //检查是否在阵容中 在的话需要重新计算球队属性
        this.player.checkHeroInArbitrarilyFormation(uid)
        //触发任务
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.TEN);
    }

    //logger.error("cultivateHero--------end----")
    return random;
};

//获取培养配置   stage  阶段
Heros.prototype.getCultivateConfigByStage = function(stage) {
    let cfg = {};
    let config = dataApi.allData.data["FootballerFoster"][stage];
    if(!config) {
        return cfg;
    }
    cfg.costMoney = config.EuroExpend;    //使用欧元每次消耗
    cfg.itemId = config.ItemID;           //使用道具ID
    cfg.itemNum = config.ItemExpend;      //使用道具每次消耗
    cfg.lowerLimit = config.LowerLimit;   //每次培养属性提升下限
    cfg.upperLimit = config.UpperLimit;   //每次培养属性提升上限
    cfg.convenientFoster = config.ConvenientFoster;     //每次培养属性提升均值
    return cfg;
}

//获取属性阶段
Heros.prototype.getCultivateStage = function(oneLevelAttr, initProperty) {
    let nowStage = 1;
    let stageList = [];

    for (let i in initProperty){
        let base = Math.floor(oneLevelAttr[i].Base);
        let stage = Math.ceil(base / initProperty[i].init);
        if(stage > 1) {
            stage -= 1;
        }
        stageList.push(stage);
    }
    // logger.error("yyyyyyyyyyyyyyyyyyyyyyyyyyyy", stageList);
    let max = Math.max.apply(null, stageList);
    let min = Math.min.apply(null, stageList);
    if(max === min) {
        nowStage = min;
    }else {
        nowStage = min;
    }
    return nowStage;
}

//根据培养方式培养球员
Heros.prototype.cultivateHeroByIndex = function(type, index, oneLevelAttr, initProperty, heroStage, resId) {
    //检查当前培养的所有属性是否已满
    if(this.checkCultivateValueIsFull(oneLevelAttr, initProperty)) {
        return[Code.MAX_FAIL];
    }
    //获取属性阶段
    let stage = this.getCultivateStage(oneLevelAttr, initProperty);
    // logger.error("阶段____________________________", stage);
    //有一项满了就是9
    if(stage > 9) {
        stage = 9;
    }
    // logger.error("cultivateHeroByIndex------------start-----------");
    let config = this.getCultivateConfigByStage(heroStage[type - 1]);
    if(JSON.stringify(config) === "{}") {
        return [Code.CONFIG_FAIL];
    }

    let slog = {
        resId: resId,
        type: type,
        stage: heroStage[type - 1],
        costCash: 0,
        useItemId: 0,
        itemNum: 0,
    }
    //使用欧元培养
    if (index === 1){
        //检查是否有足够的钱
        if(this.player.cash < config.costMoney){
            logger.error("this player cash is fail", this.player.cash);
            return [Code.CASH_FALL];
        }
        this.player.subtractResource(commonEnum.PLAY_INFO.cash, config.costMoney);
        slog.costCash = config.costMoney;
    }else {   
        //使用道具培养
        let itemNum = this.player.item.getItemCountByResId(config.itemId);  //道具数量
        let itemUid = this.player.item.getItemUidByResId(config.itemId);

        //检查物品是否足够
        if(itemNum < config.itemNum){
            return [Code.ITEM_FAIL];
        } 
        this.player.item.delItem(itemUid, config.itemNum);
        this.player.newerTask.triggerTask(commonEnum.NEWER_TASK.HERO_TRAIN);
        slog.useItemId = config.itemId;
        slog.itemNum = config.itemNum;
    }
	this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.CULTIVATE_HERO, [], slog);
    let random = randomFrom(config.lowerLimit, config.upperLimit);
    let sum = this._InnerCultivateHero(random, oneLevelAttr, initProperty, stage, type, heroStage);
    // logger.error("cultivateHeroByIndex------------end-----------");
    return sum;
};

//检查当前培养的所有属性是否已满
Heros.prototype.checkCultivateValueIsFull = function(oneLevelAttr, initProperty){ 
    let count = 0;
    for (let i in initProperty){
        if(oneLevelAttr[i].Base >= initProperty[i].max){
            count++;
        }
    }
    
    //3项属性都满了
    if(count === 3) {
        return true;
    }
    return false;
}

//属性计算
Heros.prototype._InnerCultivateHero = function(random, oneLevelAttr, initProperty, stage, type, heroStage){
    let sum = [0, 0, 0];
    let index = 0;
    let id = type - 1;
    // logger.error("_InnerCultivateHero----------start--------")
    for (let i in initProperty){
        if(i !== "ResistanceDamage"){
            //如果有已经满了的
            let stageMax = initProperty[i].init * (stage + 1);

            //兼顾老数据
            if(heroStage[id] === 1) {
                heroStage[id] = stage;
            }
            //阶段一样说明有上限
            if(stage === heroStage[id]) {
                if(oneLevelAttr[i].Base >= stageMax) {
                    oneLevelAttr[i].Base = stageMax;
                    if(oneLevelAttr[i].Base > initProperty[i].max){
                        oneLevelAttr[i].Base = initProperty[i].max;
                    }
                    index++;
                    continue;
                }
            }
            // logger.error("dddddd", stage, oneLevelAttr[i].Base, stageMax, heroStage[id])
            //加上相应的值
            oneLevelAttr[i].Base += random[index];
            if(stage === heroStage[id]) {
                if(oneLevelAttr[i].Base >= stageMax) {
                    oneLevelAttr[i].Base = stageMax;
                }
            }

            if(oneLevelAttr[i].Base > initProperty[i].max) {
                oneLevelAttr[i].Base = initProperty[i].max;
            }

            sum[index] += random[index];
            index++;
        }
    }
    // logger.error("_InnerCultivateHero----------end--------")
    return sum;
};


// 一键培养只限欧元；
// 一键培养欧元消耗=当前属性阶段每次欧元培养消耗×达到当前属性阶段所需次数
// 当前属性阶段所需次数=三项属性中差值最大的÷每次培养属性提升均值
/**
 * 一键培养
 * @param uid     球员uid
 * @param type    类型
 * @param stage   阶段
 */
Heros.prototype.oneKeyCultivateHero = function(uid, type, stage) {
    let hero = this.allheros.get(uid);
    if (!hero) {
        logger.error("cultivateHero: hero not found!", uid);
        return Code.FAIL;
    }
    if(stage === 0) {
        stage = 1;
    }
    let initProperty = {};   //属性类型
    let resID = hero["ResID"];
    let oneLevelAttr = hero["oneLevelAttr"];
    //是否突破过
    initProperty = this.getInitPropertyByType(type, resID, hero.OldBreakOut);
    //是否已达突破上限
    if (this.checkCultivateValueIsFull(oneLevelAttr, initProperty)){
        return Code.MAX_FAIL;
    }

    //检查当前培养的所有属性是否已达当前阶段培养上限
    if(this.checkCultivateValueIsMaxStageNum(oneLevelAttr, initProperty, stage)) {
        return Code.MAX_FAIL;
    }

    let config = this.getCultivateConfigByStage(stage);
    if(!config) {
        return Code.CONFIG_FAIL;
    }

    let avrNum = config.convenientFoster;
    if(!avrNum) {
        return Code.CONFIG_FAIL;
    }

    //计算培养次数
    let cultivateNum = this.calcCultivateNum(oneLevelAttr, initProperty, stage, avrNum);
    if(cultivateNum < 1) {
        return Code.CONFIG_FAIL;
    }

    let needMoney = cultivateNum * config.costMoney;
    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, needMoney)){
        return Code.CASH_FALL;
    }

    for (let i in initProperty){
        //阶段最大值
        let stageMax = initProperty[i].init * (stage + 1);
        oneLevelAttr[i].Base = stageMax;
    }
    //扣钱
    this.player.deductMoney(commonEnum.PLAY_INFO.cash, needMoney);
    this.reCalcAttrRevision(hero.Uid);
    // this.reCalcAttr(hero.Uid);
    //检查是否在阵容中 在的话需要重新计算球队属性
    this.player.checkHeroInArbitrarilyFormation(uid);
    for(let i = 0; i < cultivateNum; ++i) {
        //触发任务
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.TEN);
    }
    let slog = {
        resId: resID,
        type: type,
        stage: stage,
        costCash: needMoney,
        useItemId: 0,
        itemNum: 0,
    }
    this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.CULTIVATE_HERO, [], slog);
    return Code.OK;
}

//检查当前培养的所有属性是否已满
Heros.prototype.checkCultivateValueIsMaxStageNum = function(oneLevelAttr, initProperty, stage){
    let count = 0;
    for (let i in initProperty){
        let stageMax = initProperty[i].init * (stage + 1);
        if(oneLevelAttr[i].Base >= stageMax){
            count++;
        }
    }

    //3项属性都满了
    if(count === 3) {
        return true;
    }
    return false;
}


//计算培养次数
Heros.prototype.calcCultivateNum = function(oneLevelAttr, initProperty, stage, avrNum) {
    let stageMaxList = [];
    for (let i in initProperty){
        //阶段最大值
        let stageMax = Math.floor(initProperty[i].init * (stage + 1));
        let diffNum = stageMax - Math.floor(oneLevelAttr[i].Base);
        stageMaxList.push(diffNum);
    }
    //logger.error("yyyyyyyyyyyycalcCultivateNumyyyyyyyyyyyyyyyy", stageMaxList);
    //三项属性中差值最大的
    let maxNum = Math.max.apply(null, stageMaxList);
    let needNum = Math.ceil(maxNum / avrNum);
    return needNum;
}

//属性进阶
Heros.prototype.heroAdvanceStage = function(uid, type) {
    let hero = this.getHero(uid);
    if(!hero) {
        return Code.FAIL;
    }

    let resId = hero.ResID;
    let srcType = type + 1;
    let oneLevelAttr = hero["oneLevelAttr"];
    let initProperty = this.getInitPropertyByType(srcType, resId, hero.OldBreakOut);
    //获取属性阶段
    let stage = this.getCultivateStage(oneLevelAttr, initProperty);
    // logger.error("tttttttttttttttttttttt", stage);
    hero.isStage[type] = stage + 1;
    if(hero.isStage[type] > 9) {
        hero.isStage[type] = 9;
    }

    let config;
    let bead = resId / 10000;
    if(bead < 9) {
        config = dataApi.allData.data["Footballer"][resId];
    } else {
        config = dataApi.allData.data["FootballerPve"][resId];
    }

    //主要属性
    let mainType = this.getPropertyByPos(config.Position1);
    if(mainType === srcType) {
        this.updateSkillStatus(uid, 4, hero.isStage[type]);
    }

    return Code.OK;
}

Heros.prototype.fixAdvanceStage = function() {
    for(let [k, v] of this.allheros) {
        let hero = this.getHero(k);
        if(!hero) {
            continue;
        }
        let resId = hero.ResID;
        let oneLevelAttr = hero["oneLevelAttr"];
        let index = 0;
        for(let i = 1; i < 7; ++i) {
            let initProperty = this.getInitPropertyByType(i, resId, hero.OldBreakOut);
            //获取属性阶段
            let stage = this.getCultivateStage(oneLevelAttr, initProperty);
            if(hero.isStage[index] < stage) {
                hero.isStage[index] = stage;
            }

            if(hero.isStage[index] > 9) {
                hero.isStage[index] = 9;
            }
            index++;
        }
    }
}


//指定范围随机取值
function randomFrom(a, b){
    let i = 0;
    let random = [];
    while(i < 3){
        random.push(Math.floor(Math.random() * (b - a + 1) + a));
        i++;
    }
    return random;
};

/** 根据属性类型获取相应的初始值
 * @param  {}  type   属性类型
 * @param  {}  resId   球员id
 * @param  {}  num     突破值
 */
Heros.prototype.getInitPropertyByType = function(type, resId, num) {
    //let config = dataApi.allData.data["Footballer"][resId];
    let config;
    let bead = resId / 10000;
    if(bead < 9)
    {
        config = dataApi.allData.data["Footballer"][resId];
    }
    else
    {
        config = dataApi.allData.data["FootballerPve"][resId];
    }

    let initProperty = getInitProperty(config);
    let list = {};
    for(let i in initProperty){
        switch(type){
            case 1:           //素质
                if( i === "Speed" || i === "Strength" || i === "ExplosiveForce"){
                    list[i] = {};
                    list[i].init = initProperty[i].Init;
                    list[i].max = utils.MathRound(initProperty[i].Init * (num / 10));
                }
            break;
            case 2:          //防守
                if( i === "Jumping" || i === "StandingTackle" || i === "SlidingTackle"){
                    list[i] = {};
                    list[i].init = initProperty[i].Init;
                    list[i].max = utils.MathRound(initProperty[i].Init * (num / 10));
                }
            break;
            case 3:           //射术
                if( i === "Finishing" || i === "Heading" || i === "LongShots"){
                    list[i] = {};
                    list[i].init = initProperty[i].Init;
                    list[i].max = utils.MathRound(initProperty[i].Init * (num / 10));
                }
            break;
            case 4:           //技巧
                if( i === "Dribbling" || i === "Passing" || i === "LongPassing"){
                    list[i] = {};
                    list[i].init = initProperty[i].Init;
                    list[i].max = utils.MathRound(initProperty[i].Init * (num / 10));
                }
            break;
            case 5:           //定位球
                if( i === "Penalties" || i === "CornerKick" || i === "FreeKick"){
                    list[i] = {};
                    list[i].init = initProperty[i].Init;
                    list[i].max = utils.MathRound(initProperty[i].Init * (num / 10));
                }
            break;
            case 6:           //守门
                if( i === "Attack" || i === "Volleys" || i === "Save"){
                    list[i] = {};
                    list[i].init = initProperty[i].Init;
                    list[i].max = utils.MathRound(initProperty[i].Init * (num / 10));
                }
            break;
        }
    }
    return list;
};

/** 球员突破
 * @param  {}  UID     球员UID
 */
Heros.prototype.breakOutHero = function(uid){
    //logger.error("breakOutHero------start-------")
    let ret = {code: Code.FAIL, potential: 0, num: 0};
    let hero = this.allheros.get(uid);
    if (!hero) {
        logger.error("breakOutHero: hero not found!", uid);
        ret.code = Code.FAIL;
        return ret;
    }
    let config;
    let bead = hero.ResID / 10000;
    if(bead < 9) {
        config = dataApi.allData.data["Footballer"][hero.ResID];
    }
    else {
        config = dataApi.allData.data["FootballerPve"][hero.ResID];
    }

    let breakNum = hero.Breakthrough.length;
    //超出最大突破次数直接返回
    if(breakNum >= 10){
        logger.error("breakOutHero not have count~~~~~~~~", hero.Breakthrough.length);
        ret.code = Code.FAIL;
        return ret;
    }
    let cost = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.breakoutCost].Param;
    //检查钱是否足够
    if(this.player.cash < cost){
        logger.error("breakOutHero not have cash~~~~~~~~", this.player.cash);
        ret.code = Code.CASH_FALL;
        return ret;
    }
    let nextBreak = breakNum + 1;

    //在1-7之间随机取一个值
    // let random = Math.floor(Math.random() * 7 + 1);
    let random = this.breakWeight(config.BreakWeight, nextBreak);
    this.player.subtractResource(commonEnum.PLAY_INFO.cash, cost);
    //记录突破值
    hero.Breakthrough.push(random);
    let sum = 30;
    for(let i in hero.Breakthrough){
        sum += hero.Breakthrough[i];
    }

    //更新历史突破值
    if(sum > hero.OldBreakOut) {
        hero.OldBreakOut = sum;
    }

    //更新技能状态
    this.updateSkillStatus(uid, 2, sum);
    //重新计算球员实力值
    this.reCalcAttrRevision(uid);
    // this.reCalcAttr(uid);
    //检查是否在阵容中 在的话需要重新计算球队属性
    this.player.checkHeroInArbitrarilyFormation(uid);
    //触发任务
    if(sum === 100) {
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.SEVENTEEN); 
    }
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.SIXTEEN);
    ret.code = Code.OK;
    ret.num = random;
    //logger.error("breakOutHero------end-------")
    return ret;
}


Heros.prototype.breakWeight = function (id, num) {
    let config = dataApi.allData.data["FootballerBreak"];
    let randConfig = [];
    if(!config) {
        return Math.floor(Math.random() * 7 + 1);
    }

    let idx = 0;
    for(let i in config) {
        if(id === config[i].BreakWeight && num === config[i].BreakLayer) {
            for(let j = 1; j < 8; ++j) {
                randConfig[idx] = {};
                randConfig[idx].id = j;
                randConfig[idx].weight = config[i][j + "PercentWeight"];
                idx++;
            }
        }
    }

    if(randConfig.length < 1) {
        return Math.floor(Math.random() * 7 + 1);
    }

    let randList = [];
    for(let i in randConfig) {
        if(randConfig[i].weight !== 0) {
            for(let j = 0; j < randConfig[i].weight; ++j) {
                randList.push(randConfig[i].id);
            }
        }
    }

    let randNum = randList[Math.floor(Math.random() * randList.length)];
    return randNum;
}

/** 撤销球员突破
 * @param  {}  UID     球员UID
 */
Heros.prototype.reBreakOutHero = function(uid){
    let ret = {code: Code.FAIL, potential: 0, num: 0};
    let hero = this.allheros.get(uid);
    if (!hero) {
        logger.error("reBreakOutHero: hero not found!", uid);
        ret.code = Code.FAIL;
        return ret;
    }
    let resId = hero["ResID"];
    let oneLevelAttr = hero["oneLevelAttr"];
    //一个都没有不允许撤销
    if(hero.Breakthrough.length < 1){
        logger.debug("reBreakOutHero: reBreakOut length is 0", hero.Breakthrough.length);
        ret.code = Code.FAIL;
        return ret;
    }
    let index = -1;
    for(let i = 0; i < hero.Breakthrough.length; ++i){
        if(hero.Breakthrough[i] !== 7){
            index = i;
            break;
        }
    }
    //完美
    if (index === -1){
        logger.debug("reBreakOutHero: reBreakOut is full~~~~~~");
        ret.code = Code.FAIL;
        return ret;
    }
    let cost = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.rebreakoutCost].Param;
    //获取活动价
    let discount = this.getActivityPrice(cost, 33);
    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, discount)){
        ret.code = Code.GOLD_FALL;
        return ret;
    }

    //扣钱
    this.player.deductMoney(commonEnum.PLAY_INFO.gold, discount);
    let reCount = hero.Breakthrough.pop();

    //重新计算球员实力值
    this.reCalcAttrRevision(hero.Uid);
    // this.reCalcAttr(hero.Uid);
    //检查是否在阵容中 在的话需要重新计算球队属性
    this.player.checkHeroInArbitrarilyFormation(uid)
    ret.code = Code.OK;
    ret.num = reCount;
    return ret;
};

/**获取活动价
 *
 * @param num  数量
 * @param id  活动id
 * @returns {*}
 */
Heros.prototype.getActivityPrice = function(num, id) {
    let config = dataApi.allData.data["ActiveControl"][id];
    if(!config) {
        return num;
    }

    if(config.StartTime === "" || config.EndTime === "") {
        return num;
    }
    let nowTime = new Date().getTime();
    let startTime = new Date(config.StartTime).getTime();
    let endTime = new Date(config.EndTime).getTime();
    //不在活动范围内
    if(nowTime < startTime || nowTime > endTime) {
        return num;
    }

    let discount = 0;
    switch (id) {
        case 33:     //球员球币折扣
            if(typeof dataApi.allData.data["SystemParam"][9200] === "undefined") {
                discount = 1
            }else {
                discount = dataApi.allData.data["SystemParam"][9200].Param / 100;
            }
            break;
        case 35:     //生涯折扣
            if(typeof dataApi.allData.data["SystemParam"][9201] === "undefined") {
                discount = 1
            }else {
                discount = dataApi.allData.data["SystemParam"][9201].Param / 100;
            }
            break;
        default:
            discount = 1;
            break;
    }

    return Math.floor(discount * num);
}

/** 获取球员属性的最大值
 * @param  {}  resId   球员id
 * @param  {}  num     突破值
 */
Heros.prototype.getInitPropertyMAX = function(resId, num) {
    let config = dataApi.allData.data["Footballer"][resId];
    let initProperty = getInitProperty(config);
    let list = []
    for(let i in initProperty){
        if(i === "ResistanceDamage" || i === "SkillList"){
            continue;
        }
        list[i] = {};
        list[i].init = initProperty[i].Init;
        list[i].max = utils.MathRound(initProperty[i].Init * (num / 10));
    }
    return list;
};

/**************************************球员培养及突破******end***********************************************/

/**************************************球员特训及升星******begin***********************************************/
//特训初始化
Heros.prototype.initTrain = function(uid){ 
    //初始化19项属性
    let train = {};  
    for(let j = 1; j < 5; j++){
        let attrInfo = {}
        for (let i in commonEnum.ONE_LEVEL_ATTR_NAMES) {
            if(i === "ResistanceDamage"){
                continue;
            }
            attrInfo[i] = 0;
        }
        train[j] = attrInfo;
    }
    //当前加成的值
    let additive = {}
    for (let i in commonEnum.ONE_LEVEL_ATTR_NAMES) {
        if(i === "ResistanceDamage"){
            continue;
        }
        additive[i] = 0;
    }
    train["additive"] = additive;
    train["Uid"] = uid;
    return train;
}

/**
 * 随机获取3项属性    球员类型type  特训类型index
 */
Heros.prototype.getRoundTrain = function (type, index) {
    let ret = {result: [], randNum: []};
    let config = dataApi.allData.data["FootballerTrain"];
    let arr = [];
    let temp = []; //临时存储各项属性
    let tempTrain = []; //属性随机值
    for (let i in config) {
        if (config[i].PlayerType === type && config[i].Type === index) {
            temp = config[i].PropertyRange;
            tempTrain = config[i].Random;
            break;
        }
    }
    //过滤数组中为0的
    for(let i in temp){
        if(temp[i] === 0){
            continue;
        }
        arr.push(temp[i]);
    }
    //3项属性各随机1个数
    let randNum = randomFrom(tempTrain[0], tempTrain[1]);
    let result = [];
    let ranNum = 3;  //取3个属性
    for (let i = 0; i < ranNum; i++) {
        let ran = Math.floor(Math.random() * arr.length);
        result.push(arr.splice(ran, 1)[0]);
    }
    ret.result = result;
    ret.randNum = randNum;
    return ret;
};

/**
 * 获取球员特训信息    uid  球员uid
 */
Heros.prototype.getTrainInfo = function(uid) {
    let clientHero = {};
    let oneHeroObj = this.getOneHeroTrain(uid);
    for(let i = 1; i < 5; i++){
        clientHero["type" + i] = [];
        for(let j in oneHeroObj[i]){
            clientHero["type"+ i].push(oneHeroObj[i][j]);
        }
    }
    let additive= []; 
    for(let i in oneHeroObj.additive){
        additive.push(oneHeroObj.additive[i]);
    }
    clientHero["additive"] = additive;
    return clientHero;
}

/** 获取一个球员特训属性
 * @param  {} uid 球员uid
 */
Heros.prototype.getOneHeroTrain = function(uid) {
    return this.heroTrain.get(uid);
};

/**
 * 球员特训
 * @param  {}  UID     球员UID
 * @param  {}  index     特训类型  初级  中级 高级 定向
 * @param  {}  arr[]       定向属性
 */
Heros.prototype.trainHero = function (uid, index, arr) {
    let ret = {code: 0, property: [], random: []}
    let heroTrain = this.getOneHeroTrain(uid)[index];
    if(!heroTrain){
        ret.code = Code.FAIL;
        return ret;
    }
    if(index < 1 || index > 4){
        ret.code = Code.FAIL;
        return ret;
    }
    let hero = this.getHero(uid);
    let resId = hero.ResID;
    //球员类型
    let heroType = dataApi.allData.data["Footballer"][resId].Position1;
    let info = this.sundryTrain(heroTrain, heroType, index, arr);
    if(info.code !== Code.OK){
        ret.code = info.code;
        ret.property = info.property;
        ret.random = info.random;
        return ret;
    }
    ret.code = Code.OK;
    ret.property = info.property;
    ret.random = info.random;
    //重新计算球员实力值
    this.reCalcAttrRevision(hero.Uid);
    // this.reCalcAttr(hero.Uid);
    //检查是否在阵容中 在的话需要重新计算球队属性
    this.player.checkHeroInArbitrarilyFormation(uid);
    //触发任务
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.FIFTEEN);
    return ret;
}

//各种特训
Heros.prototype.sundryTrain = function (heroTrain, heroType, index, arr) {
    let ret = {code: 0, property: [], random: []}
    let cost = 0;
    switch(index){
        case 1:
            cost = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.elementaryCost].Param;
            break;
        case 2:
            cost = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.mediumCost].Param;
            //获取活动价
            cost = this.getActivityPrice(cost, 33);
            break;
        case 3:
            cost = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.seniorCost].Param;
            //获取活动价
            cost = this.getActivityPrice(cost, 33);
            break;
        case 4:
            cost = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.directionalCost].Param;
            //获取活动价
            cost = this.getActivityPrice(cost, 33);
            break;
    }
    if(cost === 0){
        ret.code = Code.CASH_FALL;
        return ret;
    }

    //判断钱是否足够
    if(index > 1){
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, cost)){
            ret.code = Code.GOLD_FALL;
            return ret;
        } 
    } else {
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, cost)){
            ret.code = Code.CASH_FALL;
            return ret;
        }  
    }
    //根据类型获取值
    let info = this.getRoundTrain(heroType, index);
    //先初始化一遍
    for(let i in heroTrain){
        if(heroTrain[i] === 0){
            continue;
        }
        heroTrain[i] = 0;
    }
    if(index < 4){
        for(let i in info.result){
            let num = info.result[i];
            for(let j in heroTrain){
                let value = commonEnum.ONE_LEVEL_ATTR_NAMES[j]
                if(num === value){
                    heroTrain[j] = info.randNum[i];
                    break;
                }
            }
        }
    } else { //定向
        if(arr.length < 3){
            ret.code = Code.FAIL;
            return ret;
        }
        for(let i in arr){
            let num = arr[i];
            for(let j in heroTrain){
                let value = commonEnum.ONE_LEVEL_ATTR_NAMES[j]
                if(num === value){
                    heroTrain[j] = info.randNum[i];
                    break;
                }
            }
        }
    }
    //扣钱
    if(index > 1){
        this.player.deductMoney(commonEnum.PLAY_INFO.gold, cost);
    } else {
        this.player.subtractResource(commonEnum.PLAY_INFO.cash, cost);
    }
    ret.code = Code.OK;
    if(index < 4){
        ret.property = info.result; 
    } else {
        ret.property = arr;
    }
    ret.random = info.randNum;
    return ret;
}

/**
 * 替换特训
 * @param  {}  UID     球员UID
 * @param  {}  index     特训类型  初级  中级 高级 定向
 */
Heros.prototype.replaceHeroTrain = function(uid, index){
    let ret = {code: 0}
    let heroTrain = this.getOneHeroTrain(uid);
    if(!heroTrain){
        ret.code = Code.FAIL;
        return ret;
    }
    if(index < 1 || index > 4){
        ret.code = Code.FAIL;
        return ret;
    }
    let hero = this.getHero(uid);
    let oneLevelAttr = hero.oneLevelAttr; 
    //先初始化
    for(let i in heroTrain.additive){
        heroTrain.additive[i] = 0;
        oneLevelAttr[i].Qualificate = 0;
    }
    //属性计算
    for(let i in heroTrain[index]){
        if(heroTrain[index][i] === 0){
            continue;
        }
        //把特训值赋给当前，然后清空特训的值,同时加到球员身上
        let num = heroTrain[index][i];
        heroTrain.additive[i] = num;
        oneLevelAttr[i].Qualificate = num;
        heroTrain[index][i] = 0;
    }
    //重新计算球员实力值
    this.reCalcAttrRevision(hero.Uid);
    // this.reCalcAttr(hero.Uid);
    //检查是否在阵容中 在的话需要重新计算球队属性
    this.player.checkHeroInArbitrarilyFormation(uid)
    ret.code = Code.OK;
    return ret;
}

Heros.prototype.checkHeroTrainData = function(uid){
    let heroTrain = this.getOneHeroTrain(uid);
    if(!heroTrain){
        return;
    }

    let hero = this.getHero(uid);
    if(!hero) {
        return;
    }

    let oneLevelAttr = hero.oneLevelAttr;
    for(let i in heroTrain.additive){
        if(heroTrain.additive[i] === 0) {
            oneLevelAttr[i].Qualificate = 0;
        }
    }
}

//升星初始化
Heros.prototype.initHeroStar = function(uid){ 
    //初始化19项属性
    let star = {};  
    let attrInfo = {}
    for (let i in commonEnum.ONE_LEVEL_ATTR_NAMES) {
        if(i === "ResistanceDamage"){
            continue;
        }
        attrInfo[i] = 0;
    }
    star["Property"] = attrInfo;
    star["Uid"] = uid;
    return star;
}

/** 获取一个球员升星
 * @param  {} uid 球员uid
 */
Heros.prototype.getOneHeroStar = function(uid) {
    return this.heroStar.get(uid);
};


/**
 * 升星
 * @param  {}  UID     球员UID
 * @param  {}  isLock  是否锁定   0未锁定  1锁定
 * @param  {}  isUseCar  是否使用百搭卡  0未使用  1 使用
 */
Heros.prototype.heroUpStar = function(uid, isUseCar, isLock){
    let ret = {code:0, star: 0, isSuccess: 0}
    let heroStar = this.getOneHeroStar(uid);
    let hero = this.getHero(uid);
    if(!heroStar || ! hero){
        ret.code = Code.FAIL;
        ret.star = hero.Star;
        return ret;
    }
    //如果星级已经最大
    if(hero.Star >= 9){
        ret.code = Code.FAIL;
        ret.star = hero.Star;
        return ret;
    }
    let resId = hero.ResID;
    let config = dataApi.allData.data["Footballer"][resId];
    let star = hero.Star;
    let config2 = dataApi.allData.data["FootballerStar"][star + 1];
    if(!config2 || !config){
        ret.code = Code.CONFIG_FAIL;
        ret.star = hero.Star;
        return ret;
    }
    // let initProperty = getInitProperty(config)
    let itemId_1 = config.ItemID;
    let item1 = this.player.item.getItemCountByResId(config.ItemID);  //球星卡
    let item1Uid = this.player.item.getItemUidByResId(config.ItemID);
    let item2 = 0;
    let item2Uid = "";

    let itemId_2 = 0;
    if(config.Color == commonEnum.HERO_SYSTEM.COLOR_1) {
        item2 = this.player.item.getItemCountByResId(commonEnum.HERO_SYSTEM.ITEMID_1);  //铜百搭卡
        item2Uid = this.player.item.getItemUidByResId(commonEnum.HERO_SYSTEM.ITEMID_1);
        itemId_2 = commonEnum.HERO_SYSTEM.ITEMID_1;
    }else if(config.Color == commonEnum.HERO_SYSTEM.COLOR_2 || config.Color == commonEnum.HERO_SYSTEM.COLOR_3 || config.Color == commonEnum.HERO_SYSTEM.COLOR_5) {
        item2 = this.player.item.getItemCountByResId(commonEnum.HERO_SYSTEM.ITEMID_5);  //青训百搭卡
        item2Uid = this.player.item.getItemUidByResId(commonEnum.HERO_SYSTEM.ITEMID_5);
        itemId_2 = commonEnum.HERO_SYSTEM.ITEMID_5;
    }else if(config.Color == commonEnum.HERO_SYSTEM.COLOR_6) {
        item2 = this.player.item.getItemCountByResId(commonEnum.HERO_SYSTEM.ITEMID_2);  //银百搭卡
        item2Uid = this.player.item.getItemUidByResId(commonEnum.HERO_SYSTEM.ITEMID_2);
        itemId_2 = commonEnum.HERO_SYSTEM.ITEMID_2;
    }else if(config.Color == commonEnum.HERO_SYSTEM.COLOR_9) {
        item2 = this.player.item.getItemCountByResId(commonEnum.HERO_SYSTEM.ITEMID_3);  //金百搭卡
        item2Uid = this.player.item.getItemUidByResId(commonEnum.HERO_SYSTEM.ITEMID_3);
        itemId_2 = commonEnum.HERO_SYSTEM.ITEMID_3;
    } else if(config.Color == commonEnum.HERO_SYSTEM.COLOR_15) {
        item2 = this.player.item.getItemCountByResId(commonEnum.HERO_SYSTEM.ITEMID_7);  //印象百搭卡
        item2Uid = this.player.item.getItemUidByResId(commonEnum.HERO_SYSTEM.ITEMID_7);
        itemId_2 = commonEnum.HERO_SYSTEM.ITEMID_7;
    }else if(config.Color == commonEnum.HERO_SYSTEM.COLOR_8 || config.Color == commonEnum.HERO_SYSTEM.COLOR_18 || config.Color == commonEnum.HERO_SYSTEM.COLOR_27
        || config.Color == commonEnum.HERO_SYSTEM.COLOR_28 || config.Color == commonEnum.HERO_SYSTEM.COLOR_29
        || config.Color == commonEnum.HERO_SYSTEM.COLOR_12 || config.Color == commonEnum.HERO_SYSTEM.COLOR_7) {
        item2 = this.player.item.getItemCountByResId(commonEnum.HERO_SYSTEM.ITEMID_4);  //黑百搭卡
        item2Uid = this.player.item.getItemUidByResId(commonEnum.HERO_SYSTEM.ITEMID_4);
        itemId_2 = commonEnum.HERO_SYSTEM.ITEMID_4;
    }else if(config.Color == commonEnum.HERO_SYSTEM.COLOR_31) {
        item2 = this.player.item.getItemCountByResId(commonEnum.HERO_SYSTEM.ITEMID_10);  //红百搭卡
        item2Uid = this.player.item.getItemUidByResId(commonEnum.HERO_SYSTEM.ITEMID_10);
        itemId_2 = commonEnum.HERO_SYSTEM.ITEMID_10;
    }

    //升星消耗
    let costGold = dataApi.allData.data["SystemParam"][3062].Param;
    //获取活动价
    let discount = this.getActivityPrice(costGold, 33);
    if(isLock === 1) {
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, discount)){
            ret.code = Code.GOLD_FALL;
            return ret;
        }
    }

    let itemInfo = {
        itemId_1: itemId_1,
        num1: 0,
        itemId_2: itemId_2,
        num2: 0,
        isLock: isLock,
        costGold: 0,
    }

    // 使用百搭卡
    if(isUseCar === 1){
        if(item1 >= config2.Cost){  //球星卡足够
            this.player.item.delItem(item1Uid, config2.Cost);
            itemInfo.num1 = config2.Cost;
        } else{       //球星卡不够
            if(!item1) {
                item1 = 0;
            }
            let cost = config2.Cost - item1;   //球星卡不够再用百搭卡
            if(item2 < cost){     
                ret.code = Code.ITEM_FAIL;
                return ret;
            }

            //扣除道具
            if(item1Uid){
                itemInfo.num1 = item1;
                this.player.item.delItem(item1Uid, item1);
            }
            itemInfo.num2 = cost;
            this.player.item.delItem(item2Uid, cost);
        }    
    }else {
        if(item1 < config2.Cost){
            ret.code = Code.ITEM_FAIL;
            return ret;
        }
        //扣除道具
        this.player.item.delItem(item1Uid, config2.Cost);
        itemInfo.num1 = config2.Cost;
    }

    let logArr = [resId, star];  //v1:球员Id  v2:当前星级 v3:升星后星级 
    //锁定
    if(isLock === 1){
        //扣钱
        this.player.deductMoney(commonEnum.PLAY_INFO.gold, discount);
        ret.isSuccess = 1;
        hero.Star ++;
        itemInfo.costGold = discount;
        logArr.push(star+1);
    }else {
        let downgrade = dataApi.allData.data["FootballerStar"][star+1].Downgrade;
        let rand = Math.floor(Math.random() * 10000)
        if(rand <= config2.Probability){       
            hero.Star++;
            ret.isSuccess = 1;
            logArr.push(star+1);
        }else{
            hero.Star = downgrade;
            logArr.push(downgrade);
        }
    }
    //计算升星加成
    // for(let i in heroStar.Property){
    //     let init = initProperty[i].Init;
    //     heroStar.Property[i] = init * (hero.Star * 0.04);
    // }
    for(let i in hero.oneLevelAttr){
        let init = hero.oneLevelAttr[i].Base;
        heroStar.Property[i] = init * (hero.Star * 0.04);
    }

    //重新计算球员实力值
    this.reCalcAttrRevision(hero.Uid);
    // this.reCalcAttr(hero.Uid);
    //检查是否在阵容中 在的话需要重新计算球队属性
    this.player.checkHeroInArbitrarilyFormation(uid)
    //更新技能状态
    this.updateSkillStatus(uid, 3, hero.Star);
    //触发任务
    if(hero.Star >= 9) {
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.EIGHTEEN);
    }
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.NINETEEN, 0, 0, hero.Star);
    this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.UPGRADE_STAR, logArr, itemInfo);
    ret.code = Code.OK;
    ret.star = hero.Star;
    return ret;
}

//修复升星加成
Heros.prototype.fixHeroStarData = function(uid){
    let heroStar = this.getOneHeroStar(uid);
    if(!heroStar) {
        //logger.error("wwwwwwwwwwwwwwwwwww", uid);
        return;
    }
    let hero = this.getHero(uid);
    for(let i in hero.oneLevelAttr){
        if (i !== "ResistanceDamage") {
            let init = hero.oneLevelAttr[i].Base;
            heroStar.Property[i] = init * (hero.Star * 0.04);
        }
    }
}


//球员技能初始化
Heros.prototype.initHeroSkill = function(uid){ 
     //添加技能ID
     let heroSkill = {};
     let hero = this.getHero(uid);
     //let config = dataApi.allData.data["Footballer"][hero.ResID];
    let config;
    let bead = hero.ResID / 10000;
    if(bead < 9)
    {
        config = dataApi.allData.data["Footballer"][hero.ResID];
    }
    else
    {
        config = dataApi.allData.data["FootballerPve"][hero.ResID];
    }

     if(!config){
         logger.error("initHeroSkill config is fail~~~~~~");
         return {};
     }
     let skillList = [];
     //反序
     let configSkillList = utils.deepClone(config.SkillList);
     configSkillList.reverse();
     let index = 0;
     for(let i = 0; i < configSkillList.length; ++i) {
         //过滤数组中为0的
         if (configSkillList[i] === 0) {
             continue;
         }
         let value = configSkillList[i];
         // let id = index + 1;
         // let unlockConfig = dataApi.allData.data["SkillUnlock"][id];
         // if (!unlockConfig) {
         //     continue;
         // }
         skillList[index] = {};
         skillList[index].skillId = value;
         //负面技能直接激活
         // if (unlockConfig.AbilityValue === 0 && unlockConfig.PotentialValue === 0
         //     && unlockConfig.StarValue === 0 && unlockConfig.AttributeValue === 0) {
         //     skillList[index].isActivate = 1;   //是否激活  0未激活  1已激活
         // } else {
         skillList[index].isActivate = 0;
         // }
         index++;
     }
     heroSkill["SkillList"] = skillList;
     heroSkill["Rating"] = 0;
     heroSkill["Star"] = 0;
     heroSkill["Potential"] = 30;
     heroSkill["AttributeValue"] = 1;
     heroSkill["Uid"] = uid;

     return heroSkill;
}

Heros.prototype.checkHeroSkill = function(){
    for(let [uid, v] of this.allheros) {
        let heroSkill = this.heroSkill.get(uid);
        if(!heroSkill) {
            continue;
        }
        let hero = this.getHero(uid);
        if(!hero) {
            continue;
        }
        //let config = dataApi.allData.data["Footballer"][hero.ResID];
        let config;
        let bead = hero.ResID / 10000;
        if(bead < 9) {
            config = dataApi.allData.data["Footballer"][hero.ResID];
        } else {
            config = dataApi.allData.data["FootballerPve"][hero.ResID];
        }

        this.delOrAddHeroSkill(uid, heroSkill.SkillList, config.SkillList);

        //主要属性阶段检查
        let stage = this.getHeroMainAttributeStage(uid);
        this.updateSkillStatus(uid, 4, stage);
    }
}

//获取主要属性阶段
Heros.prototype.getHeroMainAttributeStage = function (uid) {
    let hero = this.getHero(uid);
    if(!hero) {
        return 1;
    }

    let config;
    let bead = hero.ResID / 10000;
    if(bead < 9) {
        config = dataApi.allData.data["Footballer"][hero.ResID];
    } else {
        config = dataApi.allData.data["FootballerPve"][hero.ResID];
    }

    if(!config) {
        return 1;
    }

    //主要属性  大类
    let type = this.getPropertyByPos(config.Position1);
    //配表有问题
    if(type === 0) {
        return 1;
    }
    let stage = hero.isStage[type - 1];
    return stage;
}

//根据位置获取主要属性类型
Heros.prototype.getPropertyByPos = function(position) {
    let type = 0;
    if(position === "GK") {  //守门
        type = 6;
    } else if(position === "DL" || position === "DC" || position === "DR") {       //防守
        type = 2;
    } else if(position === "WL" || position === "ST" || position === "WR") {       //射术
        type = 3;
    } else if(position === "AM" || position === "ML" || position === "MC" || position === "DM" || position === "MR") {      //技巧
        type = 4;
    }
    return type;
}

//修复技能数据
Heros.prototype.onceFixHeroSkill = function(uid){
    if(this.fixId === 1) {
        return;
    }

    let heroSkill = this.heroSkill.get(uid);
    if(!heroSkill) {
        return;
    }
    let hero = this.getHero(uid);
    if(!hero) {
        return;
    }

    let config;
    let bead = hero.ResID / 10000;
    if(bead < 9)
    {
        config = dataApi.allData.data["Footballer"][hero.ResID];
    }
    else
    {
        config = dataApi.allData.data["FootballerPve"][hero.ResID];
    }

    heroSkill.SkillList = [];
    let skillList = [];
    let index = 0;
    let configSkillList = utils.deepClone(config.SkillList);
    configSkillList.reverse();
    for(let i = 0; i < configSkillList.length; ++i){
        //过滤数组中为0的
        if(configSkillList[i] === 0){
            continue;
        }
        let value = configSkillList[i];
        let id = index + 1;
        let unlockConfig = dataApi.allData.data["SkillUnlock"][id];
        if(!unlockConfig) {
            continue;
        }
        skillList[index] = {};
        skillList[index].skillId = value;
        //负面技能直接激活
        if(unlockConfig.AbilityValue === 0 && unlockConfig.PotentialValue === 0
            && unlockConfig.StarValue === 0 && unlockConfig.AttributeValue === 0){
            skillList[index].isActivate = 1;   //是否激活  0未激活  1已激活
        }else {
            skillList[index].isActivate = 0;
        }
        index++;
    }
    heroSkill.SkillList = skillList;
}

//新增或者删除技能
Heros.prototype.delOrAddHeroSkill = function(uid, src, config) {
    if(config.length < 1) {
        return;
    }


    let dest = utils.deepClone(config);
    dest.reverse();

    let index = 0;
    let isClean = false;
    for(let i = 0; i < dest.length; i++) {
        if(dest[i] !== 0) {
            let id = index + 1;
            let unlockConfig = dataApi.allData.data["SkillUnlock"][id];
            if(!src[index]) {
                src[index] = {};
                src[index].skillId = dest[i];
                if (unlockConfig.AbilityValue === 0 && unlockConfig.PotentialValue === 0
                    && unlockConfig.StarValue === 0 && unlockConfig.AttributeValue === 0) {
                    src[index].isActivate = 1;   //是否激活  0未激活  1已激活
                } else {
                    src[index].isActivate = 0;
                }
            }else if(src[index].skillId !== dest[i]) {
                isClean = true;
                src[index].skillId = dest[i];
                if (unlockConfig.AbilityValue === 0 && unlockConfig.PotentialValue === 0
                    && unlockConfig.StarValue === 0 && unlockConfig.AttributeValue === 0) {
                    src[index].isActivate = 1;   //是否激活  0未激活  1已激活
                } else {
                    src[index].isActivate = 0;
                }
            }
            index++;
        }
    }

    //只要有一个技能修改过就需要重新激活技能
    if(isClean) {
        let hero = this.getHero(uid);
        let oneLevelAttr = hero["oneLevelAttr"];
        for (let key in oneLevelAttr) {
            let attrInfo = oneLevelAttr[key];
            if (key !== "ResistanceDamage") {
                attrInfo.SkillAct = 0;
                attrInfo.Skill = 0;
            }
        }
        this.reCalcAttrRevision(uid);
        // this.reCalcAttr(uid);
    }
}

    /** 获取一个球员技能
 * @param  {} uid 球员uid
 */
Heros.prototype.getOneHeroSkill = function(uid) {
    return this.heroSkill.get(uid);
};

/**
 * 获取球员技能信息    uid  球员uid
 * 类型      1自己  2别人
 */
Heros.prototype.getHeroSkillInfo = function(uid, type) {
    let ret = {
        unLockList:[],
        clientHero: {}
    }

    if(type === 1) {
        let result = this.activateHeroSkill(uid);
        if(result.code === Code.OK) {
            ret.unLockList = result.skillList;
        }
    }

    let oneHeroObj = this.getOneHeroSkill(uid);
    if(!oneHeroObj) {
        //logger.error("ttttttttttttttttttttt", uid)
        return ret;
    }
    let index = 0;
    let skillList = [];
    for(let i in oneHeroObj.SkillList){
        skillList[index] = {};
        skillList[index].skillId = oneHeroObj.SkillList[i].skillId;
        skillList[index].isActivate = oneHeroObj.SkillList[i].isActivate;   //是否激活  0未激活  1已激活
        index++;
    }
    ret.clientHero["skillList"] = skillList;
    ret.clientHero["rating"] = oneHeroObj.Rating;
    ret.clientHero["star"] = oneHeroObj.Star;
    ret.clientHero["potential"] = oneHeroObj.Potential;
    ret.clientHero["attributeValue"] = oneHeroObj.AttributeValue;
    ret.clientHero["uid"] = uid;
    return ret;
}

/**
 * 球员技能激活
 * @param  {}  UID       球员UID
 */
Heros.prototype.activateHeroSkill = function(uid){
    let ret = {
        code: Code.FAIL,
        skillList: []
    }

    let hero = this.getHero(uid);
    let heroSkill = this.getOneHeroSkill(uid);
    if(!hero || !heroSkill){
        ret.code = Code.FAIL;
        return ret;
    }
    // logger.debug("activateHeroSkill------start-------")
    let isNeedUpdate = false;
    //实力 星级 潜能都达到要求
    for(let i = 0; i < heroSkill.SkillList.length; ++i){
        if(heroSkill.SkillList[i].isActivate === 1) {
            continue;
        }

        let index = i + 1;
        let skillId = heroSkill.SkillList[i].skillId;
        let unlockConfig = dataApi.allData.data["SkillUnlock"][index];
        let config = dataApi.allData.data["FootballerSkill"][skillId];
        if(!unlockConfig || !config) {
           continue;
        }

        if(heroSkill.Rating >= unlockConfig.AbilityValue && heroSkill.Star >= unlockConfig.StarValue
            && heroSkill.Potential >= unlockConfig.PotentialValue && heroSkill.AttributeValue >= unlockConfig.AttributeValue){

            ret.skillList.push(skillId);
            heroSkill.SkillList[i].isActivate = 1; //激活技能
            let num = 65;  // 65 = A    String.fromCharCode(num)
            let addList = [];
            for(let i = 0; i < 4; ++i) {
                let str = String.fromCharCode(num);
                // 1技能激活生效
                if(config["Type"+str+"Opportunity"] === 1) {
                    if(config["Type" + str] !== 0) {
                        if(config["Type" + str] > 10 && config["Type" + str] < 30) {
                            let propertyInfo = {};
                            propertyInfo.id = config["Type" + str] - 10;               //enum里面的属性从1开始
                            propertyInfo.value = config["Type" + str + "Value"];
                            addList.push(propertyInfo);
                        }
                    }
                }
                num++;
            }

            let oneLevelAttr = hero.oneLevelAttr;
            for(let j = 0; j < addList.length; ++j) {
                for(let i in commonEnum.ONE_LEVEL_ATTR_NAMES) {
                    if(addList[j].id === commonEnum.ONE_LEVEL_ATTR_NAMES[i]) {
                        oneLevelAttr[i].SkillAct += addList[j].value;
                    }
                }
            }
            isNeedUpdate = true;
        }
    }


    //有技能激活才计算
    if(isNeedUpdate) {
        //重新计算球员实力值
        this.reCalcAttrRevision(uid);
        // this.reCalcAttr(uid);
        //检查是否在阵容中 在的话需要重新计算球队属性
        this.player.checkHeroInArbitrarilyFormation(uid);
        this.player.upAddHero(uid)
    }

    // logger.debug("activateHeroSkill------end-------")
    ret.code = Code.OK;
    return ret;
}

//检查球员技能加成
Heros.prototype.checkHeroSkillAct = function(uid) {
    let hero = this.getHero(uid);
    let heroSkill = this.getOneHeroSkill(uid);
    if(!hero || !heroSkill){
        return;
    }
    //logger.error("checkHeroSkillAct------start-------")
    //实力 星级 潜能都达到要求
    for(let i = 0; i < heroSkill.SkillList.length; ++i){
        if(heroSkill.SkillList[i].isActivate !== 1) {
            continue;
        }

        let index = i + 1;
        let skillId = heroSkill.SkillList[i].skillId;
        let unlockConfig = dataApi.allData.data["SkillUnlock"][index];
        let config = dataApi.allData.data["FootballerSkill"][skillId];
        if(!unlockConfig || !config) {
            continue;
        }


        if(heroSkill.Rating >= unlockConfig.AbilityValue && heroSkill.Star >= unlockConfig.StarValue
            && heroSkill.Potential >= unlockConfig.PotentialValue && heroSkill.AttributeValue >= unlockConfig.AttributeValue){
            let num = 65;  // 65 = A    String.fromCharCode(num)
            let addList = [];
            for(let i = 0; i < 4; ++i) {
                let str = String.fromCharCode(num);
                //1 技能激活就生效
                if(config["Type"+str+"Opportunity"] === 1) {
                    if(config["Type" + str] !== 0) {
                        if(config["Type" + str] > 10 && config["Type" + str] < 30) {  //11-29
                            let propertyInfo = {};
                            propertyInfo.id = config["Type" + str] - 10;               //enum里面的属性从1开始
                            propertyInfo.value = config["Type" + str + "Value"];
                            addList.push(propertyInfo);
                        }
                    }
                }
                num++;
            }

            let oneLevelAttr = hero.oneLevelAttr;
            for(let j = 0; j < addList.length; ++j) {
                for(let i in commonEnum.ONE_LEVEL_ATTR_NAMES) {
                    if(addList[j].id === commonEnum.ONE_LEVEL_ATTR_NAMES[i]) {
                        oneLevelAttr[i].SkillAct += addList[j].value;
                    }
                }
            }
        }
    }
    //logger.error("checkHeroSkillAct------end-------")
}

/**
 * 更新技能状态
 * @param  {}  uid       球员UID
 * @param  {}  type      类型  1实力  2潜能  3星级  4主要属性
 * @param  {}  value     新值 
 */
Heros.prototype.updateSkillStatus = function(uid, type, value){
    let heroSkill = this.getOneHeroSkill(uid);
    if(!heroSkill){
        return;
    }

    switch(type){
        case 1:
            if(heroSkill.Rating < value){
                heroSkill.Rating = value;
            }
            break;
        case 2:
            if(heroSkill.Potential < value){
                heroSkill.Potential = value;
            }
            break;
        case 3:
            if(heroSkill.Star < value){
                heroSkill.Star = value;
            }
            break;
        case 4:
            if(heroSkill.AttributeValue < value){
                heroSkill.AttributeValue = value;
            }
            break;
        default:
            break;
    }

    //激活技能
    // this.activateHeroSkill(uid)
}

/**
 * 球员技能升级
 * @param  {}  UID       球员UID
 * @param  {}  skillId   技能Id
 */
Heros.prototype.upgradeHeroSkill = function(uid, skillId){
};

/**************************************球员特训及升星******end***********************************************/

/**************************************球员信息******begin***********************************************/
/**
 * 提升球员状态
 * @param  {}  UID       球员UID
 * @param  {}  itemId    道具Id
 */
Heros.prototype.promoteHeroStatus = function(uid, itemId){
    let ret = {};
    let hero = this.getHero(uid);
    if(!hero) {
        ret.code = Code.FAIL;
        return ret;
    }

    //已经巅峰直接返回
    if(hero.Status === commonEnum.HERO_STATUS.PEAKEDNESS) {
        ret.code = Code.HERO_CODE.STATUS_MAX;
        return ret;
    }

    let oldStatus = hero.Status;   //球员当前状态
    //获取物品数量
    let itemNum = this.player.item.getItemCountByResId(itemId);
    let itemUid = this.player.item.getItemUidByResId(itemId);
    if(itemNum < 1) {
        ret.code = Code.ITEM_FAIL;
        return ret;
    }

    if(itemId === 10700) {  //状态卡
        //普通卡
        hero.Status--;
    } else if(itemId === 10703){    //球员状态卡
        let newStatus = oldStatus;
        if(hero.ThreeDropNum >= 3) {
            newStatus = oldStatus - 2;
            hero.ThreeDropNum = 0;
        }else if(hero.TenDropNum >= 10) {
            newStatus = commonEnum.HERO_STATUS.FINE;
            hero.TenDropNum = 0;
        }else {
            newStatus = this.heroStatusRandom(oldStatus);
            //记录降级  连续降级3次
            if(newStatus > oldStatus) {
                hero.ThreeDropNum ++;
            }else {
                hero.ThreeDropNum = 0;
            }

            //记录未到巅峰次数  连续使用10次未到达巅峰
            if(newStatus !== commonEnum.HERO_STATUS.PEAKEDNESS) {
                hero.TenDropNum ++;
            }else if(newStatus === commonEnum.HERO_STATUS.PEAKEDNESS) {
                hero.TenDropNum = 0;
            }
        }
        if(newStatus < 1) {
            newStatus = 1;
        }
        hero.Status = newStatus;
    }else {
        //巅峰卡
        hero.Status = commonEnum.HERO_STATUS.PEAKEDNESS;
    }
    //消耗状态卡
    this.player.item.delItem(itemUid, 1);
	hero.BattleNum = 0;    //重新计算属性
    this.reCalcAttrRevision(uid);
    // this.reCalcAttr(uid);
    //检查是否在阵容中 在的话需要重新计算球队属性
    this.player.checkHeroInArbitrarilyFormation(uid);
    //触发任务
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.THIRTY_EIGHT);
    ret.code = Code.OK;
    ret.status = hero.Status;
    return ret;
};

/**
 * 球员状态权重随机
 * @param  status 当前球员状态
 */
Heros.prototype.heroStatusRandom = function (status) {
    let statusConfig = dataApi.allData.data["StateProbability"][status];
    if(!statusConfig) {
        return commonEnum.HERO_STATUS.COMMONLY;
    }
    let randomConfig = [];
    let index = 0;
    for(let i = 1; i < 6; ++i) {
        let statusType = "StatusType" + i;
        let weight = "StateProbability" + i;
        randomConfig[index] = {};
        randomConfig[index].type = statusConfig[statusType];
        randomConfig[index].weight = statusConfig[weight];
        index++;
    }

    let randomList = [];
    for (let i in randomConfig) {
        for (let j = 0; j < randomConfig[i].weight; j++) {
            randomList.push(randomConfig[i].type);
        }
    }
    let randomValue = randomList[Math.floor(Math.random() * randomList.length)];
    return randomValue;
};




//增加球员战斗场数
Heros.prototype.addHeroBattleNum = function(type){
    let teamFormationUid = this.player.teamFormations.getCurrTeamFormationId();
    let teamFormatin = this.player.teamFormations.getOneTeamFormation(teamFormationUid);
    let object = teamFormatin.PositionToHerosObject;
    if (!object) {
        return;
    }
    let battleNum = dataApi.allData.data["SystemParam"][commonEnum.HERO_STATUS.BATTLE_NUM].Param;
    let statusConfig = dataApi.allData.data["FootballerForm"];
    if(!battleNum || !statusConfig) {
        return;
    }

    let heroPointConfig = dataApi.allData.data["FootballerFormPoint"];
    let addPoint = 0;
    for(let j in heroPointConfig) {
        if(heroPointConfig[j].Type === type) {
            addPoint = heroPointConfig[j].Point;
            break;
        }
    }

    let isUpdate = false;
    for (let i in object) {
        let heroArr = object[i];
        for (let j = 0; j < heroArr.length; j++) {
            let hero = this.getHero(heroArr[j]);
            if(!hero) {
                continue;
            }
            hero.BattleNum += addPoint;
            if(hero.BattleNum > battleNum) {
                let randNum = this._weightRandom(statusConfig);
                hero.Status = randNum;
                hero.BattleNum = 0;
                if(!isUpdate) {
                    isUpdate = true;
                }
                //重新计算球员实力
                this.reCalcAttrRevision(heroArr[j]);
                // this.reCalcAttr(heroArr[j]);
            }
        }
    }
    
    //有状态变化 更新阵容数据
    if(isUpdate) {
        this.player.teamFormations.recalcAllTeamFormationAttr();    
        this.player.saveTeamFormation();
        this.player.updateTeamFormations();
    }
}

/**
 * 球员状态权重随机
 * @param  statusConfig 球员状态表
 */
Heros.prototype._weightRandom = function (statusConfig) {
    let randomConfig = [];
    let index = 0;
    for (let i in statusConfig) {
        randomConfig[index] = {};
        randomConfig[index].form = statusConfig[i].Form;
        randomConfig[index].weight = statusConfig[i].Weight;
        index++;
 
    }
    let randomList = [];
    for (let i in randomConfig) {
        for (let j = 0; j < randomConfig[i].weight; j++) {
            randomList.push(randomConfig[i].form);
        }
    }
    let randomValue = randomList[Math.floor(Math.random() * randomList.length)];
    return randomValue;
};


/**
 * 续约合同
 * @param  statusConfig 球员状态表
 */
Heros.prototype.renewTheContract = function (heroUidList) { 
    let ret = {};
    if(heroUidList.length < 1) {
        ret.code = Code.RANGE_FAIL;
        return ret;
    }

    let sysConfig = dataApi.allData.data["SystemParam"][commonEnum.HERO_TREATY.SEASON_NUM].Param;   //赛季
    let treatyDay = dataApi.allData.data["SystemParam"][commonEnum.HERO_TREATY.TREATY_DAY].Param;   //续约天数
    let seasonDay = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.LeagueSeason].Param;   //赛季天数
    if(!sysConfig || !treatyDay || !seasonDay) {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    let sumMoney = 0;
    for(let i = 0; i < heroUidList.length; ++i) {
        let uid = heroUidList[i];
        let hero = this.getHero(uid);
        if(!hero) {
            continue;
        }

        //已满合约跳过
        if(hero.TreatyDay === treatyDay) {
            continue;
        }
        let config = dataApi.allData.data["Footballer"][hero.ResID];
        if(!config) {
           continue;
        }
        //能续约的天数
        let canTreatyDay = treatyDay - hero.TreatyDay;
        //球员身价
        let heroMoney = config.Value;  
        // 续约花费=球员表“价值”÷“系统参数表ID103”÷21×续约天数
        let costMoney = Math.floor((heroMoney / sysConfig / seasonDay) * canTreatyDay);
        sumMoney += costMoney;
    }
    
    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, sumMoney)) {
        ret.code = Code.CASH_FALL;
        return ret;
    }
    //扣钱
    this.player.subtractResource(commonEnum.PLAY_INFO.cash, sumMoney);
    
    //恢复合约天数
    for(let i = 0; i < heroUidList.length; ++i) { 
        let uid = heroUidList[i];
        let hero = this.getHero(uid);
        if(!hero) {
            continue;
        }

        //已满合约跳过
        if(hero.TreatyDay === treatyDay) {
            continue;
        }
        
        let isUpdate = false;
        if(hero.TreatyDay === 0) {
            isUpdate = true;
        }

        hero.TreatyDay = treatyDay;
        hero.TreatyReTime = TimeUtils.now();
        this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.HERO_RENEWAL, [ hero.ResID, hero.TreatyDay], {});
        if(isUpdate) {
            //重新计算属性
            this.reCalcAttrRevision(uid);
            // this.reCalcAttr(uid);
            //检查是否在阵容中 在的话需要重新计算球队属性
            this.player.checkHeroInArbitrarilyFormation(uid)
        } 
    }
    ret.code = Code.OK;
    return ret;
}


/**
 * 检查球员合同是否到期
 * @param  uid 球员UID
 */
Heros.prototype.checkHeroTreatyDay = function () { 
    for(let [uid, v] of this.allheros) {
        let hero = this.getHero(uid);
        if(!hero) {
            continue;
        }

        //已经退役了 不再减天数
        if(hero.isDie === 1) {
            continue;
        }

        //已经到期的就不用检查了
        if(hero.TreatyDay < 1) {
            continue;
        }

        let day = TimeUtils.dayInterval(hero.TreatyReTime);
        //是否同一天
        if(!TimeUtils.isToday(hero.TreatyReTime)) {
            hero.TreatyDay -= day;
            if(hero.TreatyDay < 0) {
                hero.TreatyDay = 0;
            }
            hero.TreatyReTime = TimeUtils.now();
        }

        //合约到期重新计算球员实力
        if(hero.TreatyDay === 0) {
            this.reCalcAttrRevision(uid);
            // this.reCalcAttr(uid);
        }
    }
    // this.player.saveHeros();
}


/**
 * 增加球员疲劳值
 * @param selfScore
 * @param otherScore
 */
Heros.prototype.addHeroFatigue = function (selfScore, otherScore) {
    if(selfScore === undefined || otherScore === undefined) {
        return;
    }

    let teamFormationUid = this.player.teamFormations.getCurrTeamFormationId();
    let teamFormation = this.player.teamFormations.getOneTeamFormation(teamFormationUid);
    let team = teamFormation.PositionToHerosObject;
    if (!team) {
        return;
    }

    //上阵胜利PL系数
    let winCoefficient = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.WIN].Param;
    //上阵平局PL系数
    let drawCoefficient = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.DRAW].Param;
    //上阵输局PL系数
    let loseCoefficient = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.LOSE].Param;

    //其他球员胜利PL系数
    let otherWinCoefficient = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.OTHER_WIN].Param;
    //其他球员平局PL系数
    let otherDrawCoefficient = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.OTHER_DRAW].Param;
    //其他球员输局PL系数
    let otherLoseCoefficient = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.OTHER_LOSE].Param;

    //轻微衰减系数
    let slightInjuryNum = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.SLIGHT_INJURY_NUM].Param;
    //轻伤衰减系数
    let minorInjuryNum = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.MINOR_INJURY_NUM].Param;
    //重伤衰减系数
    let seriousInjuryNum = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.SERIOUS_INJURY_NUM].Param;

    //重伤时间
    let seriousInjuryTime = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.SERIOUS_INJURY_TIME].Param;
    //伤病球队疲劳临界值
    let teamMaxFatigue = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.TEAM_MAX_FATIGUE].Param;

    if(winCoefficient === null ||drawCoefficient === null || loseCoefficient === null
    || otherWinCoefficient === null || otherDrawCoefficient === null || otherLoseCoefficient === null
    || seriousInjuryTime === null || slightInjuryNum === null || minorInjuryNum === null || seriousInjuryNum === null) {
        return;
    }

    let teamTired = 0;
    //胜局加PL
    if(selfScore > otherScore) {
        teamTired = this.calcHeroBattleFatigue(team, winCoefficient, otherWinCoefficient, false);
    } else if(selfScore < otherScore) {
        teamTired = this.calcHeroBattleFatigue(team, loseCoefficient, otherLoseCoefficient, false);
    }else {
        teamTired = this.calcHeroBattleFatigue(team, drawCoefficient, otherDrawCoefficient, false);
    }

    //没有超过直接返回
    if(teamTired < teamMaxFatigue) {
        return;
    }

    let randomUid = this.fatigueWeightRandom(team);
    let hero = this.getHero(randomUid);
    if(!hero) {
        return;
    }

    //置状态
    let randHealth = this.heroHealthWeightRandom(hero.ResID);
    hero.Health = randHealth;
    if(hero.Health === commonEnum.HERO_HEALTH_STATUS.SLIGHT_INJURY) {
        hero.FatigueRatio = slightInjuryNum;
    } else if(hero.Health === commonEnum.HERO_HEALTH_STATUS.MINOR_INJURY){
        hero.FatigueRatio = minorInjuryNum;
    }else if(hero.Health === commonEnum.HERO_HEALTH_STATUS.SERIOUS_INJURY){
        hero.FatigueRatio = seriousInjuryNum;
    }

    //受伤其余球员减PL
    this.calcHeroBattleFatigue(team, 0, 0, true);

    //重新计算球员属性
    this.reCalcAttrRevision(randomUid);
    // this.reCalcAttr(randomUid);
    this.player.checkHeroInArbitrarilyFormation(randomUid);
    this.player.updateHeros();
    this.player.updateTeamFormations();
    this.player.updateHeroHealth(randomUid, randHealth);
    this.player.saveHeros();
    this.player.saveTeamFormation();
}

/**
 * 计算PL
 * @param teamObj  阵容数据
 * @param coefficient 上阵球员
 * @param otherCoefficient  不上阵球员
 * @param isBruise   是否受伤
 */
Heros.prototype.calcHeroBattleFatigue = function (teamObj, coefficient, otherCoefficient, isBruise) {
    //球队总PL值
    let teamTired = 0;
    let heroMap = new Map();
    //上阵球员加PL
    for (let i in teamObj) {
        let heroArr = teamObj[i];
        for (let j = 0; j < heroArr.length; j++) {
            let hero = this.getHero(heroArr[j]);
            if (!hero) {
                continue;
            }
            //PL基数
            let tiredNum = dataApi.allData.data["Footballer"][hero.ResID].Fatigue;
            if(!tiredNum) {
                continue;
            }

            if(!heroMap.has(heroArr[j])) {
                heroMap.set(heroArr[j], 1);
            }
            if(isBruise) {
                // 伤病触发后当前阵容球员疲劳系数变化
                let attenuationNum = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.ATTENUATION_NUM].Param;
                if(attenuationNum) {
                    hero.Fatigue += tiredNum * attenuationNum;
                    if(this.uid === debugCfg.tiredUid) {
                        logger.error("teamFormation calcHeroBattleFatigue--isBruise--HeroPL:", hero.Fatigue, "+configParam:", attenuationNum)
                    }
                }

                if(hero.Fatigue < 0) {
                    hero.Fatigue = 0;
                }
            } else {
                //球员PL
                hero.Fatigue += tiredNum * coefficient;
                if(this.uid === debugCfg.tiredUid) {
                    logger.error("teamFormation calcHeroBattleFatigue--HeroPL:", hero.Fatigue, "+configParam:", tiredNum)
                }
            }
            //球队PL
            teamTired += hero.Fatigue;
        }
    }

    //未上场球员减PL
    for(let [uid, obj] of this.allheros) {
        if(!heroMap.has(uid)) {
            let hero = this.getHero(uid);
            //PL基数
            let tiredNum = dataApi.allData.data["Footballer"][obj.ResID].Fatigue;
            if(!tiredNum) {
                continue;
            }
            //过滤已经是PL为的0
            if(hero.Fatigue === 0) {
                continue;
            }

            if(isBruise) {
                //伤病触发后其他球员疲劳系数变化
                let otherAttenuationNum = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.OTHER_ATTENUATION_NUM].Param;
                if(otherAttenuationNum) {
                    hero.Fatigue += tiredNum * otherAttenuationNum;
                    if(this.uid === debugCfg.tiredUid) {
                        logger.error("not in teamFormation calcHeroBattleFatigue--isBruise--otherHeroPL:", hero.Fatigue, "+configParam:", otherAttenuationNum)
                    }
                }
            } else {
                hero.Fatigue += tiredNum * otherCoefficient;
                if(this.uid === debugCfg.tiredUid) {
                    logger.error("not in teamFormation calcHeroBattleFatigue--otherHeroPL:", hero.Fatigue, "+configParam:", tiredNum)
                }
            }

            if(hero.Fatigue < 0) {
                hero.Fatigue = 0;
            }
        }
    }

    heroMap.clear();
    if(this.uid === debugCfg.tiredUid) {
        logger.error("calcHeroBattleFatigue--teamFormationPL:", teamTired)
    }
    return teamTired;
}

/**
 * 球员权重随机
 * @param teamObj  阵容数据
 */
Heros.prototype.fatigueWeightRandom = function (teamObj) {
    let randomConfig = [];
    let index = 0;
    for (let key in teamObj) {
        let heroArr = teamObj[key];
        for (let j = 0; j < heroArr.length; j++) {
            let hero = this.getHero(heroArr[j]);
            if (!hero) {
                continue;
            }

            //过滤已经生病的
            if(hero.Health === commonEnum.HERO_HEALTH_STATUS.HEALTH) {
                randomConfig[index] = {};
                randomConfig[index].uid = heroArr[j];
                randomConfig[index].weight = hero.Fatigue;
                if(this.uid === debugCfg.tiredUid) {
                    let name = dataApi.allData.data["Footballer"][hero.ResID].CnName;
                    logger.error("hero name is:", name, "weight:", hero.Fatigue);
                }
                index++;
            }
        }
    }
    
    if (randomConfig.length < 1) {
        return "";
    }

    let randomList = [];
    for (let i in randomConfig) {
        if(randomConfig[i].weight > 0) {
            for (let j = 0; j < randomConfig[i].weight; j++) {
                randomList.push(randomConfig[i].uid);
            }
        }
    }

    let range = Math.floor(Math.random() * randomList.length);
    let randomUid = randomList[range];
    if(this.uid === debugCfg.tiredUid) {
        let resId = this.getHero(randomUid).ResID;
        let heroName = dataApi.allData.data["Footballer"][resId].CnName;
        logger.error("hero rand value", range, "all weight", randomList.length, "rand hero name is:", heroName);
    }
    return randomUid;
};


/**
 * 球员健康状态权重随机
 * @param resId  球员id
 * @return randomValue  健康状态
 */
Heros.prototype.heroHealthWeightRandom = function (resId) {
    let config = dataApi.allData.data["Footballer"][resId];
    if(!config) {
        return commonEnum.HERO_HEALTH_STATUS.HEALTH;
    }

    let randomConfig = [];
    let index = 0;
    for(let j = 1; j < 4; ++j){
        randomConfig[index] = {};
        randomConfig[index].id = commonEnum.HERO_HEALTH_STATUS.HEALTH + j;
        let weight = 0;
        if(j === 1) {
            weight = config.SlightInjury;
        }else if(j === 2) {
            weight = config.MinorInjury;
        }else {
            weight = config.SeriousInjury;
        }
        randomConfig[index].weight = weight;
        index++;
    }


    if (randomConfig.length < 1) {
        return commonEnum.HERO_HEALTH_STATUS.HEALTH;
    }

    let randomList = [];
    for (let i in randomConfig) {
        if(randomConfig[i].weight > 0) {
            for (let j = 0; j < randomConfig[i].weight; j++) {
                randomList.push(randomConfig[i].id);
            }
        }
    }

    let range = Math.floor(Math.random() * randomList.length);
    let randomValue = randomList[range];
    if(this.uid === debugCfg.tiredUid) {
        logger.error("hero health random", range, "hero health all weight", randomList.length, "hero health value:", randomValue);
    }
    return randomValue;
};

 /**
 * 检查是否有重伤球员
 */
Heros.prototype.checkIsHaveSeriousInjury = function () {
    let isHave = false;
    for(let [uid, v] of this.allheros) {
        if(v.Health === commonEnum.HERO_HEALTH_STATUS.SERIOUS_INJURY) {
            isHave = true;
            break;
        }
    }
    return isHave;
}


//检查球员离线时长
Heros.prototype.checkHeroLeaveTime = function() {
    let reTime = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.RETIME].Param;
    if(!reTime) {
        logger.error("checkHeroLeaveTime reTime is fail")
        return;
    }
    for( let [uid, v] of this.allheros) {

        let hero = this.getHero(uid);
        //过滤pl为0球员
        if(hero.Fatigue === 0) {
            continue;
        }

        //PL基数
        let tiredNum = dataApi.allData.data["Footballer"][hero.ResID].Fatigue;
        if(!tiredNum) {
            continue;
        }

        //PL系数
        let num = dataApi.allData.data["SystemParam"][commonEnum.HERO_HEALTH_STATUS.RETIME_NUM].Param;
        if(!num) {
            continue;
        }

        //相差几小时
        let time = TimeUtils.hourInerval(hero.ReTimeFatigue);
        if(time === 0) {
            continue;
        }
        let fatigueNum = Math.floor(time / reTime);
        hero.Fatigue += (fatigueNum * tiredNum * num);
        if(hero.Fatigue < 0) {
            hero.Fatigue = 0;
        }
    }
}

//记录玩家离线时间
Heros.prototype.recordHeroLeaveTime = function() {
    for( let [uid, v] of this.allheros) {
        let hero = this.getHero(uid);
        if(!hero) {
            continue;
        }
        //受伤不记录
        // if(hero.Health !== commonEnum.HERO_HEALTH_STATUS.HEALTH) {
        //     continue;
        // }
        hero.ReTimeFatigue = TimeUtils.now();
    } 
}

/**
 * 检查球员列表是否有此球员
 */
Heros.prototype.checkIsHaveHero = function(uid) {
    return this.allheros.has(uid);
}

/**
 * 检查球员列表是否有此球员
 */
Heros.prototype.checkIsHaveHeroByResId = function(resId) {
    let isHave = false;
    for(let [k, v] of this.allheros) {
        if(v.ResID === resId) {
            isHave = true;
            break;
        }
    }
    return isHave;
}

/**
 * 添加生涯天数
 */
Heros.prototype.addHeroLeftDay = function(uid) {
    if (!this.checkIsHaveHero(uid)) {
        return Code.FAIL;
    }

    let hero = this.getHero(uid);
    // if(hero.isUseCareerMedicine === 1) {
    //     return Code.HERO_CODE.MAX_UP;
    // }else if(hero.isDie === 1) {
    //     return Code.BAG.HERO_DIE;
    // }
    if(hero.isDie === 1) {
        return Code.BAG.HERO_DIE;
    }

    //3次后就不用续约了
    if(hero.LifeNum >= 3) {
        return Code.FAIL;
    }

    let cost;
    if (typeof (dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.LEFT_DAY_GOLD]) === "undefined") {
        cost = 500;
    }else {
        cost = dataApi.allData.data["SystemParam"][commonEnum.HERO_SYSTEM.LEFT_DAY_GOLD].Param;
    }
    let addDay;
    if (typeof (dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.DELAY_DAY]) === "undefined") {
        addDay = 70;
    }else {
        addDay = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.DELAY_DAY].Param;
    }

    //获取活动价
    let discount = this.getActivityPrice(cost, 35);
    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, discount)){
        return Code.GOLD_FALL;
    }
    //扣钱
    this.player.deductMoney(commonEnum.PLAY_INFO.gold, discount);
    //增加退役时间
    let allLeftDay = TimeUtils.futureDayInterval(hero.leftTime);
    if(allLeftDay <= 0) {
        hero.leftTime = TimeUtils.beginningOfToday();
    }

    hero.leftTime += Math.floor(addDay * 86400000);
    // hero.isUseCareerMedicine = 1;
    hero.LifeNum ++;
    //重新计算属性
    this.reCalcAttrRevision(uid);
    // this.reCalcAttr(uid);
    return Code.OK;
}


/**************************************球员信息******end***********************************************/

//**************************************************属性计算(内部函数)Begin************************************************************* */
Heros.prototype._InnerCalcBaseValue = function(baseInit) {
    return baseInit;
};

Heros.prototype._InnerCalcSkillValue = function(config, value, rate) {
    return value * rate / 100;
};

Heros.prototype._InnerCalcHellTrainValue = function(value) {
    return value;
};

Heros.prototype._InnerCalcLeagueValue = function(config, value) {
    return 0;
};

Heros.prototype._InnerCalcQualificateValue = function(value) {
    return value;
};

Heros.prototype._InnerCalcUpgradeStarValue = function(value) {
    return value;
};

Heros.prototype._InnerCalcInspireValue =  function(config, value) {
    return value;
};
//**************************************************属性计算(内部函数)End************************************************************* */

//**************************************************消息结构体构造 Begin************************************************************* */
Heros.prototype.makeClientHero = function(uid) {
    var clientHero = {};
    var oneHeroObj = this.getHero(uid);
    if (!oneHeroObj){
        return oneHeroObj;
    }

    clientHero.Uid = oneHeroObj.Uid;
    clientHero.ResID = oneHeroObj.ResID;
    clientHero.TrainCount = oneHeroObj.TrainCount;
    // clientHero.CreateTime = oneHeroObj.CreateTime;
    clientHero.Breakthrough = oneHeroObj.Breakthrough;     //记录球员突破
    clientHero.Star = oneHeroObj.Star;        //球员星级   
    clientHero.Status = oneHeroObj.Status     //球员的状态
    clientHero.TreatyDay = oneHeroObj.TreatyDay;     //球员合约天数
    clientHero.Health = oneHeroObj.Health;          //球员健康度
    clientHero.IsTreat = oneHeroObj.isTreat;        //是否在治疗中
    clientHero.IsTrain = oneHeroObj.isTrain;        //是否在训练中
    clientHero.IsStage = oneHeroObj.isStage;
    clientHero.OldBreakOut = oneHeroObj.OldBreakOut;   //历史突破值
    clientHero.LifeNum = oneHeroObj.LifeNum;           //续约生涯次数
    clientHero.skillInfo = this.getHeroSkillInfo(uid);        //球员技能信息
    //是否使用过青春永驻药水: 1.用过, 0:没用过
    clientHero.isUseYonger = oneHeroObj.isUseCareerMedicine || 0;
    //生涯剩余天数
    // let allLeftDay = oneHeroObj.IsInitial * dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.LeagueSeason].Param;
    if(clientHero.LifeNum < 3) {
        let allLeftDay = TimeUtils.futureDayInterval(oneHeroObj.leftTime);
        clientHero.LeftRetireDay = allLeftDay;
        if(clientHero.LeftRetireDay < 0) {
            clientHero.LeftRetireDay = 0;
        }
    }else {
        clientHero.LeftRetireDay = 999;
    }
    var config = dataApi.allData.data["Footballer"][oneHeroObj.ResID];
    var initProperty = getInitProperty(config);
    let sum = 30;
    for (let i in oneHeroObj.Breakthrough){
        sum += oneHeroObj.Breakthrough[i];
    }
    let propertyList = this.getInitPropertyMAX(oneHeroObj.ResID, sum); //获取各项最大属性值
    var clientOneLevelAttr = [];
    var clientTwoLevelAttr = [];
    var oneLevelAttr = oneHeroObj["oneLevelAttr"];
    var twoLevelAttr = oneHeroObj["twoLevelAttr"];
    let index = 0;
    for (var key in oneLevelAttr) {
        const attrInfo = oneLevelAttr[key];
        clientOneLevelAttr[index] = {};
        clientOneLevelAttr[index].Cur = attrInfo.Cur;        //最终值
        //clientOneLevelAttr[index].Type = attrInfo.Type;
        clientOneLevelAttr[index].Base = attrInfo.Base;      //培养值
        clientOneLevelAttr[index].Skill = attrInfo.Skill;   //技能加成
        //logger.error("ttttttttttttttttttttttttt", clientOneLevelAttr[index].Skill)
        // clientOneLevelAttr[index].HellTrain = initProperty[key].Init + attrInfo.HellTrain;
        //clientOneLevelAttr[index].League = attrInfo.League;
        clientOneLevelAttr[index].Qualificate = attrInfo.Qualificate;   //特训加成
        clientOneLevelAttr[index].UpgradeStar = attrInfo.UpgradeStar ;  //升星加成
        clientOneLevelAttr[index].GroundTrain = attrInfo.GroundTrain ;  //球场训练加成
        clientOneLevelAttr[index].Handbook = attrInfo.Handbook;         //图鉴加成
        let trainerNum = attrInfo.TrainerSkillAtt + attrInfo.Trainer;   //教练技能加成+属性加成
        clientOneLevelAttr[index].Trainer = trainerNum;           //教练加成
        clientOneLevelAttr[index].AttackTactics = attrInfo.AttackTactics;   //进攻战术加成
        clientOneLevelAttr[index].DefTactics = attrInfo.DefTactics;         //防守战术加成
        clientOneLevelAttr[index].BeliefSkillAtt = attrInfo.BeliefSkillAtt  //信仰技能加成
        if(key !== "ResistanceDamage"){
            clientOneLevelAttr[index].MaxProperty = propertyList[key].max;
        }
        index++;
    }

    clientHero.OneLevelAttr = clientOneLevelAttr;
    index = 0;
    for (var key in twoLevelAttr) {
        const attrInfo = twoLevelAttr[key];
        clientTwoLevelAttr[index] = {};
        clientTwoLevelAttr[index].Cur = attrInfo.Cur;
        //clientTwoLevelAttr[index].Type = attrInfo.Type;
        clientTwoLevelAttr[index].Base = attrInfo.Base ;
        // clientTwoLevelAttr[index].Skill = attrInfo.Skill;
        // clientTwoLevelAttr[index].HellTrain = attrInfo.HellTrain;
        //clientTwoLevelAttr[index].League = attrInfo.League;
        clientTwoLevelAttr[index].Qualificate = attrInfo.Qualificate;
        // clientTwoLevelAttr[index].UpgradeStar = attrInfo.UpgradeStar ;
        //clientTwoLevelAttr[index].Inspire = attrInfo.Inspire ;
        // clientTwoLevelAttr[index].OldBase = attrInfo.OldBase;
        //clientTwoLevelAttr[index].SkillBuffRate = attrInfo.SkillBuffRate;
        index++;
    }

    clientHero.TwoLevelAttr = clientTwoLevelAttr;

    var attackAndDefend = oneHeroObj["AttackAndDefend"];
    var clientAttackAndDefendList = [];
    index = 0;
    for(var id in attackAndDefend)
    {
        const obj = attackAndDefend[id];
        clientAttackAndDefendList[index] = {};
        clientAttackAndDefendList[index].TeamFormationUid = id;
        clientAttackAndDefendList[index].Position = obj.position;
        clientAttackAndDefendList[index].Attack = obj.attack;
        clientAttackAndDefendList[index].Defend = obj.defend;
        index++;
    }

    clientHero.AttackAndDefendList = clientAttackAndDefendList;
    return clientHero;
};

Heros.prototype.makeClientHeroList = function() {
    var clientHeroList = [];
    let index = 0;
    for(let [ uid, obj ] of this.allheros)
    {
        if (!uid) {
            continue;
        }

        if(obj.isDie === 1) {
            continue;
        }

        let clientHero = this.makeClientHero(uid);
        clientHeroList[index] = clientHero;
        index += 1;
    }

    return clientHeroList;
};

//新手赠送球员列表
Heros.prototype.makeClientNewerHeroList = function() {
    var clientHeroList = [];
    let index = 0;

    for(let [ uid, obj] of this.allheros)
    {
        if (!uid) {
            continue;
        }

        var oneHeroObj = this.getHero(uid);
        if (!oneHeroObj){
            continue;
        }
        
        var isNewer = oneHeroObj.IsNewer;
        if ( 0 === isNewer ) {
            continue;
        }

        let clientHero = this.makeClientHero(uid);
        clientHeroList[index] = clientHero;
        index += 1;
    }

    return clientHeroList;
};

//新球员列表
Heros.prototype.makeClientUpdateNewHeroList = function(heroList) {
    var clientHeroList = [];
    if (!heroList) {
        return clientHeroList;
    }

    let index = 0;
    for (let i = 0; i < heroList.length; i++) {
        const uid = heroList[i];
        if ("" === uid) continue;
        let clientHero = this.makeClientHero(uid);
        clientHeroList[index] = clientHero;
        index++;
    }

    return clientHeroList;
};


//计算所有球员属性
Heros.prototype.reCalcAllHeroAttr = function()
{
    for(let [uid, obj] of this.allheros)
    {
        //重新计算球员属性
        this.reCalcAttrRevision(uid);
        // this.reCalcAttr(uid);
        //更新上场球员属性
        this.player.checkHeroInArbitrarilyFormation(uid);
    }

    //通知客户端更新球员
    this.player.updateHeros();
};


//**************************************************消息结构体构造 End************************************************************* */
Heros.prototype.oneKeySuperMan = function() {
    for(let [ uid, obj ] of this.allheros) {
        if (!uid) {
            continue;
        }
        
        let hero = this.getHero(uid);
        for(let i in hero.oneLevelAttr) {
            if (i !== "ResistanceDamage") { 
                hero.oneLevelAttr[i].Base = 99999;
            }
        }
    }

    for(let [ uid, obj ] of this.allheros) {
        if (!uid) {
            continue;
        }

        this.reCalcAttrRevision(uid);
        // this.reCalcAttr(uid);
    }

    return Code.OK;
};

Heros.prototype.getHeroName = function(heroUid) {
    let hero = this.getHero(heroUid);
    if(!hero) {
        return "";
    }
    let config;
    let bead = hero.ResID / 10000;
    if(bead < 9)
    {
        config = dataApi.allData.data["Footballer"][hero.ResID];
    } else {
        config = dataApi.allData.data["FootballerPve"][hero.ResID];
    }
    if(!config) {
        return "";
    }
    return config.CnName;
};



