var logger = require('pomelo-logger').getLogger('pomelo', __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var dataApi = require('../../util/dataApi');
var utils = require('../../util/utils');
var commonEnum = require('../../../../shared/enum');
var Calc = require('../../util/calc');
var Code = require('../../../../shared/code');
var Constant = require("../../../../shared/constant");
var TimeUtils = require('../../util/timeUtils');

//专业联赛 因为使用时进行，所以把五个联赛分为五个小组赛 第一组为业余联赛、一直到第五组懂超联赛
var ProfessionLeague = function(seasonId, league) {
    this.uid            = seasonId;   //赛季Id
    this.league         = league;

    this.groupPlayerMap = new Map(); //groupId -> [{ uid1}, {uid2}] //1-128 [uid1,uid2] //参赛人数据
    this.playerGroupMap = new Map(); //Uid->groupId //反向映射

    this.promotionMap   = new Map();    //round_groupId -> [uid, uid] //晋级玩家列表      
    this.eliminateMap   = new Map();    //round_groupId -> [uid, uid] //淘汰玩家列表
    this.roundBattleMap = new Map();    //round_groupId -> [battleId, battleId2] //没人对打的情况不记录 ,轮空也要记录
    this.roundRank      = new Map();    //round_groupId -> [rankObj, rankObj] //因为是过程数据也是最终数据,没有办法,只能维护一个队列了
    this.battleScheduleMap = new Map(); //round_groupId -> [ {uid, uid}]; //战斗赛程

    this.playerMap      = new Map(); // uid->obj //玩家自己信息数据 进球率
    this.battleMap      = new Map(); //[battleId]-> [battleObj];  //big data 所有轮次的数据都存在上面
    this.finalRank      = new Map(); //总榜排名

    this.finalReserveList = new Map();  //总榜名单 //groupId -> [{ uid1}, {uid2}] //1-128 [uid1,uid2]
    //最终淘汰名单
    this.finalPromotionMap = new Map(); //uid => groupId
    this.finalEliminateMap = new Map(); //uid => groupId

    this.typeId = commonEnum.LEAGUE_RUN_TYPE_ID.PROFESSION;

    //系统支持数据
    this.currRound      = 0;         //当前轮次
    this.privateConfig = [];

    //辅助数据(不保存数据)
    this.roundConfig    = [];   //轮次配置表
    this.groupMaxCount  = 0;
    this.awaitTime = 0; //战斗服返回时间
    this.MAXTimeOut = 60 * 30;  //战斗服返回超时最大时间 30分钟

    //初始化配置表
    this.initByConfig();
    this.initPrivateConfig();
};

util.inherits(ProfessionLeague, EventEmitter);

module.exports = ProfessionLeague;

//初始化配置数据
ProfessionLeague.prototype.initByConfig = function() 
{   
    //round 轮次 name: 轮次名称 required_num 每小组最大保存最大人数
    var roundConfig = [
        {round: 0, name: "准备开始",  required_num: 20},
        {round: 1, name: "第1轮",   required_num: 10},
        {round: 2, name: "第2轮",   required_num: 10},
        {round: 3, name: "第3轮",   required_num: 10},
        {round: 4, name: "第4轮",   required_num: 10},
        {round: 5, name: "第5轮",   required_num: 10},
        {round: 6, name: "第6轮",   required_num: 10},
        {round: 7, name: "第7轮",   required_num: 10},
        {round: 8, name: "第8轮",   required_num: 10},
        {round: 9, name: "第9轮",   required_num: 10},
        {round: 10, name: "第10轮", required_num: 10},

        {round: 11, name: "第11轮", required_num: 10},
        {round: 12, name: "第12轮", required_num: 10},
        {round: 13, name: "第13轮", required_num: 10},
        {round: 14, name: "第14轮", required_num: 10},
        {round: 15, name: "第15轮", required_num: 10},
        {round: 16, name: "第16轮", required_num: 10},
        {round: 17, name: "第17轮", required_num: 10},
        {round: 18, name: "第18轮", required_num: 10},
        {round: 19, name: "第19轮", required_num: 10},
        {round: 20, name: "第20轮", required_num: 10},

        {round: 21, name: "第21轮", required_num: 10},
        {round: 22, name: "第22轮", required_num: 10},
        {round: 23, name: "第23轮", required_num: 10},
        {round: 24, name: "第24轮", required_num: 10},
        {round: 25, name: "第25轮", required_num: 10},
        {round: 26, name: "第26轮", required_num: 10},
        {round: 27, name: "第27轮", required_num: 10},
        {round: 28, name: "第28轮", required_num: 10},
        {round: 29, name: "第29轮", required_num: 10},
        {round: 30, name: "第30轮", required_num: 10},

        {round: 31, name: "第31轮", required_num: 10},
        {round: 32, name: "第32轮", required_num: 10},
        {round: 33, name: "第33轮", required_num: 10},
        {round: 34, name: "第34轮", required_num: 10},
        {round: 35, name: "第35轮", required_num: 10},
        {round: 36, name: "第36轮", required_num: 10},
        {round: 37, name: "第37轮", required_num: 10},
        {round: 38, name: "第38轮", required_num: 10},
    ];
    this.groupMaxCount  = 5 + 16;//加上信仰5个组
    this.roundConfig = roundConfig;
};

ProfessionLeague.prototype.initPrivateConfig = function()
{
  let battleStatus = commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN;
  var privateConfig = 
  [
    {round: 0,  isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 1,  isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 2,  isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 3,  isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 4,  isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 5,  isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 6,  isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 7,  isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 8,  isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 9,  isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 10, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},

    {round: 11, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 12, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 13, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 14, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 15, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 16, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 17, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 18, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 19, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 20, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},

    {round: 21, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 22, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 23, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 24, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 25, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 26, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 27, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 28, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 29, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 30, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},

    {round: 31, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 32, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 33, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 34, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 35, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 36, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 37, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
    {round: 38, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
  ];
    /*isRunning: 某个函数某些地方只运行一次,
   canAction: 当前函数是否全部把事情完成(等回调), 
   battleStatus: battleStatus,
   sendWaitStartNtf: 发送等待比赛开始通知
   false, sendStartNtf: 发送比赛开始通知
  */
  this.privateConfig = privateConfig;
};

ProfessionLeague.prototype.toJSONforDB = function() {
    var profession = {
        uid: this.uid,
        currRound: this.currRound,
        groupPlayerMap: this.league.groupPlayerMapToDB(this.groupPlayerMap),
        playerGroupMap: this.league.playerGroupMapToDB(this.playerGroupMap),
    
        battleMap: this.league.battleMapToDB(this.battleMap),
        playerMap: this.league.playerMapToDB(this.playerMap),
        finalRank: this.league.finalRankToDB(this.finalRank),
    
        promotionMap: this.league.roundGroupIdToDB(this.promotionMap),
        eliminateMap: this.league.roundGroupIdToDB(this.eliminateMap),
        roundBattleMap: this.league.roundGroupIdToDB(this.roundBattleMap),
        roundRank: this.league.roundGroupIdToDB(this.roundRank),
        battleScheduleMap: this.league.roundGroupIdToDB(this.battleScheduleMap),

        finalReserveList: this.league.finalGroupPlayerToDB(this.finalReserveList),
        finalPromotionMap: this.league.finalProMapToDB(this.finalPromotionMap),
        finalEliminateMap: this.league.finalProMapToDB(this.finalEliminateMap),
        privateConfig: this.privateConfig,
    };

    return profession;
};

ProfessionLeague.prototype.initByDB = function(doc) {
    this.uid = doc.uid;
    this.groupPlayerMap = this.league.groupPlayerMapLoadFromDB(doc.groupPlayerMap) || new Map();
    this.playerGroupMap = this.league.playerGroupMapLoadFromDB(doc.playerGroupMap) || new Map();

    this.battleMap = this.league.battleMapLoadFromDB(doc.battleMap) || new Map();
    this.playerMap = this.league.playerMapLoadFromDB(doc.playerMap) || new Map();
    this.finalRank = this.league.finalRankLoadFromDB(doc.finalRank) || new Map();

    this.promotionMap = this.league.roundGroupIdLoadFromDB(doc.promotionMap) || new Map();
    this.eliminateMap = this.league.roundGroupIdLoadFromDB(doc.eliminateMap) || new Map();
    this.roundBattleMap = this.league.roundGroupIdLoadFromDB(doc.roundBattleMap) || new Map();
    this.roundRank = this.league.roundGroupIdLoadFromDB(doc.roundRank) || new Map();
    this.battleScheduleMap = this.league.roundGroupIdLoadFromDB(doc.battleScheduleMap) || new Map();

    this.finalReserveList = this.league.finalGroupPlayerDB(doc.finalReserveList) || new Map();
    this.finalPromotionMap = this.league.finalProMapLoadFromDB(doc.finalPromotionMap) || new Map();
    this.finalEliminateMap = this.league.finalProMapLoadFromDB(doc.finalEliminateMap) || new Map();

    this.privateConfig = this.league.checkPrivateConfigAbnormal(doc.privateConfig);
    if (!this.privateConfig)
    {
      logger.error("NormalLeague.initByDB: not privateConfig! doc.privateConfig", doc.privateConfig);
      this.initPrivateConfig();
    }
    //系统支持数据
    this.currRound      =  doc.currRound || 0;         //当前轮次
};

//加载上一场最终获胜名单
ProfessionLeague.prototype.loadFinalReserveList = function(doc)
{
    if (!doc)
    {
        logger.error("_loadFinalGroupPlayerMap: not found doc!");
        return;
    }

    logger.info("_loadFinalGroupPlayerMap ____________________");
    let lastPlayerMap = this.league.playerMapLoadFromDB(doc.playerMap) || new Map()
    this.finalReserveList = this.league.finalGroupPlayerDB(doc.finalReserveList) || new Map();
    for(let [k,v] of this.finalReserveList) //groupId => UidList
    {
        for (let idx in v) 
        {
            const uid = v[idx];
            this.setPlayerGroupMap(uid, k);

            let playerObj = lastPlayerMap.get(uid);
            this.setPlayerMap(uid, playerObj);
        }
        this.setGroupPlayerMap(k, v);
    }

    this.league.saveProfession();
};

ProfessionLeague.prototype.toJSONforClient = function() {
    return this.makeClientList();
};

ProfessionLeague.prototype.test = function()
{

};

ProfessionLeague.prototype.getRoundConfig = function(round)
{
  return this.league.comGetRoundConfig(this.roundConfig, round);
};

ProfessionLeague.prototype.getLastRound = function()
{
  return this.league.comGetLastRound(this.roundConfig);
};

ProfessionLeague.prototype.getFinalRound = function(isScoreRank)
{
  let finalRound = this.getLastRound();
  if (isScoreRank)
  {
    finalRound = finalRound + 1;
  }
  return finalRound;
};

ProfessionLeague.prototype.getCurrFinalRound = function(isScoreRank)
{
  let currRound = this.getCurrRound();
  let lastRound = this.getLastRound();
  let finalRound = 0;
  if (isScoreRank && currRound > 0)
  {
    //查看当前轮数的状态
    let battleStatus =this.getConfigBattleStatus(currRound);
    if (lastRound === currRound &&  battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END ) //给最终排行榜
    {
      finalRound = currRound + 1;
    }
    else if (battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END) //当前轮结束了、给当前排行榜
    {
      finalRound = currRound + 1;
    }else
    {
      finalRound = currRound - 1; //还没有结束,给上一轮排行榜
    }
  }else
  {
    finalRound = currRound; 
  }
  return finalRound;
};

ProfessionLeague.prototype.canRunNextAction = function(round) 
{
  return this.league.comCheckRunNextAction(this.privateConfig, round);
};

ProfessionLeague.prototype.setCanRunNextAction = function(round) 
{
   this.league.comSetCanRunNextAction(this.privateConfig, round);
};

ProfessionLeague.prototype.setBattleStatus = function(round, battleStatus)
{
  this.league.comSetBattleStatus(this.privateConfig, round, battleStatus);
};

ProfessionLeague.prototype.getConfigBattleStatus = function(round)
{
  return this.league.comGetConfigBattleStatus(this.privateConfig, round);
};

ProfessionLeague.prototype.setSendWaitStartNtf = function(round)
{
  this.league.comSetSendWaitStartNtf(this.privateConfig, round);
};

ProfessionLeague.prototype.checkSendWaitStartNtf = function(round)
{
  return this.league.comCheckSendWaitStartNtf(this.privateConfig, round);
};

ProfessionLeague.prototype.setSendStartNtf = function(round)
{
  this.league.comSetSendStartNtf(this.privateConfig, round);
};

ProfessionLeague.prototype.checkSendStartNtf = function(round)
{
  return this.league.comCheckSendStartNtf(this.privateConfig, round);
};

ProfessionLeague.prototype.checkRunning = function(round) 
{
  return this.league.comCheckRunning(this.privateConfig, round);
};

ProfessionLeague.prototype.setIsRunning = function(round)
{
  this.league.comSetIsRunning(this.privateConfig, round);
};

ProfessionLeague.prototype.Enroll = function(simplePlayerObj)
{   
    if (!simplePlayerObj) 
    {
        return Code.FAIL;
    }

    logger.info("PROFESSION Enroll", simplePlayerObj.playerUid);
    let uid = simplePlayerObj.playerUid;
    // var playerObj = this.league.newPlayerObj(uid, false);
    // playerObj.power = simplePlayerObj.power;
    // playerObj.enrollTime = simplePlayerObj.enrollTime;
  
    this.setPlayerMap(uid, simplePlayerObj);
    //按信仰分组
    let groupId = simplePlayerObj.beliefId + 5;//原本有5个专业联赛，所以分组要加5
    let list = [];
    if(this.groupPlayerMap.has(groupId))
    {
        list = this.groupPlayerMap.get(groupId);
        if(!utils.hasUidInList(list, uid))
        {
            list.push(uid);
        }
    }
    else
    {
        list.push(uid);
        this.groupPlayerMap.set(groupId, list);
    }
    this.playerGroupMap.set(uid, groupId);
    return Code.OK;
};

ProfessionLeague.prototype.checkGroup = function() 
{
    //处理组内人数
    //少的使用机器人天空，多的删除、不多不少不处理
    var config = this.getRoundConfig(0);
    let required_num = config.required_num;
    let robotIdx = 1;
    let groupIdList = [];
    for(let [k, v] of this.groupPlayerMap)
    {
        let uidList = v;
        let length = uidList.length;
        groupIdList.push(parseInt(k));
        if (length !== required_num)
        {
            if (required_num < length )
            {
                let count = 1;
                for (let idx in uidList) {
                    const uid = uidList[idx];
                    if (count > required_num)
                    {
                        utils.removeElement(uidList, uid);
                        if (this.hasPlayerMap(uid)) {
                            this.delPlayerMap(uid);
                            logger.warn("delete uid", uid);
                        }
                    }
                    count++;
                }
                logger.warn("delete robot uid: count", count);
            }
            else if(required_num > length)
            {
                let robotNum = required_num - length;
                let count = 0;
                for (let idx = 1; idx <= robotNum; idx++) {
                    let uid = "robot_" + robotIdx  + "_"+ utils.syncCreateUid();
                    let uidList = this.getGroupPlayerMap(k);
                    uidList.push(uid);
                    this.setPlayerGroupMap(uid, k);
          
                    //初始化玩家数据
                    if (!this.hasPlayerMap(uid)) {
                        var playerObj = this.league.newPlayerObj(uid, false);
                        this.setPlayerMap(uid, playerObj);
                    }

                    logger.warn("add robot uid", k, uid);
                    robotIdx++;
                    count++;
                }
                logger.warn("add robot uid: count", k, count);
            }
        }
    }

    if (groupIdList.length < this.groupMaxCount)
    {
      for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) 
      {
            if (utils.hasUidInList(groupIdList ,groupId))
            {
              continue;
            }

            let count = 0;
            for (let idx = 1; idx <= required_num; idx++) {
                let uid = "robot_" + robotIdx  + "_"+ utils.syncCreateUid();
                if (!this.hasGroupPlayerMap(groupId))
                {
                  this.setGroupPlayerMap(groupId, []);
                }

                let uidList = this.getGroupPlayerMap(groupId);
                uidList.push(uid);
                this.setPlayerGroupMap(uid, groupId);
      
                //初始化玩家数据
                if (!this.hasPlayerMap(uid)) {
                    var playerObj = this.league.newPlayerObj(uid, false);
                    this.setPlayerMap(uid, playerObj);
                }

                logger.warn("fix add robot uid", uid);
                robotIdx++;
                count++;
            }
            logger.warn("fix add robot uid: count", groupId, count);
        }
    }
};

//积分排序
function __score_compare_func(rankObj1, rankObj2) {
	let totalScore1 = rankObj1.totalScore;
	let goalCount1  = rankObj1.goalCount;
	let missCount1  = rankObj1.missCount;
	let power1      = rankObj1.power;
  let enrollTime1 = rankObj1.enrollTime;
  let diffGoalCount1 = goalCount1 - missCount1;

	let totalScore2 = rankObj2.totalScore;
	let goalCount2  = rankObj2.goalCount;
	let missCount2  = rankObj2.missCount;
  let power2      = rankObj2.power;
  let enrollTime2 = rankObj2.enrollTime;
  let diffGoalCount2 = goalCount2 - missCount2;

    //积分
	if (totalScore1 !== totalScore2 ) { 
		 //降序
		if (totalScore1 < totalScore2) {
          return 1;
    }else if (totalScore1 > totalScore2) {
          return -1;
    }
	}	

    //净胜球球数
	if (diffGoalCount1 !== diffGoalCount2) {
		//降序
		if (diffGoalCount1 < diffGoalCount2) {
            return 1;
    }else if (diffGoalCount1 > diffGoalCount2) {
            return -1;
    }
	}

    //进球数
    if (goalCount1 !== goalCount2) {
		 //降序
      if (goalCount1 < goalCount2) {
              return 1;
      }else if (goalCount1 > goalCount2) {
              return -1;
      }
    }

    //战力
    if (power1 != power2)
    {
        //降序
        if (power1 < power2) {
            return 1;
        }else if (power1 > power2) {
            return -1;
        }
        else {
            return 0;
        }
    }

    //报名时间 靠前的人胜利
    //升序
    if (enrollTime1 > enrollTime2) {
		  return 1;
    }else if (enrollTime1 < enrollTime2) {
      return -1;
    }
    
    return 0;
};

//生成最终总榜排名
ProfessionLeague.prototype.makeFinalGroupRank = function(groupId)
{
    let map = new Map(); //uid => [uid,Obj]
    let rankObjList = [];
    let uidList =  this.getGroupPlayerMap(parseInt(groupId));
    for (let i = 0; i < uidList.length; i++) {
        const uid = uidList[i];
        if (!uid) continue;
        let playerObj = this.league.newPlayerObj(uid, false);
        map.set(uid, playerObj)
    }
    
    for (let round = 1; round <= 38; round++) 
    {
        let round_key = this.league.getRoundKey(round, groupId);
        var rankList = this.getRoundRank(round_key);
        for (let i in rankList) 
        {
            const data = rankList[i];
            let playerUid = data.playerUid;
            if (!map.has(playerUid))
            {
                map.set(playerUid, this.league.newPlayerObj(playerUid, false));
            }
            let playerObj = map.get(playerUid);
            playerObj.battleCount +=  data.battleCount;
            playerObj.winCount += data.winCount;        //胜场
            playerObj.drawCount += data.drawCount;      //平场
            playerObj.lossCount += data.lossCount;      //负场
            playerObj.goalCount += data.goalCount;      //进球
            playerObj.missCount +=  data.missCount;     //失球
            playerObj.totalScore += data.totalScore;    //积分
            playerObj.power = data.power; //使用最近一场的战力
            //playerObj.enrollTime = data.enrollTime;
        }
    }

    for (let [k,v ] of map) 
    {
        rankObjList.push(v);
    }

    //排序
    rankObjList.sort(__score_compare_func);

    for (let i in rankObjList) {
        const data = rankObjList[i];
        let totalScore1 = data.totalScore;
        let goalCount1  = data.goalCount;
        let missCount1  = data.missCount;
        let power1      = data.power;
        let diffGoalCount1 = goalCount1 - missCount1;
        logger.info("makeRoundFinalRank: groupId, playerUid, totalScore, diff, goalCount, power1", groupId, data.playerUid, totalScore1, diffGoalCount1, goalCount1, power1);
    }

    logger.info("makeRoundFinalRank: groupId ________________", groupId);
    this.finalRank.set(groupId, rankObjList);
};

ProfessionLeague.prototype.makeTotalLastRoundGroupRank = function(groupId, lastRound)
{
    let historyRankObjMap = this.makeLastRoundGroupRank(groupId, lastRound);
    let leagueName = this.league.getProfessionLeagueName(groupId) + "联赛";
    let config = this.getRoundConfig(lastRound + 1);
    let roundName = config.name;
    let rankObjList = {
        historyRankObjMap: historyRankObjMap,
        leagueName: leagueName,
        roundName: roundName,
    };

  return rankObjList;
};

ProfessionLeague.prototype.makeLastRoundGroupRank = function(groupId, finalRound)
{
  let map = new Map(); //uid => [uid,Obj]
  let historyRankObjMap = new Map();
  let rankObjList = [];
  let uidList =  this.getGroupPlayerMap(parseInt(groupId));
  if (!uidList || uidList.length <= 0)
  {
    logger.error("makeLastRoundGroupRank: not found groupId player", groupId, finalRound);
    return historyRankObjMap; 
  }
  
  for (let i = 0; i < uidList.length; i++) {
      const uid = uidList[i];
      if (!uid) continue;
      let obj = this.league.newPlayerObj(uid, false);
      map.set(uid, obj)
  }

  for (let round = 1; round <= finalRound; round++) 
  {
      let round_key = this.league.getRoundKey(round, groupId);
      var rankList = this.getRoundRank(round_key);
      for (let i in rankList) 
      {
          const data = rankList[i];
          let playerObj = map.get(data.playerUid);
          playerObj.battleCount +=  data.battleCount;
          playerObj.winCount += data.winCount;
          playerObj.drawCount += data.drawCount;
          playerObj.lossCount += data.lossCount;
          playerObj.goalCount += data.goalCount;
          playerObj.missCount +=  data.missCount;
          playerObj.totalScore += data.totalScore;
          playerObj.power = data.power; //使用最近一场的战力
          if (this.league.checkRobot(data.playerUid)) //过滤掉机器人
          {
            playerObj.power = 0;
          }
      }
  }

  for (let [k,v ] of map) 
  {
      rankObjList.push(v);
  }

  //排序
  rankObjList.sort(__score_compare_func);

  let rank = 1;
  for (let i in rankObjList) {
      const data = rankObjList[i];
      let uid = data.playerUid;
      let winCount = data.winCount;
      let lossCount  = data.lossCount;
      let drawCount  = data.drawCount;
      let totalValue = 0;
      if (!this.league.checkRobot(uid))
      {
        totalValue = this.league.getTeamTotalValue(uid);
      }

      let obj = {
        winCount: winCount,
        lossCount: lossCount,
        drawCount: drawCount,
        rank: rank,
        totalValue: totalValue,
      };

      historyRankObjMap.set(uid, obj);
      //logger.info("makeFinalGroupRank: groupId, playerUid, ", finalRound, groupId, data.playerUid, obj);
      rank++;
  }

  return historyRankObjMap;
};

//积分排序
function __find_rank_compare_func(rankObj1, rankObj2) {
	let rank1 = rankObj1.rank;
	let rank2 = rankObj2.rank;
    //积分
	if (rank1 !== rank2 ) { 
		 //升序
		if (rank1 > rank2) {
            return 1;
        }else if (rank1 < rank2) {
            return -1;
        }
        else {
            return 0;
        }
	}	

  return 0;
};


//队伍晋降级
//Note: 思路
//先把晋级和降级的玩家分别挑选出来(注意 第一组没有降级，第五组没有升级)
//把不参与晋级和降级的玩家放入finalGroupPlayerMap
//再把晋级和降级的玩家放入到finalGroupPlayerMap中
//注意:这里移动球队的位置是以最终排名来计算，在调用的时间请先调用setTotalRank函数
ProfessionLeague.prototype.promotionTeam = function()
{
    logger.error("---------------队伍晋降级---------------");
    let promotionGroupUidMap = new Map(); //groupId = [uid] uid为晋级完成家Uid; //第五组为懂超，前四组不能晋级
    let knockoutGroupUidMap = new Map();  //groupId = [uid] uid为降级玩家Uid   //第一组为社区联赛,最后四组不能降级
    let tmkRankMap = new Map();
    let proUidList = [];    //联赛备战区晋级列表
    for (let[k, v] of this.finalRank) {
        let groupId = k;
        let rankObjList = v;
        let rank = 1;
        if(groupId > 5)//预备联赛特殊处理
        {
            if(!!rankObjList[0].playerUid && (rankObjList[0].playerUid.substr(0,6) !== "robot_"))
            {
                let uid = rankObjList[0].playerUid;
                proUidList.push(uid);
            }
            if (!tmkRankMap.has(groupId))
            {
                tmkRankMap.set(groupId, []);
            }
            continue;
        }
        for (let i in rankObjList) 
        {
            const data = rankObjList[i];
            let playerUid = data.playerUid;
            logger.info("promotionTeam playerUid",  groupId, playerUid);
            //前四组晋级
            if (rank <= 4 && 5 !== groupId)//懂超不能要
            {
                if (!promotionGroupUidMap.has(groupId))
                {
                    promotionGroupUidMap.set(groupId, []);
                }

                let uidList = promotionGroupUidMap.get(groupId);
                uidList.push(playerUid);
            }
            else if (rank >= 17 && 1 !== groupId) //最后四组降级 社区联赛最后四组不能要
            {
                if (!knockoutGroupUidMap.has(groupId))
                {
                    knockoutGroupUidMap.set(groupId, []);
                }
                let uidList = knockoutGroupUidMap.get(groupId);
                uidList.push(playerUid);
            }
            else
            {
                //logger.info("promotionTeam playerUid ::",  groupId, playerUid);+
                if (!tmkRankMap.has(groupId))
                {
                    tmkRankMap.set(groupId, []);
                }

                let rankObjList = tmkRankMap.get(groupId);
                let rankObj = {
                  uid: playerUid,
                  rank: rank,
                };
                rankObjList.push(rankObj);
            }

            rank++;
        }
        logger.info("_____________________________________");
    }

    //移动各个小组位置
    for (let k= 1;  k <= this.groupMaxCount; k++) 
    {
        if(k > 5) //预备联赛另外一个函数专门处理
            continue;
        if (1 !== k)
        { 
            let proUidList = promotionGroupUidMap.get(k-1); //上一级晋级的名单
            if (proUidList && proUidList.length > 0) 
            {
              var rank = 20 - proUidList.length + 1;
              for (let i in proUidList) 
              {
                  let uid = proUidList[i];
                  this.finalPromotionMap.set(uid, k);
                  let rankObjList = tmkRankMap.get(k);
                  let rankObj = {
                    uid: uid,
                    rank: rank,
                  };
                  rankObjList.push(rankObj);
                  rank++;
                  //logger.info("tmkRankMap k", k, rankObj, rankObjList);
              }
            }
        }

        if (5 !== k)
        {
            let knockUidList = knockoutGroupUidMap.get(k+1); //上一级淘汰的名单
            if (knockUidList && knockUidList.length > 0) 
            {
              var proRankStart = 1;
              for (let i in knockUidList) 
              {
                  let uid = knockUidList[i];
                  this.finalEliminateMap.set(uid, k);
                  let rankObjList = tmkRankMap.get(k);
                  let rankObj = {
                    uid: uid,
                    rank: proRankStart,
                  };
                  rankObjList.push(rankObj);
                  //logger.info("tmkRankMap k,proRankStart", k, proRankStart, rankObj);
                  proRankStart++;
              }
            }
        }
    }

    // //预备联赛冠军晋级业余赛
   this.promotionPlayerToTeam(proUidList, knockoutGroupUidMap, tmkRankMap);


    for (let [k,v] of this.finalPromotionMap) 
    {
        logger.info("finalPromotionMap k,v", k, v);
    }

    for (let [k,v] of this.finalEliminateMap) 
    {
        logger.info("finalEliminateMap k,v", k, v);
    }

    // for (let [k,v] of tmkRankMap) 
    // {
    //     logger.info("tmkRankMap k,v", k, v.length, v);
    // }

    for (let [k,v] of tmkRankMap) 
    {
        //排序
        v.sort(__find_rank_compare_func);
    }

    this.finalReserveList.clear();     //清空结果
    for (let [k,v] of tmkRankMap) 
    {
        logger.info("tmkRankMap k,v", k, v.length, v);
        if (!this.finalReserveList.has(k))
        {
            this.finalReserveList.set(k, []);
        }

        for(let idx in v)
        {
          let data = v[idx];
          let uidList = this.finalReserveList.get(k);
          uidList.push(data.uid);
        }
    }

    for (let [k,v] of this.finalReserveList) 
    {
        logger.info("finalReserveList k,v", k, v);
    }
};

ProfessionLeague.prototype.getFinalGroupIdRank = function() 
{
  let ctyRank = [];
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) 
  {
      let rankObjList = this.finalRank.get(groupId);
      if (!rankObjList)
      {
        //logger.warn("getFinalGroupIdRank: groupId not rank data", groupId);
        continue;
      }
  
      let rank = 1;
      for (let i in rankObjList) 
      {
        const data = rankObjList[i];
        let uid = data.playerUid;
        if (this.league.checkRobot(uid)) continue;  //过滤机器人
        let simpleRankObj = {
          uid: uid,
          groupId: groupId,
          rank: rank,
          typeId: this.getTypeIdByGroupId(groupId),
        };
    
        ctyRank.push(simpleRankObj);
        rank++;
      }
  }
  return ctyRank;
};

ProfessionLeague.prototype.getPromotionPlayer = function(round)
{
    let groupPlayerMap = new Map();
    //从第一组开始进行分人
    for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
        let round_key = this.league.getRoundKey(round, groupId);

        let uidList =  this.getPromotionMap(round_key);
        //logger.info("getPromotionPlayer: round, round_key", round, round_key, uidList);
        if (!uidList || uidList.length <= 0) continue;
        groupPlayerMap.set(round_key, uidList);
    }
    return groupPlayerMap;
};

ProfessionLeague.prototype.getPromotionUidList = function(round)
{
    let proUidList = [];
    //从第一组开始进行分人
    for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
        let round_key = this.league.getRoundKey(round, groupId);
        let uidList =  this.getPromotionMap(round_key);

        if (!uidList || uidList.length <= 0) continue;
  
        for (let i in uidList) {
            const uid = uidList[i];
            if (!uid ||  "" === uid) continue;
            proUidList.push(uid);
        }
        //logger.info("getPromotionPlayer: uidList", uidList, proUidList);
    }
    return proUidList;
};

ProfessionLeague.prototype.getPromotionUidListByGroupId = function(round, groupId) 
{
  let proUidList = [];
  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasPromotionMap(round_key))
  {
    return proUidList;
  }

  let uidList = this.getPromotionMap(round_key);
  if (!uidList || uidList.length <= 0) 
  { 
    return proUidList;
  }

  for (let i in uidList) {
    const uid = uidList[i];
    if (!uid || "" === uid) 
    {
      continue;
    }

    proUidList.push(uid);
  }
  return proUidList;
};

ProfessionLeague.prototype.getEliminateUidListByGroupId = function(round, groupId) 
{
  let proUidList = [];
  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasEliminateMap(round_key))
  {
    return proUidList;
  }

  let uidList = this.getEliminateMap(round_key);
  if (!uidList || uidList.length <= 0) 
  { 
    return proUidList;
  }

  for (let i in uidList) {
    const uid = uidList[i];
    if (!uid || "" === uid) 
    {
      continue;
    }

    proUidList.push(uid);
  }
  return proUidList;
};


ProfessionLeague.prototype.getEliminatePlayer = function(round) {
  let groupPlayerMap = new Map();
  //从第一组开始进行分人
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
    let round_key = this.league.getRoundKey(round, groupId);
    if (!this.hasEliminateMap(round_key))
    {
      continue;
    }

    let uidList = this.getEliminateMap(round_key);
    if (!uidList || uidList.length <= 0) 
    {
      continue;
    }

    groupPlayerMap.set(round_key, uidList);
  }
  return groupPlayerMap;
};

ProfessionLeague.prototype.getEliminateUidList = function(round) {
  let proUidList = [];
  //从第一组开始进行分人
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
      let round_key = this.league.getRoundKey(round, groupId);
      if (!this.hasEliminateMap(round_key))
      {
        continue;
      }

      let uidList = this.getEliminateMap(round_key);
      if (!uidList || uidList.length <= 0) 
      {
        continue;
      }

      for (let i in uidList) {
        const uid = uidList[i];
        if (!uid || "" === uid) 
        {
          continue;
        }
        proUidList.push(uid);
      }
  }
  //logger.info("getPromotionPlayer: getEliminateUidList", proUidList);
  return proUidList;
};

ProfessionLeague.prototype.getRoundBattleQueue =  function(round)
{
    var map = new Map();  
    //round_groupId = > [Host: uid1, Gust:uid2];
    for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
        let round_key = this.league.getRoundKey(round, groupId);
        let objList = this.getBattleScheduleMap(round_key);
        if (!objList) continue;
        let cpObjList = utils.cloneArray(objList);
        map.set(round_key, cpObjList);
    }
    return map;
};

//通用处理流程
ProfessionLeague.prototype.commProcessFlow = function(round) 
{
    //取当前比赛各个战斗队列
    let groupPlayerMap = this.getRoundBattleQueue(round);
    //当前轮次淘汰
    let self = this;
    this.PFGetBattleFlow(round, groupPlayerMap, function(err){
      if (!!err)
      {
        logger.error("comGetBattleFlow: error", err);
        return;
      }
      self.setCanRunNextAction(round);
      self.awaitTime = 0;
    });
    this.setIsRunning(round);
  };
  
ProfessionLeague.prototype.PFGetBattleFlow = function(round, groupPlayerMap, cb)
{
    let self = this;
    //1.拉取玩家数据
    let waitUidList = this.getWaitUidList(groupPlayerMap);
    this.league.pfGetBattleData(waitUidList, function(err){
      if (!!err)
      {
        logger.error("comGetBattleData: callback error", err);
        return cb(err);
      }
  
      //通用战斗流程
      self.comPFEliminationGroup(round, groupPlayerMap, function(err) {
        if (!!err)
        {
          logger.error("comPFEliminationGroup: catch a error!", err);
          cb(err);
          return;
        }
  
        logger.info("comPFEliminationGroup commProcessFlow .............", round);
  
        self.printDirectPromotion(round);
        for (let groupId = 1; groupId <= self.groupMaxCount; groupId++) 
        {
          self.printRank(round, groupId);
        }
  
        //self.printPromotion(round);
        cb(null);
        return;
      });
    });
};

ProfessionLeague.prototype.getWaitUidList = function(groupPlayerMap)
{
    let waitUidList = [];
    for (let [k, v] of groupPlayerMap) 
    {
        let groupId = this.league.getGroupId(k);
        let uidList = this.getGroupPlayerMap(parseInt(groupId));
        //logger.info("getWaitUidList", k, uidList);
        for (let index = 0; index < v.length; index++) {
            var obj = v[index];
            if (!obj) continue;

            let uidIdx1 = obj.Host;
            let uidIdx2 = obj.Gust;
            let uid1 = uidList[parseInt(uidIdx1 - 1)];
            let uid2 = uidList[parseInt(uidIdx2 - 1)];

            if (!this.league.checkRobot(uid1))
            {
              waitUidList.push(uid1);
            }
    
            if (!this.league.checkRobot(uid2))
            {
              waitUidList.push(uid2);
            }
        }
    }
    return waitUidList;
};
  
ProfessionLeague.prototype.comPFEliminationGroup = function(round, groupPlayerMap, cb) 
{
  for (let [k, v] of groupPlayerMap) 
  {
    let groupId = this.league.getGroupId(k);
    this.updateBattleQueueProcess(round, parseInt(groupId), v, v.length);
  }

  //全部入队列后，一起发送请求,一起处理
  this.league.battleQueue.sendBattleToBattleSvr(function(err, code){
    //logger.info("comPFEliminationGroup", err, code);
    if (!!err)
    {
      logger.error("sendBattleToBattleSvr failed! err", err);
      cb(err);
      return;
    }

    if (code !== Code.OK)
    {
      logger.error("comPFEliminationGroup: ret error! err", code);
      cb("ret error! err");
      return;
    }

    cb(null)
    return;
  });
};

//处理战斗队列
ProfessionLeague.prototype.updateBattleQueueProcess = function(round, groupId, battleObjList, length)
{
  //logger.info("updateBattleQueueProcess: round, groupId, length", round, groupId, battleObjList, length);
  var config = this.getRoundConfig(round);
  let required_every_group_num = config.required_num;

  let lastRound = round - 1;
  let historyRankData = this.makeTotalLastRoundGroupRank(groupId, lastRound);

  //把随机后玩家放入到队列表中
  let battleList = this.pfMakeBattleList(round, groupId, battleObjList, length, historyRankData);
  //处理轮空情况
  //联赛没有位置为空的情况，就有算，也上上一轮留下的机器人，位置不可能留空   职业联赛人数不足10人就会一直轮空，不需要打乱不然会出现38轮全部轮空
  // this.league.preProcessBattleList(battleList, required_every_group_num);
  let needSendBattleList = this.league.getAllPlayerBattleList(battleList);
  if (needSendBattleList.length > 0) {
    //logger.info("PF: updateBattleQueueProcess 2: round, groupId, robot_length, length", round, groupId, battleList.length - needSendBattleList.length, needSendBattleList.length);
    this.league.sendBattleQueue(needSendBattleList);
  }

  //这里直接处理不需要战斗的情况 比如玩家轮空，两个机器人对打，这些都是需要自行处理
  //可以直接出结果的
  this.proRobotBattleResult(round, groupId, battleList);
};

ProfessionLeague.prototype.pfMakeBattleList = function(round, groupId, battleObjList, length, historyRankData) 
{
  //把随机后玩家放入到队列表中
  let battleList = []; //{uid, uid2}
  for (let index = 0; index < length; index++) {
    var obj = battleObjList[index];

    if (!obj) continue;
    
    let uidList = this.getGroupPlayerMap(parseInt(groupId));
    let uidIdx1 = obj.Host;
    let uidIdx2 = obj.Gust;
    let beginTime = obj.BeginTime;
    //logger.info("sendBattleQueue obj", groupId, uidIdx1, uidIdx2, uidList);
    let uid1 = uidList[parseInt(uidIdx1 - 1)];
    let uid2 = uidList[parseInt(uidIdx2 - 1)];
    let typeId = 0;
    switch (groupId) {
        case 1:
            typeId = commonEnum.LEAGUE_TYPE_ID.AMATEUR;
            break;

        case 2:
            typeId = commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND;
            break;

        case 3:
            typeId = commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST;
            break;

        case 4:
            typeId = commonEnum.LEAGUE_TYPE_ID.CHAMPIONS;
            break;

        case 5:
            typeId = commonEnum.LEAGUE_TYPE_ID.SUPER;
            break; 
        default:
            if(groupId > 5 && groupId <= this.groupMaxCount)
                typeId = commonEnum.LEAGUE_TYPE_ID.PREPARE;//预备赛
            break;
    }

    var battleObj = {
      home: uid1, //主场
      away: uid2, //客场
      typeId: typeId,
      round: round,
      groupId: groupId,
      leagueName: historyRankData.leagueName,
      roundName: historyRankData.roundName,
      homeRank: this.league.getHistoryRank(historyRankData.historyRankObjMap, uid1),
      awayRank: this.league.getHistoryRank(historyRankData.historyRankObjMap, uid2),
      beginTime: beginTime,
    };

    battleList[index] = battleObj;
  }
  return battleList;
};

//处理机器人战斗结果
//Note: 这里没有对战分数
ProfessionLeague.prototype.proRobotBattleResult = function(round, groupId, battleResultList)
{
    for (let index in battleResultList) 
    {
      let winTeam = "";
      let lostTeam = "";
      let data = battleResultList[index];
      let robotA = "robot_" === data.home.substr(0, 6); //home是否为机器人
      let robotB = "robot_" === data.away.substr(0, 6); //away是否为机器人
      if (!robotA && !robotB) continue;

      let newHomePlayerObj = this.league.newPlayerObj(data.home, true);
      let newAwayPlayerObj = this.league.newPlayerObj(data.away, true);
      //都为机器人
      if (robotA && robotB) {
        //都是机器人 算主场赢  机器人不需要加积分
        winTeam = data.home;
        lostTeam = data.away;
        newHomePlayerObj.battleCount += 1;
        newHomePlayerObj.winCount += 1;
        newHomePlayerObj.goalCount += 0;
        newHomePlayerObj.missCount += 0;
        newHomePlayerObj.totalScore += 0;
  
        newAwayPlayerObj.battleCount += 1;
        newAwayPlayerObj.lossCount += 1;
        newAwayPlayerObj.goalCount += 0;
        newAwayPlayerObj.missCount += 0;
        newAwayPlayerObj.totalScore += 0;
      }
      else if (!robotA && robotB) 
      {       //这里的情况是人对机器人，是轮空直接判定为人赢
        //A为真人 B为机器人
        winTeam = data.home;
        lostTeam = data.away;
        newHomePlayerObj.battleCount += 1;
        newHomePlayerObj.winCount += 1;
        newHomePlayerObj.totalScore += 3;
    
        newAwayPlayerObj.battleCount += 1;
        newAwayPlayerObj.lossCount += 1;
        newAwayPlayerObj.totalScore += 0;
  
      } //A为机器人 B为真人
      else {
        winTeam = data.away;
        lostTeam = data.home;
        newHomePlayerObj.battleCount += 1;
        newHomePlayerObj.lossCount += 1;
        newHomePlayerObj.totalScore += 0;
  
        newAwayPlayerObj.battleCount += 1;
        newAwayPlayerObj.winCount += 1;
        newAwayPlayerObj.totalScore += 3;
      }
  
      this.addPlayerToRoundGroupRank(round, groupId, newHomePlayerObj);
      this.addPlayerToRoundGroupRank(round, groupId, newAwayPlayerObj);
      this.addPlayerToPromotion(round, groupId, winTeam);
      this.addPlayerToEliminate(round, groupId, lostTeam);
      this.addRoundBattle(round, groupId, this.league.comGetBattleResultObj(data, this.typeId));   //战斗结果保存
    }
};

//处理战斗服返回来的结果
//Note: 对战双方都是真人
ProfessionLeague.prototype.proPlayerBattleResult = function(result) 
{
  if (!result) {
    logger.error("proPlayerBattleResult: not result", result);
    return;
  }
  
  let winTeam = "";
  let lostTeam = "";
  let data = result;
  let homeScore = data.homeScore;
  let awayScore = data.awayScore;
  let round = data.round;
  let groupId = data.groupId;

  let robotA = "robot_" === data.home.substr(0, 6); //home是否为机器人
  let robotB = "robot_" === data.away.substr(0, 6); //away是否为机器人
  if (robotA || robotB) {
    logger.error("proPlayerBattleResult: home and away is robot", result);
    return;
  }
  
  let newHomePlayerObj = this.league.newPlayerObj(data.home, true);
  let newAwayPlayerObj = this.league.newPlayerObj(data.away, true);
  if (homeScore > awayScore) {
    //A赢
    winTeam = data.home;
    lostTeam = data.away;
    newHomePlayerObj.battleCount += 1;
    newHomePlayerObj.winCount += 1;
    newHomePlayerObj.goalCount += data.homeScore;
    newHomePlayerObj.missCount += data.awayScore;
    newHomePlayerObj.totalScore += 3;

    newAwayPlayerObj.battleCount += 1;
    newAwayPlayerObj.lossCount += 1;
    newAwayPlayerObj.goalCount += data.awayScore;
    newAwayPlayerObj.missCount += data.homeScore;
    newAwayPlayerObj.totalScore += 0; //失败加0分
  } else if (homeScore < awayScore) {
    //B赢
    winTeam = data.away;
    lostTeam = data.home;
    newHomePlayerObj.battleCount += 1;
    newHomePlayerObj.lossCount += 1;
    newHomePlayerObj.goalCount += data.homeScore;
    newHomePlayerObj.missCount += data.awayScore;

    newAwayPlayerObj.battleCount += 1;
    newAwayPlayerObj.winCount += 1;
    newAwayPlayerObj.goalCount += data.awayScore;
    newAwayPlayerObj.missCount += data.homeScore;
    newAwayPlayerObj.totalScore += 3; //失败加0分
    //logger.error("2");
    //logger.info("player 2 data.home, playerObj1", data.home, playerObj1);
  } //A平
  else {
    // let power1 = newHomePlayerObj.power;
    // let enrollTime1 = newHomePlayerObj.enrollTime;
    //
    // let power2 = newAwayPlayerObj.power;
    // let enrollTime2 = newAwayPlayerObj.enrollTime;
    // //战力
    // if (power1 != power2) {
    //   if (power1 > power2) {
    //     winTeam = data.home;
    //     lostTeam = data.away;
    //   } else if (power1 < power2) {
    //     winTeam = data.away;
    //     lostTeam = data.home;
    //   }
    // } else {
    //   //报名时间 靠前的人胜利
    //   if (enrollTime1 < enrollTime2) {
    //     winTeam = data.home;
    //     lostTeam = data.away;
    //   } else if (enrollTime1 > enrollTime2) {
    //     winTeam = data.away;
    //     lostTeam = data.home;
    //   } else {
    //     winTeam = data.home;
    //     lostTeam = data.away;
    //   }
    // }
    //
    // if (winTeam === data.home)
    // {
      newHomePlayerObj.totalScore += 1; 
    //}else
   // {
      newAwayPlayerObj.totalScore += 1; 
   // }

    //平局加一分
    newHomePlayerObj.battleCount += 1;
    newHomePlayerObj.drawCount += 1;
    newHomePlayerObj.goalCount += data.homeScore;
    newHomePlayerObj.missCount += data.awayScore;

    newAwayPlayerObj.battleCount += 1;
    newAwayPlayerObj.drawCount += 1;
    newAwayPlayerObj.goalCount += data.awayScore;
    newAwayPlayerObj.missCount += data.homeScore;
    //logger.error("3");
  }

  this.addPlayerToRoundGroupRank(round, groupId, newHomePlayerObj);
  this.addPlayerToRoundGroupRank(round, groupId, newAwayPlayerObj);
  this.addPlayerToPromotion(round, groupId, winTeam);
  this.addPlayerToEliminate(round, groupId, lostTeam);
  this.addRoundBattle(round, groupId, data);   //战斗结果保存
};

ProfessionLeague.prototype.addPlayerToPromotion = function(round, groupId, uid) 
{
    let round_key = this.league.getRoundKey(round, groupId);
    if (!this.hasPromotionMap(round_key)) {
      this.setPromotionMap(round_key, []);
    }
  
    let promotionUidList = this.getPromotionMap(round_key);
    //检查排名中是否存在uid，如果有就不用添加
    if (utils.hasUidInList(promotionUidList, uid)) {
      logger.error("addPlayerToPromotion: repeat add promotion uid", uid);
      return;
    }
  
    promotionUidList.push(uid);
    //promotionUidList = this.getPromotionMap(round_key);
    //logger.info("addPlayerToPromotion", round_key, promotionUidList.length);
};

ProfessionLeague.prototype.addPlayerToEliminate = function(round, groupId, uid)
{
  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasEliminateMap(round_key)) {
    this.setEliminateMap(round_key, []);
  }

  let uidList = this.getEliminateMap(round_key);
  //检查排名中是否存在uid，如果有就不用添加
  if (utils.hasUidInList(uidList, uid)) 
  {
    logger.error("_addPlayerToPromotion: repeat add promotion uid", uid, round_key);
    return;
  }
  //logger.info("addPlayerToEliminate", uidList);
  uidList.push(uid);
};

ProfessionLeague.prototype.addRoundBattle = function(round, groupId, data)
{
    this.setBattleMap(data.battleId, data);
    let round_key =  this.league.getRoundKey(round, groupId);
    if (!this.hasRoundBattleMap(round_key))
    {
      this.setRoundBattleMap(round_key, []);
    }
    let battleUidList = this.getRoundBattleMap(round_key);
    battleUidList.push(data.battleId);
};

ProfessionLeague.prototype.addPlayerToRoundGroupRank = function(round, groupId, playerObj)
{
  if (!playerObj) {
    logger.error("addPlayerToRoundGroupRank: not playerObj uid", playerObj);
    return;
  }

  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasRoundRank(round_key)) {
    this.setRoundRank(round_key, []);
  }

  var rankObjList = this.getRoundRank(round_key);
  rankObjList.push(utils.clone(playerObj));
  rankObjList.sort(__score_compare_func);
  this.setRoundRank(round_key, rankObjList);

  rankObjList = this.getRoundRank(round_key);
};

ProfessionLeague.prototype.printDirectPromotion = function(round)
{
  for (let [k, v] of this.groupPlayerMap) 
  {
    //logger.info("printDirectPromotion: round, groupId", round, k, v.length);
    if (k === 8);
      //logger.info("printDirectPromotion", v);
  }
};

ProfessionLeague.prototype.printRank = function(round, groupId)
{  
  let round_key = this.league.getRoundKey(round, groupId);
  var rankObjList = this.getRoundRank(round_key);
  let uidList = [];
  for (let i in rankObjList) {
    const playerObj = rankObjList[i];
    if (!playerObj) continue;
    uidList.push(playerObj.playerUid);
    // logger.info("PF  rankList: playerUid, totalScore, diff, goalCount, power1", 
    // playerObj.playerUid, playerObj.totalScore, playerObj.goalCount, playerObj.power, playerObj.enrollTime);
  }
  //logger.info("PF  rankList: groupId",groupId); 
};

ProfessionLeague.prototype.printPromotion = function(round)
{
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
    let round_key = this.league.getRoundKey(round, groupId);
    let uidList = this.getPromotionMap(round_key);
    if (!uidList) continue;
    // if (groupId === 8)
    //   logger.info("KKT printPromotion", round, groupId, uidList.length, uidList);
  }
};

//Note: 蛇形算法构建赛程表
//链接: https://blog.csdn.net/prolightsfxjh/article/details/73041715
/*构造法：蛇形安排赛程表
将1-N排成两竖列，每一轮同一行的为对手
保持1的位置不变，其他位置按顺（逆）时方向依次旋转
1    6          1    2          1    3          1    4          1    5      
2    5          3    6          4    2          5    3          6    4      
3    4          4    5          5    6          6    2          2    3
时间复杂度 O(n^2)
空间复杂度 O(n)
*/
ProfessionLeague.prototype._genBattleList = function(groupId, roundMap, startRound) 
{
    if (!roundMap)
    {
        logger.error("_genBattleList: not roundMap");
        return;
    }

    let teamNum = 20;
    let row = teamNum + 8;
    let column = 2;
    //二维数组声明也是有点奇葩
    let tArray = new Array();            //先声明一维
    for(let k = 0; k < row; k++){        //一维长度为i,i为变量，可以根据实际情况改变
        tArray[k] = new Array();         //声明二维，每一个一维数组里面的一个元素都是一个数组；
        for(let j = 0; j < column; j++)
        {                               //一维数组里面每个元素数组可以包含的数量p，p也是一个变量；
            tArray[k][j]=0;             //这里将变量初始化，我这边统一初始化为空，后面在用所需的值覆盖里面的值
        }
    }

    //初始赋值 先来初始队列 1 6, 2 5, 3 4
    for (let i = 1; i <= teamNum / 2; i++) {
        tArray[i][0] = i;
        tArray[i][1] = teamNum- i + 1;
    }
    
    let round = 1;
    for (let i = 1; i < teamNum; i++) {
        for (let j = 1; j <= teamNum / 2; j++) {
            //console.log(" ", tArray[j][0], tArray[j][1]);
            var obj = {};
            let afterRound = round + startRound;
            let beginTime = this.league.leagueFsm.getRoundBattleTime(this.typeId, afterRound);
            if (afterRound > 19)
            {
                obj = {
                    Host: tArray[j][1],  //客场
                    Gust: tArray[j][0],
                    BeginTime: beginTime,
                };
            }
            else
            {
                obj = {
                    Host: tArray[j][0],   //主场
                    Gust: tArray[j][1],
                    BeginTime: beginTime,
                };
            }

            let round_key = this.league.getRoundKey(afterRound, groupId);
            if (!roundMap.has(round_key))
            {
                roundMap.set(round_key, []);
            }

            let objList = roundMap.get(round_key);
            objList.push(obj);
            roundMap.set(round_key, objList);
        }

        //console.log("_________________________", round);
        let t = tArray[2][0];
        for (let j = 2; j < teamNum / 2; j++) {
            tArray[j][0] = tArray[j + 1][0];
        }

        tArray[teamNum / 2][0] = tArray[teamNum / 2][1];
        for (let j = teamNum / 2; j >= 2; j--) {
            tArray[j][1] = tArray[j - 1][1];
        }

        tArray[1][1] = t;
        round++;
    }
};

ProfessionLeague.prototype.genBattleSchedule = function()
{
    for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
       //分配 1-19轮
       this._genBattleList(groupId, this.battleScheduleMap, 0);
       //分配 20-38轮
       this._genBattleList(groupId, this.battleScheduleMap, 19);
       //轮次内打乱顺序
       for (let [k,v] of this.battleScheduleMap) {
           let objList = utils.arrayIndexRandom(v);
           v = objList;
           //logger.info("k,v", k, v);
       }
    }
};

//分组对阵
ProfessionLeague.prototype.SplitPrepare = function(status_obj) 
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroupSplitPrepare;
    let check = this.league.checkRound(round, localRound, "PF SplitPrepare");
    if (!check)
    { 
      return false;
    }
  
    let ret = this.checkRunning(round);
    if (ret)
    {
      ret = this.canRunNextAction(round);
      logger.info("_eliminationAudition: canRunNextAction", ret);
      if (ret) return true;
      return false;
    }

    //分组 打乱玩家的顺序
    this.checkGroup();

    //生成赛程
    this.genBattleSchedule();
    this.setIsRunning(round);
    this.setSendStartNtf(round);
    this.setSendWaitStartNtf(round);
    this.setCanRunNextAction(round);
    this.setBattleStatus(round, commonEnum.LEAGUE_BATTLE_STATUS.END);
    return false;
};

ProfessionLeague.prototype.onShowProfession = function(status_obj)
{
    let round = this.currRound;
    if (round === commonEnum.LEAGUE_RUN_ROUND.ProfessionGroupSplitPrepare && !this.checkRunning(round))
    {
        this.SplitPrepare(status_obj);
        this.commonSettle(status_obj);
    }
    return true;
};

//最近晋级玩家列表
ProfessionLeague.prototype.getFinalPrUidList = function()
{
    let uidList = [];
    let last_round = this.getLastRound();
    if (!last_round) return uidList;
    last_round = 1;
    logger.info("getFinalPrUidList last_round", last_round);

    uidList = this.getPromotionUidList(last_round);

    logger.info("getFinalPrUidList uidList, length", uidList, uidList.length);
    return uidList;
};

//发送消息通知客户端比赛即将开始
//找到该轮次所有的人,打包出来
ProfessionLeague.prototype.commonWait = function() 
{
  let roundNotifyData = [];
  let round = this.currRound;
  if (!this.checkSendWaitStartNtf(round))
  {
    logger.info("ProfessionLeague commonWait nextRound", round);
    roundNotifyData = this.getRoundNtfPlayerData(round);
    this.setSendWaitStartNtf(round);
  }
  return roundNotifyData;
};

//发送的玩家数据已经生成好了，需要从记录里面去拿
ProfessionLeague.prototype.getRoundNtfPlayerData = function(round)
{
  let roundNtfPlayerStartData = [];
  if (round > this.currRound)
  {
    return roundNtfPlayerStartData;
  }

  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
      let round_key = this.league.getRoundKey(round, groupId);
      let battleUidList = this.getRoundBattleMap(round_key);
      if (!battleUidList || battleUidList.length <= 0) continue;
      
      for (let idx in battleUidList) {
       let battleId = battleUidList[idx];
       let battleObj = this.getBattleMap(battleId);
       if (!battleObj) continue;
       
       let home = battleObj.home;
       let away = battleObj.away;
       let roomUid = battleObj.roomUid;

       //过滤有机器人的情况
       if (this.league.checkRobot(home) || this.league.checkRobot(away)) continue;
       
       let teamA = this.league.getSimpleTeamInfo(home);
       let teamB = this.league.getSimpleTeamInfo(away);
       let leagueName = this.league.getProfessionLeagueName(groupId) + "联赛";
       let homeSimpleRolePrepareInfo = {
          uid: home,
          roomUid: roomUid,
          competitorUid: away,
          teamA: teamA,
          teamB: teamB,
          beginTime: battleObj.beginTime,
          leagueName: leagueName,
          groupId: groupId,
          round: round,
        };

       let awaySimpleRolePrepareInfo = {
          uid: away,
          roomUid: roomUid,
          competitorUid: home,
          teamA: teamA,
          teamB: teamB,
          beginTime: battleObj.beginTime,
          leagueName: leagueName,
          groupId: groupId,
          round: round,
         };

        //logger.info("getRoundNtfPlayerData: homeSimpleRolePrepareInfo", homeSimpleRolePrepareInfo);
       roundNtfPlayerStartData.push(homeSimpleRolePrepareInfo);
       roundNtfPlayerStartData.push(awaySimpleRolePrepareInfo);
      }
    }
    logger.info("ProfessionLeague getRoundNtfPlayerData", round, roundNtfPlayerStartData.length);
    return roundNtfPlayerStartData;
};

ProfessionLeague.prototype.commonPlayRecord = function(status_obj)
{
  let round = this.currRound;
  let leagueName = commonEnum.LEAGUE_NAME.AMATEUR + "联赛";
  let data = {
    typeId: this.getTypeId(),
    leagueName: leagueName,
    round: round,
  };

  let isSend = false;
  if (!this.checkSendStartNtf(round))
  {
    logger.info("ProfessionLeague commonPlay nextRound", round);
    this.setSendStartNtf(round);
    isSend = true;
  }

  let battleStatus = this.getConfigBattleStatus(round)
  if(commonEnum.LEAGUE_BATTLE_STATUS.RUNNING !== battleStatus)
  {
    this.setBattleStatus(round, commonEnum.LEAGUE_BATTLE_STATUS.RUNNING);
  }

  return {isNeedSend: isSend, playRecord: data};
};

ProfessionLeague.prototype.commonSettle = function(status_obj) 
{   
    logger.info("PF commonSettle", this.currRound);
    this.setBattleStatus(this.currRound, commonEnum.LEAGUE_BATTLE_STATUS.END);
    this.league.saveProfession();
    return true;
};

ProfessionLeague.prototype.getComNotifyList = function(round, isPromotion)
{
  let notifyMailList = [];
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) 
  {
      let notifyType = 0;
      let uidList = [];
      if (isPromotion)
      {      
        uidList = this.getPromotionUidListByGroupId(round, groupId);
        notifyType = commonEnum.FINAL_MAIL_NOTIFY_TYPE.PROMOTION;
      }else
      {
        uidList = this.getEliminateUidListByGroupId(round, groupId);
        notifyType = commonEnum.FINAL_MAIL_NOTIFY_TYPE.ELIMINATE;
      }

      for(let idx in uidList)
      { 
        let pUid = uidList[idx];
        if (this.league.checkRobot(pUid)) //机器人过滤
        {
           continue;
        }

        let pBattleId = this.getBattleIdByRoundGroupId(round, groupId, pUid);
        //logger.info("getNotifyMailEliminateList: pBattleId", pBattleId);
        if (!pBattleId)
        {
          logger.info("getNotifyMailEliminateList: not found pBattleId", round, groupId, pUid, pBattleId);
          continue;
        }

        let battleObj = this.getBattleMap(pBattleId);
        if (!battleObj)
        {
          logger.info("getNotifyMailEliminateList: not found battleObj", round, groupId, pUid, pBattleId);
          continue;
        }

        let leagueName = this.league.getProfessionLeagueName(groupId) + "联赛";
        let pObj = this.league.comGetNotifyMailObj(pUid, battleObj, this.getTypeIdByGroupId(groupId), notifyType, leagueName, round);
        notifyMailList.push(pObj);
      }
    }
    
    return notifyMailList;
};

ProfessionLeague.prototype.getNotifyMailPromotionList = function(round)
{
  return this.getComNotifyList(round, true);
};

ProfessionLeague.prototype.getNotifyMailEliminateList = function(round)
{
  return this.getComNotifyList(round, false);
};

ProfessionLeague.prototype.commonNextRound = function(status_obj)
{
  this.currRound = this.currRound + 1;
  logger.info("ProfessionLeague commonNextRound currRound", this.currRound);
  return true;
};

//最终结算      在这里升降级机制
ProfessionLeague.prototype.finalSettle = function()
{   
    //计算出最终排名
    for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) 
    {  
        this.makeFinalGroupRank(groupId);  
    }
    this.promotionTeam();       //按照排名移动球队位置
    this.setBattleStatus(this.currRound, commonEnum.LEAGUE_BATTLE_STATUS.END);
    this.league.saveProfession();
    return true;
};

//通知最终晋级的玩家
ProfessionLeague.prototype.getNotifyMailFinalPromotion = function()
{
  let notifyMailList = [];
  for (let [k, v] in this.finalPromotionMap) 
  {
    let uid = k;
    let groupId = v;
    let name = this.league.getProfessionLeagueName(groupId);
    if (this.league.checkRobot(uid)) //机器人过滤
    {
       continue;
    }
    let pObj = {
      uid: uid,
      name: name,
      typeId: this.getTypeIdByGroupId(groupId),
      notifyType: commonEnum.FINAL_MAIL_NOTIFY_TYPE.PROMOTION,
    };

    notifyMailList.push(pObj);
  }

  return notifyMailList;
};

//降级玩家
ProfessionLeague.prototype.getNotifyMailFinalEliminate = function()
{
  let notifyMailList = [];
  for (let [k, v] in this.finalEliminateMap) 
  {
    let uid = k;
    let groupId = v;
    let name = this.league.getProfessionLeageName(groupId);
    let pObj = {
      uid: uid,
      name: name,
      typeId: this.getTypeIdByGroupId(groupId),
      notifyType: commonEnum.FINAL_MAIL_NOTIFY_TYPE.ELIMINATE,
    };
    notifyMailList.push(pObj);
  }

  return notifyMailList;
};

//获取业余赛玩家列表 业余赛最后16组
ProfessionLeague.prototype.getLast16TeamUidList = function()
{
    let joinerUidList = [];
    let needNum = 16;
    let groupId = 1;
    let count = 0;
    let uidList = this.getGroupPlayerMap(groupId);

    //logger.info("getLast16TeamUidList", groupId, uidList);
    if (!uidList)
    {
        logger.error("getLast16TeamUidList not uidList", groupId);
        return joinerUidList;
    }

    for (let index = uidList.length -1; index < uidList.length && index >= 0; index--) {
        const uid = uidList[index];
        if (!uid) continue;

        let playerObj = this.getPlayerMap(uid);
        if (!playerObj) continue;
        joinerUidList.push(playerObj);
        count++;
        if (count >= needNum)
        {
            break;
        }
    }

   //logger.info("getLast16TeamUidList", joinerUidList.length);
    return joinerUidList;
};

//信仰预备联赛冠军玩家晋级到业余联赛
ProfessionLeague.prototype.promotionPlayerToTeam = function(finalUidList, knockoutGroupUidMap, tmkRankMap)
{
    let groupId = 1;//业余
    let uidList = [];
    //按排名来
    let playerRank = this.finalRank.get(groupId);
    for(let i in playerRank)
    {
        uidList.push(playerRank[i].playerUid);
    }
    let knockUidList = knockoutGroupUidMap.get(2);//懂乙掉下来的4名（懂乙groupId：2）
    //logger.info("promotionPlayerToTeam 2", uidList, groupId, uidList.length);
    if (!uidList)
    {
      logger.info("promotionPlayerToTeam: not uidList", groupId);
      return;
    }
    let len = 20 - knockUidList.length;//减去懂乙降级的人数
    let num = len - finalUidList.length;
    if(num > uidList.length)
        num = uidList.length;
    let leftTop4Uid = [];
    for(let i in knockUidList)
    {
        leftTop4Uid.push(knockUidList[i]);
    }
    for (let index = knockUidList.length; index < num; index++) {
        const uid = uidList[index];
        if (!uid) continue;
        leftTop4Uid.push(uid);
    }

    // for (let idx = 0; idx < uidList.length; idx++) {
    //     const uid = uidList[idx];
    //     if (utils.hasUidInList(uidList, uid))
    //     {
    //         continue;
    //     }
    //     this.delPlayerMap(uid);//玩家个人信息
    //     this.delPlayerGroupMap(uid); //玩家groupId映射
    // }
    //
    // this.setGroupPlayerMap(groupId, leftTop4Uid);
    // uidList = this.getGroupPlayerMap(groupId);
    // logger.info("promotionPlayerToTeam 1 ", leftTop4Uid, uidList, uidList.length);
    //
    // for (let idx = 0; idx < finalUidList.length; idx++) {
    //     const uid = finalUidList[idx];
    //     let uidList = this.getGroupPlayerMap(groupId);
    //     uidList.push(uid);
    //     this.setPlayerGroupMap(uid, groupId);
    //     this.setPlayerMap(uid, this.league.newPlayerObj(uid, true));
    // }


    let upList = leftTop4Uid;

    for(let i in finalUidList)
    {
        upList.push(finalUidList[i]);
    }
    let rank = 1;
    let tmpList = [];

    for(let i in upList)
    {
        tmpList.push({uid: upList[i], rank: rank++});
    }
    tmkRankMap.set(groupId, tmpList);
    logger.info("promotionPlayerToTeam 2", uidList, uidList.length);
};

//第一次联赛按照排名将玩家的插入到各个联赛中
//groupIdUidMap 
//结构: groupId =>uidList
ProfessionLeague.prototype.insertProfessionRank = function(groupIdRankUidMap)
{
    let count = 0;
    for (let [k, v] of groupIdRankUidMap)
    {
        let groupId = k;
        if (!v) continue;
        for (let idx = 0; idx < v.length; idx++) {
            const playerObj = v[idx];
            const uid = playerObj.playerUid;

            if (!this.finalReserveList.has(groupId))
            {
                this.finalReserveList.set(groupId, []);
            }

            let groupUid = this.finalReserveList.get(groupId);
            groupUid.push(uid);
            this.addPlayer(parseInt(groupId), uid, playerObj);
            count++;
        }
    }

    this.league.saveProfession();//专业联赛玩家列表存入数据库
    logger.info("insertProfessionRank count", count);
};

//添加玩家个人信息
ProfessionLeague.prototype.addPlayer = function(groupId, uid, playerObj)
{
    if (!groupId || !uid) return;

    if (!this.hasGroupPlayerMap(groupId))
    {
        this.setGroupPlayerMap(groupId, []);
    }

    let uidList = this.getGroupPlayerMap(parseInt(groupId));
    uidList.push(uid);
    this.setPlayerGroupMap(uid, groupId);

    var obj = this.league.newPlayerObj(uid, false);
    obj.power = playerObj.power;
    this.setPlayerMap(uid, obj);
};

ProfessionLeague.prototype.getEnrollSuccess = function()
{
  let uidList = [];
  for (let [k,v] of this.finalReserveList)
  {
    for(let idx in v)
    {
      let uid = v[idx];
      if (this.league.checkRobot(uid))
      {
        continue;
      }

      uidList.push(uid);
    }
  }
  return uidList;
};

//************************************************************消息************************** */
ProfessionLeague.prototype.getCurrRound = function()
{
  return this.currRound;
};

ProfessionLeague.prototype.getFixCurrRound = function()
{
  let realCurrRound = this.getCurrRound();
  let fixCurrRound = realCurrRound;
  let battleStatus = this.getConfigBattleStatus(realCurrRound);
  if(realCurrRound > 0 && commonEnum.LEAGUE_BATTLE_STATUS.END !== battleStatus) //比赛未开始、取上一轮
  {
    fixCurrRound = realCurrRound - 1;
    logger.info("ProfessionLeague getFixCurrRound: realCurrRound, fixCurrRound", realCurrRound, fixCurrRound);
  }
  return fixCurrRound;
};

ProfessionLeague.prototype.getBattleIdByRoundGroupId = function(round, groupId, uid)
{
    let battleId = "";
    let round_key = this.league.getRoundKey(round, groupId);
    let battleUidList =  this.getRoundBattleMap(round_key);
    if (!battleUidList)
    {
        logger.info("getBattleIdByRoundGroupId: not found battleUidList! round_key", round_key);
        return battleId;
    }

    for(let idx in battleUidList)
    {
      let __battleId = battleUidList[idx];
      let battleObj =  this.getBattleMap(__battleId);
      if (uid === battleObj.home || uid === battleObj.away )
      {
        battleId = __battleId;
        break;
      }
    }
    
    return battleId;
};

//获取赛程榜
ProfessionLeague.prototype.getRoundScheduleInfo = function(round, groupId)
{
  let roundScheduleData = 
  {
    typeId: this.getTypeIdByGroupId(groupId),
    roundId: round,
    name: "",
    groupId: groupId,
    finalRound: this.getFinalRound(false),
    maxGroupCount: 1,
    scheduleTime: this.league.getBattleTime(this.getTypeIdByGroupId(groupId)),
    scheduleList: [],
  };

  if (round <= 0)
  {
    logger.error("getRoundScheduleInfo: round less 0!", round);
    return roundScheduleData;
  }

  let config= this.getRoundConfig(round);
  if (!config)
  {
    logger.error("getRoundScheduleInfo: round error!", round);
    return roundScheduleData;
  }

  roundScheduleData.name = config.name;
  if (!groupId || groupId <= 0)
  {
    logger.error("getRoundScheduleInfo: groupId error!", round, groupId);
    return roundScheduleData;
  }

  logger.info("getRoundScheduleInfo: round, groupId", round, groupId);
  let roundGroupId = this.league.getRoundKey(round, groupId);
  let battleObjList = this.getBattleScheduleMap(roundGroupId);
  if (!battleObjList) 
  {
    logger.error("getRoundScheduleInfo: battleObjList error!", round, groupId);
    return roundScheduleData;
  }
  
  let scheduleList = [];
  let battleStatus = this.getConfigBattleStatus(round);
  let uidList = this.getGroupPlayerMap(parseInt(groupId));
  if (!uidList) 
  {
    logger.error("getRoundScheduleInfo: uidList error!", round, groupId);
    return roundScheduleData;
  }

  for (const idx in battleObjList) 
  {
    let obj = battleObjList[idx];
    if (!obj) continue;
    let uidIdx1 = obj.Host;
    let uidIdx2 = obj.Gust;
    let beginTime = obj.BeginTime;
    
    let uid1 = uidList[parseInt(uidIdx1 - 1)];
    let uid2 = uidList[parseInt(uidIdx2 - 1)];

    let roomUid = "";
    let teamAScore = 0;
    let teamBScore = 0;
    let robotA = this.league.checkRobot(uid1); //home是否为机器人
    let robotB = this.league.checkRobot(uid2); //away是否为机器人

    if (robotA && robotB) continue;

    if (battleStatus !== commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN) {
        let battleId = this.getBattleIdByRoundGroupId(round, groupId, uid1);
        let battleObj =  this.getBattleMap(battleId);
        roomUid = battleObj.roomUid;
        teamAScore = battleObj.homeScore;
        teamBScore = battleObj.awayScore;
    }

    if (robotA)
    {
      uid1 = "";
    }

    if (robotB)
    {
      uid2 = "";
    }

    let ScheduleGroupInfo = {
      battleId: roomUid,
      teamA: uid1,
      teamB: uid2,
      teamAScore: teamAScore,
      teamBScore: teamBScore,
      beginTime: beginTime,
      status: battleStatus,
      teamAName: "",
      teamBName: "",
    };

    scheduleList.push(ScheduleGroupInfo);
  }

  roundScheduleData.scheduleList = scheduleList;
  logger.info("roundScheduleData", roundScheduleData.scheduleList.length);
  return roundScheduleData;
};

ProfessionLeague.prototype.getRoundScoreRankInfo = function(round, groupId)//显示每一轮的积分榜
{
  let roundScoreRankData = 
  {
    typeId: this.getTypeIdByGroupId(groupId),
    roundId: round,
    name: "",
    groupId: groupId,   //对客户端来说一个组
    finalRound: this.getCurrFinalRound(true),
    maxGroupCount: 1,
    scheduleTime: this.league.getBattleTime(this.getTypeIdByGroupId(groupId)),
    maxRound: this.getMaxRound(),
    teamInfoList: [],
  };
  
  if (round === this.getFinalRound(true))
  {
    return this.getFinalScoreRankInfo(groupId);
  }

  let config= this.getRoundConfig(round);
  if (!config)
  {
    logger.error("getRoundScoreRankInfo: round error!", round);
    return roundScoreRankData;
  }

  roundScoreRankData.name = config.name;
  if (!groupId || groupId <= 0)
  {
    logger.error("getRoundScoreRankInfo: groupId error!", round, groupId);
    return roundScoreRankData;
  }

  logger.info("getRoundScoreRankInfo: round, groupId", round, groupId);
  let teamInfoList = [];
  if (0 === round)
  {
    //比赛未开始，显示积分赛，数据全为0
    for(let [k, v ] of this.groupPlayerMap)
    {
        if (parseInt(k) === parseInt(groupId))
        {
          let uidList = v;
          for (const idx in uidList) {
              let uid = uidList[idx];
              if (this.league.checkRobot(uid)) continue;
              let obj = this.getPlayerMap(uid);
              let playerObj = {
                  playerUid: obj.playerUid,
                  name: "",
                  faceIcon: 0,
                  battleCount: obj.battleCount,
                  winCount: obj.winCount,
                  drawCount: obj.drawCount,
                  lossCount: obj.lossCount,
                  goalCount: obj.goalCount,
                  missCount: obj.missCount,
                  totalScore: obj.totalScore,
                  knockoutStatus: commonEnum.KNOCOUT_LEVEL.DOWNGRADE,
                }
              teamInfoList.push(playerObj);
          }   
      }
    }

    roundScoreRankData.roundId = 1;
    roundScoreRankData.finalRound = 0;
    roundScoreRankData.teamInfoList = teamInfoList;
    return roundScoreRankData;
  }

  let battleStatus = this.getConfigBattleStatus(round);
  if (battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN)
  {
      //大于当前轮次就返回总榜
      if (round > this.currRound)
      {
          return this.getFinalScoreRankInfo(groupId);
      }
  }

  if (commonEnum.LEAGUE_BATTLE_STATUS.END === battleStatus)
  {
    let roundGroupId = this.league.getRoundKey(round, groupId);
    let rankObjList = this.getRoundRank(roundGroupId);
    if (!rankObjList) 
    {
      logger.error("getRoundScoreRankInfo: rankObjList error!", round, groupId);
      return roundScoreRankData;
    }

    for(let idx in rankObjList)
    {
      let rank = rankObjList[idx];
      if ("" === rank.playerUid) continue;
      if (this.league.checkRobot(rank.playerUid)) continue;
    
      let playerObj = {
        playerUid: rank.playerUid,
        name: "",
        faceIcon: 0,
        battleCount: rank.battleCount,
        winCount: rank.winCount,
        drawCount: rank.drawCount,
        lossCount: rank.lossCount,
        goalCount: rank.goalCount,
        missCount: rank.missCount,
        totalScore: rank.totalScore,
        knockoutStatus: commonEnum.KNOCOUT_LEVEL.DOWNGRADE,
      }
      teamInfoList.push(playerObj);
    }
  }

  roundScoreRankData.teamInfoList = teamInfoList;
  //logger.info("roundScoreRankData", roundScoreRankData);
  return roundScoreRankData;
};

ProfessionLeague.prototype.getFinalScoreRankInfo = function(groupId)//显示总榜
{
  let roundScoreRankData = 
  {
    typeId: this.getTypeIdByGroupId(groupId),
    roundId: this.currRound + 1,//this.getLastRound() + 1,
    name: "总榜",
    groupId: groupId,
    finalRound: this.currRound + 1,//this.getCurrFinalRound(true),
    maxGroupCount: 1,
    scheduleTime: this.league.getBattleTime(this.getTypeIdByGroupId(groupId)),
    maxRound: this.getMaxRound(),
    teamInfoList: [],
  };

  let lastRound = this.getLastRound();
  // if (lastRound != this.getCurrRound()) //还没有到最后一轮
  // {
  //   logger.error("getRoundScoreRankInfo: lastRound error! round, groupId, battleStatus", lastRound, groupId);
  //   return roundScoreRankData;
  // }

   let battleStatus = this.getConfigBattleStatus(lastRound);
  // if (battleStatus !== commonEnum.LEAGUE_BATTLE_STATUS.END) //还没有结束
  // {
  //   logger.error("getRoundScoreRankInfo: battleStatus error! round, groupId, battleStatus", lastRound, groupId, battleStatus);
  //   return roundScoreRankData;
  // }

  let rankObjList = this.finalRank.get(groupId);
  if (!rankObjList)
  {
    logger.error("getRoundScoreRankInfo: rankObjList error! round, groupId, battleStatus", lastRound, groupId, battleStatus);
    return roundScoreRankData;
  }

  let teamInfoList = [];
  for (let i in rankObjList) 
  {
    const rank = rankObjList[i];
    let uid = rank.playerUid;
    if ("" === rank.playerUid) continue;
    if (this.league.checkRobot(uid)) continue;
    
    let playerObj = {
      playerUid: rank.playerUid,
      name: "",
      faceIcon: 0,
      battleCount: rank.battleCount,
      winCount: rank.winCount,
      drawCount: rank.drawCount,
      lossCount: rank.lossCount,
      goalCount: rank.goalCount,
      missCount: rank.missCount,
      totalScore: rank.totalScore,
      knockoutStatus: commonEnum.KNOCOUT_LEVEL.DOWNGRADE,
    }
    //logger.info("rank.playerUid 2", rank.playerUid);
    teamInfoList.push(playerObj);
  }
  
  roundScoreRankData.teamInfoList = teamInfoList;
  //logger.info("roundScoreRankData", roundScoreRankData);
  return roundScoreRankData;
};

ProfessionLeague.prototype.checkLastRoundEnd = function()
{
  let lastRound = this.getLastRound();
  let currRound = this.getCurrRound();
  let battleStatus = this.getConfigBattleStatus(currRound);
  if (lastRound === currRound && battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END) //还没有到最后一轮
  {
    return true;
  }

  logger.info("checkLastRoundEnd: lastRound, currRound, battleStatus", lastRound, currRound, battleStatus);
  return false;
};

ProfessionLeague.prototype.getCurrScheduleInfo =  function(groupId)
{
  let round = this.getFixCurrRound();
  let roundScheduleData = this.getRoundScheduleInfo(round, groupId);
  return roundScheduleData;
};

ProfessionLeague.prototype.getCurrScoreRankInfo = function(groupId)
{
  let round = this.getFixCurrRound();
  let roundScoreRankData = this.getRoundScoreRankInfo(round, groupId);
  return roundScoreRankData;
};

ProfessionLeague.prototype.getGroupIdByUid = function(uid)
{
  let groupId = this.getPlayerGroupMap(uid);
  if (!groupId)
  {
    return 0;
  }

  return groupId;
};

//检查玩家在某一小组，某一轮次是否
ProfessionLeague.prototype.checkBattleScheduleByUid = function(round, groupId, uid)
{
  let battleId = this.getBattleIdByRoundGroupId(round, groupId, uid);
  if ( "" !== battleId)
  {
    return true;
  }

  return false;
};

ProfessionLeague.prototype.getBattleObjByRoundGroupId = function(round, groupId, uid)
{
    let round_key = this.league.getRoundKey(round, groupId)
    let battleUidList = this.getRoundBattleMap(round_key);
    if (!battleUidList)
    {
      logger.error("getBattleObjByRoundGroupId: get battleUidList failed!", round, groupId);
      return null;
    }
    
    for (let idx in battleUidList) 
    {
      let battleId = battleUidList[idx];
      let data = this.getBattleMap(battleId);
      if (!data)
      {
        continue;
      }

      let home = data.home;
      let away = data.away;
      if (home === uid || away === uid)
      {
        return data;
      }
    }

    return null;
};

//已经结束比赛的要上积分
ProfessionLeague.prototype.addHistory = function(groupId, round, battleObj)
{
  let battleStatus = this.getConfigBattleStatus(round);
  if (battleStatus < 0)
  {
    logger.error("addHistory: battleStatus error! round, groupId, battleStatus", round, groupId, battleStatus);
    return null;
  }

  let uidList = this.getGroupPlayerMap(parseInt(groupId));
  if (!uidList)
  {
    logger.error("addHistory: uidList error! round, groupId, battleStatus", round, groupId);
    return null;
  }

  let uidIdx1 = battleObj.Host;
  let uidIdx2 = battleObj.Gust;
  let beginTime = battleObj.BeginTime;
  let uidA = uidList[parseInt(uidIdx1 - 1)];
  let uidB = uidList[parseInt(uidIdx2 - 1)];
  let teamA = this.league.getSimpleTeamInfo(uidA);
  let teamB = this.league.getSimpleTeamInfo(uidB);
  let teamAScore = 0;
  let teamBScore = 0;
  //只有打完了才有分数
  if (battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END)
  {
    let data = this.getBattleObjByRoundGroupId(round, groupId, uidA);
    if (!battleObj)
    {
      logger.error("addHistory: getBattleObjByRoundGroupId failed", round, groupId, uidA);
      return null;
    }
    teamAScore = data.homeScore;
    teamBScore = data.awayScore;
  }

  let isNull = 0;
  if (this.league.checkRobot(uidA))
  {
    teamA.playerUid = "";
    isNull = 1;
  }

  if (this.league.checkRobot(uidB))
  {
    teamB.playerUid = "";
    isNull = 1;
  }

  let history = {
    typeId: this.getTypeIdByGroupId(groupId),
    groupId: groupId,
    roundId: round,
    beginTime: beginTime,
    teamA: teamA,
    teamB: teamB,
    teamAScore: teamAScore,
    teamBScore: teamBScore,
    status: battleStatus,
    isNull: isNull
  };

  return history;
};

ProfessionLeague.prototype.getHistory = function(uid)
{
  if (!uid)
  {
    logger.error("getHistory: not uid");
    return;
  }

  let personalHistoryInfo = [];
  //检查玩家是否参赛
  if(!this.getPlayerMap(uid)) //未参赛
  {
    logger.warn("getHistory: player not join here", uid);
    return personalHistoryInfo;
  }

  let groupId = this.getPlayerGroupMap(uid);
  if (!groupId || groupId === 0)
  {
    logger.error("getHistory: not found groupId", uid);
    return personalHistoryInfo;
  }

  logger.info("getHistory", groupId, uid, this.currRound);
  if (this.currRound <= 0)
  {
    return personalHistoryInfo;
  }

  //把赛程拿出来
  for (let round = 1; round <= this.currRound; round++) 
  {
    let round_key = this.league.getRoundKey(round, groupId)
    let objList = this.getBattleScheduleMap(round_key);
    if (!objList) continue;

    logger.error("getHistory: ", round_key, objList);
    let uidList = this.getGroupPlayerMap(parseInt(groupId));
    if (!uidList) continue;

    for(let idx in objList)
    {
      let battleObj = objList[idx];
      if (!battleObj) continue;
      let uidIdx1 = battleObj.Host;
      let uidIdx2 = battleObj.Gust;

      let uid1 = uidList[parseInt(uidIdx1 - 1)];
      let uid2 = uidList[parseInt(uidIdx2 - 1)];
      if (uid === uid1 || uid === uid2)
      {
        let history = this.addHistory(groupId, round, battleObj);
        if (!history)
        {
          continue;
        }
        personalHistoryInfo.push(history);
      }
    }
  }
  return personalHistoryInfo;
};

ProfessionLeague.prototype.getAllSchedule = function(uid)
{
    if (!uid)
    {
        logger.error("getAllSchedule: not uid");
        return;
    }

    let personalHistoryInfo = [];
    //检查玩家是否参赛
    if(!this.getPlayerMap(uid)) //未参赛
    {
        logger.warn("getAllSchedule: player not join here", uid);
        return personalHistoryInfo;
    }

    let groupId = this.getPlayerGroupMap(uid);
    if (!groupId || groupId === 0)
    {
        logger.error("getAllSchedule: not found groupId", uid);
        return personalHistoryInfo;
    }

    logger.info("getAllSchedule", groupId, uid, this.currRound);
    if (this.currRound <= 0)
    {
        return personalHistoryInfo;
    }

    //把赛程拿出来
    for (let round = 1; round <= 38; round++)
    {
        let round_key = this.league.getRoundKey(round, groupId)
        let objList = this.getBattleScheduleMap(round_key);
        if (!objList) continue;

        //logger.error("getHistory: ", round_key, objList);
        let uidList = this.getGroupPlayerMap(parseInt(groupId));
        if (!uidList) continue;

        for(let idx in objList)
        {
            let battleObj = objList[idx];
            if (!battleObj) continue;
            let uidIdx1 = battleObj.Host;
            let uidIdx2 = battleObj.Gust;

            let uid1 = uidList[parseInt(uidIdx1 - 1)];
            let uid2 = uidList[parseInt(uidIdx2 - 1)];
            if (uid === uid1 || uid === uid2)
            {
                let history = this.addHistory(groupId, round, battleObj);
                if (!history)
                {
                    continue;
                }
                history.seasonId = this.uid;
                personalHistoryInfo.push(history);
            }
        }
    }
    return personalHistoryInfo;
};

ProfessionLeague.prototype.getMaxRound = function()
{
  let lastRound = this.getLastRound();
  return lastRound + 1;
};

//*************************************辅助函数*************************************************** */

//battleScheduleMap
ProfessionLeague.prototype.setBattleScheduleMap = function(round_key, battleUidList) 
{
    this.battleScheduleMap.set(round_key, battleUidList);
};

ProfessionLeague.prototype.getBattleScheduleMap = function(round_key) 
{
    return this.battleScheduleMap.get(round_key);
};

ProfessionLeague.prototype.hasBattleScheduleMap = function(round_key) 
{
    return this.battleScheduleMap.has(round_key);
};

//playerMap
ProfessionLeague.prototype.setPlayerMap = function(uid, playerObj) 
{
    this.playerMap.set(uid, playerObj);
};

ProfessionLeague.prototype.delPlayerMap = function(uid) 
{
    this.playerMap.delete(uid);
};

ProfessionLeague.prototype.getPlayerMap = function(uid) 
{
    return this.playerMap.get(uid);
};

ProfessionLeague.prototype.hasPlayerMap = function(uid) 
{
    return this.playerMap.has(uid);
};

//playerGroupMap
ProfessionLeague.prototype.setPlayerGroupMap = function(uid, groupId) 
{
    this.playerGroupMap.set(uid, groupId);
};

ProfessionLeague.prototype.getPlayerGroupMap = function(uid) 
{
    return this.playerGroupMap.get(uid);
};

ProfessionLeague.prototype.hasPlayerGroupMap = function(uid) 
{
    return this.playerGroupMap.has(uid);
};

ProfessionLeague.prototype.delPlayerGroupMap = function(uid)
{
    return this.playerGroupMap.delete(uid);
};

//groupPlayerMap
ProfessionLeague.prototype.setGroupPlayerMap = function(groupId, uidList) 
{
    this.groupPlayerMap.set(groupId, uidList);
};

ProfessionLeague.prototype.getGroupPlayerMap = function(groupId) 
{
    return this.groupPlayerMap.get(groupId);
};

ProfessionLeague.prototype.hasGroupPlayerMap = function(groupId) 
{
    return this.groupPlayerMap.has(groupId);
};

//PromotionMap
ProfessionLeague.prototype.setPromotionMap = function(round_key, promotionUidList) 
{
    this.promotionMap.set(round_key, promotionUidList);
};

ProfessionLeague.prototype.getPromotionMap = function(round_key) 
{
    return this.promotionMap.get(round_key);
};

ProfessionLeague.prototype.hasPromotionMap = function(round_key) 
{
    return this.promotionMap.has(round_key);
};

ProfessionLeague.prototype.setEliminateMap = function( round_key, uidList) {
  this.eliminateMap.set(round_key, uidList);
};

ProfessionLeague.prototype.getEliminateMap = function(round_key) {
  return this.eliminateMap.get(round_key);
};

ProfessionLeague.prototype.hasEliminateMap = function(round_key) {
  return this.eliminateMap.has(round_key);
};

//RoundBattleMap
ProfessionLeague.prototype.setRoundBattleMap = function(round_key, battleUidList) 
{
    this.roundBattleMap.set(round_key, battleUidList);
};

ProfessionLeague.prototype.getRoundBattleMap = function(round_key) 
{
    return this.roundBattleMap.get(round_key);
};

ProfessionLeague.prototype.hasRoundBattleMap = function(round_key) 
{
    return this.roundBattleMap.has(round_key);
};

//battleMap
ProfessionLeague.prototype.setBattleMap = function(battleUid, battleObj) 
{
    this.battleMap.set(battleUid, battleObj);
};

ProfessionLeague.prototype.getBattleMap = function(battleUid) 
{
    return this.battleMap.get(battleUid);
};

ProfessionLeague.prototype.hasBattleMap = function(battleUid) 
{
    return this.battleMap.has(battleUid);
};

//roundRank
ProfessionLeague.prototype.setRoundRank = function(round_key, rankList) 
{
    this.roundRank.set(round_key, rankList);
};

ProfessionLeague.prototype.getRoundRank = function(round_key) 
{
    return this.roundRank.get(round_key);
};

ProfessionLeague.prototype.hasRoundRank = function(round_key) 
{
    return this.roundRank.has(round_key);
};

//*************************************辅助函数*************************************************** */

//*************************************第1-38轮次调用函数(还有大量重复代码，后续必须考虑优化)***************************************** */
ProfessionLeague.prototype.commonCtrl = function(round, localRound, message)
{
    let check = this.league.checkRound(round, localRound, message);
    if (!check)
    { 
        logger.error(message, ": round error! currRound, needLocalRound", round, localRound);
      return false;
    }
    //检查战斗服返回时间是否超时
    this.battleTimeOutResetPrivateConfig();
    let ret = this.checkRunning(round);
    if (ret)
    {
      ret = this.canRunNextAction(round);
      //logger.info(message, ": canRunNextAction ", ret);
      if (ret) 
      {
        return true;
      }else 
      {
        return false;
      }
    }

    //logger.info("commonCtrl: commProcessFlow ", message, round);
    this.commProcessFlow(round);
    return false;
};

ProfessionLeague.prototype.PFRound_1 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup1;
    return this.commonCtrl(round, localRound, "PFRound_1");
};

ProfessionLeague.prototype.PFRound_2 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup2;
    return this.commonCtrl(round, localRound, "PFRound_2");
};

ProfessionLeague.prototype.PFRound_3 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup3;
    return this.commonCtrl(round, localRound, "PFRound_3");
};

ProfessionLeague.prototype.PFRound_4 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup4;
    return this.commonCtrl(round, localRound, "PFRound_4");
};

ProfessionLeague.prototype.PFRound_5 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup5;
    return this.commonCtrl(round, localRound, "PFRound_5");
};

ProfessionLeague.prototype.PFRound_6 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup6;
    return this.commonCtrl(round, localRound, "PFRound_6");
};

ProfessionLeague.prototype.PFRound_7 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup7;
    return this.commonCtrl(round, localRound, "PFRound_7");
};

ProfessionLeague.prototype.PFRound_8 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup8;
    return this.commonCtrl(round, localRound, "PFRound_8");
};

ProfessionLeague.prototype.PFRound_9 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup9;
    return this.commonCtrl(round, localRound, "PFRound_9");
};

ProfessionLeague.prototype.PFRound_10 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup10;
    return this.commonCtrl(round, localRound, "PFRound_10");
};

ProfessionLeague.prototype.PFRound_11 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup11;
    return this.commonCtrl(round, localRound, "PFRound_11");
};

ProfessionLeague.prototype.PFRound_12 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup12;
    return this.commonCtrl(round, localRound, "PFRound_12");
};

ProfessionLeague.prototype.PFRound_13 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup13;
    return this.commonCtrl(round, localRound, "PFRound_13");
};

ProfessionLeague.prototype.PFRound_14 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup14;
    return this.commonCtrl(round, localRound, "PFRound_14");
};

ProfessionLeague.prototype.PFRound_15 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup15;
    return this.commonCtrl(round, localRound, "PFRound_15");
};

ProfessionLeague.prototype.PFRound_16 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup16;
    return this.commonCtrl(round, localRound, "PFRound_16");
};

ProfessionLeague.prototype.PFRound_17 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup17;
    return this.commonCtrl(round, localRound, "PFRound_17");
};

ProfessionLeague.prototype.PFRound_18 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup18;
    return this.commonCtrl(round, localRound, "PFRound_18");
};

ProfessionLeague.prototype.PFRound_19 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup19;
    return this.commonCtrl(round, localRound, "PFRound_19");
};

ProfessionLeague.prototype.PFRound_20 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup20;
    return this.commonCtrl(round, localRound, "PFRound_20");
};

ProfessionLeague.prototype.PFRound_21 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup21;
    return this.commonCtrl(round, localRound, "PFRound_21");
};

ProfessionLeague.prototype.PFRound_22 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup22;
    return this.commonCtrl(round, localRound, "PFRound_22");
};

ProfessionLeague.prototype.PFRound_23 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup23;
    return this.commonCtrl(round, localRound, "PFRound_23");
};

ProfessionLeague.prototype.PFRound_24 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup24;
    return this.commonCtrl(round, localRound, "PFRound_24");
};

ProfessionLeague.prototype.PFRound_25 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup25;
    return this.commonCtrl(round, localRound, "PFRound_25");
};

ProfessionLeague.prototype.PFRound_26 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup26;
    return this.commonCtrl(round, localRound, "PFRound_26");
};

ProfessionLeague.prototype.PFRound_27 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup27;
    return this.commonCtrl(round, localRound, "PFRound_27");
};

ProfessionLeague.prototype.PFRound_28 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup28;
    return this.commonCtrl(round, localRound, "PFRound_28");
};

ProfessionLeague.prototype.PFRound_29 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup29;
    return this.commonCtrl(round, localRound, "PFRound_29");
};

ProfessionLeague.prototype.PFRound_30 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup30;
    return this.commonCtrl(round, localRound, "PFRound_30");
};

ProfessionLeague.prototype.PFRound_31 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup31;
    return this.commonCtrl(round, localRound, "PFRound_31");
};

ProfessionLeague.prototype.PFRound_32 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup32;
    return this.commonCtrl(round, localRound, "PFRound_32");
};

ProfessionLeague.prototype.PFRound_33 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup33;
    return this.commonCtrl(round, localRound, "PFRound_33");
};

ProfessionLeague.prototype.PFRound_34 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup34;
    return this.commonCtrl(round, localRound, "PFRound_34");
};

ProfessionLeague.prototype.PFRound_35 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup35;
    return this.commonCtrl(round, localRound, "PFRound_35");
};

ProfessionLeague.prototype.PFRound_36 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup36;
    return this.commonCtrl(round, localRound, "PFRound_36");
};

ProfessionLeague.prototype.PFRound_37 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup37;
    return this.commonCtrl(round, localRound, "PFRound_37");
};

ProfessionLeague.prototype.PFRound_38 = function()
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.ProfessionGroup38;
    return this.commonCtrl(round, localRound, "PFRound_38");
};

ProfessionLeague.prototype.showFinalResult = function()
{
    return true;
};

ProfessionLeague.prototype.getTypeId = function() 
{
    return this.typeId
};

ProfessionLeague.prototype.getTypeIdByGroupId = function(groupId)
{
  let typeId = 0;
  switch(groupId)
  {
    case 1: typeId = commonEnum.LEAGUE_TYPE_ID.AMATEUR; break; 
    case 2: typeId = commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND; break; 
    case 3: typeId = commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST; break; 
    case 4: typeId = commonEnum.LEAGUE_TYPE_ID.CHAMPIONS; break; 
    case 5: typeId = commonEnum.LEAGUE_TYPE_ID.SUPER; break;
      default: typeId = commonEnum.LEAGUE_TYPE_ID.PREPARE; break;
  }

  return typeId;
};

//战斗服返回超时重新重设状态
ProfessionLeague.prototype.battleTimeOutResetPrivateConfig = function()
{
    let atConfig;
    let k;
    //得到当前轮的状态
    for(let i in this.privateConfig)
    {
        if(this.privateConfig[i].round === this.currRound)
        {
            k = i;
            atConfig = this.privateConfig[i];
        }
    }
    //logger.error("战斗服返回超时重新重设状态---重设前", this.awaitTime, atConfig);
    //进入等待时间，可以开始计时
    if(atConfig.isRunning === true && atConfig.canAction === false)
    {
        this.awaitTime += 1;//因为updateFsmState函数是一秒一次所以每次加1为1秒
    }
    //超时了重置
    if((this.awaitTime >= this.MAXTimeOut) && (this.privateConfig[k].isRunning === true && this.privateConfig[k].canAction === false))
    {
        logger.warn("battle TimeOut PrivateConfig:", this.typeId, this.awaitTime, this.privateConfig[k]);
        this.privateConfig[k].isRunning = false;
        this.awaitTime = 0;
        logger.warn("battle TimeOut ResetPrivateConfig:", this.typeId, this.awaitTime, this.privateConfig[k]);
        //logger.error("战斗服返回超时重新重设状态---重设后", this.awaitTime, k, this.privateConfig[k]);
    }
};