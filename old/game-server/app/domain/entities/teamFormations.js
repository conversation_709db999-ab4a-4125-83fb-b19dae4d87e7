/**
 * Created by scott on 2019/4/4.
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var dataApi = require('../../util/dataApi');
var utils = require('../../util/utils');
var commonEnum = require('../../../../shared/enum');
var Calc = require('../../util/calc');
var Code = require('../../../../shared/code');
var TimeUtils = require('../../util/timeUtils');
var debugConfig = require('../../../config/debugConfig');

var TeamFormations = function(player) {
    this.player = player;
    this.uid = player.playerId;                //玩家UID
    this.allTeamFormations = new Map();        //所有阵容
    this.currTeamFormationId =  "";            //当前使用阵容
    this.leagueTeamFormationId = "";           //联赛阵容Id
    this.warOfFaithTeamFormationId = "";       //信仰之战阵容id
    this.allTactics = new Map();               //所有进攻战术
    this.allDefTactics = {};                   //所有防守战术 (新增)
    this.allFormations = initFormationList();  //所有阵型
    this.useTeamFormationId = 1;               //默认使用阵容1的队伍
    this.fixId = 0;
};

util.inherits(TeamFormations, EventEmitter);
module.exports = TeamFormations;
//openId: '5c31be3fe538302221f87ed9a8c9e99e'
//name: 'Ydgdhrhgdd'
TeamFormations.prototype.initByDB = function(doc) {
    this.uid = doc.uid;
    this.allTeamFormations = utils.toMap(doc.teamFormations) || new Map();
    this.currTeamFormationId = doc.currTeamFormationId || "";
    this.leagueTeamFormationId = doc.leagueTeamFormationId || "";
    this.warOfFaithTeamFormationId = doc.warOfFaithTeamFormationId || "";
    this.allTactics = this._toMap(doc.allTactics, doc.uid) || new Map();
    this.allDefTactics = doc.allDefTactics || this.initDefTactics();
    this.allFormations = doc.allFormations || initFormationList();
    this.fixId = doc.fixId || 0;
};

TeamFormations.prototype.fixRevisionTeam = function() {
    if(this.fixId === 1) {
        return;
    }

    this.fixId = 1;
    for (let [uid, team] of this.allTeamFormations) {
        if(team.isInitFormation === 1) {
            if(!team.TeamId) {
                team.TeamId = 1;
            }
            if(!team.TeamType) {
                team.TeamType = 1;
            }
        }else {
            if(!team.TeamId || !team.TeamType) {
                this.allTeamFormations.delete(uid);
            }
        }
    }
    this.player.saveTeamFormation();
}

//属性修复调用口详见 player.checkFix
TeamFormations.prototype.fixAttr = function()
{
    let isHasGroundAtk = false;
    let isHasGroundDef = false;
    let isCurrTeamIdWrong = true;
    let toFixCurrId = "";
    for (let [uid, team] of this.allTeamFormations)
    {
        //let team = this.getOneTeamFormation(uid);
        //数据兼容
        if(!team.Type) {
            team.Type = commonEnum.FORMATION_TYPE.COMMON;
        }else if(team.Type === commonEnum.FORMATION_TYPE.GROUND_ATK) {
            isHasGroundAtk = true;
        }else if(team.Type === commonEnum.FORMATION_TYPE.GROUND_DEF) {
            isHasGroundDef = true;
        }

        //数据恢复
        if(!team.UseDefTactics) {
            team.UseDefTactics = 1101;
        }
        //logger.debug("fixAttr getOneTeamFormation team: ", team);
        //教练列表
        if(typeof(team.Trainers) === "undefined") {
            team.Trainers = this.initTrainers();
        }

        if(this.currTeamFormationId === uid && team.Type === commonEnum.FORMATION_TYPE.COMMON) {
            isCurrTeamIdWrong = false;
        }
        if(team.Type === commonEnum.FORMATION_TYPE.COMMON && !toFixCurrId) {
            toFixCurrId = uid;
        }
        if(team.isInitFormation === 1) {
            if(!team.TeamId) {
                team.TeamId = 1;
            }
            if(!team.TeamType) {
                team.TeamType = 1;
            }
        }

        this.checkTeamHeroComplete(uid, team.PositionToHerosObject);
        this.reCalcTeamFormationAttrByUid(uid);

        //修复个人球场全员人数少的问题 (这里在onlineService加载的时候会出现问题,不要打开)
        //this.fixNotEnoughTeamFormation(uid);
    }

    //修复当前阵容id数据
    if(isCurrTeamIdWrong) {
        this.currTeamFormationId = toFixCurrId;
    }
    logger.debug("fixAttr teamFormation currTeamFormationId: ", isCurrTeamIdWrong, toFixCurrId);

    //修复战术
    this.checkAllTactics();

    if(!debugConfig.groundMatchHide) {
        //如果没有球场争夺战数据初始化
        if(!this.player.footballGround.groundMatch || !this.player.footballGround.groundMatch.fieldList) {
            this.player.footballGround.initGroundMatch();
        }
        //初始化默认的球场争夺战阵容
        if(!isHasGroundAtk) {
            if(this.initGroundFormation(commonEnum.FORMATION_TYPE.GROUND_ATK)) {
                let teamList = this.getTeamFormationListByType(commonEnum.FORMATION_TYPE.GROUND_ATK);
                //设置默认进攻队伍
                if(this.player.footballGround.groundMatch && this.player.footballGround.groundMatch.occupyFieldList) {
                    this.player.footballGround.groundMatch.occupyFieldList[0].teamUid = teamList[0].Uid;
                }
            }
        }
        if(!isHasGroundDef) {
            if(this.initGroundFormation(commonEnum.FORMATION_TYPE.GROUND_DEF)) {
                let teamList = this.getTeamFormationListByType(commonEnum.FORMATION_TYPE.GROUND_DEF);
                //logger.debug("teamList 111:", teamList);
                //设置默认防守队伍
                if(this.player.footballGround.groundMatch && this.player.footballGround.groundMatch.fieldList) {
                    this.player.footballGround.groundMatch.fieldList[0].teamUid = teamList[0].Uid;
                }
            }
        }
    }

    //修复球员10人的数据



    //logger.debug("fixAttr allTeamFormation: ", this.allTeamFormations);

};

TeamFormations.prototype.initTrainers = function () {
    let trainerList = [];
    for(let i = 0; i < 6; ++i) {
        let info = {
            type: i + 1,                //类型  1主教练 2进攻教练 3防守教练  4-5-6助理
            uid: "",                    //教练uid
            status: 1,                  // 1锁定  2可添加教练 3已有人
        };
        trainerList.push(info);
    }
    return trainerList;
};

TeamFormations.prototype.checkTeamHeroComplete = function(teamFormationUid, objPos) 
{
    let config = dataApi.allData.data["CreateBaller"];
    if (!config)
    {
        logger.error("checkTeamHeroComplete: not config", teamFormationUid);
        return;
    }

    for(let key in objPos) {
        for(let i = 0; i < objPos[key].length; ++i) {
            let heroUid = objPos[key][i];
            let hero = this.player.getOneHero(heroUid);
            if(!hero) //没有这个英雄，或者被删除掉了
            {
                logger.info("checkTeamHeroComplete: not exist heroUid", heroUid);
                for (let id in config) {
                    const cfg = config[id];
                    var resId = cfg.BallerID;
                    if (commonEnum.CREATE_INIT_BALLER_MODE.INIT_BALLER === cfg.Mode) 
                    {
						let isExist = this.player.checkHeroResId(resId);
						if (isExist) //存在的话，检查是否在场上
						{
                            let newHeroUid = this.player.heros.getHeroUidByResId(resId);
                            if (!newHeroUid)
                            {
                                logger.error("checkTeamHeroComplete.getHeroUidByResId: data exception heroUid", resId);
                                continue;
                            }
                            //再检查newHeroUid是否在当前阵容中
                            let isExistTeam = this._checkHeroInTeamFormation(teamFormationUid, newHeroUid);
                            //存在该阵容中的球员，直接跳过
                            if (1 === isExistTeam) 
                            {
                                logger.error("checkTeamHeroComplete._checkHeroInTeamFormation: current teamFormation exist heroUid", resId, newHeroUid);
                                continue;
                            }
                            //不存在添加到当前阵容中,添加到当前阵容中
                            objPos[key][i] = newHeroUid;
                            logger.error("checkTeamHeroComplete: move newHeroUid", teamFormationUid, resId, newHeroUid);
                            break;
						}
						let heroObj = this.player.heros.addHero(resId, 1);
						if (heroObj)
						{
                            let newHeroUid = heroObj.Uid;
                            objPos[key][i] = newHeroUid;
                            logger.error("checkTeamHeroComplete: new heroUid", teamFormationUid, resId, newHeroUid); 
                            break;
						}
                    }
                }
            }
        }
    }
};

TeamFormations.prototype._toMap = function(arr, uid) {
    var map =  new Map();
    if (!arr)
    {
        return map;
    }
    for (var i in arr)
    {
       const object =  arr[i];
       map.set(uid, object);
    }
    return map;
};

//初始化阵型列表
function initFormationList() {
    let teamFormationCfg = dataApi.allData.data["TeamFormation"];
    let formationList = [];
    let index = 0;
    for (let id in teamFormationCfg) {
        if (teamFormationCfg[id].Level === 1 && teamFormationCfg[id].NeedLevel === 0) {
            formationList[index] = {};
            formationList[index].ResId = teamFormationCfg[id].ID;
            formationList[index].Name = teamFormationCfg[id].Name;
            index ++;
        }
    }
    return formationList;
};

TeamFormations.prototype.initByConfig = function() 
{
};

TeamFormations.prototype.toJSONforDB = function() {
    var teamFormations = {
        uid: this.uid,
        teamFormations: utils.toArray(this.allTeamFormations),
        currTeamFormationId: this.currTeamFormationId,
        leagueTeamFormationId: this.leagueTeamFormationId,
        warOfFaithTeamFormationId: this.warOfFaithTeamFormationId,
        allTactics: utils.toArray(this.allTactics),
        allDefTactics: this.allDefTactics,
        allFormations: this.allFormations,
        fixId: this.fixId
    };
    // logger.info("TeamFormations.prototype.toJSONforDB ******************", this.uid, teamFormations);
    return teamFormations;
};

TeamFormations.prototype.toJSONforClient = function(serverType) {
    return this.makeClientTeamFormationList(serverType);
};

TeamFormations.prototype.checkAllTactics = function() {
    let allTactics = this.allTactics.get(this.uid);
    if(!allTactics) {
        let obj = utils.clone(commonEnum.TEAM_TACTICS_LIST);
        this.allTactics.set(this.uid, obj);
    }

    //防守战术数据初始化
    if(!this.allDefTactics || !this.allDefTactics["11"]) {
        this.initDefTactics();
    }

    for(let [uid, v] of this.allTeamFormations) {
        let useTactics = v.UseTactics;
        let num = Math.floor((useTactics % 1000) / 100);  //取百位数
        if(allTactics[num] < useTactics) {
            allTactics[num] = useTactics;
        }
    }
};

TeamFormations.prototype.initDefTactics = function() {
    if(!this.allDefTactics || !this.allDefTactics["11"]) {
        this.allDefTactics = utils.clone(commonEnum.TEAM_DEF_TACTICS_LIST);
    }
};

TeamFormations.prototype.initGroundFormation = function(type) {
    let defaultFormation = this.getOneTeamFormation(this.currTeamFormationId);
    if(!defaultFormation) {
        return false;
    }
    let addFormation = this.addTeamFormation(defaultFormation.ResId, type);
    addFormation["Name"] = "队伍一";
    if(type === commonEnum.FORMATION_TYPE.GROUND_ATK) {
        addFormation["Name"] = "进攻队伍一";
    }else if(type === commonEnum.FORMATION_TYPE.GROUND_DEF){
        addFormation["Name"] = "防守队伍一";
    }
    addFormation["Attack"] = defaultFormation["Attack"];
    addFormation["Defend"] = defaultFormation["Defend"];
    addFormation["ActualStrength"] = defaultFormation["ActualStrength"];
    addFormation["isInitFormation"] = defaultFormation["isInitFormation"];
    addFormation["PositionToHerosObject"] = utils.deepClone(defaultFormation["PositionToHerosObject"]);
    //logger.debug("deepClone 111", addFormation["PositionToHerosObject"], defaultFormation["PositionToHerosObject"]);
    addFormation["UseTactics"] = defaultFormation["UseTactics"];
    addFormation["UseDefTactics"] = defaultFormation["UseDefTactics"];
    addFormation["FreeKickPlayer"] = defaultFormation["FreeKickPlayer"];
    addFormation["PenaltiesPlayer"] = defaultFormation["PenaltiesPlayer"];
    addFormation["CornerKickPlayer"] = defaultFormation["CornerKickPlayer"];
    addFormation["Trainers"] = utils.deepClone(defaultFormation["Trainers"]);
    return true;
    //logger.debug("deepClone 222", addFormation["Trainers"], defaultFormation["Trainers"]);
};

TeamFormations.prototype.toJSONforLeague = function()
{
    let currTeamFormationId =  this.leagueTeamFormationId;
    if ("" === currTeamFormationId )
    {
        currTeamFormationId = this.currTeamFormationId;
        this.setLeagueTeamFormationId(this.currTeamFormationId);
    }

    let allTactics = this.allTactics.get(this.uid);
    if(!allTactics) {
        let obj = utils.clone(commonEnum.TEAM_TACTICS_LIST);
        this.allTactics.set(this.uid, obj);
    }
    /*
    let index = 1;
    for(let i in allTactics) {
        if(!allTactics[i]) {
            let num = index * 100 + 1;
            allTactics[i] = num;
        }
        index++;
    }
    */
    //处理没有当前阵容战术的账号
    for(let [uid, v] of this.allTeamFormations) 
    {
        if (uid === currTeamFormationId)
        {
            let oneTeam = this.getOneTeamFormation(currTeamFormationId);
            if (!oneTeam.UseTactics  || oneTeam.UseTactics <= 0)
            {
                oneTeam.UseTactics= 101;
                oneTeam.UseDefTactics = 1101;
            }
            break;
        }
    }

    var teamFormations = {
        uid: this.uid,
        teamFormations: utils.toArray(this.allTeamFormations),
        currTeamFormationId: currTeamFormationId,
        allTactics: utils.toArray(this.allTactics),
        allFormations: this.allFormations
    };

    // logger.info("TeamFormations.prototype.toJSONforDB ******************", this.uid, teamFormations);
    return teamFormations;
};

TeamFormations.prototype.toJSONforWarOfFaith = function()
{
    let currTeamFormationId =  this.warOfFaithTeamFormationId;
    if ("" === currTeamFormationId )
    {
        currTeamFormationId = this.currTeamFormationId;
        this.setWarOfFaithTeamFormationId(this.currTeamFormationId);
    }

    let allTactics = this.allTactics.get(this.uid);
    if(!allTactics) {
        let obj = utils.clone(commonEnum.TEAM_TACTICS_LIST);
        this.allTactics.set(this.uid, obj);
    }

    //处理没有当前阵容战术的账号
    for(let [uid, v] of this.allTeamFormations)
    {
        if (uid === currTeamFormationId)
        {
            let oneTeam = this.getOneTeamFormation(currTeamFormationId);
            if (!oneTeam.UseTactics  || oneTeam.UseTactics <= 0)
            {
                oneTeam.UseTactics= 101;
                oneTeam.UseDefTactics = 1101;
            }
            break;
        }
    }

    var teamFormations = {
        uid: this.uid,
        teamFormations: utils.toArray(this.allTeamFormations),
        currTeamFormationId: currTeamFormationId,
        allTactics: utils.toArray(this.allTactics),
        allFormations: this.allFormations
    };

    // logger.info("TeamFormations.prototype.toJSONforWarOfFaith ******************", this.uid, teamFormations);
    return teamFormations;
};

//**********************************************************************************************************************
/** //添加一个阵容
 * @param  {} resId 阵容resId
 * @returns {} newHero 新阵容对象
 */
TeamFormations.prototype.addTeamFormation = function(resId, type) {
    if (!type) {
        type = commonEnum.FORMATION_TYPE.COMMON;
    }
    var object = this.newTeamFormation(resId, type);
    var uid = object.Uid;
    this.allTeamFormations.set(uid, object);
    //logger.info("addTeamFormation", this.getOneTeamFormation(uid));
    return this.getOneTeamFormation(uid);
};

TeamFormations.prototype.getTeamCountByType = function(type)
{
    //logger.debug("getCurrTeamCount allTeamFormations:", this.allTeamFormations);
    let num = 0;
    for(let [k,v] of this.allTeamFormations) {
        if(v.Type === type) {
            num++;
        }
    }
    return num;
};

//获取阵容队伍数量
TeamFormations.prototype.getTeamCountByTeamId = function(teamId) {
    let num = 0;
    for(let [k,v] of this.allTeamFormations) {
        if(v.teamId === teamId) {
            num++;
        }
    }
    return num;
};

//获取阵型列表
TeamFormations.prototype.getFormationList = function() {
    let teamFormationCfg = dataApi.allData.data["TeamFormation"];
    let nameFormation = [];
    for(let i in this.allFormations){
        nameFormation.push( this.allFormations[i].Name);      
    }
    let index = this.allFormations.length;
    for (let id in teamFormationCfg) {
        if (this.player.level >= teamFormationCfg[id].NeedLevel && teamFormationCfg[id].Level === 1) {
            for(let i in nameFormation){
                if(nameFormation.indexOf(teamFormationCfg[id].Name) === -1){
                    this.allFormations[index] = {};
                    this.allFormations[index].ResId = teamFormationCfg[id].ID;
                    this.allFormations[index].Name = teamFormationCfg[id].Name;
                    nameFormation.push(teamFormationCfg[id].Name);
                    index++;
                    break;
                }
            }
        }  
    }
    return this.allFormations;
};

//获取定位球球员列表         阵容uid
TeamFormations.prototype.getFormationPlaceKickList = function(uid) {
    //logger.error("定位球球员列表", uid);
    //获取当前阵容
    let formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("getFormationPlaceKickList: not formation!" , uid);
        return Code.FAIL;
    }
    //先获取当前阵容上场的11人
    let heroList = [];
    let tmp = formation.PositionToHerosObject;
    for(let i in tmp)
    {
        for(let k in tmp[i])
        {
            heroList.push(tmp[i][k]);
        }
    }
    if(typeof(formation.FreeKickPlayer) === "undefined")
    {
        formation.FreeKickPlayer = "";
    }
    if(typeof(formation.PenaltiesPlayer) === "undefined")
    {
        formation.PenaltiesPlayer = "";
    }
    if(typeof(formation.CornerKickPlayer) === "undefined")
    {
        formation.CornerKickPlayer = "";
    }
    let FreeKickPlayer = formation.FreeKickPlayer;
    let PenaltiesPlayer = formation.PenaltiesPlayer;
    let CornerKickPlayer = formation.CornerKickPlayer;
    //返回当前阵容上场11人的实力和初始实力，任意球和任意球初始实力
    return {FreeKick: FreeKickPlayer, Penalties: PenaltiesPlayer, CornerKick: CornerKickPlayer};
};
//获取任意球，点球，角球推荐球员  [heroUid, ....]
TeamFormations.prototype.getFormationPlaceKickAttribute = function(heroList) {
    // logger.error("获取任意球，点球，角球推荐球员");
    let FreeKickProposal;//推荐的任意球球员
    let PenaltiesProposal;//推荐的点球球员
    let CornerKickProposal;//推荐的角球球员
    //let heroObjList = [];
    for(let i in heroList)
    {
        let heroUid = heroList[i];
        let hero = this.player.getOneHero(heroUid);
        let tmp = {
            uid: hero.Uid,
            ResId: hero.ResID,
            BallerRating: Math.trunc(hero.twoLevelAttr.BallerRating.Cur),            //当前实力
            BallerRatingInit: Math.trunc(hero.twoLevelAttr.BallerRating.Init),       //初始实力
            FreeKick: Math.trunc(hero.oneLevelAttr.FreeKick.Cur),                    //当前任意球
            FreeKickBase: Math.trunc(hero.oneLevelAttr.FreeKick.Base),               //初始任意球
            Penalties: Math.trunc(hero.oneLevelAttr.Penalties.Cur),                  //当前点球
            PenaltiesBase: Math.trunc(hero.oneLevelAttr.Penalties.Base),             //初始点球
            CornerKick: Math.trunc(hero.oneLevelAttr.CornerKick.Cur),                //当前角球
            CornerKickBase: Math.trunc(hero.oneLevelAttr.CornerKick.Base),           //初始角球
            LongPassing: Math.trunc(hero.oneLevelAttr.LongPassing.Cur),              //当前长传
            LongPassingBase: Math.trunc(hero.oneLevelAttr.LongPassing.Base),         //初始长传
        }
        //logger.error("获取定位球属性：：：：：：", i, tmp);
        //heroObjList.push(utils.deepCopy(tmp));
        //得到任意球推荐球员
        if(!FreeKickProposal || (FreeKickProposal.BallerRating + FreeKickProposal.FreeKick) < (tmp.BallerRating + tmp.FreeKick))
        {
            FreeKickProposal = utils.deepCopy(tmp);
        }
        //得到点球球推荐球员
        if(!PenaltiesProposal || (PenaltiesProposal.BallerRating + PenaltiesProposal.Penalties) < (tmp.BallerRating + tmp.Penalties))
        {
            PenaltiesProposal = utils.deepCopy(tmp);
        }
        //得到角球推荐球员
        if(!CornerKickProposal || (CornerKickProposal.BallerRating + CornerKickProposal.CornerKick + CornerKickProposal.LongPassing) < (tmp.BallerRating + tmp.CornerKick + tmp.LongPassing))
        {
            CornerKickProposal = utils.deepCopy(tmp);
        }
    }
    let result = {FreeKick: FreeKickProposal, Penalties: PenaltiesProposal, CornerKick: CornerKickProposal};
    //logger.error("得到的结果为---------", result);
    return result;
};
//设定阵容定位球球员         FormationUid：阵容uid， footballerUid：球员uid， duty：位置 1 任意球 2 点球 3 角球
TeamFormations.prototype.setFormationPlaceKickDuty = function(FormationUid, footballerUid, duty) {
    // logger.error("设定阵容定位球球员");
    //获取当前阵容
    let formation = this.getOneTeamFormation(FormationUid);
    if (!formation) {
        logger.error("getFormationPlaceKickList: not formation!" , FormationUid);
        return {code: Code.FAIL, footballerUid: "", duty: 0};
    }
    let falg = false;
    let code = Code.FAIL;
    //检查球员是否在当前阵容中
    for(let i in formation.PositionToHerosObject)
    {
        for(let k in formation.PositionToHerosObject[i])
        {
            if(formation.PositionToHerosObject[i][k] === footballerUid)
            {
                falg = true;
                break;
            }
        }
    }
    if(falg)
    {
        switch (duty) {
            case commonEnum.SET_FORMATION_MODE.FreeKick://任意球
                formation.FreeKickPlayer = footballerUid;
                code = Code.OK;
                break;
            case commonEnum.SET_FORMATION_MODE.Penalties://点球
                formation.PenaltiesPlayer = footballerUid;
                code = Code.OK;
                break;
            case commonEnum.SET_FORMATION_MODE.CornerKick://角球
                formation.CornerKickPlayer = footballerUid;
                code = Code.OK;
                break;
            default:
                break;
        }
    }
    let result = {
        code: code,
        footballerUid: footballerUid,
        duty: duty
    }
  return result;
};
/** 阵型升级
 * @param  resId 阵阵型resId
 * @returns resId 升级后的阵型resId
 */
TeamFormations.prototype.upgradeFormation = function(resId) {
    let ret = {code:Code.FAIL, resId:0};
    if(!resId){
        logger.error("upgradeFormation resId or itemId is fail",resId);
        return ret;
    }
    let destId = resId + 1;
    let src = dataApi.allData.data["TeamFormation"][resId];  //原阵型
    let dest = dataApi.allData.data["TeamFormation"][destId]; //目标阵型
    if(!dest || !src){
        logger.error("upgradeFormation config is not found", dest);
        return ret;
    }
    //获取升级所需物品数量
    let itemCount = this.player.item.getItemCountByResId(src.ItemID);
    let itemUid = this.player.item.getItemUidByResId(src.ItemID);
    if(!itemUid){
        logger.error("upgradeFormation itemUid is fail", itemUid);
    }
    if (itemCount < src.ItemNumber) {
        logger.error("upgradeFormation itemCount is fail", itemCount,src.ItemNumber);
        return ret;
    }

    //检查阵型
    let isHave = false;
    for(let i in this.allFormations) {
        if(resId === this.allFormations[i].ResId){
            isHave = true;
            break;
        }
    }

    //没有这个阵型
    if(!isHave) {
        return ret;
    }

    //删除物品
    this.player.bag.removeItem(itemUid,src.ItemNumber);
    // this.player.item.delItem(itemUid, src.ItemNumber);
    //升级
    for(let i in this.allFormations) {
        if(resId === this.allFormations[i].ResId){
            this.allFormations[i].ResId = destId;
            break;
        }
    }
    //更新阵容所用的阵型
    let formationList = this.getTeamFormationList();
    for (let i = 0; i < formationList.length; ++i){
        if (formationList[i].ResId === resId){    
            formationList[i].ResId = destId;
            continue;
        }
    }

    //重新计算所有球队属性
    this.recalcAllTeamFormationAttr();
    //触发任务
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.TWENTY_SEVEN);

    ret.code = Code.OK;
    ret.resId = destId;
    return ret;
};
/** 阵型重置
 * @param  resId 阵阵型resId
 * @returns resId 重置后的阵型resId
 */
TeamFormations.prototype.resetGradeFormation = function(resId) {
    let ret = {code:Code.FAIL, resId:0};
    if(!resId){
        logger.error("resetGradeFormation resId or itemId is fail",resId);
        return ret;
    }

    //检查重置阵型是否在使用中
    for(let [k, v] of this.allTeamFormations) {
        if(resId === v.ResId){
            ret.code = Code.RESETFORMATION_FAIL;
            return ret;
        }
    }

    let config = dataApi.allData.data["TeamFormation"];
    let src = config[resId];  //原阵型
    let TeamFormationList = [];
    let itemMap = new Map();
    for(let i in config)
    {
        if(config[i].Name === src.Name && config[i].Level < src.Level)
        {
            //返还的物品id和数量
            if(itemMap.has(config[i].ItemID))
            {
                let num = itemMap.get(config[i].ItemID);
                num += config[i].ItemNumber;
                itemMap.set(config[i].ItemID, num);
            }
            else
            {
                itemMap.set(config[i].ItemID, config[i].ItemNumber);
            }
            TeamFormationList.push(config[i]);
        }
    }
    let destId = TeamFormationList[0].ID;
    let dest = config[destId]; //目标阵型
    //logger.error("原阵型名字：%s, 原阵型等级：%d, 目标阵型名字：%s, 目标阵型等级：%d", src.Name, src.Level, dest.Name, dest.Level);

    if(!dest || !src){
        logger.error("resetGradeFormation config is not found", dest);
        return ret;
    }

    //扣除阵型重置卡          阵型重置卡物品Id:10501
    let itemUid = this.player.item.getItemUidByResId(10501);
    let itemCount = this.player.item.getItemCountByResId(10501);
    if(!itemUid){
        logger.error("resetGradeFormation itemUid is fail", itemUid);
    }
    if (itemCount <= 0) {
        logger.error("resetGradeFormation itemCount is fail", itemCount);
        return ret;
    }
    let code = this.player.bag.removeItem(itemUid, 1);
    if(code !== Code.OK)
    {
        logger.error("resetGradeFormation removeItem fail", code);
        return ret;
    }

   //返还阵型升级卡
   for(let [k,v] of itemMap)
   {
       this.player.bag.addItem(k, v);
   }
    //重置阵型
    for(let i in this.allFormations) {
        if(resId === this.allFormations[i].ResId){
            this.allFormations[i].ResId = destId;
            break;
        }
    }

    // //更新阵容所用的阵型  不能重置阵容使用阵型
    // let formationList = this.getTeamFormationList();
    // for (let i = 0; i < formationList.length; ++i){
    //     if (formationList[i].ResId === resId){
    //         formationList[i].ResId = destId;
    //         continue;
    //     }
    // }

    //重新计算所有球队属性
    this.recalcAllTeamFormationAttr();

    ret.code = Code.OK;
    ret.resId = destId;
    return ret;
};
TeamFormations.prototype.isHaveSameHero = function(arr, uid) {
    let isHave = false;
    for(let i in arr) {
        let srcHero = this.player.getOneHero(arr[i]);
        let destHero = this.player.getOneHero(uid);
        let srcSoleId = dataApi.allData.data["Footballer"][srcHero.ResID].SoleId;
        let destSoleId = dataApi.allData.data["Footballer"][destHero.ResID].SoleId;
        if(srcSoleId === destSoleId) {
            let srcAct = Math.floor(srcHero["twoLevelAttr"]["BallerRating"].Cur);
            let destAct = Math.floor(destHero["twoLevelAttr"]["BallerRating"].Cur);
            if (destAct > srcAct) {
                arr[i] = uid;
            }
            isHave = true;
            break;
        }
    }

    //没有相同球员
    if(!isHave) {
        arr.push(uid);
    }
}

/**
 * 自动布局
 * @param  {} uid      阵容UID
 * @param  {} destId   目标阵型id
 */
TeamFormations.prototype.autoSetFormation = function(uid){ 
    // let uid = this.currTeamFormationId;
    let formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("autoSetFormation: not formation!" , uid);
        return Code.FAIL;
    }
    if(formation.Type !== commonEnum.FORMATION_TYPE.COMMON) {
        logger.error("autoSetFormation: formation.Type not common" , formation.Type);
        return Code.FAIL;
    }

    let teamId = formation.TeamId;
    let formationList = this.getTeamFormationListByIdAndUid(teamId, uid);
    //已上阵的球员
    let onFormationHeros = new Map();
    //未上阵球员
    let notOnFormationHeros = new Map();
    for(let i=0, len = formationList.length; i < len; i++) {
        for(let k in formationList[i].PositionToHerosObject) {
            for(let m = 0; m < formationList[i].PositionToHerosObject[k].length; m++) {
                onFormationHeros.set(formationList[i].PositionToHerosObject[k][m], k);
            }
        }
    }

    for(let [k,v] of this.player.heros.allheros) {
        //过滤上阵球员和退役球员
        if(!onFormationHeros.has(k) && v.isDie !== 1 && TimeUtils.futureDayInterval(v.leftTime) >= 1) {
            notOnFormationHeros.set(k, v);
        }
    }
    //人数不够
    if(notOnFormationHeros.size < 11) {
        logger.debug("autoSetFormation hero num is not enough. left num: ", notOnFormationHeros.size);
        return Code.NOT_ENOUGH_HERO_FAIL;
    }

    //球员自动布阵
    let tempHero = [];    //临时存储球员
    for(let [uid, v] of notOnFormationHeros) {
        let hero = this.player.getOneHero(uid);
        if (!hero) {
            continue;
        }
        if(tempHero.length < 1) {
            tempHero.push(uid);
            continue;
        }
        //检查是否有相同球员
        this.isHaveSameHero(tempHero, uid);
    }

    let copyObject = utils.deepCopy(formation.PositionToHerosObject);
    for (let key in copyObject){
        copyObject[key] = [];
    }

    let rateList = [];
    for(let uid in tempHero) {
        for (let key in copyObject) {
            let hero = this.player.getOneHero(uid);
            if (!hero) {
                continue;
            }
            let pos = {};
            pos.rate = this.calcHeroMatchRate(uid, key) * 10;
            pos.key = key;
            pos.uid = uid;
            pos.act = hero["twoLevelAttr"]["BallerRating"].Cur; //球员实力
            rateList.push(pos);
        }
    }

    let tmpheroList = [];
    let index = 1;
    //第一遍先检查是否有100%匹配实力按从大到小排
    rateList.sort(this.compare_func_1);
    let formationResId = formation.ResId;
    for(let id in copyObject) {
        let destNum = this.getNum(formationResId, index);
        for(let m = 0; m < destNum; ++m) {
            let delUid = "";
            for(let i = 0; i < rateList.length; ++i) {
                if(rateList[i].key == id && rateList[i].rate == 10) {
                    copyObject[id].push(rateList[i].uid);
                    delUid = rateList[i].uid;
                    //删除数组中的数据
                    let temIndex = tempHero.indexOf(delUid);
                    tempHero.splice(temIndex, 1);
                    break;
                }
            }
            delArrParm(rateList, delUid);
            if(copyObject[id].length >= destNum) {
                break;
            }
        } 
        index++;
    }

    //第二遍把剩余的按配表顺序排序
    index = 1;
    for (let id in copyObject){
        let destNum = this.getNum(formationResId, index);
        index++;
        //球员已满
        if(copyObject[id].length >= destNum) {
            continue;
        }
        let leftNum = destNum - copyObject[id].length;
        for(let m = 0; m < leftNum; m++) {
            let rateList = this.rateListSort_1(tempHero, id);
            if( rateList.length > 0) {
                let uid = rateList[0].uid;
                copyObject[id].push(uid);
                tmpheroList.push(uid);
                utils.removeElement(tempHero, uid);
            }
        }
    }
    let PlaceKickResult = this.getFormationPlaceKickAttribute(tmpheroList);
    this.player.newerTask.triggerTask(commonEnum.NEWER_TASK.AUTOMATIC_DISPOSAL);
    formation.PositionToHerosObject = copyObject;
    formation.FreeKickPlayer = PlaceKickResult.FreeKick.uid;     //任意球球员
    formation.PenaltiesPlayer = PlaceKickResult.Penalties.uid;    //点球球员
    formation.CornerKickPlayer = PlaceKickResult.CornerKick.uid;   //角球球员
    //重新计算球队属性
    this.player.heros.reCalcAllHeroAttr();
    return Code.OK;
}

/**
 * 阵型切换
 * @param  {} uid      阵容UID
 * @param  {} destId   目标阵型id
 */
TeamFormations.prototype.setFormation = function(uid, destId){
    let formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("setFormation: not formation!" , uid, destId);
        return Code.FAIL;
    }

    let tempHero = [];    //临时存储球员
    let copyObject = utils.deepCopy(formation.PositionToHerosObject);
    // logger.info("setFormation: copyObject", copyObject);
    for (let key in copyObject){
        for(let i = 0; i < copyObject[key].length; ++i) {
            tempHero.push(copyObject[key][i]);
        }
        copyObject[key] = [];
    }

    let rateList = [];
    let checkHeroFail = false; //检查是否有失败的球员,如果出现失败就放弃切换
    for(let j = 0; j < tempHero.length; ++j){
        for (let key in copyObject)
        {
            let heroUid = tempHero[j];
            let hero = this.player.getOneHero(heroUid);
            if (!hero)
            {
                logger.error("setFormation: not hero!!!", heroUid);
                checkHeroFail = true;
                break;
            }

            let pos = {};
            pos.rate = this.calcHeroMatchRate(tempHero[j], key) * 10;
            pos.key = key;
            pos.uid = tempHero[j];
            rateList.push(pos);
        }

        if (checkHeroFail)
        {
            break;
        }
    }

    if (checkHeroFail)
    {
        logger.error("setFormation: check hero fail! found dirty data!!!");
        return Code.FAIL;
    }

    let index = 1;
    //第一遍先检查是否有100%匹配的
    for(let id in copyObject) {
        let destNum = this.getNum(destId, index);
        for(let m = 0; m < destNum; ++m) {
            let delUid = "";
            for(let i = 0; i < rateList.length; ++i) {
                if(rateList[i].key == id && rateList[i].rate == 10) {
                    copyObject[id].push(rateList[i].uid);
                    delUid = rateList[i].uid;
                    //删除数组中的数据
                    let temIndex = tempHero.indexOf(delUid);
                    tempHero.splice(temIndex, 1);
                    break;
                }
            }
            delArrParm(rateList, delUid);
            if(copyObject[id].length >= destNum) {
                break;
            }
        } 
        index++;
    }

    //第二遍把剩余的按配表顺序排序
    index = 1;
    for (let id in copyObject){
        let destNum = this.getNum(destId, index);
        index++;
        //球员已满
        if(copyObject[id].length >= destNum) {
            continue;
        }
        let leftNum = destNum - copyObject[id].length;
        for(let m = 0; m < leftNum; m++ ) {
            let rateList = this.rateListSort(tempHero, id);
            if( rateList.length > 0) {
                let uid = rateList[0].uid;
                copyObject[id].push(uid);
                utils.removeElement(tempHero, uid);
            }
            
        }
    }

    formation.PositionToHerosObject = copyObject;
    // logger.info("setFormation: copyObject 2 ", copyObject);
    //改变阵型ID
    formation.ResId = destId;
    //重新计算球队属性
    this.reCalcTeamFormationAttrByUid(uid);
    // this.player.heros.reCalcAllHeroAttr();
    return Code.OK;
};

TeamFormations.prototype.rateListSort = function(tempHero, id) {
    let rateList = [];
    for(let j = 0; j < tempHero.length; ++j){
        let pos = {};
        pos.rate = this.calcHeroMatchRate(tempHero[j], id);
        pos.uid = tempHero[j];
        rateList.push(pos);
    }
    //排序
    rateList.sort(this.compare_func("rate"));
    return rateList;
}

TeamFormations.prototype.rateListSort_1 = function(tempHero, id) {
    let rateList = [];
    for(let j = 0; j < tempHero.length; ++j){
        let hero = this.player.getOneHero(tempHero[j]);
        let pos = {};
        pos.rate = this.calcHeroMatchRate(tempHero[j], id);
        pos.uid = tempHero[j];
        pos.act = hero["twoLevelAttr"]["BallerRating"].Cur; //球员实力
        rateList.push(pos);
    }
    //排序
    rateList.sort(this.compare_func_1);
    return rateList;
}

function delArrParm(rateList, delUid) {
    for(let i = 0; i < rateList.length; ++i) {
        if(rateList[i].uid == delUid) {
            //记录：倒序不需要i--   正序需要i--
            rateList.splice(i--, 1);
        }
    }
}

TeamFormations.prototype.compare_func = function(rate) {
    return (obj1, obj2) => {
        let val1 = obj1[rate];
        let val2 = obj2[rate];
        if(val1 !== val2) {
            if(val1 < val2) {
                return 1;
            } else if(val1 > val2) {
                return -1;
            }
        }
        return 0;
    }
}

TeamFormations.prototype.compare_func_1 = function(obj1, obj2) {
    let rate1 = obj1.rate;
    let rate2 = obj2.rate;
    let act1 = obj1.act;
    let act2 = obj2.act;
    //按匹配度
    if(rate1 !== rate2) {
        if(rate1 < rate2) {
            return 1;
        } else if(rate1 > rate2) {
            return -1;
        }
    }

    //按实力
    if(act1 !== act2) {
        if(act1 < act2) {
            return 1;
        } else if(act1 > act2) {
            return -1;
        }
    }
    return 0;
}

/** //删除一个阵容
 * @param  {} uid 阵容uid
 * 
 */
TeamFormations.prototype.delTeamFormation = function(uid) {
    let ret = {code:Code.FAIL, uid:"", initTeam: {}, formationId:""};
    let formationList = this.getTeamFormationList();
    let initId = this.getCurrTeamFormationId();
    let destUid = "";
    //如果删除的是当前阵容
    if(initId === uid){
        for(let i = 0; i < formationList.length; ++i) {
            //找到当前阵容，取其UID 
            if(formationList[i].Uid === uid) {
                destUid = formationList[i-1].Uid;
                break;
            }
        }
        if(destUid === ""){
            logger.error("destUid not have uid");
            return ret;
        }
        this.setCurrTeamFormationId(destUid);
    }
    let serverType = this.allTeamFormations.get(uid).Type;
    this.allTeamFormations.delete(uid);
    //删完重新排
    let nowFormationList = this.getTeamFormationListByType(serverType);
    for (let i = 0; i < nowFormationList.length; ++i){
        //如果是默认阵容名 0是默认 1不是
        if(nowFormationList[i].isInitName === 0 && nowFormationList[i].Name !== this.getNameByType(i, serverType)){
            nowFormationList[i].Name = this.getNameByType(i, serverType);
        }
    }
    //如果删除的是当前阵容
    if(destUid !== ""){
        ret.formationId = this.getCurrTeamFormationId();
    }
    //如果删除得是信仰之战的阵容
    if(uid === this.warOfFaithTeamFormationId) {
        this.warOfFaithTeamFormationId = this.currTeamFormationId;
    }

    ret.initTeam = this.makeClientTeamFormationList(serverType);
    ret.code = Code.OK;
    ret.uid = uid;
    return ret;
};

/** //获取一个阵容
 * @param  {} uid 阵容uid
 * @returns {} 返回一个阵容对象
 */
TeamFormations.prototype.getOneTeamFormation= function(uid) {
    return this.allTeamFormations.get(uid);
};

//获取主力队伍的uid
TeamFormations.prototype.getMainTeamUid= function(id) {
    let uid = "";
    for (let [k, v] of this.allTeamFormations) {
        if(id === v.TeamId && v.TeamType === 1) {
            uid = v.Uid;
            break;
        }
    }
    return uid;
};

//获取初始阵容的uid
TeamFormations.prototype.getInitTeamUid= function() {
    let uid = "";
    for (let [k, v] of this.allTeamFormations) {
        if(v.isInitFormation === 1) {
            uid = v.Uid;
            break;
        }
    }
    return uid;
};

//查找球员位置
TeamFormations.prototype.getHeroPositionByTeamId = function(formationUid, heroUid) {
    let formation = this.getOneTeamFormation(formationUid);
    for(let key in formation.PositionToHerosObject) {
        for(let i = 0, len = formation.PositionToHerosObject[key].length; i < len; i++) {
            if (formation.PositionToHerosObject[key][i] === heroUid) {
                return key;
            }
        }
    }
    return "";
};

TeamFormations.prototype.isHavePostionByTeamId = function(formationUid, position) {
    let isHave = false;
    let formation = this.getOneTeamFormation(formationUid);
    for(let key in formation.PositionToHerosObject) {
        if(key === position && formation.PositionToHerosObject[key].length > 0) {
            isHave = true;
            break;
        }
    }
    return isHave;
};

//查找对应位置的球员列表
TeamFormations.prototype.getHeroArrayByPositionName = function(formationUid, poistion) {
    let formation = this.getOneTeamFormation(formationUid);
    for(let key in formation.PositionToHerosObject) {
        if(key === poistion) {
            return formation.PositionToHerosObject[key];
        }
    }
    return [];
};

TeamFormations.prototype.checkNowTeamType = function (id) {
    let teamType = 0;
    for (let [k, v] of this.allTeamFormations) {
        if(v.TeamId === id && v.TeamType > teamType) {
            teamType = v.TeamType;
        }
    }
    return teamType;
}

TeamFormations.prototype.checkIsHaveTeamType = function (id, teamType) {
    let isHave = false;
    for (let [k, v] of this.allTeamFormations) {
        if(v.TeamId === id && v.TeamType === teamType) {
            isHave = true;
            break;
        }
    }
    return isHave;
}

//检查是否有主力队
TeamFormations.prototype.checkIsHaveMainTeamType = function (teamId, teamType) {
    let isHave = false;
    if(teamType === 1) {
        isHave = true;
        return isHave;
    }

    for (let [k, v] of this.allTeamFormations) {
        if(v.TeamId === teamId && v.TeamType === 1) {
            isHave = true;
            break;
        }
    }
    return isHave;
}

/**
 * 新增一个队伍(阵容改版)
 * @param teamId    阵容id   1 2 3 4
 * @param teamType  队伍类型   1 2 3   1主力 2替补 3业余
 * @returns {*}
 */
TeamFormations.prototype.addNewTeam = function (teamId, teamType) {
    if(!teamId) {
        return {code: Code.FAIL, uid: ""}
    }

    //这个队伍类型已经存在
    if (this.checkIsHaveTeamType(teamId, teamType)) {
        return {code: Code.TEAM_TYPE_FAIL, uid: ""};
    }

    //检查当前阵容是否有主力队
    if (!this.checkIsHaveMainTeamType(teamId, teamType)) {
        return {code: Code.NOT_MAIN_TEAM_TYPE, uid: ""};
    }

    let formationList = this.getTeamFormationListById(teamId);
    // logger.error("addNewTeam teamId----------------------", formationList.length, teamId, teamType)
    //已上阵的球员
    let onFormationHeros = new Map();
    //未上阵球员
    let notOnFormationHeros = new Map();

    for(let i=0, len = formationList.length; i < len; i++) {
        for(let k in formationList[i].PositionToHerosObject) {
            for(let m = 0; m < formationList[i].PositionToHerosObject[k].length; m++) {
                onFormationHeros.set(formationList[i].PositionToHerosObject[k][m], k);
            }
        }
    }

    for(let [k,v] of this.player.heros.allheros) {
        //过滤上阵球员和退役球员
        if(!onFormationHeros.has(k) && v.isDie !== 1 && TimeUtils.futureDayInterval(v.leftTime) >= 1) {
            notOnFormationHeros.set(k, v);
        }
    }
    //人数不够
    if(notOnFormationHeros.size < 11) {
        // logger.debug("addNewTeam hero num is not enough. left num: ", notOnFormationHeros.size);
        return {code: Code.NOT_ENOUGH_HERO_FAIL, uid: ""};
    }

    let initTeamUid = this.getMainTeamUid(teamId);
    //如果是跨阵容，取第一个阵容的主力uid
    if(initTeamUid === "") {
        initTeamUid = this.getInitTeamUid();
    }
    let srcFormation = this.getOneTeamFormation(initTeamUid);
    //创建阵容
    let copyTeam = utils.deepCopy(srcFormation);
    let newFormation = this.addTeamFormation(copyTeam.ResId);
    newFormation.UseTactics = copyTeam.UseTactics;
    newFormation.UseDefTactics = copyTeam.UseDefTactics;
    newFormation.Trainers = copyTeam.Trainers;
    newFormation.isInitFormation = 0;    //是否是初始阵容  0不是  1是
    newFormation.TeamType = teamType;
    newFormation.TeamId = teamId;
    //主力队
    if(teamType === 1) {
        this.currTeamFormationId = newFormation.Uid;
    }

    //球员自动布阵
    let tempHero = [];    //临时存储球员
    let copyObject = utils.deepCopy(newFormation.PositionToHerosObject);
    for (let key in copyObject){
        copyObject[key] = [];
    }

    let rateList = [];
    for(let [uid, v] of notOnFormationHeros) {
        let hero = this.player.getOneHero(uid);
        if (!hero) {
            continue;
        }
        if(tempHero.length < 1) {
            tempHero.push(uid);
            continue;
        }
        //检查是否有相同球员
        this.isHaveSameHero(tempHero, uid);
    }

    for(let uid in tempHero) {
        for (let key in copyObject) {
            let hero = this.player.getOneHero(uid);
            if (!hero) {
                continue;
            }
            let pos = {};
            pos.rate = this.calcHeroMatchRate(uid, key) * 10;
            pos.key = key;
            pos.uid = uid;
            pos.act = hero["twoLevelAttr"]["BallerRating"].Cur; //球员实力
            rateList.push(pos);
        }
    }

    let tmpheroList = [];
    let index = 1;
    //第一遍先检查是否有100%匹配实力按从大到小排
    rateList.sort(this.compare_func_1);
    let formationResId = newFormation.ResId;
    for(let id in copyObject) {
        let destNum = this.getNum(formationResId, index);
        for(let m = 0; m < destNum; ++m) {
            let delUid = "";
            for(let i = 0; i < rateList.length; ++i) {
                if(rateList[i].key == id && rateList[i].rate == 10) {
                    copyObject[id].push(rateList[i].uid);
                    delUid = rateList[i].uid;
                    //删除数组中的数据
                    let temIndex = tempHero.indexOf(delUid);
                    tempHero.splice(temIndex, 1);
                    break;
                }
            }
            delArrParm(rateList, delUid);
            if(copyObject[id].length >= destNum) {
                break;
            }
        }
        index++;
    }

    //第二遍把剩余的按配表顺序排序
    index = 1;
    for (let id in copyObject){
        let destNum = this.getNum(formationResId, index);
        index++;
        //球员已满
        if(copyObject[id].length >= destNum) {
            continue;
        }
        let leftNum = destNum - copyObject[id].length;
        for(let m = 0; m < leftNum; m++) {
            let rateList = this.rateListSort_1(tempHero, id);
            if( rateList.length > 0) {
                let uid = rateList[0].uid;
                copyObject[id].push(uid);
                tmpheroList.push(uid);
                utils.removeElement(tempHero, uid);
            }
        }
    }
    let PlaceKickResult = this.getFormationPlaceKickAttribute(tmpheroList);
    this.player.newerTask.triggerTask(commonEnum.NEWER_TASK.AUTOMATIC_DISPOSAL);
    newFormation.PositionToHerosObject = copyObject;
    newFormation.FreeKickPlayer = PlaceKickResult.FreeKick.uid;     //任意球球员
    newFormation.PenaltiesPlayer = PlaceKickResult.Penalties.uid;    //点球球员
    newFormation.CornerKickPlayer = PlaceKickResult.CornerKick.uid;   //角球球员

    //重新计算球队属性
    this.reCalcTeamFormationAttrByUid(newFormation.Uid);
    return {code: Code.OK, uid: newFormation.Uid};
}


//新建一个阵容
TeamFormations.prototype.newTeamFormation = function (resId, type) {
    var teamFormation = {};
    teamFormation["Uid"] = utils.syncCreateUid();    //Id(不是配置表的ID，而是玩家生成阵容的顺序)
    teamFormation["ResId"] = resId;                  //配置阵型Id
    teamFormation["Name"] = "阵容一";
    //团队基础属性
    teamFormation["Attack"] = 0;        //球队进攻值
    teamFormation["Defend"] = 0;        //球队防守值
    teamFormation["ActualStrength"] = 0; //球队的实力
    teamFormation["isInitName"] = 0;    //是否是初始名字 0是  1不是
    teamFormation["isInitFormation"] = 0;       //是否初始阵容一  0不是 1是
    teamFormation["IsLeague"] =  0;             //是否为联赛专用阵容 0为不是 1是为
    teamFormation["TeamId"] = 1;           //阵容Id   1, 2, 3, 4  阵容1, 2, 3, 4
    teamFormation["TeamType"] = 1;        //小队类型   1主力队  2替补队  3预备队
    var positionToHerosObject = {
        "GK": [],   //Position => [Hero1Id, Heros2Id]
        "DC": [],
        "DL": [],
        "DR": [],
        "DM": [],
        "MC": [],
        "ML": [],
        "MR": [],
        "AM": [],
        "ST": [],
        "WL": [],
        "WR": []
    };
	teamFormation["PositionToHerosObject"] = positionToHerosObject;        //存储球员Map 主键为数字1-11 值为Hero
    teamFormation["ScenceUse"] = [];                //阵型使用场景标记
    teamFormation["InspireRate"] =  0;              //鼓舞加成比例,暂定为0
    teamFormation["UseTactics"] = 101;              //当前阵容使用的战术
    teamFormation["UseDefTactics"] = 1101;          //当前阵容使用防守的战术
    teamFormation["FreeKickPlayer"] = "";           //指定的任意球球员
    teamFormation["PenaltiesPlayer"] = "";          //指定的点球球员
    teamFormation["CornerKickPlayer"] = "";         //指定的角球球员
    teamFormation["Trainers"] = this.initTrainers();   //教练
    teamFormation["Type"] = type || commonEnum.FORMATION_TYPE.COMMON;   //阵容类型 (用途)
    return teamFormation;
};

/**
 * 添加球员到阵容中
 * @param  {} uid      阵容UID
 * @param  {} position 阵型位置(比如"GK")
 * @param  {} heroUid  球员Uid
 */
TeamFormations.prototype.addHero = function (uid, position, heroUid) {
    var formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("PositionToHerosObject add Hero is null!", uid);
        return;
    }

    if (!heroUid){
        logger.error("TeamFormationObject add Hero is null ", heroUid);
        return Code.FAIL;
    }

    var hero =  this.player.getOneHero(heroUid);
    if (!hero) {
        logger.error("TeamFormationObject delHero not found!", uid);
        return Code.FAIL;
    }

    if (!position) {
        logger.error("TeamFormationObject addHero: position not found!", uid);
        return Code.FAIL;
    }

    //位置数量检测
    var isLessThan = this._InnerCheckTeamPositionHeroNumLessThan(uid, position);
    if (0 === isLessThan) {
        logger.error("TeamFormationObject addHero: this position already reach max num! uid, position", uid, position);
        return Code.FAIL;
    }

    //查重
    var isSame = this._InnercheckHeroIsSameResID(uid, heroUid);
    if (1 === isSame) {
        logger.error("TeamFormationObject _InnercheckHeroIsSameResID failed! the hero already in team formation", uid, heroUid);
        return Code.FAIL;
    }

    //设置个人争夺战球员阵容时, 不能是已经上阵的球员
    if(formation.Type === commonEnum.FORMATION_TYPE.GROUND_DEF || formation.Type === commonEnum.FORMATION_TYPE.GROUND_ATK) {
        let onFormationMap = new Map();
        let list = this.getTeamFormationListByType(formation.Type);
        for(let i=0,len=list.length;i<len;i++) {
            for (let key in list[i].PositionToHerosObject){
                for(let j=0,len1=list[i].PositionToHerosObject[key].length; j<len1; j++) {
                    //logger.debug("111111", list[i].PositionToHerosObject[key][j], heroUid);
                    onFormationMap.set(list[i].PositionToHerosObject[key][j], 1);
                }

            }
        }
        if(onFormationMap.has(heroUid)) {
            return Code.GROUND_MATCH.SET_ON_FORMATION_HERO;
        }
    }

    //检查位置是否已经满人了
    for (let key in formation.PositionToHerosObject){
        if (position === key){
            this.allTeamFormations.get(uid).PositionToHerosObject[key].push(heroUid);
            //logger.info("TeamFormationObject add hero", uid, heroUid);
            break;
        }
    }

    // this.reCalcTeamFormationAttrByUid(uid); //计算属性
    return Code.OK;
};

/**
 * 添加球员到阵容中
 * @param  {} uid      阵容UID
 * @param  {} position 阵型位置(比如"GK")
 * @param  {} index    插入位置
 * @param  {} heroUid  球员Uid
 */
TeamFormations.prototype.addHeroInTeam = function (uid, position, index, heroUid) {
    let isOK = false;
    var formation = this.getOneTeamFormation(uid);
    //查重
    var isSame = this._InnercheckHeroIsSameResID(uid, heroUid);
    if (1 === isSame) {
        logger.error("TeamFormationObject _InnercheckHeroIsSameResID failed! the hero already in team formation", uid, heroUid);
        return isOK;
    }

    //检查位置是否已经满人了
    for (let key in formation.PositionToHerosObject){
        if (position === key){
            this.allTeamFormations.get(uid).PositionToHerosObject[key].splice(index, 0, heroUid);
            //logger.info("TeamFormationObject add hero", uid, heroUid);
            isOK = true;
            break;
        }
    }
    return isOK;
};

/**
 * 内部函数 检查阵容中的是否包含了同一个球员和相同resID球员
 * @param  {} uid     阵容ID
 * @param  {} heroUid 球员ID 
 * @return {} isSame  是否存在相同的ResID 1为相同 0为不相同
 */
TeamFormations.prototype._InnercheckHeroIsSameResID = function (uid, heroUid) {
    var isSame = 1; //默认为相同
    var formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("PositionToHerosObject add Hero is null!", uid);
        return isSame;
    }

    var hero = this.player.getOneHero(heroUid);
    if (!hero) {
        logger.error("_InnercheckHeroIsSameResID: hero not found!", uid, heroUid);
        return isSame;
    }

    isSame = 0;
    var ResId =  hero["ResID"];
    // logger.error("formation.PositionToHerosObjectuuuuuuuuuuuuuuuuuuuuuu",formation.PositionToHerosObject)
    for (let key in formation.PositionToHerosObject)
    {
        let heroArr = formation.PositionToHerosObject[key];
        for (let i = 0; i < heroArr.length; i++) {
            let tmpUid = heroArr[i];
            var tmpHero = this.player.getOneHero(tmpUid);
            if (!tmpHero) {
                var tmpResID = tmpHero["ResID"];
                if (tmpResID === ResId) {
                    isSame = 1;
                    break;
                }
            }

            if (heroUid === tmpUid){
                isSame = 1;
                break;
            }          
        }
    }
    // logger.error("formation.PositionToHerosObject[[[[[[[[[[[[[",formation.PositionToHerosObject)

    return isSame;
};


/**
 * 内部函数 检查阵容的某一位置的球员数量是否小于配置所规定的的数量
 * @param  {} uid      阵容Uid
 * @param  {} position 检查该阵容的位置
 * @return {} isLessThan 0为不小于 1为小于
 */
TeamFormations.prototype._InnerCheckTeamPositionHeroNumLessThan = function (uid, position) {
    var isLessThan = 0; //默认超过
    var oneTeamFormation = this.getOneTeamFormation(uid);
    if (!oneTeamFormation) {
        logger.error("_InnerCheckTeamPositionHeroNum uid is null!", uid);
        return isLessThan;
    }

    if (!position) {
        logger.error("_InnerCheckTeamPositionHeroNum: position not found!", uid);
        return isLessThan;
    }

    var object = oneTeamFormation.PositionToHerosObject;
    if (!object) {
        logger.error("_InnerCheckTeamPositionHeroNum object not found!", uid);
        return isLessThan;
    }

    //logger.error("_InnerCheckTeamPositionHeroNum: uid, object", uid, object);

    var resID = oneTeamFormation["ResId"];
    const heroArr = object[position];
    if (!heroArr) {
        logger.error("_InnerCheckTeamPositionHeroNum position not found!", uid);
        return;
    }

    var length = heroArr.length;
    var id = commonEnum.TEAM_FORMATION_CONFIG_POSITION_TO_ID[position];
    var num = this.getNum(resID, id);

    if ( length < num) {
        isLessThan = 1
    }

    //logger.error("_InnerCheckTeamPositionHeroNum: uid, position, length, num.", uid, position, length, num);

    return isLessThan;
};

/**
 * 删除阵容中球员的位置
 * @param  {} uid      TeamId
 * @param  {} heroUid  球员Id
 * @returns {} Code    OK 成功 FAIL 失败
 */
TeamFormations.prototype.delHero = function(uid, heroUid) {
    var formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("delHero formation not found!", uid);
        return Code.FAIL;
    }

    var position = this.getHeroPositionByTeamId(uid, heroUid);
    if ("" === position ) {
        logger.error("TeamFormationObject delHero is null", uid);
        return Code.FAIL;
    }

    var hero =  this.player.getOneHero(heroUid);
    if (!hero) {
        logger.error("TeamFormationObject delHero not found!", uid);
        return Code.FAIL;
    }

    let index = -1;
    for(let key in formation.PositionToHerosObject)
    {
        if (position === key)
        {
            const heroArr = formation.PositionToHerosObject[key];
            for (let i = 0; i < heroArr.length; i++) {
                const tmpUid = heroArr[i];
                if (heroUid === tmpUid)
                {
                    index = i;
                    break;
                }          
            }
        }
    }

    if (-1 !== index)
    {
        this.allTeamFormations.get(uid).PositionToHerosObject[position].splice(index, 1);
        //删除hero身上的attack和defend数据
        this.player.DelHeroAttackAndDefend(heroUid, uid);
    }

    //logger.info("TeamFormationObject delete hero", uid, heroUid);
    return Code.OK;
};

/**
 * 删除阵容中球员的位置
 * @param  {} uid      TeamId
 * @param  {} heroUid  球员Id
 * @returns {} Code    OK 成功 FAIL 失败
 */
TeamFormations.prototype.delHeroInTeam = function(uid, heroUid) {
    let ret = {position: "", index: -1}
    var formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("delHero formation not found!", uid);
        return ret;
    }

    var position = this.getHeroPositionByTeamId(uid, heroUid);
    if ("" === position ) {
        logger.error("TeamFormationObject delHero is null", uid);
        return ret;
    }

    var hero =  this.player.getOneHero(heroUid);
    if (!hero) {
        logger.error("TeamFormationObject delHero not found!", uid);
        return ret;
    }

    let index = -1;
    for(let key in formation.PositionToHerosObject){
        if (position === key){
            index = formation.PositionToHerosObject[key].indexOf(heroUid);
            ret.index = index;
            ret.position = key;
            break;
        }
    }
    if (-1 !== index){
        this.allTeamFormations.get(uid).PositionToHerosObject[position].splice(index, 1);
        //删除hero身上的attack和defend数据
        this.player.DelHeroAttackAndDefend(heroUid, uid);
    }
    return ret;
};

TeamFormations.prototype.getAttackRate = function(resID, positionID) {
    var attack_rate_column_str= "AttackArg" + positionID;
    var config = dataApi.allData.data["TeamFormation"][resID];
    var val = config[attack_rate_column_str];
    return val?val:0;
};

TeamFormations.prototype.getDefendRate =  function(resID, positionID) {
    var defend_rate_column_str= "DefendArg" + positionID;
    var config = dataApi.allData.data["TeamFormation"][resID];
    var val = config[defend_rate_column_str];
    return val?val:0;
};

TeamFormations.prototype.getNum =  function(resID, positionID) {
    var num_column_str= "Num" + positionID;
    var config = dataApi.allData.data["TeamFormation"][resID];
    var val = config[num_column_str];
    return val?val:0;
};

TeamFormations.prototype.getNameByType =  function(index, type) {
    if(type === commonEnum.FORMATION_TYPE.COMMON) {
        return "阵容" + commonEnum.TEAM_TYPE_NAME[index];
    }else if(type === commonEnum.FORMATION_TYPE.GROUND_ATK ) {
        return "进攻队伍" + commonEnum.TEAM_TYPE_NAME[index];
    }else if(type === commonEnum.FORMATION_TYPE.GROUND_DEF) {
        return "防守队伍" + commonEnum.TEAM_TYPE_NAME[index];
    }
    return "阵容" + commonEnum.TEAM_TYPE_NAME[index];
};

TeamFormations.prototype.getPostionValue = function(positionID, position) {
    var config = dataApi.allData.data["PositionMatch"][positionID];
    var value =  config[position];
    return value?value:0;
};

/**
 * 计算球队进攻基础属性
 * 计算规则=上场球员1球员_进攻值当前值*上场球员1阵型位置进攻系数/1000
    +上场球员2球员_进攻值当前值*上场球员2阵型位置进攻系数/1000
    +…
    +上场球员11球员_进攻值当前值*上场球员11阵型位置进攻系数/1000
    +经理等级*10
 * @param  {} uid 阵容uid
 * @returns {} 进攻数值
 */
TeamFormations.prototype.calcTeamBaseAttack = function(uid)  {
    var sum = 0;
    var self = this;
    var formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("calcTeamBaseAttack formation not found!", uid)
        return;
    }
    var object = formation.PositionToHerosObject;
    if (!object) {
        logger.error("calcTeamBaseAttack object not found!", uid)
        return;
    }

    var self = this;
    var resID = formation["ResId"];
    for (var i in object) {
        const position = i;
        const heroArr = object[i];
        var id = commonEnum.TEAM_FORMATION_CONFIG_POSITION_TO_ID[position];
        var rate = this.getAttackRate(resID, id);
        for(var j=0; j<heroArr.length; j++) {
            var hero = self.player.getOneHero(heroArr[j]);
            if(!!hero) {
                var attackAndDefend = hero.AttackAndDefend;
                var obj = attackAndDefend[uid];
                //logger.error("calcTeamBaseAttack obj", obj);
                var attack_value = 0;
                if (obj.attack) 
                {
                    attack_value = obj.attack;
                }
                attack_value = attack_value * 100;
                sum = sum + attack_value * rate / 1000 / 100;
                //logger.error("calcTeamBaseAttack attack_value", rate, attack_value, sum);
            }
        }
    }
    
    var baseValue = utils.MathRound(sum + self.player.level * 10);
    //logger.info("calcTeamBaseAttack baseValue!", baseValue);
    return baseValue;
};

/*
上场球员1球员_防守值当前值*上场球员1阵型位置防守系数/1000
*/

//计算球队防守基础属性
/**
 * @param  {string} uid 阵型uid
 */
TeamFormations.prototype.calcTeamBaseDefend = function(uid)  {
    var sum = 0;
    var formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("calcTeamBaseDefend formation not found!", uid)
        return;
    }

    var object = formation.PositionToHerosObject;
    if (!object) {
        logger.error("calcTeamBaseDefend object not found!", uid)
        return;
    }

    var self = this;
    var resID = formation["ResId"];
    for (var i in object) {
        const position = i;
        const heroArr = object[i];
        var id = commonEnum.TEAM_FORMATION_CONFIG_POSITION_TO_ID[position];
        var rate = this.getDefendRate(resID, id);
        heroArr.forEach( function(heroUid){
            var hero = self.player.getOneHero(heroUid);
            if(!!hero) {
                var attackAndDefend = hero.AttackAndDefend;
                var obj = attackAndDefend[uid];
                //logger.error("calcTeamBaseDefend obj", obj);

                var defend_value = 0;
                if (obj.defend) {
                    defend_value = obj.defend;
                }
                defend_value = defend_value * 100;
                sum = sum + defend_value * rate / 1000 / 100;
                //logger.error("calcTeamBaseDefend defend_value", rate, defend_value, sum);
            }
        });
    }

    var baseValue = utils.MathRound(sum + self.player.level * 8);
    //logger.info("calcTeamBaseAttack baseValue!", baseValue);
    return baseValue;
};

//计算球队的总实力
TeamFormations.prototype.calcTotalRating = function(uid) {
    let self = this;
    let formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("calcTotalRating formation not found!", uid);
        return 0;
    }
    let object = formation.PositionToHerosObject;
    if (!object) {
        logger.error("calcTotalRating object not found!", uid);
        return 0;
    }

    let sum = 0;
    for (let i in object) {
        let position = i;
        const heroArr = object[i];
        if(!!heroArr)
        {
            heroArr.forEach( function(heroUid){
                let hero = self.player.getOneHero(heroUid);
                if(!!hero) {
                    let value = self.player.heros.calcHeroInFormationRate(heroUid, position, uid);
                    sum += value;
                    //logger.info("calcTotalRating: uid, heroUid, position, value, sum", uid, heroUid, position, value, sum);
                }
            });
        }
    }
    // logger.error("球队实力____________________________________",sum)
    return sum;
};


//计算球队属性(战斗用)
TeamFormations.prototype.reCalcTeamFormationAttrByUidToBattle = function(uid) {
    var self = this;
    var formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("reCalcTeamFormationAttrByUid formation not found!", uid)
        return;
    }
    var object = formation.PositionToHerosObject;
    if (!object) {
        logger.error("reCalcTeamFormationAttrByUid object not found!", uid)
        return;
    }


    for (var i in object) {
        const position = i;
        const heroArr = object[i];
        if(!!heroArr) {
            heroArr.forEach( function(heroUid){
                var hero = self.player.getOneHero(heroUid);
                if(!!hero) {
                    //logger.info("reCalcTeamFormationAttrByUid object !")
                    //先计算出初始实力
                    self.player.reCalcAttrToBattle(heroUid, uid);
                    // self.player.heros.calcHeroInFormationData(heroUid, position);        //1.先计算每个球员的属性
                    self.player.calcBallerAttackAndDefend(heroUid, uid, position);          //2.计算球员的进攻和防守值
                }
            });
        }
    }
    //计算阵型属性也就是球队的属性
    var attack = this.calcTeamBaseAttack(uid);
    var defend = this.calcTeamBaseDefend(uid);
    let actualStrength = this.calcTotalRating(uid);
    var inspireRate = this.getOneTeamFormation(uid).InspireRate;
    this.getOneTeamFormation(uid).Attack = attack + attack * inspireRate;
    this.getOneTeamFormation(uid).Defend = defend + defend * inspireRate;
    this.getOneTeamFormation(uid).ActualStrength = actualStrength;
    this.player.newerTask.triggerTask(commonEnum.NEWER_TASK.STRENGTH_UP,0,0, actualStrength);
    //计算球队身价
    this.calcTeamValue(uid);
    //当前使用阵容
    if(uid === this.currTeamFormationId) {
        if(!!this.player.league) {
            this.player.updateStrength(this.uid, this.player.league, actualStrength);
        }
    }
};


//计算球队属性
TeamFormations.prototype.reCalcTeamFormationAttrByUid = function(uid) {
    var self = this;
    //检查战术加成
    // this.checkHeroInCurFormation();
    //检查是否是当前阵容中的队伍
    if(!this.checkIsMainTeamByUid(uid)) {
        return;
    }

    var formation = this.getOneTeamFormation(uid);
    if (!formation) {
        logger.error("reCalcTeamFormationAttrByUid formation not found!", uid)
        return;
    }
    var object = formation.PositionToHerosObject;
    if (!object) {
        logger.error("reCalcTeamFormationAttrByUid object not found!", uid)
        return;
    }


    for (var i in object) {
        const position = i;
        const heroArr = object[i];
        if(!!heroArr) {
            heroArr.forEach( function(heroUid){
                var hero = self.player.getOneHero(heroUid);
                if(!!hero) {
                    //logger.info("reCalcTeamFormationAttrByUid object !")
                    //先计算出初始实力
                    self.player.reCalcAttr(heroUid);
                    // self.player.heros.calcHeroInFormationData(heroUid, position);        //1.先计算每个球员的属性
                    self.player.calcBallerAttackAndDefend(heroUid, uid, position);          //2.计算球员的进攻和防守值
                }
            });
        }
    }
    //计算阵型属性也就是球队的属性
    var attack = this.calcTeamBaseAttack(uid);
    var defend = this.calcTeamBaseDefend(uid);
    let actualStrength = this.calcTotalRating(uid);
    var inspireRate = this.getOneTeamFormation(uid).InspireRate;
    this.getOneTeamFormation(uid).Attack = attack + attack * inspireRate;
    this.getOneTeamFormation(uid).Defend = defend + defend * inspireRate;
    this.getOneTeamFormation(uid).ActualStrength = actualStrength;
    this.player.newerTask.triggerTask(commonEnum.NEWER_TASK.STRENGTH_UP,0,0, actualStrength);
    //计算球队身价
    this.calcTeamValue(uid);
    //当前使用阵容
    if(uid === this.currTeamFormationId) {
        if(!!this.player.league) {
            this.player.updateStrength(this.uid, this.player.league, actualStrength);
        }
    }
};


/**
 * 计算球员在阵容中的位置匹配度
 * @param heroUid  球员Uid
 * @param currPostion     球员在阵容的位置
 *  */
TeamFormations.prototype.calcHeroMatchRate = function(heroUid, currPostion) 
{
    let hero = this.player.getOneHero(heroUid);
    // if (!hero)
    // {
    //     logger.error("calcHeroMatchRate: not hero!!!", heroUid);
    //     return 0;
    // }

    let resId = hero.ResID;
    //let config = dataApi.allData.data["Footballer"][resId];
    let config;
    let bead = resId / 10000;
    if(bead < 9)
    {
        config = dataApi.allData.data["Footballer"][resId];
    }
    else
    {
        config = dataApi.allData.data["FootballerPve"][resId];
    }

    let position1 =  config.Position1;
    let position2 =  config.Position2;
    if ("" === position1 && "" === position2) {
        logger.error("calcHeroMatchRate position1 and position2 not data!", uid);
        return 0;
    }

    //1.先查看当前位置是否为球员擅长位置
    let positionMatchRate = 0;
    if (currPostion === position1 || currPostion === position2) {
        positionMatchRate = 1;
    }else {
        let positionMatchId = config.PositionMatch;
        //2.不属于擅长位置，查表
        if ( "" === position1 ) {
            positionMatchRate = 0;
        } else if(positionMatchId > 0) {
            positionMatchRate = this.player.heros.getPositionMatchRateById(positionMatchId, currPostion)
        } else {
            positionMatchRate = this.player.heros.getPositionMatchRate(position1, currPostion)
        }
    
        if (0 === positionMatchRate || NaN === positionMatchRate) {
            //走到这里应该是配置出错了
            positionMatchRate = 0;
        }
    }
    return positionMatchRate;
}


/**
 * 计算所有阵容属性
 * @param 无
 */
TeamFormations.prototype.recalcAllTeamFormationAttr = function () {
    var self = this;
    this.allTeamFormations.forEach(function(object, Uid){
        self.reCalcTeamFormationAttrByUid(Uid);
    });
};

/**
 * 获取阵容列表
 * @param 无
 */
TeamFormations.prototype.getTeamFormationList = function() {
    //仅返回普通类型的阵容数据
    /*
    let list = [];
    for(let [k,v] of this.allTeamFormations) {
        if(v.Tpye === commonEnum.FORMATION_TYPE.COMMON) {
            list.push(v);
        }
    }
    return list;
    */
    return utils.toArray(this.allTeamFormations);
};

/**
 * 根据阵容类型获取阵容列表
 * @param 无
 */
TeamFormations.prototype.getTeamFormationListByType = function(type) {
    //仅返回普通类型的阵容数据
    let list = [];
    for(let [k,v] of this.allTeamFormations) {
        if((type === v.Type) || (type === commonEnum.FORMATION_TYPE.ALL_TYPE)
            || (type === commonEnum.FORMATION_TYPE.GROUND && (v.Type === commonEnum.FORMATION_TYPE.GROUND_ATK
                || v.Type === commonEnum.FORMATION_TYPE.GROUND_DEF))) {
            list.push(v);
        }
    }
    //logger.debug("getTeamFormationListByType list: ", list);
    return list;
};

/**
 * 根据阵容Id获取阵容列表
 * @param 无
 */
TeamFormations.prototype.getTeamFormationListById = function(id) {
    let list = [];
    for(let [k,v] of this.allTeamFormations) {
        if(id === v.TeamId) {
            list.push(v);
        }
    }
    return list;
};

/**
 * 根据阵容Id和uid获取阵容列表
 * @param 无
 */
TeamFormations.prototype.getTeamFormationListByIdAndUid = function(id, uid) {
    let list = [];
    for(let [k,v] of this.allTeamFormations) {
        if(id === v.TeamId && uid !== v.Uid) {
            list.push(v);
        }
    }
    return list;
};

//检查球员是否存在于阵容中
/**
 * @param  {} teamFormationUid
 * @param  {} heroUid
 * @returns {} isExist 0为不存在 1为存在
 */
TeamFormations.prototype._checkHeroInTeamFormation = function(teamFormationUid, heroUid) {
    var isExist = 0;
    var oneTeamFormation = this.getOneTeamFormation(teamFormationUid);
    if (!oneTeamFormation) {
        logger.error("TeamFormations._checkHeroInTeamFormation error reason: formation not found!");
        return isExist;
    }

    if (!heroUid) {
        logger.error("TeamFormations._checkHeroInTeamFormation error reason: heroUid not found!");
        return isExist;
    }

    var object = oneTeamFormation.PositionToHerosObject;
    if (!object) {
        logger.error("TeamFormations._checkHeroInTeamFormation object not found!", teamFormationUid)
        return isExist;
    }

    //logger.info("TeamFormations._checkHeroInTeamFormation: teamFormationUid, heroUid", teamFormationUid, heroUid);
    for (var i in object) {
        const heroArr = object[i];
        for (let i = 0; i < heroArr.length; i++) {
            const tmpHeroUid = heroArr[i];
            if (tmpHeroUid === heroUid) {
                isExist = 1;
                break;
            }
        }  
        if(isExist === 1){
            break;
        }
    }
    
    //logger.error("TeamFormations._checkHeroInTeamFormation: isExist", isExist);
    return isExist;
};

/**
 * 计算球员战术加成
 * @param  {} type  1全部计算  2单个计算
 *
 */
TeamFormations.prototype.calcHeroTacticsAttr = function(type, uid, teamUid) {
    let oneTeamFormation = this.getOneTeamFormation(teamUid);
    if(!oneTeamFormation) {
        return;
    }

    let config;
    let resId;
    let bead;
    if(type === 1) {
        // for(let [heroUid, v] of this.player.heros.allheros) {
        //     this.player.heros.reCalcAttrRevision(heroUid);
        //     // this.player.heros.reCalcAttr(heroUid);
        // }
    }else if(type === 2) {
        let hero = this.player.getOneHero(uid);
        resId = hero.ResID;
        bead = resId / 10000;
        if(bead < 9) {
            config = dataApi.allData.data["Footballer"][resId];
        } else {
            config = dataApi.allData.data["FootballerPve"][resId];
        }

        if(config.Position1 === "" && config.Position2 === "") {
            return;
        }

        this.player.heros.calcTacticsAttrByType(uid, config.Position1, teamUid, 1);   //进攻战术加成
        this.player.heros.calcTacticsAttrByType(uid, config.Position1, teamUid, 2);   //防守战术加成
    }
}


/**
 * 检查球员是否存在于任意阵容中
 * @param  {} heroUid
 * isExist 0为不存在 1为存在
 */
TeamFormations.prototype.checkHeroInArbitrarilyFormation = function(heroUid) {
    let isExist = false;
    let team = [];
    let teamId = this.getCurrTeamTeamId();
    if(teamId === 0) {
        return isExist;
    }

    for(let [uid, v] of this.allTeamFormations) {
        if (!uid || "" === uid) {
            continue;
        }
        let oneTeamFormation = this.getOneTeamFormation(uid);
        if (!oneTeamFormation) {
            continue;
        }

        let object = oneTeamFormation.PositionToHerosObject;
        if (!object) {
            continue;
        }
        for (let i in object) {
            const heroArr = object[i];
            for (let j = 0; j < heroArr.length; j++) {
                const tmpHeroUid = heroArr[j];
                if (tmpHeroUid === heroUid && teamId === v.TeamId) {
                    isExist = true;
                    break;
                }
            }   
            if(isExist){
                break;
            }
        }
        if(isExist){
            team.push(uid);
        }
    }
    return team;
};

/**
 * 检查球员是否存在于任意阵容中 (用于解雇)
 * @param  {} heroUid
 * isExist 0为不存在 1为存在
 */
TeamFormations.prototype.checkHeroInFormation = function(heroUid) {
    let isExist = false;
    let team = [];
    for(let [uid, v] of this.allTeamFormations) {
        if (!uid || "" === uid) {
            continue;
        }
        let oneTeamFormation = this.getOneTeamFormation(uid);
        if (!oneTeamFormation) {
            continue;
        }

        let object = oneTeamFormation.PositionToHerosObject;
        if (!object) {
            continue;
        }

        for (let i in object) {
            const heroArr = object[i];
            for (let j = 0; j < heroArr.length; j++) {
                const tmpHeroUid = heroArr[j];
                if (tmpHeroUid === heroUid) {
                    isExist = true;
                    break;
                }
            }   

            //找到返回
            if(isExist){
                break;
            }
        }

        //找到一个直接返回
        if(isExist){
            team.push(uid);
            break;
        }
    }
    return team;
};

/**
 * 内部函数 用于实现两个球员位置交互 第一趟 uid1 -> uid2的位置,第二趟 uid2 -> uid1 的位置,最后重新计算属性
 * @param  {} teamFormationUid 阵容Uid
 * @param  {} heroUid1  替换球员Uid
 * @param  {} heroUid2  被替换球员Uid
 * @returns {} code OK 成功 FAIL 失败
 */
TeamFormations.prototype._InnerHeroPositionExchange = function(teamFormationUid, heroUid1, heroUid2) 
{
    var retCode = Code.FAIL;
    var oneTeamFormation = this.getOneTeamFormation(teamFormationUid);
    var object = oneTeamFormation.PositionToHerosObject;
    // logger.error("_InnerHeroPositionExchange: =======================", heroUid1, heroUid2, object);
    var isBreakAll = false;
    var hero1ReplacePosition = ""; //为了防止第二趟把刚刚替换的UID还原回去
    var hero1ReplacePos = 0;
    for(var key in object) {
        const heroArr = object[key];
        for (let index = 0; index < heroArr.length; index++) {
            const tmpHeroUid = heroArr[index];
            if (tmpHeroUid === heroUid2) {
                oneTeamFormation.PositionToHerosObject[key][index] = heroUid1;
                retCode = Code.OK;
                //logger.info("_InnerHeroPositionExchange heroUid1", key, index, heroUid1);
                hero1ReplacePosition = key;
                hero1ReplacePos = index;
                isBreakAll = true;
                break;
            }
        }    

        if (isBreakAll) {
            break;
        }
    }

    // logger.error("_InnerHeroPositionExchange object", hero1ReplacePos, hero1ReplacePosition, object);

    var isBreakAll = false; //调出多重循环
    for(var key in object) {
        const heroArr = object[key];
        for (let index = 0; index < heroArr.length; index++) {
            const tmpHeroUid = heroArr[index];
            if (tmpHeroUid === heroUid1) {
                if (hero1ReplacePosition !== key) {
                    oneTeamFormation.PositionToHerosObject[key][index] = heroUid2;
                    retCode = Code.OK;
                    //logger.info("_InnerHeroPositionExchange heroUid2", key, hero1ReplacePosition, index, heroUid2);
                    isBreakAll = true;
                    break;
                }else {  //key is same
                    if (hero1ReplacePos !== index) {
                        oneTeamFormation.PositionToHerosObject[key][index] = heroUid2;
                        retCode = Code.OK;
                        //logger.info("_InnerHeroPositionExchange heroUid2", key, hero1ReplacePosition, index, heroUid2);
                        isBreakAll = true;
                        break;
                    }
                }
            }
        }
        
        if (isBreakAll) {
            break;
        }
    }

    if (retCode == Code.OK) {
        //重新计算属性
        // this.reCalcTeamFormationAttrByUid(teamFormationUid);

        //重新计算换下来的球员
        // this.player.reCalcAttr(heroUid1);
        this.player.upAddHero(heroUid1);
        this.player.upAddHero(heroUid2);
    }

    // logger.error("_InnerHeroPositionExchange:++++++++++++++++++++++++++++ ", heroUid1, heroUid2, object);
    //logger.error("_InnerHeroPositionExchange getOneHero heroUid1", heroUid1, this.player.getOneHero(heroUid1));
    //logger.error("_InnerHeroPositionExchange getOneHero heroUid2", heroUid2, this.player.getOneHero(heroUid2));
    // logger.error("_InnerHeroPositionExchange object", object);
    return retCode;
};

/**
 * 获取未上场的球员(除去上场的球员就是未上场的)
 * @param  {} teamFormationUid 阵容Uid
 * @param  {} heroUid1  替换球员Uid
 * @param  {} heroUid2  被替换球员Uid
 * @returns {} code OK 成功 FAIL 失败
 */
TeamFormations.prototype.getNoPositionHerosByUid =function(teamFormationUid) {
    var noPositionHerosArray = [];
    var oneTeamFormation = this.getOneTeamFormation(teamFormationUid);

    if (!oneTeamFormation) {
        return noPositionHerosArray;
    }

    var object = oneTeamFormation.PositionToHerosObject;
    if (!object) {
        return noPositionHerosArray;
    }

    var ret = this.player.getHeroList();
    if (!ret) {
        return noPositionHerosArray;
    }

    var heroListArray = ret.heroList;
    for (let i = 0; i < heroListArray.length; i++) {
        const heroObject = heroListArray[i];
        var heroUid = heroObject.Uid;
        var isExist = this._checkHeroInTeamFormation(teamFormationUid, heroUid);
        //存在该阵容中的球员，直接跳过
        if (1 === isExist) {
            continue;
        }
        noPositionHerosArray.push(heroUid);
    }

    return noPositionHerosArray;
};

//降序
function compare(rating){
    return function(a,b){
        var value1 = a[rating];
        var value2 = b[rating];
        return  value2 - value1;
    }
};

/**
 * 获取未上场的球员，要求按照实力降序排序(除去上场的球员就是未上场的)
 * @param  {} teamFormationUid 阵容Uid
 * @returns {} array
 */
TeamFormations.prototype.getNoPositionHerosRatingSortByUid =function(teamFormationUid) {
    var noPositionHerosObject = [];
    var noPositionHerosObjectNew =[];
    var oneTeamFormation = this.getOneTeamFormation(teamFormationUid);

    if (!oneTeamFormation) {
        return noPositionHerosObjectNew;
    }

    var object = oneTeamFormation.PositionToHerosObject;
    if (!object) {
        return noPositionHerosObjectNew;
    }

    var ret = this.player.getHeroList();
    if (!ret) {
        return noPositionHerosObjectNew;
    }

    var heroListArray = ret.heroList;
    for (let i = 0; i < heroListArray.length; i++) {
        const heroObject = heroListArray[i];
        var heroUid = heroObject.Uid;
        var isExist = this._checkHeroInTeamFormation(teamFormationUid, heroUid);
        //存在该阵容中的球员，直接跳过
        if (1 === isExist) {
            continue;
        }

        var rating = this.player.getRating(heroUid);
        var sortObj = {
            Uid: heroUid,
            rating: rating
        };

        noPositionHerosObject.push(sortObj);
    }

    //降序 排序
    noPositionHerosObject.sort(compare("rating"));

    for(var index = 0; index < noPositionHerosObject.length; index++) {
        const obj = noPositionHerosObject[index];
        const Uid =  obj.Uid;
        var hero = this.player.getOneHero(Uid);
        if (hero) {
            noPositionHerosObjectNew.push(hero);
        }
    }
    
    return noPositionHerosObjectNew;
};

/**
 * 用于同一阵容中两个球员进行交换
 * @param  {} teamFormationUid 阵容Uid
 * @param  {} heroUid1  替换球员Uid
 * @param  {} heroUid2  被替换球员Uid
 * @returns {} code OK 成功 FAIL 失败
 */
TeamFormations.prototype.teamFormationExchangeHero = function (teamFormationUid, heroUid1, heroUid2) 
{
    if (!teamFormationUid) 
    {
		logger.error("teamFormationExchangeHero error reason: the heroUid is error!", teamFormationUid);
		return Code.FAIL;
	}

    if (heroUid1 <= 0) 
    {
		logger.error("teamFormationExchangeHero error reason: the uid is error!", heroUid1);
		return Code.FAIL;
	}

    if (heroUid2 <= 0) 
    {
		logger.error("teamFormationExchangeHero error reason: the uid is error!", heroUid2);
		return Code.FAIL;
    }

    var oneTeamFormation = this.getOneTeamFormation(teamFormationUid);
    if (!oneTeamFormation) 
    {
        logger.error("teamFormationExchangeHero error reason: formation not found!", teamFormationUid);
        return Code.FAIL;
    }

    //检查球员是否存在
    let heroObj1 = this.player.heros.getHero(heroUid1);
    if (!heroObj1)
    {
        logger.error("teamFormationExchangeHero error reason: heroUid1 not found!", teamFormationUid, heroUid1);
        return Code.FAIL;
    }

    //个人球场争夺战阵容下, 已经上阵球员不能放至在其他阵容上
    if(oneTeamFormation.Type === commonEnum.FORMATION_TYPE.GROUND_ATK || oneTeamFormation.Type === commonEnum.FORMATION_TYPE.GROUND_DEF) {
        let heroFormationIndex1, heroFormationIndex2;
        let formationList = this.getTeamFormationListByType(oneTeamFormation.Type);
        for(let i=0,len=formationList.length;i<len;i++) {
            for(let k in formationList[i].PositionToHerosObject) {
                for(let j=0,len1=formationList[i].PositionToHerosObject[k].length;j<len1;j++) {
                    if(heroUid1 === formationList[i].PositionToHerosObject[k][j]) {
                        heroFormationIndex1 = i;
                    }
                    if(heroUid2 === formationList[i].PositionToHerosObject[k][j]) {
                        heroFormationIndex2 = i;
                    }
                }

            }
        }
        //logger.debug("june 1111111", heroFormationIndex1, heroFormationIndex2);
        if(heroFormationIndex1 && heroFormationIndex2 && heroFormationIndex1 !== heroFormationIndex2) {
            logger.debug("on formation hero cannot exchage: ", heroFormationIndex1, heroFormationIndex2, oneTeamFormation.Type);
            return Code.GROUND_MATCH.SET_ON_FORMATION_HERO;
        }
    }

    // logger.error("sssssssssssssheroObj1ssssssssssssssssssss",heroUid1, heroUid2)
    //退役不能上场
    if(heroObj1.isDie === 1) {
        return Code.BAG.HERO_DIE;
    }

    let heroObj2 = this.player.heros.getHero(heroUid2);
    if (!heroObj2)
    {
        logger.error("teamFormationExchangeHero error reason: heroUid2 not found!", teamFormationUid, heroUid2);
        return Code.FAIL;
    }

    //logger.error("sssssssssssssheroObj2ssssssssssssssssssss",heroObj2.isDie)
    //退役不能上场
    if(heroObj2.isDie === 1) {
        return Code.BAG.HERO_DIE;
    }

    // if(TimeUtils.futureDayInterval(heroObj2.leftTime) <= 0) {
    //     return Code.BAG.HERO_DIE;
    // }

    let heroSoleId1 = dataApi.allData.data["Footballer"][heroObj1.ResID].SoleId;
    let heroSoleId2 = dataApi.allData.data["Footballer"][heroObj2.ResID].SoleId;
    //检查被替换的球员是否存在阵容中
    let isExist1 = this._checkHeroInTeamFormation(teamFormationUid, heroUid1);
    let isExist2 = this._checkHeroInTeamFormation(teamFormationUid, heroUid2);
    if(heroSoleId1 !== heroSoleId2) {
        //只要替换球员和被替换球员不在场上就要检查一下场上是否有相同球员
        if(isExist1 === 0) {
            if(this.checkIsHaveSameHero(heroUid1, teamFormationUid)) {
                return Code.HERO_CODE.HAVE_SAME_HERO;
            }
        }else if(isExist1 === 1 && isExist2 === 0) {
            if(this.checkIsHaveSameHero(heroUid2, teamFormationUid)) {
                return Code.HERO_CODE.HAVE_SAME_HERO;
            }
        }
    }

    //检查被替换的球员是否存在阵容中
    if (0 === isExist1 && 0=== isExist2) {
        logger.error("teamFormationExchangeHero error reason: _checkHeroInTeamFormation heroUid2 failed! ");
        return Code.FAIL;
    }

    //不管hero1的位置如何，被替换球员的位置必须要有位置
    var position1 = this.getHeroPositionByTeamId(teamFormationUid, heroUid1);
    var position2 = this.getHeroPositionByTeamId(teamFormationUid, heroUid2);
    if ("" === position1 && "" === position2) {
        logger.error("teamFormationExchangeHero error reason: getHeroPositionByTeamId position failed!teamFormationUid, heroUid1, heroUid2!", teamFormationUid, heroUid1, heroUid2);
        return Code.FAIL;
    }

    //定位球员1的位置
    //找得到位置，heroUid1在这个阵容中,只需要两个球员的id互换
    var retCode = this._InnerHeroPositionExchange(teamFormationUid, heroUid1, heroUid2);
    if (retCode !==Code.OK) {
        logger.error("teamFormationExchangeHero error reason: _InnerHeroPositionExchange position failed!");
        return Code.FAIL;
    }

    //heroUid在这个阵容找不到位置，相当于从未上场的球员选上场,那么被替换的球员需要下场，需要删除信息
    if ("" === position1) {
        retCode = this.player.DelHeroAttackAndDefend(heroUid2, teamFormationUid);
        //logger.info("TeamFormations.teamFormationExchangeHero error reason: DelHeroAttackAndDefend heroUid2 failed!");
        //检查被替换的球员是否有被设置定位球球员,如果有从设置中删除
        if(oneTeamFormation.FreeKickPlayer === heroUid2)
            oneTeamFormation.FreeKickPlayer = "";
        if(oneTeamFormation.PenaltiesPlayer === heroUid2)
            oneTeamFormation.PenaltiesPlayer = "";
        if(oneTeamFormation.CornerKickPlayer === heroUid2)
            oneTeamFormation.CornerKickPlayer = "";
    }

    if ("" === position2) {
        retCode = this.player.DelHeroAttackAndDefend(heroUid1, teamFormationUid);
        //logger.info("TeamFormations.teamFormationExchangeHero error reason: DelHeroAttackAndDefend heroUid1 failed!");
        //return Code.FAIL;
        //检查被替换的球员是否有被设置定位球球员,如果有从设置中删除
        if(oneTeamFormation.FreeKickPlayer === heroUid1)
            oneTeamFormation.FreeKickPlayer = "";
        if(oneTeamFormation.PenaltiesPlayer === heroUid1)
            oneTeamFormation.PenaltiesPlayer = "";
        if(oneTeamFormation.CornerKickPlayer === heroUid1)
            oneTeamFormation.CornerKickPlayer = "";
    }
    this.player.reCalcAttr(heroUid1);
    this.player.reCalcAttr(heroUid2);
    //重新计算球队实力
    this.reCalcTeamFormationAttrByUid(teamFormationUid);
    return Code.OK;
};

//检查阵容是否有相同球员
TeamFormations.prototype.checkIsHaveSameHero = function(heroUid, teamUid) {
    let oneTeamFormation = this.getOneTeamFormation(teamUid);
    let posObj = oneTeamFormation.PositionToHerosObject;
    let hero = this.player.getOneHero(heroUid);
    let soleId = dataApi.allData.data["Footballer"][hero.ResID].SoleId;
    let isHave = false;
    for(let i in posObj) {
        let heroArr = posObj[i];
        for (let index = 0; index < heroArr.length; index++) {
            let tmpHeroUid = heroArr[index];
            let heroObj = this.player.getOneHero(tmpHeroUid);
            let heroSoleId = dataApi.allData.data["Footballer"][heroObj.ResID].SoleId;
            if(soleId === heroSoleId) {
                isHave = true;
                break;
            }
        }

        if(isHave) {
            break;
        }
    }

    return isHave;
}

//设置初始阵容
TeamFormations.prototype.setCurrTeamFormationId = function(uid) 
{
	if (!uid|| "" === uid) {
		return;
	}

	var oneTeamFormation = this.getOneTeamFormation(uid);
	if(!oneTeamFormation) {
		logger.warn('setCurrTeamFormationId: getOneTeamFormation err uid',uid);
		return;
    }
    
	this.currTeamFormationId = uid;
	this.player.heros.reCalcAllHeroAttr();
	// this.recalcAllTeamFormationAttr();
	// this.reCalcTeamFormationAttrByUid(uid);
};

TeamFormations.prototype.getCurrTeamFormationId = function() {
	return this.currTeamFormationId;
};

//设置联赛阵容
TeamFormations.prototype.setLeagueTeamFormationId = function(uid) {
	if (!uid|| "" === uid) {
		return;
	}
	var oneTeamFormation = this.getOneTeamFormation(uid);
	if(!oneTeamFormation) {
		logger.warn('setLeagueTeamFormationId: getOneTeamFormation err uid!',uid);
		return;
    }
    
	this.leagueTeamFormationId = uid;
};

TeamFormations.prototype.getLeagueTeamFormationId = function() {
	return this.leagueTeamFormationId;
};

//设置信仰之战阵容
TeamFormations.prototype.setWarOfFaithTeamFormationId = function(uid) {
    if (!uid|| "" === uid) {
        return;
    }
    var oneTeamFormation = this.getOneTeamFormation(uid);
    if(!oneTeamFormation) {
        logger.warn('setWarOfFaithTeamFormationId: getOneTeamFormation err uid!',uid);
        return;
    }

    this.warOfFaithTeamFormationId = uid;
    this.reCalcTeamFormationAttrByUid(uid);
};

//获取信仰之战阵容
TeamFormations.prototype.getWarOfFaithTeamFormationId = function() {
    if(this.warOfFaithTeamFormationId === "") {
        this.setWarOfFaithTeamFormationId(this.currTeamFormationId);
        return this.currTeamFormationId;
    }
    return this.warOfFaithTeamFormationId;
};

//检查该阵容是否为联赛专用
TeamFormations.prototype.hasLeagueTeamFormationId = function(uid) 
{
    let has = false;
    var oneObj = this.getOneTeamFormation(uid);
    if (!oneObj){
        return has;
    }

    if (1 == oneObj.IsLeague) {
        has = true;
    }

    return has;
};

//检查阵容中是否专业联赛
TeamFormations.prototype.hasLeagueInList =  function()
{
    for(let [uid, v] of this.allTeamFormations) {
        if (!uid || "" === uid) {
            continue;
        }
        let has = this.hasLeagueTeamFormationId(uid);
        if (has) {
            return true;
        }
    }

    return false;
};

//设置比赛阵容
TeamFormations.prototype.setLeagueTeamFormation = function(uid) 
{
    let has = false;
    let tf = this.getOneTeamFormation(uid);
    if (!tf)
    {
        return has;
    }

    tf.IsLeague = 1;

    has = true;
    return has;
};

TeamFormations.prototype.getLastActualStrength = function()
{
    let tf = this.getOneTeamFormation(this.getCurrTeamFormationId());
    if (!tf)
    {
        return 0;
    }
    
    return tf.ActualStrength;
};

TeamFormations.prototype.getLeagueActualStrength = function() 
{
    let tf = this.getOneTeamFormation(this.getLeagueTeamFormationId());
    if (!tf)
    {
        return 0;
    }
    return tf.ActualStrength;
};


/**
 * 创建新阵容
 */
TeamFormations.prototype.createTeamFromInit = function()
{
    let formationList = this.getTeamFormationList();
	let srcUid = "";
	for(let i = 0; i < formationList.length; ++i) {
		//如果是初始阵容，取其UID 
		if(formationList[i].isInitFormation === 1 && formationList[i].Type === commonEnum.FORMATION_TYPE.COMMON) {
            srcUid = formationList[i].Uid;
            break;		
        }
    }
    
	if (!srcUid || "" === srcUid) {
		logger.error("createTeamFromInit  srcUid failed! srcUid");
		return {code: Code.FAIL, uid: ""};
    }
    
	let src = this.getOneTeamFormation(srcUid);
	
    //创建阵容
    let copyTeam = utils.deepCopy(src);
	let dst = this.addTeamFormation(copyTeam.ResId);
    dst.PositionToHerosObject = copyTeam.PositionToHerosObject;
    let teamCount = this.getTeamCountByType(copyTeam.Type);
    logger.debug("teamCount: ", teamCount, copyTeam.Type, this.allTeamFormations);
    dst.Name = this.getNameByType(teamCount-1, copyTeam.Type);
    logger.debug("dst.Name: ", dst.Name);
    dst.UseTactics = copyTeam.UseTactics;
    dst.UseDefTactics = copyTeam.UseDefTactics;
    dst.Trainers = copyTeam.Trainers;
    dst.isInitFormation = 0;
    
    //设置初始阵容Id
	//this.setCurrTeamFormationId(dst.Uid);

    //重新计算战术加成
    // this.calcHeroTacticsAttr(1);

	//重新计算球队属性
    this.reCalcTeamFormationAttrByUid(dst.Uid);
    return {code: Code.OK, uid: dst.Uid};
};

TeamFormations.prototype.fixNotEnoughTeamFormation = function(uid) {
    let formation = this.getOneTeamFormation(uid);
    let type = formation.Type;
    if(type !== commonEnum.FORMATION_TYPE.GROUND_ATK && type !== commonEnum.FORMATION_TYPE.GROUND_DEF) {
        return ;
    }
    let num = 0;
    for(let k in formation.PositionToHerosObject) {
        for(let m=0; m<formation.PositionToHerosObject[k].length; m++) {
            num++;
        }
    }
    logger.debug("fixNotEnoughTeamFormation uid:", uid, type, num);
    if(num < 11) {
        logger.error("fixNotEnoughTeamFormation need to fix, uid: ", uid);
        let formationList = this.getTeamFormationListByType(type);
        //已上阵的球员
        let onFormationHeros = new Map();
        let notOnFormationHeros = new Map();
        for(let i=0,len=formationList.length; i<len; i++) {
            for(let k in formationList[i].PositionToHerosObject) {
                for(let m=0; m<formationList[i].PositionToHerosObject[k].length; m++) {
                    onFormationHeros.set(formationList[i].PositionToHerosObject[k][m], k);
                }
            }
        }
        for(let [k,v] of this.player.heros.allheros) {
            //过滤上阵球员和退役球员
            if(!onFormationHeros.has(k) && v.isDie !== 1 && TimeUtils.futureDayInterval(v.leftTime) >= 1) {
                notOnFormationHeros.set(k, v);
            }
        }
        let addList = [];
        for(let [k,v] of notOnFormationHeros) {
            addList.push(v);
        }
        let index = 0;
        for(let key in formation.PositionToHerosObject) {
            let destNum = this.getNum(formation.ResId, index);
            let lessNum = destNum - formation.PositionToHerosObject[key].length;
            if(lessNum > 0) {
                for(let i=0;i<lessNum;i++) {
                    let heroUid = addList.pop().Uid;
                    formation.PositionToHerosObject[key].push(heroUid);
                }
            }
            index++;
        }
        //重新计算球队属性
        this.reCalcTeamFormationAttrByUid(formation.Uid);
        //重新计算战术加成
        // this.calcHeroTacticsAttr(1);
    }


};

TeamFormations.prototype.createTeamByNoRepeat = function(type)
{
    if(type !== commonEnum.FORMATION_TYPE.GROUND_ATK && type !== commonEnum.FORMATION_TYPE.GROUND_DEF) {
        logger.debug("createTeamByNoRepeat type param err: ", type);
        return {code: Code.PARAM_FAIL};
    }
    let formationList = this.getTeamFormationListByType(type);
    //已上阵的球员
    let onFormationHeros = new Map();
    let notOnFormationHeros = new Map();

    let initTeamUid;
    for(let i=0,len=formationList.length; i<len; i++) {
        for(let k in formationList[i].PositionToHerosObject) {
            for(let m=0; m<formationList[i].PositionToHerosObject[k].length; m++) {
                onFormationHeros.set(formationList[i].PositionToHerosObject[k][m], k);
            }
        }
        //修复数据
        if(len === 1) {
            formationList[i].isInitFormation = 1;
        }
        if(formationList[i].isInitFormation === 1) {
            initTeamUid = formationList[i].Uid;
        }
    }
    if (!initTeamUid || "" === initTeamUid) {
        logger.error("createTeamFromInit initTeamUid param failed! ");
        return {code: Code.FAIL, uid: ""};
    }
    for(let [k,v] of this.player.heros.allheros) {
        //过滤上阵球员和退役球员
        if(!onFormationHeros.has(k) && v.isDie !== 1 && TimeUtils.futureDayInterval(v.leftTime) >= 1) {
            notOnFormationHeros.set(k, v);
        }
    }
    //人数不够
    if(notOnFormationHeros.size < 11) {
        logger.debug("createTeamByNoRepeat hero num is not enough. left num: ", notOnFormationHeros.size);
        return {code: Code.NOT_ENOUGH_HERO_FAIL};
    }
    let initFormation = this.getOneTeamFormation(initTeamUid);
    let newFormation = this.addTeamFormation(initFormation.ResId, type);
    let teamCount = this.getTeamCountByType(newFormation.Type);
    newFormation.Name = this.getNameByType(teamCount-1, newFormation.Type);
    logger.debug("newFormation.Name: ", newFormation.Name);
    newFormation.UseTactics = initFormation.UseTactics;
    newFormation.UseDefTactics = initFormation.UseDefTactics;
    newFormation.Trainers = initFormation.Trainers;
    newFormation.isInitFormation = 0;

    //球员自动布阵
    let tempHero = [];    //临时存储球员
    let copyObject = utils.deepCopy(newFormation.PositionToHerosObject);
    //teamFormation["ResId"] = resId;                  //配置阵型Id
    for (let key in copyObject){
        copyObject[key] = [];
    }

    let rateList = [];
    for(let [uid, v] of notOnFormationHeros) {
        let hero = this.player.getOneHero(uid);
        if (!hero) {
            continue;
        }
        if(tempHero.length < 1) {
            tempHero.push(uid);
            continue;
        }
        //检查是否有相同球员
        this.isHaveSameHero(tempHero, uid);
    }

    for(let uid in tempHero) {
        for (let key in copyObject) {
            let hero = this.player.getOneHero(uid);
            if (!hero) {
                continue;
            }
            let pos = {};
            pos.rate = this.calcHeroMatchRate(uid, key) * 10;
            pos.key = key;
            pos.uid = uid;
            pos.act = hero["twoLevelAttr"]["BallerRating"].Cur; //球员实力
            rateList.push(pos);
        }
    }

    let tmpheroList = [];
    let index = 1;
    //第一遍先检查是否有100%匹配实力按从大到小排
    rateList.sort(this.compare_func_1);
    let formationResId = newFormation.ResId;
    for(let id in copyObject) {
        let destNum = this.getNum(formationResId, index);
        for(let m = 0; m < destNum; ++m) {
            let delUid = "";
            for(let i = 0; i < rateList.length; ++i) {
                if(rateList[i].key == id && rateList[i].rate == 10) {
                    copyObject[id].push(rateList[i].uid);
                    delUid = rateList[i].uid;
                    //删除数组中的数据
                    let temIndex = tempHero.indexOf(delUid);
                    tempHero.splice(temIndex, 1);
                    break;
                }
            }
            delArrParm(rateList, delUid);
            if(copyObject[id].length >= destNum) {
                break;
            }
        }
        index++;
    }

    //第二遍把剩余的按配表顺序排序
    index = 1;
    for (let id in copyObject){
        let destNum = this.getNum(formationResId, index);
        index++;
        //球员已满
        if(copyObject[id].length >= destNum) {
            continue;
        }
        let leftNum = destNum - copyObject[id].length;
        for(let m = 0; m < leftNum; m++) {
            let rateList = this.rateListSort_1(tempHero, id);
            if( rateList.length > 0) {
                let uid = rateList[0].uid;
                copyObject[id].push(uid);
                tmpheroList.push(uid);
                utils.removeElement(tempHero, uid);
            }
        }
    }
    let PlaceKickResult = this.getFormationPlaceKickAttribute(tmpheroList);
    this.player.newerTask.triggerTask(commonEnum.NEWER_TASK.AUTOMATIC_DISPOSAL);
    newFormation.PositionToHerosObject = copyObject;
    newFormation.FreeKickPlayer = PlaceKickResult.FreeKick.uid;     //任意球球员
    newFormation.PenaltiesPlayer = PlaceKickResult.Penalties.uid;    //点球球员
    newFormation.CornerKickPlayer = PlaceKickResult.CornerKick.uid;   //角球球员

    //重新计算球队属性
    this.reCalcTeamFormationAttrByUid(newFormation.Uid);
    //重新计算战术加成
    // this.calcHeroTacticsAttr(1);

    return {code: Code.OK, uid: newFormation.Uid};
};


/**
 * 设置阵容名字
 * @param uid   阵容uid
 * @param name  阵容名字
 */
TeamFormations.prototype.setFormationName = function(uid, name) {
    let formation = this.getOneTeamFormation(uid);
    if (!formation){
        return Code.FAIL;
    }
    if(name ==="" || !name){
        return Code.FAIL;
    }
    if(formation.Name === name){
        return Code.OK;
    }
    formation.isInitName = 1;
    formation.Name = name;
    return Code.OK;
};

//区分进攻战术还是防守战术
TeamFormations.prototype.atkOrDefTactics = function(resId) {
    if(Math.floor(resId / 100) <= 10) {
        return "atkTactics";
    }else {
        return "defTactics";
    }
};

/**
 * 战术升级
 * @param resId  当前战术id
 * 
 */
TeamFormations.prototype.upgradeTactics = function(resId) {
    let destId = resId + 1;
    let src = dataApi.allData.data["Tactic"][resId];
    let dest = dataApi.allData.data["Tactic"][destId];
    if (!src || !dest) {
        logger.error("upgradeTactics is not found 3");
        return Code.FAIL;
    }
    //获取升级所需物品数量
    let itemCount = this.player.item.getItemCountByResId(src.ItemID);
    let itemUid = this.player.item.getItemUidByResId(src.ItemID);
    if(!itemUid){
        logger.error("upgradeTactics itemUid is fail", src.ItemID, resId);
        return Code.FAIL;
    }
    if (itemCount < src.ItemNumber) {
        logger.error("upgradeTactics itemCount is fail 1", itemCount,src.ItemNumber,itemUid);
        return Code.FAIL;
    }
    //进攻还是防守战术
    let tactics = this.allTactics.get(this.uid);
    let tacticsType = "atkTactics";
    if(this.atkOrDefTactics(resId) === "defTactics") {
        tactics = this.allDefTactics;
        tacticsType = "defTactics";
    }
    //只能一级一级升，防止跳级
    if (destId - tactics[src.Type] !== 1) {
        logger.error("upgradeTactics is fail 2");
        return Code.FAIL;
    }
    //删除物品
    this.player.bag.removeItem(itemUid, src.ItemNumber);
    //战术升级
    tactics[src.Type] = destId;
    //更新阵容所用的战术
    for(let [uid, v] of this.allTeamFormations) {
        let oneTeam = this.getOneTeamFormation(uid);
        let useTactics = oneTeam.UseTactics;
        if(tacticsType === "defTactics") {
            useTactics = oneTeam.UseDefTactics;
        }
        if (dataApi.allData.data["Tactic"][useTactics].Type === dataApi.allData.data["Tactic"][resId].Type){
            if(tacticsType === "defTactics") {
                oneTeam.UseDefTactics = destId;
            }else {
                oneTeam.UseTactics = destId;
            }
            //重新计算属性
            this.reCalcTeamFormationAttrByUid(uid);
        }
    }

    //重新计算球员技能加成
    // this.calcHeroTacticsAttr(1);
    //触发任务
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.TWENTY_EIGHT);
    return Code.OK;
};

/**
 * 战术重置
 * @param resId  当前战术id
 *
 */
TeamFormations.prototype.resetTactics = function(resId, costType) {
    let level = dataApi.allData.data["Tactic"][resId].Level;
    if(level <= 1) {
        logger.debug("resetTactics level is 1 or less", level);
        return Code.OK;
    }
    if(costType !== 1 && costType !== 2) {
        logger.error("resetTactics error costType: ", costType);
        return Code.FAIL;
    }
    //校验该战术是否和用户数据一致
    let isHas = false;
    let ownTacticsList = this.allTactics.get(this.uid);
    for(let index in ownTacticsList) {
        if(ownTacticsList[index] === resId) {
            isHas = true;
            break;
        }
    }
    for(let index in this.allDefTactics) {
        if(this.allDefTactics[index] === resId) {
            isHas = true;
            break;
        }
    }
    if(!isHas) {
        logger.error("resetTactics err param from client.", resId, ownTacticsList);
        return Code.FAIL;
    }
    //重置消耗
    let costConfig = dataApi.allData.data["TacticReset"][level];
    if(costType === 1) {
        let costItemId = costConfig["ResetItemID"];
        //策划定死数量为1
        let costItemNum = 1;
        //获取重置所需物品数量
        let itemCount = this.player.item.getItemCountByResId(costItemId);
        let itemUid = this.player.item.getItemUidByResId(costItemId);
        if (!itemUid) {
            logger.error("resetTactics itemUid is fail");
            return Code.ITEM_FAIL;
        }
        if (itemCount < costItemNum) {
            logger.error("resetTactics itemCount is fail 1", itemCount, costItemNum, costItemId);
            return Code.ITEM_FAIL;
        }
        //删除物品
        this.player.bag.removeItem(itemUid, costItemNum);
    }else if(costType === 2) {
        let costGold = costConfig["ResetCoinNumber"];
        //球币
        if(this.player.gold < costGold) {
            logger.warn("resetTactics gold is not enough. ", this.player.gold, costGold);
            return Code.GOLD_FALL;
        }
        //扣除球币
        this.player.deductMoney(commonEnum.PLAY_INFO.gold, costGold);
    }
    //返还道具列表
    let returnList = {};
    for(let i=level-1;i>0;i--) {
        let config = dataApi.allData.data["Tactic"];
        let tmpResId = resId-i;
        let addItemId = dataApi.allData.data["Tactic"][tmpResId]["ItemID"];
        let addItemNum = dataApi.allData.data["Tactic"][tmpResId]["ItemNumber"];
        if(!returnList[addItemId]) {
            returnList[addItemId] = addItemNum;
        }else {
            returnList[addItemId] += addItemNum;
        }
    }
    for(let k in returnList) {
        let id = k;
        if(typeof k === "string") {
            id = parseInt(k);
        }
        this.player.bag.addItem(id, returnList[k]);
    }
    //战术重置
    let tacticsType = this.atkOrDefTactics(resId);
    let key = dataApi.allData.data["Tactic"][resId].Type.toString();
    let resetResId = resId - level + 1;
    for(let j=0, lens=dataApi.allData.data["Tactic"].length; j<lens; j++) {
        if(dataApi.allData.data["Tactic"][resId].Type === dataApi.allData.data["Tactic"][j].Type && dataApi.allData.data["Tactic"][j].Level === 1)
        resetResId = dataApi.allData.data["Tactic"][j].ID;
    }
    if(tacticsType === "atkTactics") {
        let tactics = this.allTactics.get(this.uid);
        tactics[key] = resetResId;
    }else {
        this.allDefTactics[key] = resetResId;
    }

    //默认战术设置
    for(let [uid, v] of this.allTeamFormations) {
        let oneTeam = this.getOneTeamFormation(uid);
        let useTactics = oneTeam.UseTactics;
        if(tacticsType === "defTactics") {
            useTactics = oneTeam.UseDefTactics;
        }
        if (dataApi.allData.data["Tactic"][useTactics].Type === dataApi.allData.data["Tactic"][resId].Type){
            if(tacticsType === "defTactics") {
                oneTeam.UseDefTactics = resetResId;
            }else {
                oneTeam.UseTactics = resetResId;
            }
            //重新计算属性
            this.reCalcTeamFormationAttrByUid(uid);
        }
    }

    //重新计算加成
    // this.calcHeroTacticsAttr(1);
    return Code.OK;
    /*
    let destId = resId + 1;
    let src = dataApi.allData.data["Tactic"][resId];
    let dest = dataApi.allData.data["Tactic"][destId];
    if (!src || !dest) {
        logger.error("upgradeTactics is not found 3");
        return Code.FAIL;
    }
    //获取升级所需物品数量
    let itemCount = this.player.item.getItemCountByResId(src.ItemID);
    let itemUid = this.player.item.getItemUidByResId(src.ItemID);
    if(!itemUid){
        logger.error("itemUid is fail");
        return Code.FAIL;
    }
    if (itemCount < src.ItemNumber) {
        logger.error("upgradeTactics itemCount is fail 1", itemCount,src.ItemNumber,itemUid);
        return Code.FAIL;
    }
    //进攻还是防守战术
    let tactics = this.allTactics.get(this.uid);
    let tacticsType = "atkTactics";
    if(this.atkOrDefTactics(resId) === "defTactics") {
        tactics = this.allDefTactics;
        tacticsType = "defTactics";
    }
    //只能一级一级升，防止跳级
    if (destId - tactics[src.Type] !== 1) {
        logger.error("upgradeTactics is fail 2");
        return Code.FAIL;
    }
    //删除物品
    this.player.bag.removeItem(itemUid, src.ItemNumber);
    //战术升级
    tactics[src.Type] = destId;
    //更新阵容所用的战术
    for(let [uid, v] of this.allTeamFormations) {
        let oneTeam = this.getOneTeamFormation(uid);
        let useTactics = oneTeam.UseTactics;
        if(tacticsType === "defTactics") {
            useTactics = oneTeam.UseDefTactics;
        }
        if (useTactics === resId){
            if(tacticsType === "defTactics") {
                oneTeam.UseDefTactics = destId;
            }else {
                oneTeam.UseTactics = destId;
            }
            //重新计算属性
            this.reCalcTeamFormationAttrByUid(uid);
            break;
        }
    }
    //触发任务
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.TWENTY_EIGHT);
    return Code.OK;
    */
};


/** 设置阵容使用战术
 * @param uid     阵容uid
 * @param resId   战术id
*/
TeamFormations.prototype.setFormationTactics = function(uid, resId) {
    if (!uid|| "" === uid) {
		return Code.FAIL;
	}
	var oneTeamFormation = this.getOneTeamFormation(uid);
	if(!oneTeamFormation) {
		logger.error('getOneTeamFormation err uid',uid);
		return Code.FAIL;
    }
	//判定是进攻还是防守战术
    let type = this.atkOrDefTactics(resId);
	if(type === "atkTactics") {
        if (oneTeamFormation.UseTactics === resId) {
            return Code.OK;
        }
        //校验resId是否是自己的战术
        let allTactics = this.allTactics.get(this.uid);
        //logger.debug("11111111",  this.allTactics, allTactics, this.uid, uid);
        let isHas = false;
        for(let i in allTactics) {
            if(allTactics[i] === resId) {
                isHas = true;
                break;
            }
        }
        if(!isHas) {
            logger.warn("setFormationTactics error param 1: uid, resId: ", uid, resId, allTactics);
            return Code.FAIL;
        }
        oneTeamFormation.UseTactics = resId;
    }
    else {
        if (oneTeamFormation.UseDefTactics === resId) {
            return Code.OK;
        }
        //校验resId是否是自己的战术
        let isHas = false;
        for(let i in this.allDefTactics) {
            if(this.allDefTactics[i] === resId) {
                isHas = true;
                break;
            }
        }
        if(!isHas) {
            logger.warn("setFormationTactics error param 2: uid, resId: ", uid, resId);
            return Code.FAIL;
        }
        oneTeamFormation.UseDefTactics = resId;
    }
    /*
    if (oneTeamFormation.UseTactics === resId) {
        return Code.OK;
    }
    oneTeamFormation.UseTactics = resId;
    */
    //重新计算属性
    this.reCalcTeamFormationAttrByUid(uid);
    //重新计算加成
    // this.calcHeroTacticsAttr(1);
    return Code.OK;
};

/**
 * 获取战术列表 (进攻)
 */
TeamFormations.prototype.getTacticsList = function() {
    let tacticsList = [];
    let allTactics = this.allTactics.get(this.uid);
    if(!allTactics) {
        return tacticsList;
    }
    for (let i in allTactics) {
        tacticsList.push(allTactics[i]);
    }
    return tacticsList;
};

TeamFormations.prototype.getDefTacticsList = function() {
    let tacticsList = [];
    for(let i in this.allDefTactics) {
        tacticsList.push(this.allDefTactics[i]);
    }
    return tacticsList;
};

//通过heroUid获取hero信息
TeamFormations.prototype.getHeroByHeroUid = function(heroUid) {
    return this.player.heros.getHero(heroUid);
};

//**************************************************消息结构体构造 Begin************************************************************* */
TeamFormations.prototype.makeClientTeamFormation = function(uid) {
    var clientEntry = {};
    var oneObj = this.getOneTeamFormation(uid);
    if (!oneObj){
        return clientEntry;
    }

    //logger.info("makeClientTeamFormation", uid);
    clientEntry.Uid = oneObj.Uid;
    clientEntry.ResId = oneObj.ResId;
    clientEntry.Attack = oneObj.Attack;
    clientEntry.Defend = oneObj.Defend;
    clientEntry.InspireRate = oneObj.InspireRate;
    clientEntry.ActualStrength = oneObj.ActualStrength;
    // clientEntry.Name = oneObj.Name;
    clientEntry.UseTactics = oneObj.UseTactics;
    clientEntry.UseDefTactics = oneObj.UseDefTactics;
    clientEntry.FormationId = oneObj.TeamId;
    clientEntry.TeamId = oneObj.TeamType;
    let trainerUid;
    if(oneObj.Trainers.length > 0) {
        trainerUid = oneObj.Trainers[0].uid;  //主教练
    }
    let trainer = this.player.trainer.getOneTrainer(trainerUid);
    if(!trainer) {
        clientEntry.TrainersResId = 0;
    }else {
        clientEntry.TrainersResId = trainer.ResId;
    }

    var ClientSkillList =[];
    var skillList = oneObj["ScenceUse"];
    for (let i = 0; i < skillList.length; i++) {
        const skillId = skillList[i];
        ClientSkillList.push(skillId);
    }
    clientEntry.ScenceUse = ClientSkillList;

    var clientPositionToHeros = [];
    var positionToHerosObject = oneObj["PositionToHerosObject"];
    var index = 0;
    for (var key in positionToHerosObject) {
        const obj = positionToHerosObject[key];
        clientPositionToHeros[index] = {};
        clientPositionToHeros[index].Position = key;
        //logger.info("makeClientTeamFormation", uidList,  obj.length);
        clientPositionToHeros[index].HeroList = [];
        clientPositionToHeros[index].HeroResIdList = [];
        let idx = 0;
        for(let m=0; m < obj.length; m++) {
            let heroUid = obj[m];
            let heroObj = this.player.heros.getHero(heroUid);
            if (!heroObj)
            {
                logger.info("makeClientTeamFormation: heroUid", heroUid, heroObj);
                continue;
            }

            let resId = heroObj.ResID;
            clientPositionToHeros[index].HeroResIdList.push(resId);
            clientPositionToHeros[index].HeroList[idx] = {};
            clientPositionToHeros[index].HeroList[idx].uid = heroUid;
            clientPositionToHeros[index].HeroList[idx].strength = this.player.heros.calcHeroInFormationRate(heroUid, key, uid);
            //logger.error("uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu----", clientPositionToHeros[index].HeroList[idx].strength);
            clientPositionToHeros[index].HeroList[idx].star = heroObj.Star;
            idx++;
        }

        //logger.info("=======================::::::::::"+key+":::"+obj);
        index++;
    }
    clientEntry.PositionToHeros = clientPositionToHeros;
    clientEntry.ServerType = oneObj.Type;
    //logger.debug("makeClientTeamFormation clientEntry:", clientEntry);
    return clientEntry;
};

TeamFormations.prototype.makeClientTeamFormationList = function(serverType) {
    var clientList = [];
    let index = 0;
    for(let [uid, v] of this.allTeamFormations) {
        if (!uid || "" === uid) {
            continue;
        }
        if(serverType === commonEnum.FORMATION_TYPE.ALL_TYPE || v.Type === serverType || (serverType === commonEnum.FORMATION_TYPE.GROUND &&
            (v.Type === commonEnum.FORMATION_TYPE.GROUND_ATK || v.Type === commonEnum.FORMATION_TYPE.GROUND_DEF))) {
            let clientEntry = this.makeClientTeamFormation(uid);
            clientList[index] = clientEntry;
            index += 1;
        }
        //logger.info("makeClientTeamFormationList uid", uid);
    }
    //logger.info("makeClientTeamFormationList", clientList);
    return clientList;
};

TeamFormations.prototype.getCurrTeamFormationData = function() {
    let clientEntry = [];
    var uid = this.player.getCurrTeamFormationId();
    if (!uid || "" === uid) {
        return clientEntry;
    }

    clientEntry = this.makeClientTeamFormation(uid);
    return clientEntry;
};

//获取某一属性最高的球员
TeamFormations.prototype.getBestAttrHeroInTeam = function (formationUid, attrName) {
    let formation = this.getOneTeamFormation(formationUid);
    let maxAttr = 0;
    let bestHeroUid = "";
    for(let key in formation.PositionToHerosObject) {
        for(let i = 0, len = formation.PositionToHerosObject[key].length; i < len; i++) {
            //门将除外
            if(key === "GK") continue;
            let heroUid = formation.PositionToHerosObject[key][i];
            let hero = this.player.heros.getHero(heroUid);
            //logger.debug("hero resId, attrName, hero", hero.ResID, attrName, hero);
            let tmpAttr = hero.oneLevelAttr[attrName].Cur;
            //logger.debug("resId: ", hero.ResID);
            //logger.debug("hero tmpAttr", tmpAttr);
            if(maxAttr === 0) {
                maxAttr = tmpAttr;
                bestHeroUid = heroUid;
            }
            else if(maxAttr < tmpAttr) {
                maxAttr = tmpAttr;
                bestHeroUid = heroUid;
            }
        }
    }
    return bestHeroUid;
};
//获取阵容设置的定位球球员
TeamFormations.prototype.getPlaceKickAttribute = function (formationUid, PlaceName)
{
    //logger.error("获取阵容设置的定位球球员----getPlaceKickAttribute");
    let formation = this.getOneTeamFormation(formationUid);
    return formation[PlaceName];
};

//**************************************************消息结构体构造 End************************************************************* */


TeamFormations.prototype.makeClientTeamFormationHeroRateList = function() {
    var clientList = [];
    let index = 0;
    for(let [uid, v] of this.allTeamFormations) {
        if (!uid || "" === uid) {
            continue;
        }
        //logger.info("makeClientTeamFormationList uid", uid);
        let clientEntry = this.makeClientTeamFormationRate(uid);
        clientList[index] = clientEntry;
        index++;
    }
    // logger.error("makeClientTeamFormationList", clientList);
    return clientList;
};

TeamFormations.prototype.makeClientTeamFormationRate = function(uid) {
    let clientEntry = {};
    let oneObj = this.getOneTeamFormation(uid);
    if (!oneObj){
        return clientEntry;
    }

    clientEntry.allPosition = [];
    let positionToHerosObject = oneObj["PositionToHerosObject"];
    let index = 0;
    for (let key in positionToHerosObject) {
        let heroArr = positionToHerosObject[key];
        clientEntry.allPosition[index] = {};
        clientEntry.allPosition[index].position = key;
        clientEntry.allPosition[index].heroList = [];
        let idx = 0;
        for(let j = 0; j < heroArr.length; ++j){
            let heroUid = heroArr[j];
            clientEntry.allPosition[index].heroList[idx] = {};
            clientEntry.allPosition[index].heroList[idx].uid = heroUid;
            clientEntry.allPosition[index].heroList[idx].strength = this.player.heros.calcHeroInFormationRate(heroUid, key, uid);
            idx++;
        }
        index++;
    }
    return clientEntry;
};

//计算球队身价
TeamFormations.prototype.calcTeamValue = function(uid) {
    //获取球队所有球员之和
    let formation = this.getOneTeamFormation(uid);
    if (!formation){
        return 0;
    }
    let value = 0;
    for (let key in formation.PositionToHerosObject) {
        let heroArr = formation.PositionToHerosObject[key];
        for(let j = 0; j < heroArr.length; ++j){
            let heroUid = heroArr[j];
            let heroObj = this.player.heros.getHero(heroUid);
            if (!heroObj)
            {
                logger.info("calcTeamValue: heroUid", heroUid, heroObj);
                continue;
            }

            let resId = heroObj.ResID;
            //var config = dataApi.allData.data["Footballer"][resId];
            let config;
            let bead = resId / 10000;
            if(bead < 9)
            {
                config = dataApi.allData.data["Footballer"][resId];
            }
            else
            {
                config = dataApi.allData.data["FootballerPve"][resId];
            }

            if(!!config) {
                value += config.Value;
            }
        }
    }
    if(this.player.level <= 10) {
        let teamPrice = Math.floor(value*0.7);
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.TWENTY, 0, 0, teamPrice);
        return teamPrice;
    }
    else {
        let teamPrice = Math.floor(value*0.7);
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.TWENTY, 0, 0, teamPrice);
        return teamPrice;
    }
};


/******************************************************教练团队****************************************************************/
/**
 * 解锁教练团格子
 */
TeamFormations.prototype.unlockTrainersPos = function (teamUid) {
    let teamObj = this.getOneTeamFormation(teamUid);
    if (!teamObj) {
        return;
    }

    let needLevel = 0;
    let level = this.player.level;
    for (let i = 0; i < teamObj.Trainers.length; ++i) {
        switch (i) {
            case 0:
                needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_1].Param;
                if (level >= needLevel && teamObj.Trainers[i].status === 1) {
                    teamObj.Trainers[i].status = 2;
                }
                break;
            case 1:
                needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_2].Param;
                if (level >= needLevel && teamObj.Trainers[i].status === 1) {
                    teamObj.Trainers[i].status = 2;
                }
                break;
            case 2:
                needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_3].Param;
                if (level >= needLevel && teamObj.Trainers[i].status === 1) {
                    teamObj.Trainers[i].status = 2;
                }
                break;
            case 3:
                needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_4].Param;
                if (level >= needLevel && teamObj.Trainers[i].status === 1) {
                    teamObj.Trainers[i].status = 2;
                }
                break;
            case 4:
                needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_5].Param;
                if (level >= needLevel && teamObj.Trainers[i].status === 1) {
                    teamObj.Trainers[i].status = 2;
                }
                break;
            case 5:
                needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_6].Param;
                if (level >= needLevel && teamObj.Trainers[i].status === 1) {
                    teamObj.Trainers[i].status = 2;
                }
                break;
            default:
                break;
        }
    }
}

/**获取当前阵容的教练信息
 *
 * @param uid
 * @returns {{code: number, trainerList: Array, allActual: number}}
 */
TeamFormations.prototype.getCurrTeamFormationTrainer = function(teamUid) {
    let ret = {code :Code.OK, trainerList: [], allActual: 0};
    this.unlockTrainersPos(teamUid);
    let teamObj = this.getOneTeamFormation(teamUid);
    if(!teamObj) {
        return ret;
    }
    let trainerList = [];
    for (let i in teamObj.Trainers) {
        let uid = teamObj.Trainers[i].uid;
        let type = teamObj.Trainers[i].type;
        let status = teamObj.Trainers[i].status;
        let obj = {
            uid: uid,
            type: type,
            resId: 0,
            level: 0,
            star: 0,
            actual: 0,
            skillId: 0,
            status: status
        }
        if(uid === "") {
            trainerList.push(obj);
        }else {
            let trainer = this.player.trainer.getOneTrainer(uid);
            if(!trainer) {
                continue;
            }
            obj.resId = trainer.ResId;
            obj.level = trainer.Level;
            obj.star = trainer.Star;
            obj.actual = this.calcTrainerActual(trainer.Actual, type);;
            obj.skillId = trainer.SkillId;
            trainerList.push(obj);
        }
    }
    ret.trainerList = trainerList;
    ret.allActual = this.calcTotalTrainerActual(teamUid);
    return ret;
}

//获取当前阵容教练战术加成  type  战术类型
TeamFormations.prototype.getCurrFormationTrainerTacticsAttr = function(type) {
    let teamObj = this.getOneTeamFormation(this.currTeamFormationId);
    let addLevel = 0;   //加成等级
    //进攻加成
    // let useTactics = teamObj.UseTactics;
    // let tacticsConfig = dataApi.allData.data["Tactic"][useTactics];
    // let useDefTactics = teamObj.UseDefTactics;
    // let defTacticsConfig = dataApi.allData.data["Tactic"][useDefTactics];

    for (let i in teamObj.Trainers) {
        let uid = teamObj.Trainers[i].uid;
        let teamType = teamObj.Trainers[i].type;
        if(uid !== "") {
            let trainer = this.player.trainer.getOneTrainer(uid);
            if(!trainer) {
                continue;
            }
            addLevel += this.calcTrainerTacticsAttr(trainer, teamType, type)
        }

    }
    return addLevel;
}

//计算教练战术加成  进攻、防守教练才享受战术加成
TeamFormations.prototype.calcTrainerTacticsAttr = function(trainer, teamType, type) {
    let addLevel = 0;
    switch (teamType) {
        case 2:
            if(trainer.Tactics.length < 1) {
                break;
            }
            if(trainer.Tactics[0].type === type) {
                addLevel += trainer.Tactics[0].level;
            }
            break;
        case 3:
            if(trainer.Tactics.length < 2) {
                break;
            }
            if(trainer.Tactics[1].type === type) {
                addLevel += trainer.Tactics[1].level;
            }
            break;
        default:
            break;
    }
    return addLevel;
}

//计算教练技能加成
TeamFormations.prototype.calcTrainerSkillAttr = function(heroUid, teamUid) {
    let teamObj = this.getOneTeamFormation(teamUid);
    if(!teamObj) {
        return;
    }
    let hero = this.player.getOneHero(heroUid);
    if(!hero) {
        return;
    }

    let canAttrId = Math.floor(teamObj.ResId / 1000);
    // logger.error("阵型---------------------", canAttrId, teamObj.ResId, typeof(teamObj.ResId))
    let sum = {};  //所有教练技能加成总和
    for (let i in teamObj.Trainers) {
        let uid = teamObj.Trainers[i].uid;
        let type = teamObj.Trainers[i].type;
        //主、助理教练享受技能加成
        if((type !== 2 || type !== 3) && uid !== "") {
            let trainer = this.player.trainer.getOneTrainer(uid);
            if(!trainer) {
                continue;
            }
            let skillId = trainer.SkillId;
            let config = dataApi.allData.data["CoachSkill"][skillId];
            if(!config) {
                continue;
            }

            //检查当前阵型是否有加成  1任意阵型都有加成
            if(config.FormationId !== 1 && canAttrId !== config.FormationId) {
                continue;
            }

            //检查球员是否有加成
            if(!this.checkHeroIsHaveTrainerSkillAttr(heroUid, skillId)) {
                continue;
            }

            //加成值
            let attrValue = [];
            //加成类型
            let attrType = [];
            for(let j = 1; j < 19; ++j) {
                attrType.push(config["AddType" + j]);
                attrValue.push(config["AddValue" + j]);
            }

            for(let key in commonEnum.ONE_LEVEL_ATTR_NAMES) {
                if(key !== "ResistanceDamage") {
                    if(typeof (sum[key]) === "undefined") {
                        sum[key] = 0;
                    }
                    let val = commonEnum.ONE_LEVEL_ATTR_NAMES[key];
                    let attrInfo = hero.oneLevelAttr[key];
                    for(let n = 0; n < attrType.length; ++n) {
                        if(attrType[n] === val) {
                            sum[key] += attrInfo.Base * attrValue[n] / 10000;
                        }
                    }
                }
            }
        }
    }

    for(let id in commonEnum.ONE_LEVEL_ATTR_NAMES) {
        if(id !== "ResistanceDamage") {
            if(hero.oneLevelAttr[id].TrainerSkillAtt !== 0) {
                hero.oneLevelAttr[id].TrainerSkillAtt = 0;
            }

            if(typeof (sum[id]) !== "undefined") {
                hero.oneLevelAttr[id].TrainerSkillAtt = sum[id];
            }
        }
    }
}

//检查球员是否享有教练技能加成
TeamFormations.prototype.checkHeroIsHaveTrainerSkillAttr = function(uid, skillId) {
    let config = dataApi.allData.data["CoachSkill"][skillId];
    let hero = this.player.getOneHero(uid);
    let heroConfig;
    let resId = hero.ResID;
    let bead = resId / 10000;
    if(bead < 9) {
        heroConfig = dataApi.allData.data["Footballer"][resId];
    } else {
        heroConfig = dataApi.allData.data["FootballerPve"][resId];
    }

    let isHave = false;
    for(let j = 1; j < 13; ++j) {
        let addPosition = config["AddPosition" + j];
        if(heroConfig.Position1 === addPosition) {
            isHave = true;
            break;
        }
    }
    return isHave;
}

/**
 * 添加教练
 * @param uid        教练uid
 * @param index      位置
 * @param teamUid    队伍uid
 * @
 */
TeamFormations.prototype.addTrainInTransfer = function (uid, index, teamUid) {
    let ret = {
        code: Code.FAIL,
        allActual: 0,
        trainerList: []
    }
    if (index < 0 || index > 5) {
        ret.code = Code.RANGE_FAIL;
        return ret;
    }

    if(!teamUid) {
        ret.code = Code.PARAM_FAIL;
        return ret;
    }

    let teamObj = this.getOneTeamFormation(teamUid);
    if(!teamObj) {
        ret.code = Code.FAIL;
    }

    //未解锁
    if (teamObj.Trainers[index].status === 1) {
        ret.code = Code.UNLOCK_FAIL;
        return ret;
    }

    //检查其他位置是否有此教练
    let other = this.checkTrainerInOtherPos(teamObj, uid);
    //如果有
    if(other.index !== -1){
        let temp = {
            uid: teamObj.Trainers[index].uid,
            status: teamObj.Trainers[index].status,
        }

        teamObj.Trainers[index].uid = other.uid;
        teamObj.Trainers[index].status = other.status;
        teamObj.Trainers[other.index].uid = temp.uid;
        teamObj.Trainers[other.index].status = temp.status; //状态  1锁住  2可添加  3已有人
    }else {
        teamObj.Trainers[index].uid = uid;
        teamObj.Trainers[index].status = 3; //状态  1锁住  2可添加  3已有人
    }

    this.player.trainer.calcAllTrainerAttr();
    // this.player.heros.reCalcAllHeroAttr();
    this.reCalcTeamFormationAttrByUid(teamUid);
    ret.code = Code.OK;
    ret.allActual = this.calcTotalTrainerActual(teamUid);
    ret.trainerList = this.getCurrTeamFormationTrainer(teamUid).trainerList;
    return ret;
}

//计算全部教练实力
TeamFormations.prototype.calcTotalTrainerActual = function(teamUid) {
    let teamObj = this.getOneTeamFormation(teamUid);
    if(!teamObj) {
        return 0;
    }

    let sum = 0;
    for (let i in teamObj.Trainers) {
        let uid = teamObj.Trainers[i].uid;
        let teamType = teamObj.Trainers[i].type;
        if(uid !== "") {
            let trainer = this.player.trainer.getOneTrainer(uid);
            if (!trainer) {
                continue;
            }
            sum += this.calcTrainerActual(trainer.Actual, teamType);
        }
    }
    return Math.floor(sum);
};

//根据教练位置算实力  type 1 主教练  type 2 进攻教练  type 3 防守教练
TeamFormations.prototype.calcTrainerActual = function(actual, type) {
    let addNum = 0;
    switch (type) {
        case 1:
            addNum = actual * 1;
            break;
        case 2:
            addNum = actual * 0.8;
            break;
        case 3:
            addNum = actual * 0.8;
            break;
        default:
            addNum = actual * 0.5;
            break;
    }
    return Math.floor(addNum);  //教练实力
}

/**
 * 检查上场教练是否已在其他位置上
 * @param uid    教练uid
 */
TeamFormations.prototype.checkTrainerInOtherPos = function (teamObj, uid) {
    let info  = {
        uid:"",
        index: -1,
        status: 1,
    };

    for(let i = 0; i < teamObj.Trainers.length; ++i){
        if(uid === teamObj.Trainers[i].uid){
            info.uid = teamObj.Trainers[i].uid;
            info.status = teamObj.Trainers[i].status;
            info.index = i;
        }
    }
    return info;
}

//计算教练属性加成
TeamFormations.prototype.calcHeroTrainerAttr = function(heroUid, teamUid) {
    let hero = this.player.getOneHero(heroUid);
    let teamObj = this.getOneTeamFormation(teamUid);
    if(!teamObj) {
        return;
    }
    let sum = {};
    for (let i in teamObj.Trainers) {
        let uid = teamObj.Trainers[i].uid;
        let teamType = teamObj.Trainers[i].type;
        if(uid !== "") {
            let trainer = this.player.trainer.getOneTrainer(uid);
            if(!trainer) {
                continue;
            }

            //根据位置算相应的加成
            this.calcTrainerPropertyAttr(sum, trainer.OneLevelAttr, teamType);
        }
    }


    for(let key in commonEnum.ONE_LEVEL_ATTR_NAMES){
        if(key !== "ResistanceDamage") {
            if( hero.oneLevelAttr[key].Trainer !== 0) {
                hero.oneLevelAttr[key].Trainer = 0;
            }

            if(typeof (sum[key]) !== "undefined") {
                hero.oneLevelAttr[key].Trainer += sum[key];
            }
        }
    }

};

//根据教练位置计算属性加成
TeamFormations.prototype.calcTrainerPropertyAttr = function(obj, OneLevelAttr, type) {
    for(let j in OneLevelAttr){
        if(j !== "ResistanceDamage") {
            if(typeof (obj[j]) === "undefined") {
                obj[j] = 0;
            }
            let addNum = 0;
            switch (type) {
                case 1:
                    addNum = OneLevelAttr[j] * 1;
                    obj[j] += addNum;
                    break;
                case 2:
                    addNum = OneLevelAttr[j] * 0.8;
                    obj[j] += addNum;
                    break;
                case 3:
                    addNum = OneLevelAttr[j] * 0.8;
                    obj[j] += addNum;
                    break;
                default:
                    addNum = OneLevelAttr[j] * 0.5;
                    obj[j] += addNum;
                    break;
            }
        }
    }
}

//获取当前队伍的阵容Id
TeamFormations.prototype.getCurrTeamTeamId = function() {
    let teamId = 0;
    for(let [k, v] of this.allTeamFormations) {
        if(this.currTeamFormationId === k) {
            teamId = v.TeamId;
            break;
        }
    }
    return teamId;
}

//检查队伍是否在主力阵容中的队伍
TeamFormations.prototype.checkIsMainTeamByUid = function(uid) {
    let isExist = false;
    let teamId = this.getCurrTeamTeamId();
    if(teamId === 0) {
        return isExist;
    }

    for(let [k, v] of this.allTeamFormations) {
        if(teamId === v.TeamId && uid === v.Uid) {
            isExist = true;
            break;
        }
    }

    return isExist;
}

//获取球员所在主阵容的队伍uid
TeamFormations.prototype.getHeroInMainTeamUid = function(uid) {
    let teamUid = "";
    for(let [k, v] of this.allTeamFormations) {
        let PositionToHerosObject = v.PositionToHerosObject;
        for(let i in PositionToHerosObject) {
            for(let j in PositionToHerosObject[i]) {
                if(PositionToHerosObject[i][j] === uid) {
                    teamUid = v.Uid;
                    break;
                }
            }

            if(this.checkIsMainTeamByUid(teamUid)) {
                break;
            }else {
                teamUid = "";
            }
        }

        if(this.checkIsMainTeamByUid(teamUid)) {
            break;
        }else {
            teamUid = "";
        }
    }
    return teamUid;
}



/** 获取赛事阵容（改版后）
 */
TeamFormations.prototype.getMatchTeamFormation = function() {
    let arr = [];
    let league = this.getOneTeamFormation(this.getLeagueTeamFormationId());
    if(!!league) {
        let leagueTeamId = league.TeamId;
        let matchInfo = {
            matchType: commonEnum.MATCH_TYPE.PvpLeague,
            teamId: leagueTeamId
        }
        arr.push(matchInfo);
    }

    let warOfFaith = this.getOneTeamFormation(this.getWarOfFaithTeamFormationId());
    if(!!warOfFaith) {
        let warOfFaithTeamId = warOfFaith.TeamId;
        let matchInfo = {
            matchType: commonEnum.MATCH_TYPE.PvpWarOfFaith,
            teamId: warOfFaithTeamId
        }
        arr.push(matchInfo);
    }

    return arr;
}

/** 设置赛事阵容（改版后）
 * @param  {} matchType 赛事类型
 * @param  {} teamId 阵容id
 */
TeamFormations.prototype.setMatchTeamFormation = function(matchType, teamId) {
    let uid = "";
    for(let [k, v] of this.allTeamFormations) {
        if(v.TeamId === teamId && v.TeamType === 1) {
            uid = v.Uid;
            break;
        }
    }
    if(!uid) {
        return Code.PARAM_FAIL;
    }

    switch (matchType) {
        case commonEnum.MATCH_TYPE.PvpLeague:
            this.setLeagueTeamFormationId(uid);
            break;
        case commonEnum.MATCH_TYPE.PvpWarOfFaith:
            this.setWarOfFaithTeamFormationId(uid);
            break;
        default:
            break;
    }
    return Code.OK;
}


/** 删除一个队伍（改版后）
 * @param  {} uid 阵容uid
 *
 */
TeamFormations.prototype.delTeamFormationByUid = function(uid) {
    let teamObj = this.allTeamFormations.get(uid);
    if(!teamObj) {
        return Code.FAIL;
    }

    let initId = this.getCurrTeamFormationId();
    let leagueUid = this.getLeagueTeamFormationId();
    let warUid = this.getWarOfFaithTeamFormationId();

    if(uid === initId) {
        return Code.USE_TEAM_FAIL;
    }

    if(uid === leagueUid) {
        return Code.NOT_DEL_TEAM;
    }

    if(uid === warUid) {
        return Code.NOT_DEL_WAR_TEAM;
    }

    if(teamObj.isInitFormation === 1) {
        return Code.INIT_TEAM_FAIL;
    }

    this.allTeamFormations.delete(uid);
    return Code.OK;
}

/** 删除一套阵容（改版后）
 * @param  {} teamId 阵容id
 *
 */
TeamFormations.prototype.delTeamFormationByTeamId = function(teamId) {
    if(teamId === 1) {
        return Code.FAIL;
    }
    let list = this.getTeamFormationListById(teamId);
    if(list.length < 1) {
        return Code.PARAM_FAIL;
    }
    let initId = this.getCurrTeamFormationId();
    let leagueUid = this.getLeagueTeamFormationId();
    let warUid = this.getWarOfFaithTeamFormationId();
    let isNowTeam = false, isLeagueTeam = false, isWarTeam = false, isInitTeam = false;
    for(let i = 0; i < list.length; ++i) {
        let teamObj = this.allTeamFormations.get(list[i].Uid);
        //是否当前阵容
        if(list[i].Uid === initId) {
            isNowTeam = true;
            break;
        }

        //是否联赛阵容
        if(list[i].Uid === leagueUid) {
            isLeagueTeam = true;
            break;
        }

        //是否信仰争夺战阵容
        if(list[i].Uid === warUid) {
            isWarTeam = true;
            break;
        }

        //是否是初始阵容
        if(teamObj.isInitFormation === 1) {
            isInitTeam = true;
            break;
        }
    }

    if(isNowTeam) {
        return Code.USE_TEAM_FAIL;
    }

    if(isLeagueTeam) {
        return Code.NOT_DEL_TEAM;
    }

    if(isWarTeam) {
        return Code.NOT_DEL_WAR_TEAM;
    }

    if(isInitTeam) {
        return Code.INIT_TEAM_FAIL;
    }

    for(let j = 0; j < list.length; ++j) {
        this.allTeamFormations.delete(list[j].Uid);
    }
    return Code.OK;
}
