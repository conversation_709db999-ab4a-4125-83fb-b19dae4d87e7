/**
 * Idea and Persist
 * Created by June on 2019/4/9.
 */

let logger = require('pomelo-logger').getLogger(__filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let async = require('async');

let utils = require('../../util/utils');
let Room = require('../entities/room');
let timeUtils = require('../../util/timeUtils');
let commonEnum = require('../../../../shared/enum');
let dataApi = require('./../../util/dataApi');
//let Player = require('../entities/player');

module.exports.create = function(app){
    return new BattleService(app);
};

let BattleService = function(app){
    EventEmitter.call(this);
    this.app = app;
    this.roomList = new Map();      //roomUid, roomInfo
};

util.inherits(BattleService, EventEmitter);

BattleService.prototype.initPveBattle = function(msg, battleType, cb) {
    //创建战斗房间
    let roomUid = utils.syncCreateUid();
    let room = new Room(roomUid);
    //初始化
    room.pveInit(msg, battleType);
    //添加房间到Cache, 战斗结束后清除房间
    this.roomList.set(roomUid, room);
    cb(Code.OK, roomUid);
};

BattleService.prototype.pvpMatchBattle = function(msg, cb) {
    //参数验证
    if(!msg.home || !msg.away) {
        logger.warn("pvpMatchBattle param error, msg:", msg);
        return cb(Code.FAIL);
    }
    //创建战斗房间
    let roomUid = utils.syncCreateUid();
    let room = new Room(roomUid);
    //初始化
    room.pvpInit(msg, commonEnum.BATTLE_TYPE.PvpMatch);
    //添加房间到Cache, 战斗结束后清除房间
    this.roomList.set(roomUid, room);
    //战斗计算
    //1.rpc -> game 获取用户数据
    let self = this;
    async.waterfall([
        function(callback){
            let sessionHome = {frontendId: self.app.getServerId(), toServerId: msg.homeGid};
            self.app.rpc.game.entryRemote.getBattleData(sessionHome, {playerId: msg.home}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getBattleData error: ", err, msg.home);
                    return callback(Code.FAIL);
                }
                room.initPlayerByBattleData("teamA", ret.battleData);
                callback(null);
            });
        },
        function(callback){
            let sessionAway = {frontendId: self.app.getServerId(), toServerId: msg.awayGid};
            self.app.rpc.game.entryRemote.getBattleData(sessionAway, {playerId: msg.away}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getBattleData error: ", err, msg.away);
                    return callback(Code.FAIL);
                }
                room.initPlayerByBattleData("teamB", ret.battleData);
                callback(null);
            });
        }
    ], function(error){
        if(error){
            return cb(Code.FAIL);
        }
        //2.计算前初始化其他战斗数据
        room.initPVPLeagueBattleOtherData();
        //3.计算战斗结果
        let result = room.calcBattleResult();
        let homeFansChangeNum = result.battleEndInfo.businessReward.homeReward.FansChangeNum;
        let awayFansChangeNum = result.battleEndInfo.businessReward.awayReward.FansChangeNum;
        cb(Code.OK, {home: room.teamA.playerId, away: room.teamB.playerId, homeScore: result.battleEndInfo.stInfos[0].shotNum,
            awayScore: result.battleEndInfo.stInfos[1].shotNum, beginTime: timeUtils.now(), roomUid: roomUid, homeFansChangeNum: homeFansChangeNum, awayFansChangeNum: awayFansChangeNum}, result);
    });
};

BattleService.prototype.pvpLeagueBattle = function(msg, cb) {
    //参数验证
    if(!msg.home || !msg.away || !msg.homeBattleData || !msg.awayBattleData) {
        logger.warn("pvpLeagueBattle param error, msg:", msg);
        return cb(Code.FAIL);
    }
    //创建战斗房间
    let roomUid = utils.syncCreateUid();
    let room = new Room(roomUid);
    //初始化
    room.pvpInit(msg, commonEnum.BATTLE_TYPE.PvpLeague);
    //添加房间到Cache, 战斗结束后清除房间
    this.roomList.set(roomUid, room);
    //战斗计算
    //1.用户数据初始化
    room.initPlayerByBattleData("teamA", msg.homeBattleData);
    room.initPlayerByBattleData("teamB", msg.awayBattleData);
    //2.计算前初始化其他战斗数据
    room.initPVPLeagueBattleOtherData(msg);
    //3.计算战斗结果
    let result = room.calcBattleResult();
    cb(Code.OK, {home: room.teamA.playerId, away: room.teamB.playerId, homeScore: result.battleEndInfo.stInfos[0].shotNum,
        awayScore: result.battleEndInfo.stInfos[1].shotNum, roomUid: roomUid}, result);
};

BattleService.prototype.testBattle = function(sessionId, msg, cb) {
    logger.debug('BattleService ---- testBattle ------  msg:',msg);
    //创建战斗房间
    let roomUid = utils.syncCreateUid();
    let room = new Room(roomUid);
    //测试战斗初始化
    room.initByTestConfig();
    //战斗计算
    let result = room.calcBattleResult();
    cb(Code.OK, result, roomUid);
};

//PVE战斗通用服务, 内部已通过room.battleType战斗类型去做区分处理
BattleService.prototype.PveBattle = function(msg, cb) {
    logger.debug('BattleService ---- PveBattle ------  msg:',msg);
    //1.校验
    let room = this.roomList.get(msg.roomUid);
    if(!room) {
        logger.warn('BattleService-PveBattle-warn: msg.roomUid error', msg.roomUid);
        return cb(Code.FAIL);
    }
    if(msg.playerId !== room.teamA.playerId) {
        logger.warn('BattleService-PveBattle-warn: msg.playerId error', msg.playerId);
        return cb(Code.FAIL);
    }
    //2.rpc -> game 获取用户数据
    let self = this;
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.game.entryRemote.getBattleData(session, {playerId: msg.playerId}, function (err, ret) {
        if(!!err) {
            logger.debug("rpc getBattleData error: ", err);
            return cb(Code.FAIL);
        }
        room.initPveBattleData(ret.battleData);
        //计算战斗结果
        let result = room.calcBattleResult();
        //其他数据
        cb(Code.OK, result, room.teamA.playerId, room.teamB.teamResId, room.roomUid);
    });
};

BattleService.prototype.PVELeagueCopyBattle = function(msg, cb) {
    //logger.debug('BattleService ---- PVELeagueCopyBattle ------  msg:',msg);
    //1.校验
    let room = this.roomList.get(msg.roomUid);
    if(!room) {
        logger.warn('BattleService-PVELeagueCopyBattle-warn: msg.roomUid error', msg.roomUid);
        return cb(Code.FAIL);
    }
    if(msg.playerId !== room.teamA.playerId) {
        logger.warn('BattleService-PVELeagueCopyBattle-warn: msg.playerId error', msg.playerId);
        return cb(Code.FAIL);
    }
    //2.rpc -> game 获取用户数据
    let self = this;
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.game.entryRemote.getBattleData(session, {playerId: msg.playerId}, function (err, ret) {
        if(!!err) {
            logger.debug("rpc getBattleData error: ", err);
            return cb(Code.FAIL);
        }
        room.initPveBattleData(ret.battleData);
        //计算战斗结果
        let result = room.calcBattleResult();
        //其他数据
        cb(Code.OK, result, room.teamA.playerId, room.teamB.teamResId, room.roomUid, room.battleType);
    });
};

BattleService.prototype.deleteBattleRoom = function(roomUid) {
    if(this.roomList.has(roomUid)) {
        this.roomList.delete(roomUid);
    }
    else {
        logger.debug('deleteBattleRoom fail. not found roomUid:', roomUid);
    }
};

//懂球杯赛战斗
BattleService.prototype.pvpDqCupBattle = function(msg, cb) {
    //参数验证
    if(!msg.home || !msg.away) {
        logger.warn("pvpDqCupBattle param error, msg:", msg);
        return cb(Code.FAIL);
    }
    //创建战斗房间
    let roomUid = utils.syncCreateUid();
    let room = new Room(roomUid);
    //初始化
    room.pvpInit(msg, commonEnum.BATTLE_TYPE.PvpDqCupMatch);
    //添加房间到Cache, 战斗结束后清除房间
    this.roomList.set(roomUid, room);
    //战斗计算
    //1.rpc -> game 获取用户数据
    let self = this;
    async.waterfall([
        function(callback){
            let sessionHome = {frontendId: self.app.getServerId(), toServerId: msg.homeGid};
            self.app.rpc.game.entryRemote.getBattleData(sessionHome, {playerId: msg.home}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getBattleData error: ", err, msg.home);
                    return callback(Code.FAIL);
                }
                room.initPlayerByBattleData("teamA", ret.battleData);
                callback(null);
            });
        },
        function(callback){
            let sessionAway = {frontendId: self.app.getServerId(), toServerId: msg.awayGid};
            self.app.rpc.game.entryRemote.getBattleData(sessionAway, {playerId: msg.away}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getBattleData error: ", err, msg.away);
                    return callback(Code.FAIL);
                }
                room.initPlayerByBattleData("teamB", ret.battleData);
                callback(null);
            });
        }
    ], function(error){
        if(error){
            return cb(Code.FAIL);
        }
        //2.计算前初始化其他战斗数据
        room.initPVPLeagueBattleOtherData();
        //3.计算战斗结果
        let result = room.calcBattleResult();
        cb(Code.OK, {home: room.teamA.playerId, away: room.teamB.playerId, homeScore: result.battleEndInfo.stInfos[0].shotNum,
            awayScore: result.battleEndInfo.stInfos[1].shotNum, beginTime: timeUtils.now(), roomUid: roomUid}, result);
    });
};


//懂球帝外站排名活动战斗 msg: {home , homeGid, homeType, away, awayGid, awayType}
BattleService.prototype.pvpDqdRankActivityBattle = function (msg, cb) {
    //创建战斗房间
    let roomUid = utils.syncCreateUid();
    let room = new Room(roomUid);
    //初始化
    room.pvpInit(msg, commonEnum.BATTLE_TYPE.PvpDqdRankActivity);
    //添加房间到Cache, 战斗结束后清除房间
    this.roomList.set(roomUid, room);
    //战斗计算
    //1.rpc -> game 获取用户数据
    let self = this;
    async.waterfall([
        function(callback){
            if(msg.home.playerType === commonEnum.BATTLE_TEAM_TYPE.Robot) {
                //最后5名机器人使用模板数据
                if(msg.home.uid === "dqdRobotLastFive") {
                    room.initNoneAccountBattleData("teamA", dataApi.allData.data["BattleTest"]["4"]);
                }else {
                    room.initRobotBattleDataByDoc("teamA", msg.home.doc);
                }
                callback(null);
            } else if(msg.home.playerType === commonEnum.BATTLE_TEAM_TYPE.NoneAccount) {
                //白板用户数据
                room.initNoneAccountBattleData("teamA", dataApi.allData.data["BattleTest"]["3"]);
                callback(null);
            } else {
                let sessionHome = {frontendId: self.app.getServerId(), toServerId: msg.home.gid};
                self.app.rpc.game.entryRemote.getBattleData(sessionHome, {playerId: msg.home.uid}, function (err, ret) {
                    if(!!err) {
                        logger.debug("rpc getBattleData error: ", err, msg.home);
                        return callback(Code.FAIL);
                    }
                    room.initPlayerByBattleData("teamA", ret.battleData);
                    callback(null);
                });
            }
        },
        function(callback){
            if(msg.away.playerType === commonEnum.BATTLE_TEAM_TYPE.Robot) {
                if(msg.away.uid === "dqdRobotLastFive") {
                    room.initNoneAccountBattleData("teamB", dataApi.allData.data["BattleTest"]["4"]);
                }else {
                    room.initRobotBattleDataByDoc("teamB", msg.away.doc);
                }
                callback(null);
            } else if(msg.away.playerType === commonEnum.BATTLE_TEAM_TYPE.NoneAccount) {
                room.initNoneAccountBattleData("teamB", dataApi.allData.data["BattleTest"]["3"]);
                callback(null);
            } else {
                let sessionAway = {frontendId: self.app.getServerId(), toServerId: msg.away.gid};
                self.app.rpc.game.entryRemote.getBattleData(sessionAway, {playerId: msg.away.uid}, function (err, ret) {
                    if (!!err) {
                        logger.debug("rpc getBattleData error: ", err, msg.away);
                        return callback(Code.FAIL);
                    }
                    room.initPlayerByBattleData("teamB", ret.battleData);
                    callback(null);
                });
            }
        }
    ], function(error){
        if(error){
            return cb(Code.FAIL);
        }
        //2.计算前初始化其他战斗数据
        room.initPVPLeagueBattleOtherData();
        //3.计算战斗结果
        let result = room.calcBattleResult();
        let retMsg = [];

        for(let i=0; i<2; i++) {
            let tmp = {};
            tmp.score = result.battleEndInfo.stInfos[i].shotNum;
            tmp.shotNum = result.battleEndInfo.stInfos[i].shotNum;
            tmp.control = result.battleEndInfo.stInfos[i].ctrlBallPer;
            tmp.break = result.battleEndInfo.stInfos[i].breakPer;
            tmp.bestId = result.battleEndInfo.stInfos[i].bestBaller;
            tmp.placeKickNum = result.battleEndInfo.stInfos[i].placeKickNum;
            //tmp.goalBaller = result.battleEndInfo.goalRecord[i];
            retMsg.push(tmp);
        }
        cb(Code.OK, {roomUid: roomUid, retMsg: retMsg, beginTime: timeUtils.now(), goalRecord: result.battleEndInfo.goalRecord}, result);
    });
};

//主席争夺战战斗
BattleService.prototype.pvpChairmanBattle = function(msg, cb) {
    //参数验证
    if(!msg.home || !msg.away) {
        logger.warn("pvpChairmanBattle param error, msg:", msg);
        return cb(Code.FAIL);
    }
    //创建战斗房间
    let roomUid = utils.syncCreateUid();
    let room = new Room(roomUid);
    //初始化
    room.pvpInit(msg, commonEnum.BATTLE_TYPE.chairman);
    //添加房间到Cache, 战斗结束后清除房间
    this.roomList.set(roomUid, room);
    //战斗计算
    //1.rpc -> game 获取用户数据
    let self = this;
    async.waterfall([
        function(callback){
            let sessionHome = {frontendId: self.app.getServerId(), toServerId: msg.homeGid};
            self.app.rpc.game.entryRemote.getBattleData(sessionHome, {playerId: msg.home}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getBattleData error: ", err, msg.home);
                    return callback(Code.FAIL);
                }
                room.initPlayerByBattleData("teamA", ret.battleData);
                callback(null);
            });
        },
        function(callback){
            let sessionAway = {frontendId: self.app.getServerId(), toServerId: msg.awayGid};
            self.app.rpc.game.entryRemote.getBattleData(sessionAway, {playerId: msg.away}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getBattleData error: ", err, msg.away);
                    return callback(Code.FAIL);
                }
                room.initPlayerByBattleData("teamB", ret.battleData);
                callback(null);
            });
        }
    ], function(error){
        if(error){
            return cb(Code.FAIL);
        }
        //2.计算前初始化其他战斗数据
        room.initPVPLeagueBattleOtherData();
        //3.计算战斗结果
        let result = room.calcBattleResult();
        cb(Code.OK, {home: room.teamA.playerId, away: room.teamB.playerId, homeScore: result.battleEndInfo.stInfos[0].shotNum,
            awayScore: result.battleEndInfo.stInfos[1].shotNum, beginTime: timeUtils.now(), roomUid: roomUid}, result);
    });
};

//巅峰赛战斗
BattleService.prototype.pvpPkmatchBattle = function(msg, cb) {
    //参数验证
    if(!msg.home || !msg.away) {
        logger.warn("pvpPkmatchBattle param error, msg:", msg);
        return cb(Code.FAIL);
    }
    //创建战斗房间
    let roomUid = utils.syncCreateUid();
    let room = new Room(roomUid);
    //初始化
    room.pvpInit(msg, commonEnum.BATTLE_TYPE.peak);
    //添加房间到Cache, 战斗结束后清除房间
    this.roomList.set(roomUid, room);
    //战斗计算
    //1.rpc -> game 获取用户数据
    let self = this;
    async.waterfall([
        function(callback){
            let sessionHome = {frontendId: self.app.getServerId(), toServerId: msg.homeGid};
            self.app.rpc.game.entryRemote.getBattleData(sessionHome, {playerId: msg.home}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getBattleData error: ", err, msg.home);
                    return callback(Code.FAIL);
                }
                room.initPlayerByBattleData("teamA", ret.battleData);
                callback(null);
            });
        },
        function(callback){
            let sessionAway = {frontendId: self.app.getServerId(), toServerId: msg.awayGid};
            self.app.rpc.game.entryRemote.getBattleData(sessionAway, {playerId: msg.away}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getBattleData error: ", err, msg.away);
                    return callback(Code.FAIL);
                }
                room.initPlayerByBattleData("teamB", ret.battleData);
                callback(null);
            });
        }
    ], function(error){
        if(error){
            return cb(Code.FAIL);
        }
        //2.计算前初始化其他战斗数据
        room.initPVPLeagueBattleOtherData();
        //3.计算战斗结果
        let result = room.calcBattleResult();
        //4.取消平局
        room.tiebreaker(result);
        cb(Code.OK, {home: room.teamA.playerId, away: room.teamB.playerId, homeScore: result.battleEndInfo.stInfos[0].shotNum,
            awayScore: result.battleEndInfo.stInfos[1].shotNum, beginTime: timeUtils.now(), roomUid: roomUid}, result);
    });
};


//信仰之战战斗
BattleService.prototype.pvpWarOfFaithBattle = function(msg, cb) {
    //参数验证
    if(!msg.attackPlayerId || !msg.defendPlayerId) {
        logger.warn("pvpWarOfFaithBattle param error, msg:", msg);
        return cb(Code.FAIL);
    }

    msg.home = msg.attackPlayerId;
    msg.away = msg.defendPlayerId;
    //创建战斗房间
    let roomUid = utils.syncCreateUid();
    let room = new Room(roomUid);
    //初始化
    room.pvpInit(msg, commonEnum.BATTLE_TYPE.PvpWarOfFaith);
    //添加房间到Cache, 战斗结束后清除房间
    this.roomList.set(roomUid, room);
    //战斗计算
    //1.rpc -> game 获取用户数据
    let self = this;
    async.waterfall([
        function(callback){
            let attackSession = {frontendId: self.app.getServerId(), toServerId: msg.attackGid};
            self.app.rpc.game.entryRemote.getWarOfFaithBattleData(attackSession, {playerId: msg.attackPlayerId}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getWarOfFaithBattleData error: ", err, msg.attackPlayerId);
                    return callback(Code.FAIL);
                }
                room.initWarOfFaithBattleData("teamA", ret.battleData, msg.attackInspireNum);
                callback(null);
            });
        },
        function(callback){
            let defendSession = {frontendId: self.app.getServerId(), toServerId: msg.defendGid};
            self.app.rpc.game.entryRemote.getWarOfFaithBattleData(defendSession, {playerId: msg.defendPlayerId}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getWarOfFaithBattleData error: ", err, msg.defendPlayerId);
                    return callback(Code.FAIL);
                }
                room.initWarOfFaithBattleData("teamB", ret.battleData, msg.defendInspireNum);
                callback(null);
            });
        }
    ], function(error){
        if(error){
            return cb(Code.FAIL);
        }
        //2.计算前初始化其他战斗数据
        room.initPVPLeagueBattleOtherData();
        //3.计算战斗结果
        let result = room.calcBattleResult();
        cb(Code.OK, {homeScore: result.battleEndInfo.stInfos[0].shotNum, awayScore: result.battleEndInfo.stInfos[1].shotNum, beginTime: timeUtils.now(), roomUid: roomUid}, result);
    });
};


//个人球场争夺战
BattleService.prototype.pvpGroundMatchBattle = function(msg, cb) {
    //参数验证
    if(!msg.home || !msg.away) {
        logger.warn("pvpGroundMatchBattle param error, msg:", msg);
        return cb(Code.FAIL);
    }
    //创建战斗房间
    let roomUid = utils.syncCreateUid();
    let room = new Room(roomUid);
    //初始化
    room.pvpInit(msg, commonEnum.BATTLE_TYPE.PvpGroundMatch);
    //添加房间到Cache, 战斗结束后清除房间
    this.roomList.set(roomUid, room);
    //战斗计算
    //1.rpc -> game 获取用户数据
    let self = this;
    async.waterfall([
        function(callback){
            let sessionHome = {frontendId: self.app.getServerId(), toServerId: msg.homeGid};
            self.app.rpc.game.entryRemote.getBattleData(sessionHome, {playerId: msg.home}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getBattleData error: ", err, msg.home);
                    return callback(Code.FAIL);
                }
                room.initPlayerByBattleData("teamA", ret.battleData);
                callback(null);
            });
        },
        function(callback){
            let sessionAway = {frontendId: self.app.getServerId(), toServerId: msg.awayGid};
            self.app.rpc.game.entryRemote.getBattleData(sessionAway, {playerId: msg.away}, function (err, ret) {
                if(!!err) {
                    logger.debug("rpc getBattleData error: ", err, msg.away);
                    return callback(Code.FAIL);
                }
                room.initPlayerByBattleData("teamB", ret.battleData);
                callback(null);
            });
        }
    ], function(error){
        if(error){
            return cb(Code.FAIL);
        }
        //2.计算前初始化其他战斗数据
        room.initPVPLeagueBattleOtherData();
        //3.计算战斗结果
        let result = room.calcBattleResult();
        cb(Code.OK, {home: room.teamA.playerId, away: room.teamB.playerId, homeScore: result.battleEndInfo.stInfos[0].shotNum,
            awayScore: result.battleEndInfo.stInfos[1].shotNum, beginTime: timeUtils.now(), roomUid: roomUid}, result);
    });
};
