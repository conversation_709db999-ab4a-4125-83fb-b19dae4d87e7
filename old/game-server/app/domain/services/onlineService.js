/**
 * Created by June on 2019/4/5.
 * Manage Global Player Online State
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');
var Code = require('../../../../shared/code');
var commonEnum = require('../../../../shared/enum');
var Utils = require('../../util/utils');
var TimeUtil = require('../../util/timeUtils');
var Calc = require('../../util/calc');
var Player = require('../entities/player');
var dataApi = require('../../util/dataApi');

var serversConfig = require('../../../config/servers');
var debugConfig = require('../../../config/debugConfig');
var gameConfig = require('../../../config/game');
var mongoClient = require("mongodb").MongoClient;

module.exports.create = function(app, dbclient){
    return new OnlineService(app, dbclient);
};

var OnlineService = function(app, dbclient) {
    EventEmitter.call(this);
    this.app = app;
    //Db init
    this.accountDao = require('../../dao/accountDao').create(dbclient);
    //Cache (结构 Key: playerId, Value 对象: {loginState: , battleState: , gid: })
    this.onlineCache = new Map();

    //用户数据层, 用于处理跨服用户数据
    this.playerCache = new Map();
    this.playerCacheList = [];
    this.maxPlayerCacheNum = debugConfig.maxPlayerCacheNum;
    if(debugConfig.isForTest) {
        this.maxPlayerCacheNum = 200;
    }
    //单服拉取的最大用户个数 (预留1倍的server的cache, 用于补充新登录用户)
    this.perServerCacheNum = Math.floor(this.maxPlayerCacheNum / (serversConfig.development.game.length * 2));

    this.baseDbList = [commonEnum.DB_NAME.player, commonEnum.DB_NAME.heros, commonEnum.DB_NAME.teamFormation,
        commonEnum.DB_NAME.trainer, commonEnum.DB_NAME.footballGround];

    //随机匹配, 匹配池
    this.strengthCache = [];
    this.totalAssetCache = [];

    this.groundMatchToplist = {};

    //球场争夺战跨服 new Cache
    this.groundMatchCache = new Map();
    //cacheObj: uid_teamIndex
    // {
    //     olGroundMatch:     个人球场数据 + 主球场等级 + 粉丝数 + str + assets
    // }

    this.groundMatchLockList = new Map();   //联合key: uid_teamIndex

};

util.inherits(OnlineService, EventEmitter);


OnlineService.prototype.initOnePlayerState = function(playerId, gid) {
    this.onlineCache.set(playerId, {
        loginState: commonEnum.LoginState.INIT,
        battleState: commonEnum.BattleState.INIT,
        gid : gid
    })
};

OnlineService.prototype.syncLoginStateAfterAuth = function(msg, cb)
{
    //logger.debug("syncLoginStateAfterAuth param:", msg);
    if(!msg.playerId || !msg.gid) {
        cb(Code.INVALID);
        return;
    }
    let code = Code.OK;
    let v = this.onlineCache.get(msg.playerId);
    if(!v) {
        //初始化
        this.initOnePlayerState(msg.playerId, msg.gid);
        v = this.onlineCache.get(msg.playerId);
    }
    else if(v.loginState === commonEnum.LoginState.LOGIN) {
        if(msg.gid === v.gid) {
            code = Code.LOGIN_STATE_CHECK.KICK_ROLE_SAME_ZONE;
        }
        else {
            code = Code.LOGIN_STATE_CHECK.KICK_ROLE_DIFF_ZONE;
        }
    }
    v.loginState = msg.loginState;
    this.onlineCache.set(msg.playerId, v);
    cb(code);
};

OnlineService.prototype.useLeave = function(msg, cb)
{
    //logger.debug("useLeave param:", msg);
    if(!msg.playerId || !msg.gid) {
        cb(Code.INVALID);
        return;
    }
    //let v = this.onlineCache.get(msg.playerId);
    if(this.onlineCache.has(msg.playerId))
    {
        this.onlineCache.delete(msg.playerId);
    }
    cb(Code.OK);
};

OnlineService.prototype.setLoginState = function(msg, cb) {
    if(!msg.playerId || !msg.gid) {
        cb(Code.FAIL);
    }
    //初始化
    if(!this.onlineCache.has(msg.playerId)) {
        this.initOnePlayerState(msg.playerId, msg.gid);
    }
    let v = this.onlineCache.get(msg.playerId);
    v.loginState = msg.loginState;
    this.onlineCache.set(msg.playerId, v);
    cb(Code.OK);
};

OnlineService.prototype.getLoginState = function(msg, cb) {
    if(this.onlineCache.has(msg.playerId)) {
        cb(Code.OK, this.onlineCache.get(msg.playerId).loginState);
    }
    else{
        cb(Code.FAIL);
    }
};

OnlineService.prototype.getGidByPlayerId = function(playerId, cb) {
    if(!playerId) {
        logger.error("getGidByPlayerId param [playerId] error.");
        return cb();
    }
    this.accountDao.findByPlayerId(playerId, function (code, doc) {
        if (code !== Code.OK) {
            return cb();
        }
        if (!doc)
        {
            logger.debug("getGidByPlayerId not find player", playerId);
            return cb();
        }
        return cb(doc.gid);
    });
};

OnlineService.prototype.getOtherPlayerDB = function (playerId, myGid, collectionNames, cb) {
    //获取玩家服务器id
    let self = this;
    self.accountDao.findByPlayerId(playerId, function (code, doc) {
        if(code !== Code.OK) {
            return cb(code, "", null, 0);
        }
        if (!doc || !doc.gid)
        {
            logger.warn("OnlineService.getOtherPlayer: not gid!", playerId, doc);
            return cb(Code.FAIL, "", null, 0);
        }
        //转发到对应服务器
        logger.debug("OnlineService getOtherPlayerDB myGid, toGid, playerId: ", myGid, doc.gid, playerId);
        if(myGid === doc.gid) {
            cb(Code.OK, doc.gid, null, 0);
        }
        else {
            let session = {frontendId: self.app.getServerId(), toServerId: doc.gid};
            self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, {playerId: playerId, collectionNames: collectionNames}, function (code, playerDoc, isNew) {
                cb(code, doc.gid, playerDoc, isNew);
            });
        }
    });
};

OnlineService.prototype.getOtherPlayerDBByGid = function(playerId, gid, collectionNames, cb) {
    let session = {frontendId: this.app.getServerId(), toServerId: gid};
    this.app.rpc.game.entryRemote.getLocalPlayerDoc(session, {playerId: playerId, collectionNames: collectionNames}, function (code, playerDoc, isNew) {
        cb(code, doc.gid, playerDoc, isNew);
    });
};

OnlineService.prototype.getPlayerRelayDB = function (playerId, myGid, cb) {
    //获取玩家服务器id
    let self = this;
    self.accountDao.findByPlayerId(playerId, function (code, doc) {
        if(code !== Code.OK) {
            return cb(code, "", null, 0);
        }
        if (!doc.gid)
        {
            logger.warn("OnlineService.getPlayerRelayDB: not gid!", playerId, doc);
            return cb(Code.FAIL, "", null, 0);
        }
        //转发到对应服务器
        logger.debug("OnlineService getPlayerRelayDB myGid, toGid, playerId: ", myGid, doc.gid, playerId);
        let session = {frontendId: self.app.getServerId(), toServerId: doc.gid};
        self.app.rpc.game.entryRemote.getRelayDoc(session, {playerId: playerId}, function (code, playerDoc) {
            cb(code, doc.gid, playerDoc);
        });
    });
};

OnlineService.prototype.getLocalLeagueBattleData = function (playerId, cb) 
{
    let self = this;
    self.getGidByPlayerId(playerId, function (gid) {
		//logger.debug("gid:", gid);
		if(!gid) {
			logger.error("getGidByPlayerId fail. gid is null");
			return cb(Code.FAIL, null);
        }
        let session = {frontendId: self.app.getServerId(), toServerId: gid};
        self.app.rpc.game.entryRemote.getLocalLeagueBattleData(session, {playerId: playerId}, function (err, battleData) {
            if(!!err) 
            {	
                logger.error("getLocalLeagueBattleData get battle data error", err);
                return cb(Code.FAIL ,null);
            }
            cb(Code.OK, battleData);
        });
    });
};

OnlineService.prototype.getPlayerOnlineState = function(playerId) {
    let status = commonEnum.ONLINE_PLAYER_ONLINE_STATE.OFFLINE;
    if(this.onlineCache.has(playerId)) {
        let loginState = this.onlineCache.get(playerId).loginState;
        if (loginState === commonEnum.LoginState.LOGIN)  //登陆成功才算在线
        {
            status = commonEnum.ONLINE_PLAYER_ONLINE_STATE.ONLINE;
        }
    }
   return status;
};

OnlineService.prototype.getOnlineStateByList = function (msg, cb) 
{   
    let playerIdList = msg.playerIdList;
    let onlineStateList = [];
    if (!playerIdList)
    {
        logger.error("OnlineService.getOnlineStateByList: not playerIdList", msg);
        cb(Code.FAIL, onlineStateList);
        return;
    }

    if (playerIdList.length <= 0)
    {
        logger.error("OnlineService.getOnlineStateByList: playerIdList length is 0", msg);
        cb(Code.OK, onlineStateList);
        return;
    }

    for(let idx in playerIdList)
    {
        let uid =  playerIdList[idx];
        let status = this.getPlayerOnlineState(uid);
        let obj = {
            uid: uid,
            status: status,
        };
        onlineStateList.push(obj);
    }
    cb(Code.OK, onlineStateList);
};

OnlineService.prototype.onlineFollow = function (msg, cb) 
{   
	let followUid = msg.followUid;
	let wantFollowUid = msg.wantFollowUid;
    if (!followUid || !wantFollowUid)
    {
        logger.error("OnlineService.onlineFollow: not followUid or wantFollowUid", msg);
        cb(Code.FAIL);
        return;
    }
    logger.info("OnlineService.onlineFollow", msg);
    let self = this;
    async.waterfall([
        function (callback) {
            self.accountDao.findByPlayerId(followUid, function (code, doc) {
                if(code !== Code.OK) 
                {
                    logger.error("onlineFollow");
                    callback(code);
                    return;
                }
                //转发到对应服务器
                callback(null, doc.gid);
            });
        }, function (gid, callback) {
            let session = {frontendId: self.app.getServerId(), toServerId: gid};
            self.app.rpc.game.entryRemote.onlineProcessFollow(session, msg, function(err, code) {
                if (!!err)
                {
                    logger.error("onlineProcessFollow: catch a error!", err);
                    callback(err);
                    return;
                }

                if(code !== Code.OK) {
                    logger.error("onlineProcessFollow failed!", code);
                    callback("onlineProcessFollow failed!");
                    return;
                }
                callback(null);
            });
        }
    ], function(err){
        if (!!err)
        {
            logger.info("OnlineService.onlineFollow: waterfull error msg", err);
            cb(Code.FAIL);
            return;
        }
        cb(Code.OK);
    });
};

OnlineService.prototype.onlineGiveFollowEnergy = function (msg, cb)
{
    let followUidList = msg.followUidList;
    let wantFollowUid = msg.wantFollowUid;
    if (!followUidList || !wantFollowUid)
    {
        logger.error("OnlineService.onlineGiveFollowEnergy: not followUid or wantFollowUid", msg);
        cb(Code.FAIL);
        return;
    }
    logger.info("OnlineService.onlineGiveFollowEnergy", msg);
    let self = this;
    let num = 0;
    async.eachSeries(followUidList, function (followUid, callback) {
        async.waterfall([
            function (cb1) {
                self.accountDao.findByPlayerId(followUid, function (code, doc) {
                    if(code !== Code.OK)
                    {
                        logger.error("onlineGiveFollowEnergy", code);
                        cb1(null);
                        return;
                    }
                    //转发到对应服务器
                    cb1(null, doc.gid);
                });
            }, function (gid, cb1) {
                if(!gid)
                {
                    callback(null);
                }
                let session = {frontendId: self.app.getServerId(), toServerId: gid};
                let ssMsg = {
                    followUid: followUid,
                    wantFollowUid: wantFollowUid,
                };
                self.app.rpc.game.entryRemote.onlineGiveFollowEnergy(session, ssMsg, function(err, code) {
                    if (!!err)
                    {
                        logger.error("onlineGiveFollowEnergy: catch a error!", err);
                        callback(null);
                        return;
                    }

                    if(code !== Code.OK) {
                        logger.error("onlineGiveFollowEnergy failed!", code);
                        callback(null);
                        return;
                    }
                    if(code === Code.OK)
                    {
                        num += 1;
                    }
                    callback(null);
                });
            }
        ], function(err){
            if (!!err)
            {
                logger.info("OnlineService.onlineGiveFollowEnergy: waterfull error msg", err);
                callback(null);
                return;
            }
            callback(null);
        });
    }, function (err) {
        cb(Code.OK, num);
        });

};

OnlineService.prototype.onlineUnFollow = function (msg, cb) 
{   
    let unFollowUid = msg.unFollowUid;
	let wantUnFollowUid = msg.wantUnFollowUid;
    if (!unFollowUid || !wantUnFollowUid)
    {
        logger.error("OnlineService.onlineUnFollow: not unFollowUid or wantUnFollowUid", msg);
        cb(Code.FAIL);
        return;
    }
    let self = this;
    async.waterfall([
        function (callback) {
            self.accountDao.findByPlayerId(unFollowUid, function (code, doc) {
                if(code !== Code.OK) 
                {
                    logger.error("onlineUnFollow", code);
                    callback(code);
                    return;
                }
                callback(null, doc.gid);
            });
        }, function (gid, callback) {
            let session = {frontendId: self.app.getServerId(), toServerId: gid};
            self.app.rpc.game.entryRemote.onlineProcessUnFollow(session, msg, function(err, code) {
                if (!!err)
                {
                    logger.error("onlineProcessUnFollow: catch a error!", err);
                    callback(err);
                    return;
                }

                if(code !== Code.OK) {
                    logger.error("onlineProcessUnFollow failed!", code);
                    callback("onlineProcessUnFollow failed!");
                    return;
                }
                callback(null);
            });
        }
    ], function(err){
        if (!!err)
        {
            logger.info("OnlineService.onlineUnFollow: waterfull error msg", err);
            cb(Code.FAIL);
            return;
        }
        cb(Code.OK);
    });
};

OnlineService.prototype.getAllServersLoginNum = function () {
    var numInfo = {};
    var serverList = this.app.getServersByType("game");
    for(var i=0; i<serverList.length; i++) {
        numInfo[serverList[i].id] = 0;
    }
    for(let [playerId, stateInfo] of this.onlineCache) {
        if(stateInfo.loginState === commonEnum.LoginState.LOGIN) {
            numInfo[stateInfo.gid]++;
        }
    }
    logger.debug("OnlineService getAllServersLoginNum: ", numInfo);
    return numInfo;
};

/* -----------------------------------  个人球场争夺战  -------------------------------- */

OnlineService.prototype.loadPlayerUserCache = function() {
    let self = this;
    let gameDbList = {};
    async.waterfall([
        //1. 连接gamedb
        function (callback) {
            async.eachSeries(serversConfig.production.game, function (gameServer, cb1) {
                let serverId = gameServer.id;
                let gameDbUrl = "mongodb://" + serverId+ '-admin:' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
                mongoClient.connect(gameDbUrl, { useNewUrlParser: true }, function (error, dbClient) {
                    if (error) {
                        logger.error("connect gameDbUrl failed! err: " + error);
                        return callback(error);
                    }
                    gameDbList[serverId] = {
                        dbClient: dbClient,
                        playerList: [],
                        relatedList: []         //关联用户
                    };
                    logger.debug("connect game finish. serverId:", serverId);
                    cb1();
                })
            }, function (err) {
                callback(null)
            });
        },
        //2. 单服最近登录用户数据拉取
        function (callback) {
            async.eachSeries(serversConfig.production.game, function (gameServer, cb1) {
                let serverId = gameServer.id;
                let db = gameDbList[serverId].dbClient.db(serverId);
                db.collection(commonEnum.DB_NAME.player, function (err, col) {
                    col.find({level: {$gt: 10}}).sort({leaveTime: -1}).limit(self.perServerCacheNum).toArray(function(err, doc){
                        if(!!err){
                            logger.error("sort player by leaveTime err, errMsg:", err);
                            return cb1(Code.FAIL);
                        }
                        for(let i=0,len=doc.length;i<len;i++) {
                            let data = {};
                            doc[i].gid = serverId;
                            data[commonEnum.DB_NAME.player] = doc[i];
                            self.playerCache.set(doc[i].uid, data);
                            self.playerCacheList.push({uid: doc[i].uid, activeTime: doc[i].leaveTime});
                            gameDbList[serverId].playerList.push(doc[i].uid);
                        }
                        logger.debug("serverId & playerList length: ", serverId, gameDbList[serverId].playerList.length);
                        cb1();
                    });
                });
            }, function (err) {
                callback(null)
            });
        },
        //3. 每个用户最小化数据拉取
        function (callback) {
            let dbList = [];
            for(let i=0, len=self.baseDbList.length; i<len; i++) {
                if(self.baseDbList[i] !== commonEnum.DB_NAME.player) {
                    dbList.push(self.baseDbList[i]);
                }
            }
            async.eachSeries(serversConfig.production.game, function (gameServer, cb1) {
                let serverId = gameServer.id;
                let db = gameDbList[serverId].dbClient.db(serverId);
                async.eachSeries(gameDbList[serverId].playerList, function (uid, cb2) {
                    let index = 0;
                    async.whilst(
                        function() {return index < dbList.length},
                        function (cb3) {
                            let name = dbList[index++];
                            db.collection(name, function (err, col) {
                                col.findOne({uid: uid}, function (err, doc) {
                                    let data = self.playerCache.get(uid);
                                    data[name] = doc;
                                    cb3();
                                });
                            });
                        },
                        function(err){
                            cb2();
                        }
                    )
                }, function (err) {
                    cb1();
                });
            }, function (err) {
                callback(null)
            });
        },
        //3+. 占领关联的用户数据也需要加载进入内存
        function (callback) {
            let tmpMap = new Map();
            for(let [k,v] of self.playerCache) {
                let groundMatch = v[commonEnum.DB_NAME.footballGround].groundMatch;
                if(!groundMatch || !groundMatch.fieldList) {
                    logger.debug("delete null groundMatch");
                    self.playerCache.delete(k);
                    for(let i=0;i<self.playerCacheList.length;i++) {
                        if(self.playerCacheList[i].uid === k) {
                            self.playerCacheList.splice(i, 1);
                            i--;
                        }
                    }
                    continue;
                }
                for(let i=0;i<3;i++) {
                    let uid = groundMatch.fieldList[i].beOccupiedUid;
                    let gid = groundMatch.fieldList[i].beOccupiedGid;
                    logger.debug("k uid xxxxxx: ", k, uid);
                    if(uid && gid && !self.playerCache.has(uid) && !tmpMap.has(uid)){
                        gameDbList[gid].relatedList.push(uid);
                        tmpMap.set(uid, 1);
                    }
                }
            }
            logger.debug("gameDbList[gid].relatedList: ", gameDbList, self.playerCache.size);
            callback(null);
        },
        //关联用户数据加载
        function (callback) {
            async.eachSeries(serversConfig.production.game, function (gameServer, cb1) {
                let serverId = gameServer.id;
                let db = gameDbList[serverId].dbClient.db(serverId);
                async.eachSeries(gameDbList[serverId].relatedList, function (uid, cb2) {
                    let index = 0;
                    async.whilst(
                        function() {return index < self.baseDbList.length},
                        function (cb3) {
                            let name = self.baseDbList[index++];
                            db.collection(name, function (err, col) {
                                col.findOne({uid: uid}, function (err, doc) {
                                    let data = self.playerCache.get(uid);
                                    if(!data) {
                                        data = {};
                                        data[name] = doc;
                                        logger.debug("doc xxxxx: ", uid, doc);
                                        if(name === commonEnum.DB_NAME.player) {
                                            data[commonEnum.DB_NAME.player].gid = serverId;
                                            self.playerCacheList.push({uid: uid, activeTime: doc.leaveTime});
                                        }
                                        data.isRelated = true;
                                        self.playerCache.set(uid, data);
                                    }else {
                                        data[name] = doc;
                                        if(name === commonEnum.DB_NAME.player) {
                                            data[commonEnum.DB_NAME.player].gid = serverId;
                                            self.playerCacheList.push({uid: uid, activeTime: doc.leaveTime});
                                        }
                                        data.isRelated = true;
                                    }
                                    cb3();
                                });
                            });
                        },
                        function(err){
                            cb2();
                        }
                    )
                }, function (err) {
                    cb1();
                });
            }, function (err) {
                callback(null);
            });
        },
        function (callback) {
            //去重 排序
            let tmpMap = new Map();
            for(let i=0; i<self.playerCacheList.length; i++) {
                if(tmpMap.has(self.playerCacheList[i].uid)) {
                    self.playerCacheList.splice(i, 1);
                    i--;
                }else {
                    tmpMap.set(self.playerCacheList[i].uid, 1);
                }
            }
            let _sortFunc = function(obj1, obj2) {
                if(obj1.activeTime < obj2.activeTime) {
                    return 1;
                }else if(obj1.activeTime > obj2.activeTime) {
                    return -1;
                }else {
                    return 0;
                }
            };
            self.playerCacheList.sort(_sortFunc);
            logger.debug("loadPlayerUserCache: playerCache size: ", self.playerCache.size, self.playerCacheList.length, self.playerCacheList);
            callback(null);
        },
        //4. 初始化匹配池
        function (callback) {
            //to do 转化为 groundMatchCache
            for(let [k,v] of self.playerCache) {
                if(v.isRelated) {
                    logger.debug("data is related not need add.");
                    continue;
                }
                for(let i=0; i<3; i++) {
                    self.addFieldCacheByPlayerData(k, v[commonEnum.DB_NAME.player].gid, i);
                }
            }
            logger.debug("load this.groundMatchCache: ", self.groundMatchCache.size);
            //to do插入到排序的匹配池中
            let index = 0;
            for(let [k, v] of self.groundMatchCache) {
                let n = k.lastIndexOf('_');
                let uid = k.substring(0, n);
                let teamIndex = k.substring(n+1);
                logger.debug("k, uid, teamIndex: ", k, uid, teamIndex);
                self.strengthCache[index] = {};
                self.strengthCache[index].uid = uid;
                self.strengthCache[index].gid = v.fieldInfo.ownerGid;
                self.strengthCache[index].teamUid = v.fieldInfo.ownerTeamUid;
                self.strengthCache[index].str = v.fieldInfo.str;
                self.strengthCache[index].protectEndTime = v.fieldInfo.protectEndTime;
                self.strengthCache[index].teamIndex = teamIndex;

                self.totalAssetCache[index] = {};
                self.totalAssetCache[index].uid = uid;
                self.totalAssetCache[index].gid = v.fieldInfo.ownerGid;
                self.totalAssetCache[index].teamUid = v.fieldInfo.ownerTeamUid;
                self.totalAssetCache[index].assets = v.fieldInfo.assets;
                self.totalAssetCache[index].protectEndTime = v.fieldInfo.protectEndTime;
                self.totalAssetCache[index].teamIndex = teamIndex;
                index++;
            }

            // let toDeleteList = [];
            // let dataErrNum = 0;
            // for(let [k,v] of self.playerCache) {
            //     let player = self.makeNewPlayerObj(k, v);
            //     //球场数据异常, 从cache中删除
            //     if(self.checkPlayerDataError(player)) {
            //         toDeleteList.push(k);
            //         dataErrNum++;
            //         continue;
            //     }
            //     //初始化球场数据同步
            //     v[commonEnum.DB_NAME.footballGround] = player.footballGround.toJSONforDB();
            //     //logger.debug("v ......", v[commonEnum.DB_NAME.player].gid);
            //     //self.insertGroundMatchCache(k, player, v[commonEnum.DB_NAME.player].gid);
            // }
            // logger.debug("checkPlayerDataError num: ", dataErrNum);
            // //删除失效数据
            // for(let n=0,len=toDeleteList.length; n<len; n++) {
            //     self.playerCache.delete(toDeleteList[n]);
            //     for(let i=0, len=self.playerCacheList.length; i<len; i++) {
            //         if(self.playerCacheList[i].uid === toDeleteList[n]) {
            //             self.playerCacheList.splice(i, 1);
            //             break;
            //         }
            //     }
            // }

            //排序
            self.strengthCacheSort();
            self.totalAssetCacheSort();
            logger.debug("this.strengthCache, this.totalAssetCache", self.strengthCache.length, self.totalAssetCache.length);
            callback(null);
        },
        //5. 关闭连接
        function (callback) {
            for(let key in gameDbList) {
                gameDbList[key].dbClient.close();
            }
            callback();
        },
        //6. 排行榜数据初始化及定时器
        function (callback) {
            self.syncGroundMatchToplistByCache();
            callback();
        },

    ], function (err) {
        if(err) {
            logger.error("loadPlayerUserCache fail. err code:", err);
        }else {
            logger.error("loadPlayerUserCache finish");
        }
    });
};

OnlineService.prototype.strengthCacheSort = function () {
    let compare = function (v1, v2) {
        if(v1.str < v2.str) {
            return 1;
        }else if(v1.str > v2.str) {
            return -1;
        }
        return 0;
    };
    this.strengthCache.sort(compare);
};

OnlineService.prototype.totalAssetCacheSort = function () {
    let compare = function (v1, v2) {
        if(v1.assets < v2.assets) {
            return 1;
        }else if(v1.assets > v2.assets) {
            return -1;
        }
        return 0;
    };
    this.totalAssetCache.sort(compare);
};

OnlineService.prototype.checkPlayerDataError = function (player) {
    //球场数据异常
    if(!player.footballGround.mainGround.get(player.footballGround.uid)) {
        return true;
    }
    return false;
};

OnlineService.prototype.searchGroundMatchByRandom = function (myUid, myStr, myAssets, cb) {
    //1. 过滤
    //大于自己资产的球场
    let assetsList = [];
    //大于自己实力的球场
    let strMoreList = [];
    //小于自己实力的球场
    let strLessList = [];
    let self = this;
    let now = TimeUtil.now();

    for(let i=0,len=this.totalAssetCache.length; i<len; i++) {
        if(this.totalAssetCache[i].assets > myAssets && this.totalAssetCache[i].uid !== myUid) {
            assetsList.push({uid: this.totalAssetCache[i].uid, teamIndex: this.totalAssetCache[i].teamIndex});
        }
    }
    for(let i=0,len=this.strengthCache.length; i<len; i++) {
        if(this.strengthCache[i].str > myStr && this.strengthCache[i].uid !== myUid) {
            strMoreList.push({uid: this.strengthCache[i].uid, teamIndex: this.strengthCache[i].teamIndex});
        }else if(this.strengthCache[i].str < myStr && this.strengthCache[i].uid !== myUid) {
            strLessList.push({uid: this.strengthCache[i].uid, teamIndex: this.strengthCache[i].teamIndex});
        }
    }

    //极端情况处理
    if(assetsList.length === 0) {
        let topNum = Math.floor(this.totalAssetCache.length * 10 /100);
        for(let i=0; i<topNum; i++) {
            if(this.totalAssetCache[i].uid !== myUid) {
                assetsList.push({uid: this.totalAssetCache[i].uid, teamIndex: this.totalAssetCache[i].teamIndex});
            }
        }
    }
    if(strMoreList.length === 0) {
        let topNum = Math.floor(this.strengthCache.length * 10 /100);
        for(let i=0; i<topNum; i++) {
            if(this.strengthCache[i].uid !== myUid) {
                strMoreList.push({uid: this.strengthCache[i].uid, teamIndex: this.strengthCache[i].teamIndex});
            }
        }
    }
    if(strLessList.length === 0) {
        let topNum = Math.floor(this.strengthCache.length * 10 /100);
        for(let i=this.strengthCache.length-1; i>(this.strengthCache.length-topNum); i--) {
            if(this.strengthCache[i].uid !== myUid) {
                strLessList.push({uid: this.strengthCache[i].uid, teamIndex: this.strengthCache[i].teamIndex});
            }
        }
    }

    let _makeOneSearchData = function(listObj) {
        let key = listObj.uid+'_'+listObj.teamIndex;
        let obj = {};
        let oneCache = self.groundMatchCache.get(key);
        obj.resId = oneCache.fieldInfo.resId;
        obj.ownerUid = oneCache.fieldInfo.ownerUid;
        obj.ownerName = oneCache.fieldInfo.ownerName;
        obj.ownerTeamUid = oneCache.fieldInfo.ownerTeamUid;
        obj.ownerGid = oneCache.fieldInfo.ownerGid;
        obj.ownerFaceUrl = oneCache.fieldInfo.ownerFaceUrl;
        obj.ownerBallFan = oneCache.fieldInfo.ballFan;
        obj.mainGroundLevel = oneCache.fieldInfo.mainGroundLevel;
        obj.leftProtectTime = Math.floor((oneCache.fieldInfo.protectEndTime - now) / 1000);
        obj.leftProtectType = oneCache.fieldInfo.protectType;
        obj.teamIndex = parseInt(listObj.teamIndex);
        if(obj.leftProtectTime < 0) {
            obj.leftProtectTime = 0;
            obj.leftProtectType = 0;
        }
        obj.str = oneCache.fieldInfo.str;
        obj.attack = oneCache.fieldInfo.attack;
        obj.defend = oneCache.fieldInfo.defend;

        if(!!oneCache.occupyInfo.occupyUid) {
            obj.occupyUid = oneCache.occupyInfo.occupyUid;
            obj.occupyName = oneCache.occupyInfo.occupyName;
            obj.occupyTeamUid = oneCache.occupyInfo.occupyTeamUid;
            obj.occupyGid = oneCache.occupyInfo.occupyGid;
            obj.occupyTeamName = oneCache.occupyInfo.occupyTeamName;
            obj.occupyFaceUrl = oneCache.occupyInfo.occupyFaceUrl;
            let occupyTime = Math.floor((now - oneCache.fieldInfo.occupyStartTime) / 1000);
            let maxTime = dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime);
            if(occupyTime > maxTime) occupyTime = maxTime;
            if(occupyTime < 0) occupyTime = 0;
            obj.occupyTime = occupyTime;
            obj.leftProtectTime = Math.floor((oneCache.occupyInfo.protectEndTime - now) / 1000);
            obj.leftProtectType = oneCache.occupyInfo.protectType;
            if(obj.leftProtectTime < 0) {
                obj.leftProtectTime = 0;
                obj.leftProtectType = 0;
            }
            obj.str = oneCache.occupyInfo.str;
            obj.attack = oneCache.occupyInfo.attack;
            obj.defend = oneCache.occupyInfo.defend;
            obj.reportList = oneCache.occupyInfo.reportList;
        }

        return obj;
    };

    let resultList = [];
    let randIndex = Calc.randRange(0, assetsList.length-1);
    let randObj = _makeOneSearchData(assetsList[randIndex]);
    resultList.push(randObj);

    randIndex = Calc.randRange(0, strMoreList.length-1);
    randObj = _makeOneSearchData(strMoreList[randIndex]);
    resultList.push(randObj);

    randIndex = Calc.randRange(0, strLessList.length-1);
    randObj = _makeOneSearchData(strLessList[randIndex]);

    // if(debugConfig.isForTest) {
    //     randObj = _makeOneSearchData({uid: "eHv4lgA3rrUp-XTjXoIpnJ1M", teamIndex: 0})
    // }
    resultList.push(randObj);

    logger.debug("searchGroundMatchByRandom: ", resultList);
    cb(Code.OK, resultList);

    // //球场等级
    // let mainGround = owner.footballGround.mainGround.get(obj.uid);
    //
    // if(!fieldInfo.protectEndTime) fieldInfo.protectEndTime = 0;
    // obj.leftProtectTime = Math.floor((fieldInfo.protectEndTime - now) / 1000);
    // obj.leftProtectType = fieldInfo.protectType;
    // if(obj.leftProtectTime < 0) {
    //     obj.leftProtectTime = 0;
    //     obj.leftProtectType = 0;
    // }
    // obj.str = owner.teamFormations.calcTotalRating(fieldInfo.teamUid);
    // let teamFormation = owner.teamFormations.getOneTeamFormation(fieldInfo.teamUid);
    // obj.attack = (!teamFormation) ? 0 : teamFormation.Attack;
    // obj.defend = (!teamFormation) ? 0 : teamFormation.Defend;
    // //obj.recordList = fieldInfo.recordList;
    // obj.reportList = [];
    // logger.debug("searchGroundMatchByRandom 11111", fieldInfo);
    // if(!!fieldInfo.beOccupiedUid) {
    //     let maxTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
    //     let occupyTime = Math.floor((now - fieldInfo.occupyStartTime) / 1000);
    //     if(occupyTime < 0) occupyTime = 0;
    //     if(fieldInfo.occupyStartTime > 0 && occupyTime < maxTime) {
    //         obj.occupyUid = fieldInfo.beOccupiedUid;
    //         obj.occupyTeamUid = fieldInfo.beOccupiedTeamUid;
    //         obj.occupyGid = fieldInfo.beOccupiedGid;
    //         obj.occupyTeamName = fieldInfo.name;
    //         obj.occupyFaceUrl = fieldInfo.faceUrl;
    //         obj.occupyTime = occupyTime;
    //         //获取占领者数据
    //         let doc = self.playerCache.get(obj.occupyUid);
    //         if(!doc) {
    //             self.loadPlayerDocToCache(obj.occupyUid, function (code, playerDoc) {
    //                 let occPlayer = self.makeNewPlayerObj(obj.occupyUid, playerDoc);
    //                 let occList = occPlayer.footballGround.groundMatch.occupyFieldList;
    //                 obj.occupyName = occPlayer.name || fieldInfo.name;
    //                 let occInfo;
    //                 for(let j=0; j<occList.length; j++) {
    //                     if(occList[j].occupyTeamIndex === index) {
    //                         obj.reportList = occList[j].beReportedList;
    //                         occInfo = occList[j];
    //                         break;
    //                     }
    //                 }
    //                 if(!!occInfo) {
    //                     if(!occInfo.protectEndTime) occInfo.protectEndTime = 0;
    //                     obj.leftProtectTime = Math.floor((occInfo.protectEndTime - now) / 1000);
    //                     obj.leftProtectType = occInfo.protectType;
    //                     if(obj.leftProtectTime < 0) {
    //                         obj.leftProtectTime = 0;
    //                         obj.leftProtectType = 0;
    //                     }
    //                 }
    //                 obj.str = occPlayer.teamFormations.calcTotalRating(obj.occupyTeamUid);
    //                 let occTeamFormation = occPlayer.teamFormations.getOneTeamFormation(obj.occupyTeamUid);
    //                 obj.attack = (!occTeamFormation) ? 0 : occTeamFormation.Attack;
    //                 obj.defend = (!occTeamFormation) ? 0 : occTeamFormation.Defend;
    //                 cb1(obj);

    /*

    //满足条件的
    let topNum = 20;
    let maxRandomNum = 10;
    // if(debugConfig.isForTest) {
    //     topNum = 5;
    //     maxRandomNum = 3;
    // }
    //前10校验
    let checkTop = function (myUid, checkList) {
        for(let i=0;i<topNum;i++) {
            if(!!checkList[i] && checkList[i].uid === myUid) {
                return true;
            }
        }
        return false;
    };


    //随机列表预处理
    let _preProcessByType = function(type, self) {
        let srcList = self.totalAssetCache;
        let myParam = myAssets;
        let randomList = [];
        if(type === "moreList" || type === "lessList") {
            srcList = self.strengthCache;
            myParam = myStr;
        }
        let backUpList = [];
        //过滤符合条件的
        if(checkTop(myUid, srcList)) {
            logger.debug("srcList 111: ", srcList.length);
            for(let i=0;i<topNum;i++) {
                let v = self.playerCache.get(srcList[i].uid);
                if(!v || !v[commonEnum.DB_NAME.footballGround] || !v[commonEnum.DB_NAME.footballGround].groundMatch) {
                    logger.debug("uid not have data 111.", v, srcList[i].uid);
                    continue;
                }
                let groundMatch = v[commonEnum.DB_NAME.footballGround].groundMatch;
                let list = groundMatch.fieldList;
                let teamIndex = srcList[i].teamIndex;
                //过滤: 占领者中也不能是搜索者
                if(list && list[teamIndex] && list[teamIndex].beOccupiedUid && (list[teamIndex].beOccupiedUid === myUid)) continue;
                if(!!srcList[i] && srcList[i].uid !== myUid && srcList[i].protectEndTime < now) {
                    randomList.push({uid: srcList[i].uid, teamUid: srcList[i].teamUid, teamIndex: teamIndex});
                }
            }
        }else {
            for(let i=0,len=srcList.length;i<len;i++) {
                let v = self.playerCache.get(srcList[i].uid);
                if(!v || !v[commonEnum.DB_NAME.footballGround] || !v[commonEnum.DB_NAME.footballGround].groundMatch) {
                    logger.debug("uid not have data 222.", v, srcList[i].uid);
                    continue;
                }
                let groundMatch = v[commonEnum.DB_NAME.footballGround].groundMatch;
                let list = groundMatch.fieldList;
                let teamIndex = srcList[i].teamIndex;
                //过滤: 占领者中也不能是搜索者
                if(list && list[teamIndex] && list[teamIndex].beOccupiedUid && (list[teamIndex].beOccupiedUid === myUid)) continue;
                let param = srcList[i].assets;
                if(type === "moreList" || type === "lessList") {
                    param = srcList[i].str;
                }
                //logger.debug("srcList[i]: ", srcList[i], param, myParam, type, myAssets, myStr);
                if((type === "moreList" || type === "assetList") && param >= myParam && srcList[i].uid !== myUid) {
                    randomList.push({uid: srcList[i].uid, teamUid: srcList[i].teamUid, teamIndex: srcList[i].teamIndex});
                } else if((type === "lessList") && param < myParam && srcList[i].uid !== myUid) {
                    randomList.push({uid: srcList[i].uid, teamUid: srcList[i].teamUid, teamIndex: srcList[i].teamIndex});
                }
                if(srcList[i].uid !== myUid) {
                    backUpList.push({uid: srcList[i].uid, teamUid: srcList[i].teamUid, teamIndex: srcList[i].teamIndex});
                }
            }
        }
        if(randomList.length > maxRandomNum) {
            for(let i=0,len=randomList.length-maxRandomNum;i<len;i++) {
                randomList.shift();
            }
        }
        logger.debug("preProcessByType randomList: ", randomList, myUid, type);
        let result = {};
        if(randomList.length <= 0) {
            let rand = Calc.randRange(0, backUpList.length-1);
            result.uid = backUpList[rand].uid;
            result.teamUid = backUpList[rand].teamUid;
            result.teamIndex = backUpList[rand].teamIndex;
            logger.warn("preProcessByType special: ", type, rand, backUpList[rand]);
        }else {
            let rand = Calc.randRange(0, randomList.length-1);
            result.uid = randomList[rand].uid;
            result.teamUid = randomList[rand].teamUid;
            result.teamIndex = srcList[rand].teamIndex;
            logger.debug("preProcessByType rand: ", type, rand, randomList[rand]);
        }
        logger.debug("preProcessByType result: ", result);
        return result;
    };

    let resultList = [];
    let typeList = ["assetList", "moreList", "lessList"];

    let _makeObjInfo = function (obj, index, cb1) {
        let doc = self.playerCache.get(obj.uid);
        let owner = self.makeNewPlayerObj(obj.uid, doc);
        let fieldInfo = owner.footballGround.groundMatch.fieldList[obj.teamIndex];
        obj.resId = fieldInfo.resId;
        obj.ownerUid = obj.uid;
        obj.ownerName = owner.name;
        obj.ownerTeamUid = fieldInfo.teamUid;
        obj.ownerGid = doc[commonEnum.DB_NAME.player].gid;
        obj.ownerFaceUrl = owner.faceUrl;
        obj.ownerBallFan = owner.footballGround.ballFan;
        //球场等级
        let mainGround = owner.footballGround.mainGround.get(obj.uid);
        obj.mainGroundLevel = mainGround.Level;
        if(!fieldInfo.protectEndTime) fieldInfo.protectEndTime = 0;
        obj.leftProtectTime = Math.floor((fieldInfo.protectEndTime - now) / 1000);
        obj.leftProtectType = fieldInfo.protectType;
        if(obj.leftProtectTime < 0) {
            obj.leftProtectTime = 0;
            obj.leftProtectType = 0;
        }
        obj.str = owner.teamFormations.calcTotalRating(fieldInfo.teamUid);
        let teamFormation = owner.teamFormations.getOneTeamFormation(fieldInfo.teamUid);
        obj.attack = (!teamFormation) ? 0 : teamFormation.Attack;
        obj.defend = (!teamFormation) ? 0 : teamFormation.Defend;
        //obj.recordList = fieldInfo.recordList;
        obj.reportList = [];
        logger.debug("searchGroundMatchByRandom 11111", fieldInfo);
        if(!!fieldInfo.beOccupiedUid) {
            let maxTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
            let occupyTime = Math.floor((now - fieldInfo.occupyStartTime) / 1000);
            if(occupyTime < 0) occupyTime = 0;
            if(fieldInfo.occupyStartTime > 0 && occupyTime < maxTime) {
                obj.occupyUid = fieldInfo.beOccupiedUid;
                obj.occupyTeamUid = fieldInfo.beOccupiedTeamUid;
                obj.occupyGid = fieldInfo.beOccupiedGid;
                obj.occupyTeamName = fieldInfo.name;
                obj.occupyFaceUrl = fieldInfo.faceUrl;
                obj.occupyTime = occupyTime;
                //获取占领者数据
                let doc = self.playerCache.get(obj.occupyUid);
                if(!doc) {
                    self.loadPlayerDocToCache(obj.occupyUid, function (code, playerDoc) {
                        let occPlayer = self.makeNewPlayerObj(obj.occupyUid, playerDoc);
                        let occList = occPlayer.footballGround.groundMatch.occupyFieldList;
                        obj.occupyName = occPlayer.name || fieldInfo.name;
                        let occInfo;
                        for(let j=0; j<occList.length; j++) {
                            if(occList[j].occupyTeamIndex === index) {
                                obj.reportList = occList[j].beReportedList;
                                occInfo = occList[j];
                                break;
                            }
                        }
                        if(!!occInfo) {
                            if(!occInfo.protectEndTime) occInfo.protectEndTime = 0;
                            obj.leftProtectTime = Math.floor((occInfo.protectEndTime - now) / 1000);
                            obj.leftProtectType = occInfo.protectType;
                            if(obj.leftProtectTime < 0) {
                                obj.leftProtectTime = 0;
                                obj.leftProtectType = 0;
                            }
                        }
                        obj.str = occPlayer.teamFormations.calcTotalRating(obj.occupyTeamUid);
                        let occTeamFormation = occPlayer.teamFormations.getOneTeamFormation(obj.occupyTeamUid);
                        obj.attack = (!occTeamFormation) ? 0 : occTeamFormation.Attack;
                        obj.defend = (!occTeamFormation) ? 0 : occTeamFormation.Defend;
                        cb1(obj);
                    })
                }else {
                    let occPlayer = self.makeNewPlayerObj(obj.occupyUid, doc);
                    let occList = occPlayer.footballGround.groundMatch.occupyFieldList;
                    obj.occupyName = occPlayer.name || fieldInfo.name;
                    let occInfo;
                    for(let j=0; j<occList.length; j++) {
                        if(occList[j].occupyTeamIndex === index) {
                            obj.reportList = occList[j].beReportedList;
                            occInfo = occList[j];
                            break;
                        }
                    }
                    if(!!occInfo) {
                        if(!occInfo.protectEndTime) occInfo.protectEndTime = 0;
                        obj.leftProtectTime = Math.floor((occInfo.protectEndTime - now) / 1000);
                        obj.leftProtectType = occInfo.protectType;
                        if(obj.leftProtectTime < 0) {
                            obj.leftProtectTime = 0;
                            obj.leftProtectType = 0;
                        }
                    }
                    obj.str = occPlayer.teamFormations.calcTotalRating(obj.occupyTeamUid);
                    let occTeamFormation = occPlayer.teamFormations.getOneTeamFormation(obj.occupyTeamUid);
                    obj.attack = (!occTeamFormation) ? 0 : occTeamFormation.Attack;
                    obj.defend = (!occTeamFormation) ? 0 : occTeamFormation.Defend;
                    cb1(obj);
                }
            }else {
                cb1(obj);
            }
        }else {
            cb1(obj);
        }
    };
    let index = 0;
    async.eachSeries(typeList, function (info, cb2) {
        let obj = _preProcessByType(info, self);
        _makeObjInfo(obj, index, function (obj) {
            resultList.push(obj);
            index++;
            cb2();
        });
    }, function (err) {
        logger.debug("onlineService.searchGroundMatchByRandom resultList: ", resultList);
        cb(Code.OK, resultList);
    });
    */
};

OnlineService.prototype.onlineServiceGetPlayerDB = function(playerId, gid, collectionNames, cb) {
    let ret = {code: Code.OK, gid: "", playerDoc: null};
    let self = this;
    async.waterfall([
        function (callback) {
            if(!gid) {
                self.accountDao.findByPlayerId(playerId, function (code, doc) {
                    if (code !== Code.OK) {
                        ret.code = code;
                        return callback(code);
                    }
                    if (!doc || !doc.gid) {
                        logger.warn("OnlineService.getOtherPlayerDocByCache: not gid!", playerId, doc);
                        ret.code = Code.FAIL;
                        return callback(Code.FAIL);
                    }
                    gid = doc.gid;
                    callback();
                })
            }else {
                callback();
            }
        },
        function (callback) {
            //转发到对应服务器
            let session = {frontendId: self.app.getServerId(), toServerId: gid};
            self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, {playerId: playerId, collectionNames: collectionNames}, function (code, playerDoc, isNew) {
                ret.code = code;
                ret.gid = gid;
                ret.playerDoc = playerDoc;
                callback();
            });
        }
    ], function (err) {
        cb(ret.code, ret.gid, ret.playerDoc);
    });
};

//syncDelayTime 同步刷新时间(秒)
OnlineService.prototype.getOtherPlayerDocByCache = function (playerId, gid, collectionNames, syncDelayTime, cb) {
    let self = this;
    if(!syncDelayTime) {
        syncDelayTime = 0;
    }
    //必须包含player数据
    let isHasPlayerDB = false;
    for(let i=0,len=collectionNames.length;i<len;i++) {
        if(collectionNames[i] === commonEnum.DB_NAME.player) {
            isHasPlayerDB = true;
            break;
        }
    }
    if(!isHasPlayerDB) {
        logger.debug("getOtherPlayerDocByCache isHasPlayerDB false!");
        collectionNames.push(commonEnum.DB_NAME.player);
    }
    let oneCache = this.playerCache.get(playerId);
    if(!oneCache) {
        self.onlineServiceGetPlayerDB(playerId, gid, collectionNames, function (code, gid, playerDoc) {
            //数据同步到Cache
            self.syncPlayerDocToCache(playerId, playerDoc, gid, collectionNames, function () {
                return cb(code, gid, playerDoc);
            });
        })
    }else {
        let now = TimeUtil.now();
        //已存在Cache的查看是否在刷新时间内, 直接取cache数据
        if((oneCache.refreshTime + syncDelayTime*1000) >= now) {
            let addList = [];
            for(let i=0,len=collectionNames.length;i<len;i++) {
                if(!oneCache.dbList.has(collectionNames[i])) {
                    addList.push(collectionNames[i]);
                }
            }
            if(addList.length > 0) {
                this.onlineServiceGetPlayerDB(playerId, gid, addList, function (code, gid, playerDoc) {
                    //数据同步到Cache
                    self.syncPlayerDocToCache(playerId, playerDoc, gid, collectionNames, function () {
                        cb(code, gid, oneCache);
                    });
                })
            }else {
                return cb(Code.OK, oneCache[commonEnum.DB_NAME.player].gid, oneCache, 0);
            }
        }else {
            this.onlineServiceGetPlayerDB(playerId, gid, collectionNames, function (code, gid, playerDoc) {
                //数据同步到Cache
                self.syncPlayerDocToCache(playerId, playerDoc, gid, collectionNames, function () {
                    return cb(code, gid, playerDoc);
                });
            })
        }
    }
};

OnlineService.prototype.syncPlayerDocToCache = function (playerId, playerDoc, gid, collectionNames, cb) {
    //1. 查看是否在cache内
    let now = TimeUtil.now();
    let cacheData = this.playerCache.get(playerId);
    logger.debug("syncPlayerDocToCache playerId, gid, collectionNames", playerId, gid, collectionNames);
    //logger.debug("syncPlayerDocToCache 11111", cacheData[commonEnum.DB_NAME.footballGround].groundMatch);
    //logger.debug("syncPlayerDocToCache playerDoc", playerDoc[commonEnum.DB_NAME.footballGround].groundMatch);
    let self = this;
    if(this.playerCache.has(playerId)) {
        //赋值并更新刷新时间
        for(let i=0,len=collectionNames.length;i<len;i++) {
            let dbName = collectionNames[i];
            cacheData[dbName] = playerDoc[dbName];
            if(!cacheData.dbList.has(dbName)) {
                cacheData.dbList.set(dbName, 1);
            }
        }
        if(!cacheData[commonEnum.DB_NAME.footballGround] || !cacheData[commonEnum.DB_NAME.footballGround].uid) {
            logger.debug("no footballGround data in cache.", playerId, cacheData[commonEnum.DB_NAME.footballGround]);
            return cb(Code.OK);
        }
        cacheData[commonEnum.DB_NAME.player].gid = gid;
        cacheData.refreshTime = now;
        for(let i=0, len=this.playerCacheList.length; i<len; i++) {
            if(this.playerCacheList[i].uid === playerId) {
                this.playerCacheList[i].refreshTime = now;
                break;
            }
        }
        let player = this.makeNewPlayerObj(playerId, cacheData);
        this.updateGroundMatchCache(playerId, player);
        //logger.debug("syncPlayerDocToCache 22222", cacheData[commonEnum.DB_NAME.footballGround].groundMatch);
        cb(Code.OK);
    }else {
        //查看最大个数是否超过上限
        if(this.playerCacheList.length > this.maxPlayerCacheNum) {
            logger.debug("delete now ....")
            this.playerCacheListSort();
            //删除最早的refreshTime的player
            let delUid = this.playerCacheList.shift().uid;
            if(this.playerCache.size > this.maxPlayerCacheNum) {
                this.playerCache.delete(delUid);
            }
            //匹配池也同步删除
            this.deleteGroupMatchCache(delUid);
        }
        //从db里面加载基本数据
        let dbList = this.baseDbList;
        this.getOtherPlayerDB(playerId, "", dbList, function (code, playerGid, oneData, isNew) {
            for(let i=0,len=collectionNames.length;i<len;i++) {
                let dbName = collectionNames[i];
                oneData[dbName] = playerDoc[dbName];
                if(!oneData.dbList) {
                    oneData.dbList = new Map();
                }
                if(!oneData.dbList.has(dbName)) {
                    oneData.dbList.set(dbName, 1);
                }
            }
            oneData[commonEnum.DB_NAME.player].gid = gid;
            oneData.refreshTime = now;
            self.playerCache.set(playerId, oneData);
            self.updatePlayerCacheList(playerId, now);
            //同步到匹配池
            let player = self.makeNewPlayerObj(playerId, oneData);
            self.insertGroundMatchCache(playerId, player, gid);
            //logger.debug("syncPlayerDocToCache 33333", oneData[commonEnum.DB_NAME.footballGround].groundMatch);
            cb(Code.OK);
        });
    }
};

OnlineService.prototype.updatePlayerCacheList = function(playerId, now) {
    let isHas = false;
    for(let i=0, len=this.playerCacheList.length; i<len; i++) {
        if(playerId === this.playerCacheList[i].uid) {
            this.playerCacheList[i].refreshTime = now;
            isHas = true;
            break;
        }
    }
    if(!isHas) {
        this.playerCacheList.push({uid: playerId, refreshTime: now});
    }
};

OnlineService.prototype.syncGroundTeamUidToCache = function (playerId, teamIndex, teamUid, cb) {
    let v = this.playerCache.get(playerId);
    if(!v) {
        logger.debug("syncGroundTeamUidToCache do not find in cache.", playerId);
        cb(Code.OK);
    }else {
        let list = v[commonEnum.DB_NAME.footballGround].groundMatch.occupyFieldList;
        for(let i=0;i<list.length;i++) {
            if(list[i].occupyTeamIndex === teamIndex) {
                logger.debug("syncGroundTeamUidToCache sync cache ok.", playerId, teamIndex, teamUid);
                list[i].occupyTeamUid = teamUid;
                break;
            }
        }
        cb(Code.OK);
    }
};

OnlineService.prototype.syncTeamAndHeroDataToCache = function (playerId, teamFormations, heros, cb) {
    let v = this.playerCache.get(playerId);
    if(!v) {
        logger.debug("syncTeamAndHeroDataToCache do not find in cache.", playerId);
        cb(Code.OK);
    }else {
        v[commonEnum.DB_NAME.teamFormation] = teamFormations;
        v[commonEnum.DB_NAME.heros] = heros;
        cb(Code.OK);
    }
};

OnlineService.prototype.syncDocDataToCache = function (playerId, collections, doc, cb) {
    let v = this.playerCache.get(playerId);
    if(!v) {
        logger.debug("syncDocDataToCache do not find in cache.", playerId);
        cb(Code.OK);
    }else {
        for(let i=0; i<collections.length; i++) {
            v[collections[i]] = doc[collections[i]];
        }
        cb(Code.OK);
    }
};

OnlineService.prototype.loadPlayerDocToCache = function (playerId, cb) {
    //1. 查看是否在cache内
    let now = TimeUtil.now();
    let self = this;
    logger.debug("loadPlayerDocToCache playerId: ", playerId, this.playerCache.size);
    if(this.playerCache.has(playerId)) {
        cb(Code.OK, this.playerCache.get(playerId));
    }else {
        /* june
        //查看最大个数是否超过上限
        if(this.playerCacheList.length > this.maxPlayerCacheNum) {
            logger.debug("loadPlayerDocToCache delete 111: ", playerId, this.playerCache.size, this.strengthCache.length,
                this.totalAssetCache.length, this.playerCacheList.length);
            this.playerCacheListSort();
            //删除最早的refreshTime的player
            let delUid = this.playerCacheList.shift().uid;
            if(this.playerCache.size > this.maxPlayerCacheNum) {
                this.playerCache.delete(delUid);
            }
            //匹配池也同步删除
            this.deleteGroupMatchCache(delUid);
            logger.debug("loadPlayerDocToCache delete 222: ", playerId, this.playerCache.size, this.strengthCache.length,
                this.totalAssetCache.length, this.playerCacheList.length);
        }
        */
        //从db里面加载基本数据
        let dbList = this.baseDbList;
        this.getOtherPlayerDB(playerId, "", dbList, function (code, playerGid, oneData) {
            oneData.dbList = new Map();
            for(let i=0;i<dbList.length;i++) {
                oneData.dbList.set(dbList[i], 1);
            }
            oneData[commonEnum.DB_NAME.player].gid = playerGid;
            oneData.refreshTime = now;
            /* june
            self.playerCache.set(playerId, oneData);
            self.updatePlayerCacheList(playerId, now);
            //同步到匹配池
            let player = self.makeNewPlayerObj(playerId, oneData);
            self.insertGroundMatchCache(playerId, player, playerGid);
            logger.debug("loadPlayerDocToCache delete 333: ", playerId, self.playerCache.size, self.strengthCache.length,
                self.totalAssetCache.length, self.playerCacheList.length);
            */
            cb(Code.OK, oneData);
        });
    }
};

OnlineService.prototype.playerCacheListSort = function () {
    let compare = function (v1, v2) {
        if(v1.refreshTime < v2.refreshTime) {
            return 1;
        }else if(v1.refreshTime > v2.refreshTime) {
            return -1;
        }
        return 0;
    };
    this.playerCacheList.sort(compare);
};

OnlineService.prototype.makeNewPlayerObj = function (playerId, doc) {
    let player = new Player(playerId);
    player.initByDB(doc[commonEnum.DB_NAME.player]);
    player.heros.initByDB(doc[commonEnum.DB_NAME.heros]);
    player.teamFormations.initByDB(doc[commonEnum.DB_NAME.teamFormation]);
    player.footballGround.initByDB(doc[commonEnum.DB_NAME.footballGround]);
    player.trainer.initByDB(doc[commonEnum.DB_NAME.trainer]);
    player.heros.checkHeroTreatyDay();
    player.checkFix();
    return player;
};

OnlineService.prototype.updateGroundMatchCache = function (playerId, player) {
    let fieldList = player.footballGround.groundMatch.fieldList;
    for(let i=0, len=fieldList.length; i<len; i++) {
        if(!!fieldList[i].teamUid) {
            let str = player.teamFormations.calcTotalRating(fieldList[i].teamUid);
            let assets = player.teamFormations.calcTeamValue(fieldList[i].teamUid);
            for(let j=0,len1=this.strengthCache.length;j<len1;j++) {
                if(this.strengthCache[j].uid === playerId && this.strengthCache[j].teamUid === fieldList[i].teamUid) {
                    this.strengthCache[j].str = str;
                    this.strengthCache[j].protectEndTime = fieldList[i].protectEndTime;
                    this.strengthCache[j].teamIndex = i;
                }
                if(this.totalAssetCache[j].uid === playerId && this.strengthCache[j].teamUid === fieldList[i].teamUid) {
                    this.totalAssetCache[j].assets = assets;
                    this.strengthCache[j].protectEndTime = fieldList[i].protectEndTime;
                    this.strengthCache[j].teamIndex = i;
                }
            }
        }
    }
};

OnlineService.prototype.insertGroundMatchCache = function (playerId, player, gid) {
    let fieldList = player.footballGround.groundMatch.fieldList;
    //logger.debug("fieldList: ", fieldList, player.name, playerId, player.footballGround.groundMatch);
    let toUpdateList = [];
    for(let i=0, len=fieldList.length; i<len; i++) {
        if(!!fieldList[i].teamUid) {
            let str = player.teamFormations.calcTotalRating(fieldList[i].teamUid);
            let assets = player.teamFormations.calcTeamValue(fieldList[i].teamUid);
            //如果存在该数据就直接更新
            toUpdateList.push({uid: playerId, str: str, gid: gid, teamUid: fieldList[i].teamUid, protectEndTime: fieldList[i].protectEndTime,
                teamIndex: i, assets: assets, isStrUpdate: 0, isAssetUpdate: 0});
        }
    }

    let tmpMap = new Map();
    for(let i=0,len=this.strengthCache.length; i<len; i++) {
        tmpMap.set(this.strengthCache[i].teamUid, i);
    }
    for(let i=0,len=toUpdateList.length; i<len; i++) {
        let index = tmpMap.get(toUpdateList[i].teamUid);
        if(!!index) {
            this.strengthCache[index].str = toUpdateList[i].str;
            this.strengthCache[index].protectEndTime = toUpdateList[i].protectEndTime;
            this.strengthCache[index].teamIndex = toUpdateList[i].teamIndex;
            toUpdateList[i].isStrUpdate = 1;
        }
    }
    tmpMap = new Map();
    for(let i=0,len=this.totalAssetCache.length; i<len; i++) {
        tmpMap.set(this.totalAssetCache[i].teamUid, i);
    }
    for(let i=0,len=toUpdateList.length; i<len; i++) {
        let index = tmpMap.get(toUpdateList[i].teamUid);
        if(!!index) {
            this.totalAssetCache[index].assets = toUpdateList[i].assets;
            this.totalAssetCache[index].protectEndTime = toUpdateList[i].protectEndTime;
            this.totalAssetCache[index].teamIndex = toUpdateList[i].teamIndex;
            toUpdateList[i].isAssetUpdate = 1;
        }
    }
    for(let i=0,len=toUpdateList.length; i<len; i++) {
        if(toUpdateList[i].isStrUpdate === 0) {
            this.strengthCache.push({uid: playerId, str: toUpdateList[i].str, gid: toUpdateList[i].gid, teamUid: toUpdateList[i].teamUid,
                protectEndTime: toUpdateList[i].protectEndTime, teamIndex: toUpdateList[i].teamIndex});
        }
        if(toUpdateList[i].isAssetUpdate === 0) {
            this.totalAssetCache.push({uid: playerId, assets: toUpdateList[i].assets, gid: toUpdateList[i].gid,
                teamUid: toUpdateList[i].teamUid, protectEndTime: toUpdateList[i].protectEndTime, teamIndex: toUpdateList[i].teamIndex});
        }
    }
    //logger.debug("insertGroundMatchCache: ", this.strengthCache.length, this.totalAssetCache.length);
};

OnlineService.prototype.deleteGroupMatchCache = function (playerId) {
    let toDeleteStrList = [];
    let toDeleteAssetList = [];
    for(let i=0, len=this.strengthCache.length;i<len;i++) {
        if(playerId === this.strengthCache[i].uid) {
            toDeleteStrList.push(i);
        }
        if(playerId === this.totalAssetCache[i].uid) {
            toDeleteAssetList.push(i);
        }
    }
    for(let i=0,len=toDeleteStrList.length;i<len;i++) {
        this.strengthCache.splice(toDeleteStrList[i]-i, 1);
    }
    for(let i=0,len=toDeleteAssetList.length;i<len;i++) {
        this.totalAssetCache.splice(toDeleteAssetList[i]-i, 1);
    }
    //logger.debug("deleteGroupMatchCache: this.strengthCache, this.totalAssetCache: ", this.strengthCache.length, this.totalAssetCache.length);
};

OnlineService.prototype.searchGroundMatchByName = function (name, cb) {
    let resultList = [];
    let self = this;
    self.accountDao.getAccountByName(name, function (err, account) {
        if(!!err || !account) {
            logger.debug("getAccountByName not found.");
            return cb(Code.GROUND_MATCH.NO_NAME_IN_DB)
        }
        //logger.debug("searchGroundMatchByName find account: ", account);
        let session = {frontendId: self.app.getServerId(), toServerId: account.gid};
        let now = TimeUtil.now();
        let _makeObjInfo = function(player, fieldInfo, cb2) {
            let obj = {};
            obj.resId = fieldInfo.resId;
            obj.ownerUid = account.uid;
            obj.ownerName = player.name;
            obj.ownerTeamUid = fieldInfo.teamUid;
            obj.ownerGid = account.gid;
            obj.ownerFaceUrl = player.faceUrl;
            //球场等级
            obj.mainGroundLevel = player.footballGround.mainGround.get(player.playerId).Level;
            obj.ownerBallFan = player.footballGround.ballFan;
            let protectEndTime = fieldInfo.protectEndTime;
            if(!protectEndTime) protectEndTime = 0;
            obj.leftProtectTime = Math.floor((protectEndTime - now) / 1000);
            obj.leftProtectType = fieldInfo.protectType;
            if(obj.leftProtectTime < 0) {
                obj.leftProtectTime = 0;
                obj.leftProtectType = 0;
            }
            let formation = player.teamFormations.getOneTeamFormation(fieldInfo.teamUid);
            obj.str = (!formation) ? 0 : player.teamFormations.calcTotalRating(fieldInfo.teamUid);
            obj.attack = (!formation) ? 0 : formation.Attack;
            obj.defend = (!formation) ? 0 : formation.Defend;
            //obj.recordList = fieldInfo.recordList;
            obj.recordList = [];
            if(!!fieldInfo.beOccupiedUid) {
                let maxTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
                let occupyTime = Math.floor((now - fieldInfo.occupyStartTime) / 1000);
                if(occupyTime < 0) occupyTime = 0;
                logger.debug("occupyTime 11111111 : ", occupyTime);
                if(occupyTime > maxTime) occupyTime = maxTime;
                if(fieldInfo.occupyStartTime > 0) {
                    obj.occupyUid = fieldInfo.beOccupiedUid;
                    obj.occupyTeamUid = fieldInfo.beOccupiedTeamUid;
                    obj.occupyTeamName = fieldInfo.name;
                    obj.occupyGid = fieldInfo.beOccupiedGid;
                    obj.occupyFaceUrl = fieldInfo.faceUrl;
                    obj.occupyTime = occupyTime;
                    //获取占领者数据
                    let doc = self.playerCache.get(obj.occupyUid);
                    if(doc) {
                        let occPlayer = self.makeNewPlayerObj(obj.occupyUid, doc);
                        obj.occupyName = occPlayer.name || fieldInfo.name;
                        let occList = occPlayer.footballGround.groundMatch.occupyFieldList;
                        let occInfo;
                        for(let j=0; j<occList.length; j++) {
                            if(occList[j].teamUid === obj.occupyTeamUid) {
                                obj.reportList = occList[j].beReportedList;
                                occInfo = occList[j];
                            }
                        }
                        logger.debug("66666666 ", occInfo, obj.occupyTeamUid, occList);
                        if(!occInfo) occInfo = {protectEndTime: 0, protectType: 0};
                        if(!occInfo.protectEndTime) occInfo.protectEndTime = 0;
                        obj.leftProtectTime = Math.floor((occInfo.protectEndTime - now) / 1000);
                        obj.leftProtectType = occInfo.protectType;
                        if(obj.leftProtectTime < 0) {
                            obj.leftProtectTime = 0;
                            obj.leftProtectType = 0;
                        }
                        obj.str = occPlayer.teamFormations.calcTotalRating(obj.occupyTeamUid);
                        let occTeamFormation = occPlayer.teamFormations.getOneTeamFormation(obj.occupyTeamUid);
                        obj.attack = (!occTeamFormation) ? 0 : occTeamFormation.Attack;
                        obj.defend = (!occTeamFormation) ? 0 : occTeamFormation.Defend;
                        cb2(obj);
                    }else {
                        self.loadPlayerDocToCache(obj.occupyUid, function (code, playerDoc) {
                            let occPlayer = self.makeNewPlayerObj(obj.occupyUid, playerDoc);
                            obj.occupyName = occPlayer.name || fieldInfo.name;
                            let occList = occPlayer.footballGround.groundMatch.occupyFieldList;
                            let occInfo;
                            for(let j=0; j<occList.length; j++) {
                                if(occList[j].teamUid === obj.occupyTeamUid) {
                                    obj.reportList = occList[j].beReportedList;
                                    occInfo = occList[j];
                                }
                            }
                            logger.debug("777777 ", occInfo, obj.occupyTeamUid, occList);
                            if(!occInfo) occInfo = {protectEndTime: 0, protectType: 0};
                            if(!occInfo.protectEndTime) occInfo.protectEndTime = 0;
                            obj.leftProtectTime = Math.floor((occInfo.protectEndTime - now) / 1000);
                            obj.leftProtectType = occInfo.protectType;
                            if(obj.leftProtectTime < 0) {
                                obj.leftProtectTime = 0;
                                obj.leftProtectType = 0;
                            }
                            obj.str = occPlayer.teamFormations.calcTotalRating(obj.occupyTeamUid);
                            let occTeamFormation = occPlayer.teamFormations.getOneTeamFormation(obj.occupyTeamUid);
                            obj.attack = (!occTeamFormation) ? 0 : occTeamFormation.Attack;
                            obj.defend = (!occTeamFormation) ? 0 : occTeamFormation.Defend;
                            cb2(obj);
                        })
                    }
                }else {
                    cb2(obj);
                }
            }else {
                cb2(obj);
            }
        };

        self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, {playerId: account.uid, collectionNames: [commonEnum.DB_NAME.player,
                commonEnum.DB_NAME.teamFormation, commonEnum.DB_NAME.heros, commonEnum.DB_NAME.footballGround,
                commonEnum.DB_NAME.trainer]}, function (code, playerDoc, isNew) {
            logger.debug("searchGroundMatchByName playerDoc", playerDoc[commonEnum.DB_NAME.footballGround].groundMatch);
            let player = self.makeNewPlayerObj(account.uid, playerDoc);
            self.updateGroundMatchCache(account.uid, player);
            let fieldList = player.footballGround.groundMatch.fieldList;
            logger.debug("searchGroundMatchByName fieldList", fieldList);

            let index = 0;
            async.eachSeries(fieldList, function (fieldInfo, cb1) {
                _makeObjInfo(player, fieldInfo, function (obj) {
                    obj.teamIndex = index;
                    resultList.push(obj);
                    index++;
                    cb1();
                })
            }, function (err) {
                cb(Code.OK, resultList);
            });
        });
    });
};

OnlineService.prototype.syncGroundMatchToplistByCache = function () {
    let numList = {};
    for(let i in commonEnum.GROUND_MATCH_TOPLIST_TYPE) {
        this.groundMatchToplist[commonEnum.GROUND_MATCH_TOPLIST_TYPE[i]] = [];
        numList[commonEnum.GROUND_MATCH_TOPLIST_TYPE[i]] = 0;
    }
    let maxNum = 1000; //最多记录1000个用户
    for(let i=0, len=this.totalAssetCache.length; i<len; i++) {
        let info = {};
        info.uid = this.totalAssetCache[i].uid;
        info.teamIndex = this.totalAssetCache[i].teamIndex;
        info.assets = this.totalAssetCache[i].assets;
        let doc = this.playerCache.get(info.uid);
        info.name = doc[commonEnum.DB_NAME.player].name;
        info.faceUrl = doc[commonEnum.DB_NAME.player].faceUrl;
        info.resId = doc[commonEnum.DB_NAME.footballGround].groundMatch.fieldList[info.teamIndex].resId;
        this.groundMatchToplist[commonEnum.GROUND_MATCH_TOPLIST_TYPE.ASSETS_RANK].push(info);
        numList[commonEnum.GROUND_MATCH_TOPLIST_TYPE.ASSETS_RANK]++;
        if(numList[commonEnum.GROUND_MATCH_TOPLIST_TYPE.ASSETS_RANK] >= maxNum) {
            break;
        }
    }
    for(let i=0, len=this.strengthCache.length; i<len; i++) {
        let info = {};
        info.uid = this.strengthCache[i].uid;
        info.teamIndex = this.strengthCache[i].teamIndex;
        info.str = this.strengthCache[i].str;
        let doc = this.playerCache.get(info.uid);
        info.name = doc[commonEnum.DB_NAME.player].name;
        info.faceUrl = doc[commonEnum.DB_NAME.player].faceUrl;
        info.resId = doc[commonEnum.DB_NAME.footballGround].groundMatch.fieldList[info.teamIndex].resId;
        //logger.error("yyyyyyyyyyyyyyyyyy", info.resId, info.teamIndex, doc[commonEnum.DB_NAME.footballGround]);
        let typeName = dataApi.allData.data["GroundMatchField"][info.resId].Name;
        let type = 0;
        if(typeName === "世界级") {
            type = commonEnum.GROUND_MATCH_TOPLIST_TYPE.WOLRD_RANK;
        }else if(typeName === "洲际级") {
            type = commonEnum.GROUND_MATCH_TOPLIST_TYPE.INTERCONTINENTAL_RANK;
        }else if(typeName === "国家级") {
            type = commonEnum.GROUND_MATCH_TOPLIST_TYPE.COUNTRY_RANK;
        }else if(typeName === "地区级") {
            type = commonEnum.GROUND_MATCH_TOPLIST_TYPE.AREA_RANK;
        }else if(typeName === "社区级") {
            type = commonEnum.GROUND_MATCH_TOPLIST_TYPE.COMMUNITY_RANK;
        }
        if(type > 0) {
            if(numList[type] >= maxNum) {
                continue;
            }
            this.groundMatchToplist[type].push(info);
            numList[type]++;
        }
    }
    let compare_1 = function (v1, v2) {
        if(v1.str < v2.str) {
            return 1;
        }else if(v1.str > v2.str) {
            return -1;
        }
        return 0;
    };
    let compare_2 = function (v1, v2) {
        if(v1.assets < v2.assets) {
            return 1;
        }else if(v1.assets > v2.assets) {
            return -1;
        }
        return 0;
    };
    for(let i in commonEnum.GROUND_MATCH_TOPLIST_TYPE) {
        if(commonEnum.GROUND_MATCH_TOPLIST_TYPE[i] !== commonEnum.GROUND_MATCH_TOPLIST_TYPE.ASSETS_RANK) {
            this.groundMatchToplist[commonEnum.GROUND_MATCH_TOPLIST_TYPE[i]].sort(compare_1);
        }else {
            this.groundMatchToplist[commonEnum.GROUND_MATCH_TOPLIST_TYPE[i]].sort(compare_2);
        }
    }
    //logger.debug("after syncGroundMatchToplistByCache: ", this.groundMatchToplist, this.groundMatchToplist[commonEnum.GROUND_MATCH_TOPLIST_TYPE.ASSETS_RANK]);
};

OnlineService.prototype.getGroundMatchToplist = function (type, playerId, cb) {
    let toplist = this.groundMatchToplist[type];
    let myRank = {};
    logger.debug("getGroundMatchToplist : ", type, playerId);
    for(let i=0, len=toplist.length; i<len; i++) {
        if(playerId === toplist[i].uid) {
            myRank.rank = i;
            myRank.name = toplist[i].name;
            if(type === commonEnum.GROUND_MATCH_TOPLIST_TYPE.ASSETS_RANK) {
                myRank.assets = toplist[i].assets;
            }
            myRank.resId = toplist[i].resId;
            break;
        }
    }
    //取前100
    let clientToplist = [];
    for(let i=0;i<100;i++) {
        if(!toplist[i]) {
            break;
        }
        clientToplist.push(toplist[i]);
    }
    cb(Code.OK, clientToplist, myRank);
};

OnlineService.prototype.searchAndLockOwnerField = function (ownerUid, teamIndex, cb) {
    let key = ownerUid + '_' + teamIndex;
    let now = TimeUtil.now();
    if(this.groundMatchLockList.has(key)) {
        let time = this.groundMatchLockList.get(key);
        let passTime = now - time;
        if(passTime < 20 * 1000) {
            return cb(Code.GROUND_MATCH.FIELD_IS_LOCK);
        }
    }else {
        this.groundMatchLockList.set(key, now);
    }
    cb(Code.OK);
};

OnlineService.prototype.unlockOwnerField = function (ownerUid, teamIndex, cb) {
    let key = ownerUid + '_' + teamIndex;
    this.groundMatchLockList.delete(key);
    cb(Code.OK);
};

OnlineService.prototype.tenMiniteLoop = function () {
    //每10分钟排一次序
    this.strengthCacheSort();
    this.totalAssetCacheSort();
    this.syncGroundMatchToplistByCache();
};

OnlineService.prototype.getBeliefListForAS = function (playerList, cb) {
    this.accountDao.getPlayerListAS(playerList, function (list) {
        list.sort(__ActualStrength_rank_func);
        cb(list);
    });
};

function __ActualStrength_rank_func(rankObj1, rankObj2) {
    if(rankObj1.actualStrength < rankObj2.actualStrength) {
        return 1;
    }else if(rankObj1.actualStrength > rankObj2.actualStrength) {
        return -1;
    }
    return 0;
}

OnlineService.prototype.addFieldCacheByPlayerData = function (playerId, gid, teamIndex) {
    let playerDoc = this.playerCache.get(playerId);
    let player = this.makeNewPlayerObj(playerId, playerDoc);
    if(!player) return ;
    let playerField = player.footballGround.groundMatch.fieldList[teamIndex];
    if(!playerField) return ;

    let fieldInfo = {};
    fieldInfo.resId = playerField.resId;
    fieldInfo.ownerUid = playerId;
    fieldInfo.ownerName = player.name;
    fieldInfo.ownerTeamUid = playerField.teamUid;
    fieldInfo.ownerTeamName = player.name;
    fieldInfo.ownerGid = gid;
    fieldInfo.ownerFaceUrl = player.faceUrl;
    //logger.debug("player.footballGround.mainGround: ", player.footballGround.mainGround);
    fieldInfo.mainGroundLevel = player.footballGround.mainGround.get(playerId).Level;
    fieldInfo.occupyStartTime = playerField.occupyStartTime;
    fieldInfo.protectType = playerField.protectType;
    fieldInfo.protectEndTime = playerField.protectEndTime;
    fieldInfo.str = player.teamFormations.calcTotalRating(playerField.teamUid);
    let teamFormation = player.teamFormations.getOneTeamFormation(playerField.teamUid);
    fieldInfo.attack = (!teamFormation) ? 0 : teamFormation.Attack;
    fieldInfo.defend = (!teamFormation) ? 0 : teamFormation.Defend;
    fieldInfo.ballFan = player.footballGround.ballFan;
    fieldInfo.assets = player.teamFormations.calcTeamValue(playerField.teamUid);

    if(!playerField.beOccupiedUid) {
        this.groundMatchCache.set(playerId+'_'+teamIndex, {fieldInfo: fieldInfo, occupyInfo: {}});
        return ;
    }
    let occPlayer = this.makeNewPlayerObj(playerField.beOccupiedUid, this.playerCache.get(playerField.beOccupiedUid));
    if(!occPlayer) {
        this.groundMatchCache.set(playerId+'_'+teamIndex, {fieldInfo: fieldInfo, occupyInfo: {}});
        return ;
    }
    let occupyInfo = {};
    let occList = occPlayer.footballGround.groundMatch.occupyFieldList;
    let occInfo;
    for(let j=0; j<occList.length; j++) {
        if(occList[j].teamUid === playerField.beOccupiedTeamUid) {
            occInfo = occList[j];
            break;
        }
    }
    if(!occInfo) {
        this.groundMatchCache.set(playerId+'_'+teamIndex, {fieldInfo: fieldInfo, occupyInfo: {}});
        return ;
    }

    occupyInfo.occupyUid = playerField.beOccupiedUid;
    occupyInfo.occupyGid = playerField.beOccupiedGid;
    occupyInfo.occupyName = occPlayer.name;
    occupyInfo.occupyTeamUid = playerField.beOccupiedTeamUid;
    occupyInfo.occupyTeamIndex = teamIndex;
    occupyInfo.occupyTeamName = playerField.beOccupiedTeamName;
    occupyInfo.occupyFaceUrl = occPlayer.faceUrl;
    occupyInfo.protectType = occInfo.protectType;
    occupyInfo.protectEndTime = occInfo.protectEndTime;
    occupyInfo.str = occPlayer.teamFormations.calcTotalRating(playerField.beOccupiedTeamUid);
    let occTeam = occPlayer.teamFormations.getOneTeamFormation(playerField.beOccupiedTeamUid);
    occupyInfo.attack = (!occTeam) ? 0 : occTeam.Attack;
    occupyInfo.defend = (!occTeam) ? 0 : occTeam.Defend;
    occupyInfo.reportList = occInfo.beReportedList;
    //occupyInfo.lastBeReportTime = occInfo.lastBeReportTime;
    this.groundMatchCache.set(playerId+'_'+teamIndex, {fieldInfo: fieldInfo, occupyInfo: occupyInfo});
};

//更新球场所有者数据变更
OnlineService.prototype.syncGroundMatchDataToDatanode = function (uid, teamIndex, syncType, syncData, cb) {
    let key = uid+'_'+teamIndex;
    if(!this.groundMatchCache.has(key)) {
        logger.debug("syncFieldInfoToDatanode no need to fresh cache.");
        return cb(Code.OK);
    }
    let oneCache = this.groundMatchCache.get(key);
    let cacheField = oneCache.fieldInfo;
    let cacheOccupy = oneCache.occupyInfo;
    let now = TimeUtil.now();
    logger.debug("syncGroundMatchDataToDatanode before: ", oneCache, syncData);

    switch (syncType) {
        case commonEnum.GROUND_MATCH_SYNC_TYPE.OCCUPY_TIME_OVER:
            //清除球场占领者数据
            cacheField.occupyStartTime = 0;
            delete oneCache.occupyInfo;
            this.groundMatchCache.get(key).occupyInfo = {};
            break;
        case commonEnum.GROUND_MATCH_SYNC_TYPE.SET_FORMATION:
            if(syncData.srcIndex > 2) {
                cacheField.ownerTeamUid = syncData.teamUid;
            }else {
                cacheField.ownerTeamUid = syncData.teamUid;
                let tmpCache = this.groundMatchCache.get(uid+'_'+syncData.srcIndex);
                if(tmpCache && tmpCache.fieldInfo) {
                    tmpCache.fieldInfo.ownerTeamUid = "";
                }
            }
            break;
        case commonEnum.GROUND_MATCH_SYNC_TYPE.ROB_EVENT:
            cacheField.occupyStartTime = now;
            cacheOccupy.occupyUid = syncData.occupyInfo.occupyUid;
            cacheOccupy.occupyGid = syncData.occupyInfo.occupyGid;
            cacheOccupy.occupyName = syncData.occupyInfo.occupyName;
            cacheOccupy.occupyTeamUid = syncData.occupyInfo.occupyTeamUid;
            cacheOccupy.occupyTeamIndex = syncData.teamIndex;
            cacheOccupy.occupyTeamName = syncData.occupyInfo.occupyTeamName;
            cacheOccupy.occupyFaceUrl = syncData.occupyInfo.occupyFaceUrl;
            cacheOccupy.protectType = syncData.occupyInfo.protectType;
            cacheOccupy.protectEndTime = syncData.occupyInfo.protectEndTime;
            cacheOccupy.str = syncData.occupyInfo.str;
            cacheOccupy.attack = syncData.occupyInfo.attack;
            cacheOccupy.defend = syncData.occupyInfo.defend;
            break;
        case commonEnum.GROUND_MATCH_SYNC_TYPE.DRIVE_AWAY:
            cacheField.occupyStartTime = 0;
            delete oneCache.occupyInfo;
            this.groundMatchCache.get(key).occupyInfo = {};
            break;
        case commonEnum.GROUND_MATCH_SYNC_TYPE.LEAVE_AWAY:
            cacheField.occupyStartTime = 0;
            delete oneCache.occupyInfo;
            this.groundMatchCache.get(key).occupyInfo = {};
            break;
        case commonEnum.GROUND_MATCH_SYNC_TYPE.USE_PROTECT:
            if(syncData.teamType === commonEnum.FORMATION_TYPE.GROUND_DEF) {
                cacheField.protectType = syncData.protectType;
                cacheField.protectEndTime = syncData.protectEndTime;
            }else if(cacheOccupy.occupyUid) {
                cacheOccupy.protectType = syncData.protectType;
                cacheOccupy.protectEndTime = syncData.protectEndTime;
            }
            break;
        case commonEnum.GROUND_MATCH_SYNC_TYPE.REPORT:
            if(cacheOccupy.occupyUid) {
                cacheOccupy.reportList = syncData.reportList;
            }
            break;
        case commonEnum.GROUND_MATCH_SYNC_TYPE.UPGRADE_MAIN_GROUND:
            cacheField.mainGroundLevel = syncData.mainGroundLevel;
            cacheField.ballFan = syncData.ballFan;
            cacheField.resId = syncData.maxResId;
            cacheField = this.groundMatchCache.get(syncData.uid+'_'+1);
            cacheField.mainGroundLevel = syncData.mainGroundLevel;
            cacheField.ballFan = syncData.ballFan;
            cacheField.resId = syncData.maxResId-1;
            cacheField = this.groundMatchCache.get(syncData.uid+'_'+2);
            cacheField.mainGroundLevel = syncData.mainGroundLevel;
            cacheField.ballFan = syncData.ballFan;
            cacheField.resId = syncData.maxResId-2;
            break;
    }

    logger.debug("syncGroundMatchDataToDatanode after: ", oneCache);
    cb(Code.OK);
};
