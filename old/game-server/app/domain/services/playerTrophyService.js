/**
 * Idea and Persist
 * Created by <PERSON> on 2019/7/15.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var calc = require('../../util/calc');
var TimeUtils = require('../../util/timeUtils');
var commonEnum = require('../../../../shared/enum');
var dataApi = require('../../util/dataApi');

module.exports.create = function(app, dbclient) {
	return new PlayerTrophyService(app, dbclient);
};

var PlayerTrophyService = function (app, dbclient) 
{
	this.app = app;
	this.teamActStrMap = new Map();   //杯赛副本实力缓存(这部分缓存代码和playerService差不多，因策划要可控阵容id,算出来的实力值会有差别，所以这里也要一个类似的结构)
};

util.inherits(PlayerTrophyService, EventEmitter);

PlayerTrophyService.prototype.initActStr = function()
{
	var copyConfig = dataApi.allData.data["Trophy"];
	var teamConfig = dataApi.allData.data["TrophyTeam"];
	if (!copyConfig) {
		logger.error("initActStr: copyConfig not found!");
		return;
	}

	if (!teamConfig) {
		logger.error("initActStr: teamConfig not found!");
		return;
	}
	let playerService = this.app.get("playerService");

	//默认初始化取第一条数据
	for (var key in copyConfig) 
	{
		const data = copyConfig[key];
		if (!data) continue;
		var trophyId = data.Id;
		if (!trophyId || trophyId <= 0) continue;
		for (let idx in teamConfig) 
		{
			const config = teamConfig[idx];
			if (config.CopyType === trophyId && config.TeamID > 0) 
			{
				this.teamActStrMap.set(config.TeamID, playerService.calcEnemyRating(config.TeamID, config.Formation, config.Lv));
			}
		}
	}
};

//获取界面信息
PlayerTrophyService.prototype.getTrophy = function(playerId, msg, cb) 
{
	let trophyInfo = {};
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerTrophyService.getTrophy: player not exist !', playerId);
		cb(Code.FAIL, trophyInfo);
		return;
	}

	//跨天检查
	player.trophyCopy.checkCrossDay();
	trophyInfo = player.trophyCopy.makeClientTrophyList();
	let limitType = player.trophyCopy.getBattleNumLimitType();
	logger.info("PlayerTrophyService.getTrophy: trophyInfo", playerId, limitType, trophyInfo);
	cb(Code.OK, trophyInfo, limitType);
	return;
};

//搜索
PlayerTrophyService.prototype.getTrophyTeam = function(playerId, msg, cb)
{
	let trophyTeamInfo = {};
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerTrophyService.getTrophyTeam: player not exist !', playerId);
		cb(Code.FAIL, trophyTeamInfo);
		return;
	}

	let trophyId = msg.trophyId;
	if (!trophyId || trophyId <= 0)
	{
		logger.error('PlayerTrophyService.getTrophyTeam: trophyId not exist !', playerId);
		cb(Code.FAIL, trophyTeamInfo); 
		return;
	}

	trophyTeamInfo = player.trophyCopy.makeClientTeamCopyList(trophyId);
	for(let idx in trophyTeamInfo)
	{
		let copy =  trophyTeamInfo[idx];
		copy.actualStrength = this.teamActStrMap.get(copy.teamId);
		//logger.info("PlayerTrophyService.getTrophyTeam: teamId, actualStrength", copy.teamId, copy.actualStrength);
	}

	//logger.info("PlayerTrophyService.getTrophyTeam:", trophyId, trophyTeamInfo);
	cb(Code.OK, trophyTeamInfo);
	return;
};

PlayerTrophyService.prototype.trophyBattle = function(playerId, msg, cb) 
{
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player) {
		logger.debug('PlayerTrophyService.trophyBattle: player not exist !');
		cb(Code.FAIL, enemyInfo, enemyFanRankInfo, matchResult);
		return;
	}

	let trophyId = msg.trophyId;
	let teamId = msg.teamId;
	if (!trophyId || trophyId<= 0) {
		logger.error("PlayerService.trophyBattle: argc is error", playerId, trophyId, teamId);
		cb(Code.FAIL, "");
		return;
	}
	if (!teamId || teamId<= 0) 
	{
		logger.error("PlayerService.trophyBattle: argc is error", playerId, trophyId, teamId);
		cb(Code.FAIL, "");
		return;
	}
	
	let retCode = player.trophyCopy.pveBattleCheck(trophyId, teamId);
	if (retCode !== Code.OK)
	{
		cb(retCode, "");
		return;
	}

	var result = player.trophyCopy.getTrophyTeamConfig(trophyId, teamId);
	var config = result.config;
	if (result.code !== Code.OK)
	{
		logger.error("PlayerTrophyService.trophyBattle: getTrophyTeamConfig failed!", trophyId);
		cb(Code.FAIL, "");
		return;
	}

	//检查消耗
    let consumeType = config.ConsumeType;
    let consumeValue = config.ConsumeValue;
	if(consumeType === commonEnum.CURRENCY_TYPE.CASH) //欧元
	{
		if (!player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, consumeValue))
		{
			logger.error("PlayerTrophyService.trophyBattle: cash is not enough", playerId, player.cash, consumeValue);
			cb(Code.CASH_FALL, "");
			return;
		}

		//检查都过了，扣除资源
		player.subtractResource(commonEnum.PLAY_INFO.cash, consumeValue);
		player.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value:player.cash}]); //欧元变更通知
	}
	else if (consumeType === commonEnum.CURRENCY_TYPE.GOLD) //懂币
	{
		if (!player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, consumeValue))
		{
			logger.error("PlayerTrophyService.trophyPurchase: glod is not enough", playerId, player.gold, consumeValue);
			cb(Code.GOLD_FALL, "");
			return;
		}

		//检查都过了，扣除资源
		player.subtractResource(commonEnum.PLAY_INFO.gold, consumeValue)
		player.upPlayerInfo([{type: commonEnum.PLAY_INFO.gold, value: player.gold}]); //金币变更通知
	}else if (consumeType === commonEnum.CURRENCY_TYPE.ENERGY)
	{
		if (!player.checkResourceIsEnough(commonEnum.PLAY_INFO.energy, consumeValue))
		{
			logger.error("PlayerTrophyService.trophyPurchase: energy is not enough", playerId, player.energy, consumeValue);
			cb(Code.ENERGY_FAIL, "");
			return;
		}

		//检查都过了，扣除资源
		player.subtractResource(commonEnum.PLAY_INFO.energy, consumeValue)
		player.upPlayerInfo([{type: commonEnum.PLAY_INFO.energy, value: player.energy}]); //金币变更通知
	}
	else
	{
		logger.info("PlayerTrophyService.trophyPurchase: config is error!", trophyId, config);
		cb(Code.FAIL, "");
		return;
	}

	let limitType = player.trophyCopy.getBattleNumLimitType();
	logger.info("limitType", playerId, limitType);
	switch (limitType) 
	{
        case commonEnum.TROPHY_LIMIT_TYPE.NO_LIMIT: //无限制
			logger.info("not limit", limitType);
            break;
		case commonEnum.TROPHY_LIMIT_TYPE.TOTAL_LIMIT: //副本限制
			var num = player.trophyCopy.getTrophyNum(trophyId, teamId, commonEnum.TROPHY_UPDATE_TYPE.TOTAL_NUM);
			player.trophyCopy.updateTrophyNum(trophyId, teamId, commonEnum.TROPHY_UPDATE_TYPE.TOTAL_NUM, num + 1);
            break;
		case commonEnum.TROPHY_LIMIT_TYPE.TEAM_LIMIT: //队伍限制
			var num = player.trophyCopy.getTrophyNum(trophyId, teamId, commonEnum.TROPHY_UPDATE_TYPE.TEAM_NUM);
			player.trophyCopy.updateTrophyNum(trophyId, teamId, commonEnum.TROPHY_UPDATE_TYPE.TEAM_NUM, num + 1);
            break;
        default:
            logger.error("PlayerService.trophyBattle: not case hint", type);
            break;
    }
	
	msg.configId = config.Id; //表格id
	let self = this;
	let tmpSession = {frontendId: this.app.getServerId()};
	msg.playerId = playerId;
	//通知BattleSvr
	this.app.rpc.battle.battleRemote.pveTrophyBattleStart(tmpSession, msg, function (code, roomUid) {
		if(code !== Code.OK) {
			logger.warn("rpc pveTrophyBattleStart fail, ret code: ", code);
			cb(code, "");
			return;
		}

		cb(code, roomUid);
		return;
	});
};

PlayerTrophyService.prototype.updateTrophyResult = function(playerId, trophyId, teamId, selfScore, enemyScore, cb)
{
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if (!player){
		logger.error("PlayerTrophyService.updateTrophyResult player not exist!", playerId);
		cb(Code.FAIL);
		return;
	}

	logger.info("PlayerTrophyService.updateTrophyResult", trophyId, teamId, selfScore, enemyScore);
	let ret = player.trophyCopy.pveTrophyCopyBattle(trophyId, teamId, selfScore, enemyScore);
	if (ret.code !== Code.OK) {
		logger.error("PlayerTrophyService.updateTrophyResult error reason: get getLeagueCopyData error!");
		return cb(ret.code);
	}

	var trophyCopyData = ret.trophyCopyData;
	for(let idx in trophyCopyData)
	{
		let copy =  trophyCopyData[idx];
		copy.actualStrength = this.teamActStrMap.get(copy.teamId);
		//logger.info("updateTrophyResult: teamId, actualStrength", copy.teamId, copy.actualStrength);
	}

	this.app.get("pushMessageService").unicast("game.playerHandler.updateTrophyResult", { 
		selfScore: ret.selfScore, enemyScore: ret.enemyScore, trophyId: trophyId, teamId: teamId, 
		trophyTeamInfo: ret.trophyCopyData, trophyInfo: ret.trophyInfo}, player);

	let itemUidList = ret.itemUidList;
	if (itemUidList.length > 0)
	{
		player.updateBag();
		player.saveItem();
		player.saveBag();
	}

	var heroUidList = ret.heroUidList;
	if (heroUidList && heroUidList.length > 0) 
	{
		player.saveHeros();
		for(let idx in heroUidList)
		{
			let heroUid = heroUidList[idx];
			player.upAddHero(heroUid);
			logger.info('PlayerTrophyService.updateTrophyResult ....!', heroUid);
		}
	}

	player.saveTrophyCopy();
	cb(Code.OK);
	return;
};

PlayerTrophyService.prototype.trophyPurchase = function(playerId, msg, cb) 
{
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player) {
		logger.debug('PlayerTrophyService.trophyPurchase: player not exist !', playerId);
		cb(Code.FAIL, 0, {});
		return;
	}

	let trophyId = msg.trophyId;
	if (!trophyId)
	{
		logger.info("PlayerTrophyService.trophyPurchase: trophyId not exist!");
		cb(Code.FAIL, 0, {});
		return ;
	}

	if (!player.trophyCopy.getOneTrophy(trophyId))
	{
		logger.error("PlayerTrophyService.trophyPurchase: trophyId not found", trophyId);
		cb(Code.TROPHY.NOT_FOUND_TROPHY_ID, 0, {});
		return ;
	}

	let num = player.trophyCopy.getTrophyNum(trophyId, 0, commonEnum.TROPHY_UPDATE_TYPE.TOTAL_NUM);
	var result = player.trophyCopy.getTrophyConfig(trophyId);
	var config = result.config;
	if (result.code !== Code.OK)
	{
		logger.error("PlayerTrophyService.trophyPurchase: getTrophyConfig failed", trophyId);
		cb(Code.FAIL, 0, {});
		return;
	}

 	//当前次数还有消耗完,不允许购买
	if (num < config.LimitNum)
	{
		logger.error("PlayerTrophyService.trophyPurchase: trophy num is enough, not allow purchase", num, config.LimitNum);
		cb(Code.TROPHY.NUM_IS_LEFT_NOT_ALLOW_PURCHASE, 0, {});
		return;
	}

    let purchaseType = config.PurchaseType;
    let purchaseMaxNum = config.PurchaseMaxNum;
    let purchaseParameters = config.PurchaseParameters;
	//检查是否还有购买次数
	let purchaseNum = player.trophyCopy.getTrophyNum(trophyId, 0, commonEnum.TROPHY_UPDATE_TYPE.TOTAL_ALREADY_PURCHASE_NUM);
	//已经达到最大购买次数
	if (purchaseNum >= purchaseMaxNum)
	{
		logger.error("PlayerTrophyService.trophyPurchase: trophy purchaseNum than  purchaseMaxNum!", num, config.LimitNum);
		cb(Code.TROPHY.TROPHY_PURCHASE_NUM_NOT_ENOUGH, 0, {});
		return;
	}

	if(purchaseType === commonEnum.CURRENCY_TYPE.CASH) //欧元
	{
		if (!player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, purchaseParameters))
		{
			logger.error("PlayerTrophyService.trophyPurchase: cash is not enough", playerId, player.cash, purchaseParameters);
			cb(Code.CASH_FALL, 0 , {});
			return;
		}
		//检查都过了，扣除资源
		player.subtractResource(commonEnum.PLAY_INFO.cash, purchaseParameters);
		player.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value: player.cash}]); //欧元变更通知
	}
	else if (purchaseType === commonEnum.CURRENCY_TYPE.GOLD) //懂币
	{
		if (!player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, purchaseParameters))
		{
			logger.error("PlayerTrophyService.trophyPurchase: gold is not enough", playerId, player.gold, purchaseParameters);
			cb(Code.GOLD_FALL, 0 , {});
			return;
		}
		//检查都过了，扣除资源
		player.subtractResource(commonEnum.PLAY_INFO.gold, purchaseParameters)
		player.upPlayerInfo([{type: commonEnum.PLAY_INFO.gold, value: player.gold}]); //金币变更通知
	}else
	{
		logger.info("PlayerTrophyService.trophyPurchase: config is error!", trophyId, config);
		cb(Code.FAIL, 0, {});
		return;
	}
	
	//次数恢复上去
	let afterNum = num - 1;
	let afterPurchaseNum = purchaseNum + 1;
	player.trophyCopy.updateTrophyNum(trophyId, 0, commonEnum.TROPHY_UPDATE_TYPE.TOTAL_NUM, afterNum);  //
	player.trophyCopy.updateTrophyNum(trophyId, 0, commonEnum.TROPHY_UPDATE_TYPE.TOTAL_ALREADY_PURCHASE_NUM, afterPurchaseNum);//购买次数加1
	let limitInfo = 
	{
		num: afterNum,
		limitNum: config.LimitNum,
		alreadyPurchaseNum: afterPurchaseNum,
	};

	cb(Code.OK, trophyId, limitInfo);
	return;
};

PlayerTrophyService.prototype.trophyTeamPurchase = function(playerId, msg, cb) 
{
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player) {
		logger.debug('PlayerTrophyService.trophyTeamPurchase: player not exist !');
		cb(Code.FAIL, 0, 0, {});
		return;
	}

	let trophyId = msg.trophyId;
	let teamId = msg.teamId;
	if (!trophyId)
	{
		logger.info("PlayerTrophyService.trophyTeamPurchase: trophyId not exist!");
		cb(Code.FAIL, 0,0, {});
		return ;
	}

	if (!teamId)
	{
		logger.info("PlayerTrophyService.trophyTeamPurchase: teamId not exist!");
		cb(Code.FAIL, 0, 0, {});
		return ;
	}

	if (!player.trophyCopy.getOneTrophy(trophyId))
	{
		logger.error("PlayerTrophyService.trophyTeamPurchase: trophyId not found", trophyId);
		cb(Code.TROPHY.NOT_FOUND_TROPHY_ID, 0, 0,{});
		return ;
	}

	if (!player.trophyCopy.getOneTrophyByCopyId(teamId))
	{
		logger.error("PlayerTrophyService.trophyTeamPurchase: teamId not found", trophyId, teamId);
		cb(Code.TROPHY.NOT_FOUND_TEAM_ID, 0, 0,{});
		return;
	}

	let num = player.trophyCopy.getTrophyNum(trophyId, teamId, commonEnum.TROPHY_UPDATE_TYPE.TEAM_NUM);
	var result = player.trophyCopy.getTrophyTeamConfig(trophyId, teamId);
	var config = result.config;
	if (result.code !== Code.OK)
	{
		logger.error("PlayerTrophyService.trophyTeamPurchase: getTrophyTeamConfig failed!", trophyId);
		cb(Code.FAIL, 0, 0, {});
		return;
	}

 	//当前次数还有消耗完,不允许购买
	if (num < config.LimitNum)
	{
		logger.error("PlayerTrophyService.trophyTeamPurchase: trophy num is enough, not allow purchase", num, config.LimitNum);
		cb(Code.TROPHY.NUM_IS_LEFT_NOT_ALLOW_PURCHASE, 0, 0, {});
		return;
	}

    let purchaseType = config.PurchaseType;
    let purchaseMaxNum = config.PurchaseMaxNum;
    let purchaseParameters = config.PurchaseParameters;
	//检查是否还有购买次数
	let purchaseNum = player.trophyCopy.getTrophyNum(trophyId, teamId, commonEnum.TROPHY_UPDATE_TYPE.TEAM_ALREADY_PURCHASE_NUM);
	//已经达到最大购买次数
	if (purchaseNum >= purchaseMaxNum)
	{
		logger.error("PlayerTrophyService.trophyTeamPurchase: trophy purchaseNum than  purchaseMaxNum!", num, config.LimitNum);
		cb(Code.TROPHY.TROPHY_PURCHASE_NUM_NOT_ENOUGH, 0, 0, {});
		return;
	}

	if(purchaseType === commonEnum.CURRENCY_TYPE.CASH) //欧元
	{
		if (!player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, purchaseParameters))
		{
			logger.error("PlayerTrophyService.trophyTeamPurchase: cash is not enough", playerId, player.cash, purchaseParameters);
			cb(Code.CASH_FALL, 0, 0, {});
			return;
		}

		//检查都过了，扣除资源
		player.subtractResource(commonEnum.PLAY_INFO.cash, purchaseParameters);
		player.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value: player.cash}]); //欧元变更通知
	}
	else if (purchaseType === commonEnum.CURRENCY_TYPE.GOLD) //懂币
	{
		if (!player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, purchaseParameters))
		{
			logger.error("PlayerTrophyService.trophyPurchase: glod is not enough", playerId, player.gold, purchaseParameters);
			cb(Code.GOLD_FALL, 0, 0, {});
			return;
		}

		//检查都过了，扣除资源
		player.subtractResource(commonEnum.PLAY_INFO.gold, purchaseParameters)
		player.upPlayerInfo([{type: commonEnum.PLAY_INFO.gold, value: player.gold}]); //金币变更通知
	}else if (purchaseType === commonEnum.CURRENCY_TYPE.ENERGY)
	{
		if (!player.checkResourceIsEnough(commonEnum.PLAY_INFO.energy, purchaseParameters))
		{
			logger.error("PlayerTrophyService.trophyPurchase: energy is not enough", playerId, player.energy, purchaseParameters);
			cb(Code.ENERGY_FAIL, 0, 0, {});
			return;
		}
		//检查都过了，扣除资源
		player.subtractResource(commonEnum.PLAY_INFO.energy, purchaseParameters)
		player.upPlayerInfo([{type: commonEnum.PLAY_INFO.energy, value: player.energy}]); //金币变更通知
	}
	else
	{
		logger.info("PlayerTrophyService.trophyPurchase: config is error!", trophyId, config);
		cb(Code.FAIL, 0, 0, {});
		return;
	}
	
	//次数恢复上去
	let afterNum = num - 1;
	let afterPurchaseNum = purchaseNum + 1;
	player.trophyCopy.updateTrophyNum(trophyId, teamId, commonEnum.TROPHY_UPDATE_TYPE.TEAM_NUM, afterNum);  //
	player.trophyCopy.updateTrophyNum(trophyId, teamId, commonEnum.TROPHY_UPDATE_TYPE.TEAM_ALREADY_PURCHASE_NUM, afterPurchaseNum);//购买次数加1

	let limitInfo = {
		num: afterNum,
		limitNum: config.LimitNum,
		alreadyPurchaseNum: afterPurchaseNum,
	};

	cb(Code.OK, trophyId, teamId, limitInfo);
	return;
};