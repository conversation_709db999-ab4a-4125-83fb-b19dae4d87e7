/**
 * Created by shine on 2015/3/20.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var pomelo = require('pomelo');

module.exports = function(){
    return new Filter();
};

var Filter = function(){

};

Filter.prototype.before = function(msg, session, next){
    if(!pomelo.app.get('isReady')){
        logger.error("gconnector server is not ready. msg=" + JSON.stringify(msg));
        next("gconnector server not ready");
        return;
    }
    next(null);
};

