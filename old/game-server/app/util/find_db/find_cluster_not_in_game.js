let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');

let clusterConfig = require('../../../config/cluster.json');
let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');

let fs = require('fs');

/******************** 查询cluster多余的账号信息 ********************/

let isJustFind = true;

let clusterCollections = [
    "account",
    "matchRank"
];

//to do list: 1. 连接cluster, 连接game, 2.
let clusterDb = {};
let gameDbList = {};

let allCheckMap = {
    "account" : new Map(),
    "matchRank" : new Map()
};

let deleteResult = {
    "account" : [],
    "matchRank" : []
};

async.waterfall([
    function (callback) {
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let clusterUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(clusterUrl, { useNewUrlParser: true }, function (error, dbclient) {
            if(error){
                logger.error("connect clusterDBUrl failed! err: " + error);
                return callback(error);
            }
            clusterDb = dbclient;
            let db = dbclient.db(clusterConfig.clusterDBName);
            async.eachSeries(clusterCollections, function (colName, cb) {
                db.collection(colName, function (err, col) {
                    var cursor = col.find();
                    cursor.forEach(function (account) {
                        allCheckMap[colName].set(account.uid, 0);
                    }, function () {
                        cb();
                    });
                });
            }, function (err) {
                callback();
            });
        });
    },
    function (callback) {
        async.eachSeries(serversConfig.production.game, function (gameServer, cb1) {
            let serverId = gameServer.id;
            let gameDbUrl = "mongodb://" + serverId+ '-admin:' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
            mongoClient.connect(gameDbUrl, { useNewUrlParser: true }, function (error, dbclient) {
                if (error) {
                    logger.error("connect gameDbUrl failed! err: " + error + gameDbUrl);
                    return callback(error);
                }
                gameDbList[serverId] = dbclient;
                let db = dbclient.db(serverId);
                async.eachSeries(clusterCollections, function (colName, cb2) {
                    db.collection("player", function (err, col) {
                        var cursor = col.find();
                        cursor.forEach(function (player) {
                            if(allCheckMap[colName].has(player.uid)) {
                                allCheckMap[colName].set(player.uid, 1);
                            }
                        }, function () {
                            cb2();
                        });
                    })
                }, function (err) {
                    cb1();
                });
            })
        }, function (err) {
            callback(null);
        });
    },
    function (callback) {
        let fd = fs.openSync("clusterDataNotInGame.json", 'w');//打开文件
        for(let col in allCheckMap) {
            let map = allCheckMap[col];
            for(let [k, v] of map) {
                if(v === 0) {
                    deleteResult[col].push(k);
                }
            }
        }
        fs.writeSync(fd, JSON.stringify(deleteResult));
        fs.closeSync(fd);//关闭文件
        for(let i=0,lens=clusterCollections.length;i<lens;i++) {
            logger.debug("collection[%s] to delete num:", clusterCollections[i], deleteResult[clusterCollections[i]].length);
        }
        callback();
    },
    function (callback) {
        if(isJustFind) {
            logger.debug("just find, no need delete data.");
            return callback();
        }
        let db = clusterDb.db(clusterConfig.clusterDBName);
        async.eachSeries(clusterCollections, function (colName, cb1) {
            db.collection(colName, function (err, col) {
                async.eachSeries(deleteResult[colName], function (uid, cb2) {
                    col.deleteOne({
                        uid: uid
                    }, function (err) {
                        if(!!err) {
                            logger.error("delete collection[%s], uid: ", colName, uid);
                        }
                        cb2();
                    })
                }, function () {
                    cb1();
                });
            });
        }, function (err) {
            callback();
        });
    }
], function (err) {
    logger.debug("find cluster data not in game finished !");
    clusterDb.close();
    for(let key in gameDbList) {
        gameDbList[key].close();
    }
    process.exit();
});

/*
let playerId = "nRnkCgLINNh8BF8YTMINEo8a";
let gid = "";
let errorDataCollectionList = [];
let inMap = {
    'heros': 1,
    'teamFormation': 1,
    'footballGround': 1,
    'trophyCopy': 1,
    'trainer': 1,
    'follow': 1,
    'everyDaySign': 1,
    'seasonStore': 1
};

let gameCollections = [
    "player",
    "heros",
    "email",
    "item",
    "bag",
    "teamFormation",
    "leagueCopy",
    "scout",
    "tasks",
    "footballGround",
    "businessMatch",
    "trophyCopy",
    "trainer",
    "follow",
    "store",
    "newPlayerSign",
    "sevenDaySign",
    "everyDaySign",
    "vipShop",
    "seasonStore",
    "act",
    "limitStore",
    "offlineEvent",
    "newerGuide",
    "worldCup",
    "everyDayEnergy",
    "newerTask",
    "middleEastCup"
];

let clusterCollections = [
    "account",      //uid
    "matchRank",    //uid
    "renameCard"   //playerId
];
*/

/*
async.waterfall([
    function (callback) {
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let clusterUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(clusterUrl, { useNewUrlParser: true }, function (error, dbclient) {
            if(error){
                logger.error("connect clusterDBUrl failed! err: " + error);
                return callback(error);
            }
            clusterDb = dbclient;
            let db = dbclient.db(clusterConfig.clusterDBName);
            db.collection("account", function (err, col) {
                col.findOne({uid: playerId}, function (err, doc) {
                    if(!!err) {
                        logger.error("account find playerId fail", playerId);
                        return callback(err);
                    }
                    gid = doc.gid;
                    logger.debug("the player gid:", gid);
                    callback(null);
                })
            });
        });
    },
    function (callback) {
        let gameDbUrl = "mongodb://" + gid+ '-admin:' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + gid;
        mongoClient.connect(gameDbUrl, { useNewUrlParser: true }, function (error, dbclient) {
            if (error) {
                logger.error("connect gameDb failed! err: " + error);
                return callback(error);
            }
            gameDbList[gid] = dbclient;
            let db = dbclient.db(gid);
            async.eachSeries(gameCollections, function(colName, cb) {
                db.collection(colName, function (err, col) {
                    if(!!err) {
                        return cb(err);
                    }
                    let cursor = col.find({uid: playerId});
                    let n = 0;
                    cursor.forEach(function (info) {
                        //logger.debug("collections[%s]", colName, info._id);
                        if(colName === "heros") {
                            logger.debug("collections data: ", colName, n, info.heros.length, info._id);
                        }
                        if(colName === "teamFormation") {
                            logger.debug("collections data: ", colName, n, info.allFormations, info._id);
                        }
                        if(colName === "footballGround") {
                            logger.debug("collections data: ", colName, n, info.adminGround, info._id);
                        }
                        if(colName === "trophyCopy") {
                            logger.debug("collections data: ", colName, n, info.allTrophyCopys, info._id);
                        }
                        if(colName === "trainer") {
                            logger.debug("collections data: ", colName, n, info.allTrainer.length, info._id);
                        }
                        if(colName === "follow") {
                            logger.debug("collections data: ", colName, n, info.followCache.length, info._id);
                        }
                        if(colName === "everyDaySign") {
                            logger.debug("collections data: ", colName, n, info.signDay, info._id);
                        }
                        if(colName === "seasonStore") {
                            logger.debug("collections data: ", colName, n, info.refreshTime, info._id);
                        }
                        if(colName === "bag") {
                            logger.debug("collections data: ", colName, n, info.itemUidToBookMarkId.length, info._id);
                        }
                        if(colName === "item") {
                            logger.debug("collections data: ", colName, n, info.resId2Uid.length, info._id);
                        }
                        if(colName === "everyDayEnergy") {
                            logger.debug("collections data: ", colName, n, info.everyDayEnergyList.length, info._id);
                        }
                        if(colName === "scott") {
                            logger.debug("collections data: ", colName, n, info.scoutPack.length, info._id);
                        }
                        if(colName === "offlineEvent") {
                            logger.debug("collections data: ", colName, n, info.offlineEventList.length, info._id);
                        }
                        if(colName === "newPlayerSign") {
                            logger.debug("collections data: ", colName, n, info.signInfo, info._id);
                        }
                        if(colName === "store") {
                            logger.debug("collections data: ", colName, n, info.everyDayBuy.length, info._id);
                        }
                        if(colName === "tasks") {
                            logger.debug("collections data: ", colName, n, info.finishTask.length, info._id);
                        }
                        if(colName === "vipShop") {
                            logger.debug("collections data: ", colName, n, info.historyGoodBuyList.length, info._id);
                        }
                        if(colName === "worldCup") {
                            logger.debug("collections data: ", colName, n, info.reTime, info._id);
                        }
                        n++;
                    }, function () {
                        if(n>1) {
                            //logger.error("err data more data. collection name: ", colName);
                            errorDataCollectionList.push(colName);
                        }
                        cb();
                    });
                });
            }, function (err) {
                callback(null);
            });
        });
    }
], function (err) {
    logger.debug("find err data finished !", errorDataCollectionList);
    clusterDb.close();
    for(let key in gameDbList) {
        gameDbList[key].close();
    }
    process.exit();
});
*/
