
let gameConfig = require("../../../config/game.json");
let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let adminDBUrl = '*****************************************************';
let clusterConfig = require('../../../config/cluster.json');
let brecordConfig = require('../../../config/brecord.json');
let serversConfig = require("../../../config/servers.json");

//console.log("gameConfig: ", gameConfig);
var clusterDbUser = clusterConfig.clusterDBName + '-admin';
var clusterDBUrl = "mongodb://" + clusterDbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
mongoClient.connect(clusterDBUrl, { useNewUrlParser: true },function(error, dbclient){
    if(error){
        logger.error("gate connect clusterDBUrl failed! err: " + error);
        return;
    }

    let db = dbclient.db(clusterConfig.clusterDBName);
    db.dropDatabase();
    logger.info("----------drop database cluster finish!----------");
});

//game
for(let i=0, len=serversConfig.development.game.length; i<len; i++) 
{
    let serverId = serversConfig.development.game[i].id;
    let dbUser = serverId + '-admin';
    let dbUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
    //logger.debug('game connect gameDBUrl : ', dbUrl);
    mongoClient.connect(dbUrl, { useNewUrlParser: true }, function(error, dbclient){
        if(error){
            logger.error("game connect gameDBUrl failed! err: " + error);
            return;
        }
        let client = dbclient.db(serverId);
        client.dropDatabase();
        logger.info("----------drop database game finish!----------", serverId);
    });
}

// for(let i=0, len=serversConfig.development.brecord.length; i<len; i++) 
// {
//     let serverId = serversConfig.development.brecord[i].id;
//     let dbUser = serverId + '-admin';
//     let dbUrl = "mongodb://" + dbUser + ':' + brecordConfig.dbPasswd + '@' + brecordConfig.brecordDBUrl + '/' + serverId;
//     //logger.debug('brecord server connect brecordDBUrl : ', dbUrl);
//     mongoClient.connect(dbUrl, { useNewUrlParser: true }, function(error, dbclient){
//         if(error){
//             logger.error("game connect brecordDBUrl failed! err: " + error);
//             return;
//         }
//         let client = dbclient.db(serverId);
//         client.dropDatabase();
//         logger.info("----------drop database brecord finish!----------", serverId);
//     });
// }