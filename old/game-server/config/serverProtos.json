{"message OneServer": {"optional uInt32 Id": 1, "optional string Name": 2, "optional string GameIp": 3, "optional string BattleIp": 4, "optional uInt32 GamePort": 5, "optional uInt32 BattlePort": 6}, "message OneSkill": {"optional uInt32 SkillID": 1}, "message AttrInfo": {"optional uInt32 Type": 1, "optional double Cur": 2, "optional uInt32 Init": 3, "optional uInt32 Base": 4, "optional uInt32 Skill": 5, "optional uInt32 Handbook": 6, "optional uInt32 Trainer": 7, "optional uInt32 Qualificate": 8, "optional uInt32 UpgradeStar": 9, "optional uInt32 DefTactics": 10, "optional uInt32 BaseTrain": 11, "optional uInt32 AttackTactics": 12, "optional uInt32 MaxProperty": 13, "optional uInt32 GroundTrain": 14, "optional uInt32 BeliefSkillAtt": 15}, "message OneTeamFTAttackAndDefend": {"optional String TeamFormationUid": 1, "optional uInt32 Attack": 2, "optional uInt32 Defend": 3, "optional string Position": 4}, "message Hero": {"optional string Uid": 1, "optional uInt32 ResID": 2, "optional uInt32 TrainCount": 3, "repeated OneSkill SkillList": 4, "repeated AttrInfo OneLevelAttr": 5, "repeated AttrInfo TwoLevelAttr": 6, "repeated OneTeamFTAttackAndDefend AttackAndDefendList": 7, "optional uInt32 CreateTime": 8, "repeated uInt32 Breakthrough": 9, "optional uInt32 Star": 10, "optional uInt32 Status": 11, "optional uInt32 TreatyDay": 12, "optional uInt32 LeftRetireDay": 13, "optional uInt32 isUseYonger": 14, "optional uInt32 Health": 15, "optional uInt32 IsTreat": 16, "optional uInt32 IsTrain": 17, "repeated uInt32 IsStage": 18, "optional uInt32 OldBreakOut": 19, "optional HeroSkill skillInfo": 20, "optional uInt32 LifeNum": 21}, "message OnePositionToHeros": {"optional string Position": 1, "repeated uInt32 HeroResIdList": 2, "repeated HeroRate HeroList": 3}, "message oneScenceUse": {"optional uInt32 ScenceId": 1}, "message PersonInfo": {"optional string name": 1, "optional uInt32 level": 2, "optional uInt32 fame": 3, "optional uInt32 energy": 4, "optional uInt32 formationAct": 5, "optional string league": 6, "optional uInt32 faceIcon": 7, "optional uInt32 vip": 8, "optional uInt32 vipExp": 9, "optional uInt32 beliefId": 10}, "message OneTeamFormation": {"optional string Uid": 1, "optional uInt32 ResId": 2, "optional uInt32 Attack": 3, "optional uInt32 Defend": 4, "optional uInt32 InspireRate:": 5, "repeated OnePositionToHeros PositionToHeros": 6, "repeated oneScenceUse ScenceUse": 7, "optional uInt32 ActualStrength": 8, "optional uInt32 UseTactics": 9, "optional uInt32 UseDefTactics": 10, "optional uInt32 TrainersResId": 11, "optional uInt32 ServerType": 12, "optional uInt32 FormationId": 13, "optional uInt32 TeamId": 14}, "message teamFormation": {"optional uInt32 ResId": 1, "optional string Name": 2}, "message OneCopy": {"optional uInt32 TeamCopyId": 1, "optional uInt32 Process": 2, "optional uInt32 TakeCopyRewardProcess": 3, "optional uInt32 IsFinshed": 4, "optional uInt32 UnlockState": 5, "optional uInt32 Attack": 6, "optional uInt32 Defend": 7, "optional uInt32 ActualStrength": 8}, "message OneLeague": {"optional uInt32 Uid": 1, "optional uInt32 IsTakeLeagueReward": 2, "optional uInt32 IsAllCopyPassed": 3, "optional uInt32 CurPassCount": 4, "optional uInt32 Count": 5, "repeated OneCopy CopyData": 6}, "message BattleMemberInfo": {"required string heroUid": 1, "required string name": 2, "required string position": 3, "required uInt32 rating": 4, "required uInt32 resId": 5}, "message PreBattleTeam": {"required string type": 1, "required string teamName": 2, "required uInt32 rating": 3, "required uInt32 formationID": 4, "required uInt32 attackTacticID": 5, "required uInt32 defendTacticID": 6, "repeated BattleMemberInfo memberInfo": 7, "optional string leagueName": 8, "optional string roundName": 9, "optional uInt32 winCount": 10, "optional uInt32 lossCount": 11, "optional uInt32 drawCount": 12, "optional uInt32 rank": 13, "optional uInt32 battleType": 14, "optional uInt32 totalValue": 15, "optional string faceUrl": 16, "optional string battleName": 17, "optional uInt32 atkTacticsAState": 18, "optional uInt32 atkTacticsBState": 19, "optional uInt32 srcAtkValue": 20, "optional uInt32 srcDefValue": 21, "optional uInt32 atkValueFactor": 22, "optional uInt32 defValueFactor": 23, "optional int32 atkTrainerFactor": 24, "optional int32 defTrainerFactor": 25}, "message PeriodMemberInfo": {"optional string heroUid": 1, "optional uInt32 attrType1": 2, "optional uInt32 attrValue1": 3, "optional int32 addValue1": 4, "optional uInt32 attrType2": 5, "optional uInt32 attrValue2": 6, "optional int32 addValue2": 7, "optional uInt32 resId": 8}, "message SkillEffectPer": {"optional uInt32 skillId": 1, "optional string teamType": 2, "optional int32 addPer": 3, "optional string heroUid": 4}, "message PeriodInfo": {"optional PeriodMemberInfo A1Info": 1, "optional PeriodMemberInfo A2Info": 2, "optional PeriodMemberInfo BInfo": 3, "optional PeriodMemberInfo GKInfo": 4, "optional uInt32 actionID": 5, "optional uInt32 startCommentID": 6, "optional uInt32 resultCommentID": 7, "optional uInt32 actionPer": 8, "optional uInt32 actionResult": 9, "optional uInt32 moraleA": 10, "optional uInt32 moraleB": 11, "repeated SkillEffectPer skillEffectList": 12}, "message BattleRoundInfo": {"required uInt32 eventTime": 1, "required uInt32 moraleA": 2, "required uInt32 moraleB": 3, "required uInt32 moraleSlotA": 4, "required uInt32 moraleSlotB": 5, "required string attackerType": 6, "required uInt32 attackMode": 7, "repeated PeriodInfo periodInfo": 8, "required uInt32 scoreA": 9, "required uInt32 scoreB": 10}, "message BattleSkillRecord": {"repeated DurativeSkillRecord durRecord": 1, "repeated InstantSkillRecord insRecord": 2, "repeated NextAtkSkillRecord nextAtkRecord": 3}, "message DurativeSkillRecord": {"required uInt32 skillId": 1, "required uInt32 startTime": 2, "required uInt32 endTime": 3, "optional string heroUid": 4, "required uInt32 round": 5, "required uInt32 period": 6}, "message InstantSkillRecord": {"required uInt32 skillId": 1, "required uInt32 round": 2, "required uInt32 period": 3, "optional string heroUid": 4}, "message NextAtkSkillRecord": {"required uInt32 skillId": 1, "required uInt32 round": 2, "required uInt32 period": 3, "required uInt32 startTime": 4, "required uInt32 endTime": 5, "optional string heroUid": 6, "optional uInt32 effectRound": 7}, "message MailText": {"optional string Title": 1, "optional uInt32 ContentTypeId": 2, "optional string Content": 3}, "message AttachData": {"optional uInt32 ItemType": 1, "optional uInt32 ResId": 2, "optional uInt32 Num": 3, "optional uInt32 Param1": 4}, "message SpecialAttachInfo": {"optional string roomUid": 1}, "message Mail": {"optional string Uid": 1, "optional uInt32 Type": 2, "optional uInt32 DelType": 3, "optional MailText TextMsg": 4, "repeated AttachData AttachList": 5, "optional uInt32 CreateTime": 6, "optional uInt32 IsOpen": 7, "optional uInt32 TakeTime": 8, "optional string SenderUid": 9, "optional SpecialAttachInfo SpecialAttachData": 10}, "message TakeMail": {"optional uInt32 code": 1, "optional string Uid": 2, "repeated AttachData AttachList": 3}, "message Item": {"optional string Uid": 1, "optional uInt32 ResID": 2, "optional uInt32 Num": 3, "optional uInt32 Bind": 4, "optional uInt32 Type": 5, "optional uInt32 TimeLimit": 6, "optional uInt32 Invalid": 7}, "message ItemCache": {"optional uInt32 Pos": 1, "optional uInt32 Unlock": 2, "optional Item ItemData": 3}, "message BookMark": {"optional uInt32 Id": 1, "repeated uInt32 ItemType": 2, "optional uInt32 CurrUnlockCount": 3, "optional uInt32 MaxUnlockCount": 4, "repeated ItemCache ItemList": 5}, "message GoalRecord": {"required uInt32 time": 1, "required string ballerName": 2, "required string teamType": 3}, "message BattleStatistic": {"required string teamType": 1, "required uInt32 shotNum": 2, "required uInt32 ctrlBallPer": 3, "required uInt32 breakPer": 4, "required uInt32 placeKickNum": 5, "optional string bestBaller": 6}, "message LootItem": {"required uInt32 resId": 1, "required uInt32 num": 2, "optional string uid": 3}, "message TeamReward": {"optional uInt32 cash": 1, "optional uInt32 percent": 2, "optional int32 FansChangeNum": 3}, "message BusinessReward": {"optional uInt32 totalCash": 1, "optional TeamReward homeReward": 2, "optional TeamReward awayReward": 3}, "message BattleEndInfo": {"repeated BattleStatistic stInfos": 1, "repeated LootItem lootItemList": 2, "repeated GoalRecord goalRecord": 3, "optional BusinessReward businessReward": 4}, "message Robot": {"optional string Uid": 1}, "message PlayerInfo": {"optional string name": 1, "optional uInt32 level": 2, "optional uInt32 exp": 3, "optional uInt32 cash": 4, "optional uInt32 gold": 5, "optional uInt32 energy": 6, "optional uInt32 trophy": 7, "optional uInt32 faceId": 8}, "message ItemResIdList": {"optional uInt32 resId": 1, "optional uInt32 count": 2}, "message HeroResIdList": {"optional uInt32 resId": 1, "optional uInt32 count": 2}, "message TeamInfo": {"optional string playerUid": 1, "optional string name": 3, "optional uInt32 faceIcon": 4, "optional uInt32 battleCount": 5, "optional uInt32 winCount": 6, "optional uInt32 drawCount": 7, "optional uInt32 lossCount": 8, "optional uInt32 goalCount": 9, "optional uInt32 missCount": 10, "optional uInt32 totalScore": 11, "optional uInt32 knockoutStatus": 12}, "message ScheduleGroupInfo": {"optional string battleId": 1, "optional string teamA": 2, "optional string teamB": 3, "optional uInt32 teamAScore": 4, "optional uInt32 teamBScore": 5, "optional uInt32 beginTime": 6, "optional uInt32 status": 7, "optional string teamAName": 8, "optional string teamBName": 9, "optional string teamAfaceUrl": 10, "optional string teamBfaceUrl": 11, "optional string winTeam": 12}, "message SimpleTeamInfo": {"optional string playerUid": 1, "optional string name": 2, "optional uInt32 faceIcon": 3, "optional uInt32 actualStrength": 4, "optional uInt32 totalValue": 5, "optional string faceUrl": 6}, "message personalHistory": {"optional uInt32 typeId": 1, "optional uInt32 groupId": 2, "optional uInt32 roundId": 3, "optional uInt32 beginTime": 4, "optional SimpleTeamInfo teamA": 5, "optional SimpleTeamInfo teamB": 6, "optional uInt32 teamAScore": 7, "optional uInt32 teamBScore": 8, "optional uInt32 status": 9, "optional uInt32 isNull": 10, "optional string roomUid": 11}, "message roundScheduleInfo": {"optional uInt32 typeId": 1, "optional uInt32 roundId": 2, "optional string name": 3, "optional uInt32 groupId": 4, "repeated ScheduleGroupInfo scheduleList": 5, "optional uInt32 finalRound": 6, "optional uInt32 maxGroupCount": 7, "optional uInt32 scheduleTime": 8}, "message roundScoreRankInfo": {"optional uInt32 typeId": 1, "optional uInt32 roundId": 2, "optional string name": 3, "optional uInt32 groupId": 4, "repeated TeamInfo teamInfoList": 5, "optional uInt32 finalRound": 6, "optional uInt32 maxGroupCount": 7, "optional uInt32 scheduleTime": 8, "optional uInt32 maxRound": 9}, "message AllFaceIcon": {"repeated uInt32 clubFaceIcon": 1, "repeated uInt32 countryFaceIcon": 2}, "message Individual": {"optional string name": 1, "optional uInt32 level": 2, "optional uInt32 fame": 3, "optional uInt32 faceIcon": 4, "optional uInt32 cash": 5, "optional uInt32 gold": 6, "optional uInt32 energy": 7, "optional uInt32 trophy": 8, "optional string league": 9, "optional uInt32 vip": 10, "optional uInt32 formationAct": 11, "optional uInt32 vipExp": 12, "repeated uInt32 isFirstRecharge": 13, "optional uInt32 qualified": 14, "optional string faceUrl": 15, "optional uInt32 createTime": 16, "optional uInt32 freeChatNum": 17, "optional uInt32 isBuyedCoach": 18, "optional uInt32 loginFlag": 19, "optional uInt32 worldCoin": 20, "optional uInt32 beliefId": 21, "optional uInt32 honor": 22, "optional uInt32 beliefNum": 23, "optional uInt32 isShowTactics": 24, "optional uInt32 beliefLiveness": 25, "optional string playerId": 26, "optional uInt32 buyTeamFormationGiftNum": 27}, "message ScoutGroup": {"optional uInt32 type": 1, "optional uInt32 count": 2, "optional uInt32 getTime": 3}, "message ScoutHeroInfo": {"optional uInt32 resId": 1}, "message ScoutInfo": {"optional uInt32 scoutRp": 1, "optional uInt32 scoutEnergy": 2, "repeated ScoutGroup scoutGroup": 3, "optional uInt32 sysTime": 4, "repeated ScoutHeroInfo scoutPack": 5}, "message Task": {"optional uInt32 resId": 1, "optional uInt32 type": 2, "optional uInt32 Num": 3, "optional uInt32 status": 4, "optional uInt32 isActive": 5}, "message TaskList": {"repeated Task task0": 1, "repeated Task task1": 2, "repeated Task task2": 3, "repeated Task task3": 4}, "message HeroTrain": {"repeated uInt32 type1": 1, "repeated uInt32 type2": 2, "repeated uInt32 type3": 3, "repeated uInt32 type4": 4, "repeated uInt32 additive": 5}, "message SkillList": {"optional uInt32 skillId": 1, "optional uInt32 isActivate": 2}, "message HeroSkill": {"repeated SkillList skillList": 1, "optional uInt32 rating": 2, "optional uInt32 star": 3, "optional uInt32 potential": 4, "optional uInt32 attributeValue": 5, "optional string uid": 6}, "message GroundRewardList": {"optional uInt32 id": 1, "optional uInt32 num": 2}, "message GroundInfo": {"optional uInt32 type": 1, "optional uInt32 level": 2, "optional uInt32 upTime": 3, "optional uInt32 isUpgrade": 4, "optional uInt32 isUnLock": 5, "optional uInt32 isLive": 6, "repeated GroundRewardList rewardList": 7}, "message GroundReward": {"optional uInt32 code": 1, "optional uInt32 num": 2, "optional uInt32 Id": 3}, "message AllGround": {"optional GroundInfo adminGround": 1, "optional GroundInfo mainGround": 2, "optional GroundInfo trainGround": 3, "optional GroundInfo transferGround": 4, "optional GroundInfo hospitalGround": 5, "optional GroundInfo notableGround": 6, "optional uInt32 prestige": 7, "optional uInt32 ballFan": 8, "optional uInt32 trainCount": 9, "optional uInt32 isGetBallFansRewrd": 10}, "message MatchGetRival": {"optional string name": 1, "optional uInt32 Attack": 2, "optional uInt32 Defend": 3, "optional uInt32 ActualStrength": 4, "optional uInt32 FansRank": 5, "optional string enemyUid": 6, "optional string faceUrl": 7}, "message MatchRecord": {"optional string roomUid": 1, "optional SimpleTeamInfo teamA": 2, "optional SimpleTeamInfo teamB": 3, "optional uInt32 result": 4, "optional uInt32 teamAScore": 5, "optional uInt32 teamBScore": 6, "optional uInt32 beginTime": 7, "optional uInt32 teamARank": 8, "optional uInt32 teamBRank": 9, "optional int32 FansChangeNum": 10}, "message FightTimesInfo": {"optional uInt32 totalTimes": 1, "optional uInt32 leftTimes": 2, "optional uInt32 buyTimes": 3}, "message FanRankInfo": {"optional uInt32 ballFanCount": 1, "optional uInt32 fanRank": 2}, "message GlobalFanRank": {"optional string name": 1, "optional uInt32 rank": 2, "optional uInt32 fansCount": 3}, "message MatchReward": {"optional uInt32 cash": 1}, "message MatchResult": {"optional MatchRecord matchRecord": 1, "optional FightTimesInfo fightTimesInfo": 2, "optional MatchReward rewardInfo": 3}, "message TrainPos": {"optional uInt32 state": 1, "optional string pos": 2, "optional uInt32 beginTime": 3, "optional uInt32 isLockTrain": 4}, "message HeroProperty": {"optional uInt32 type": 1, "optional uInt32 num": 2}, "message TrophyReward": {"optional uInt32 type": 1, "optional uInt32 resId": 2, "optional uInt32 num": 3}, "message LimitNum": {"optional uInt32 num": 1, "optional uInt32 limitNum": 2, "optional uInt32 alreadyPurchaseNum": 3}, "message Trophy": {"optional uInt32 id": 1, "optional LimitNum limitInfo": 3, "repeated TrophyReward rewardShowIcons": 4}, "message TrophyTeam": {"optional uInt32 id": 1, "optional uInt32 teamId": 2, "optional uInt32 trophyId": 3, "optional LimitNum limitInfo": 4, "repeated TrophyReward rewardShowIcons": 9, "optional uInt32 actualStrength": 10}, "message Handbook": {"optional uInt32 bookId": 1, "repeated HandbookPlayer player": 2}, "message HandbookPlayer": {"optional uInt32 id": 1, "optional uInt32 isHave": 2}, "message TransferPos": {"optional string uid": 1, "optional uInt32 type": 2, "optional uInt32 resId": 3, "optional uInt32 star": 4, "optional uInt32 level": 5, "optional uInt32 actual": 6, "optional uInt32 status": 7, "optional uInt32 skillId": 9}, "message Trainer": {"optional string uid": 1, "optional uInt32 resId": 2, "optional uInt32 level": 3, "optional uInt32 actual": 4, "optional uInt32 star": 5, "optional uInt32 skillId": 6}, "message NotablePos": {"optional string uid": 1, "optional uInt32 resId": 2, "optional uInt32 star": 3, "optional uInt32 trainCount": 4, "optional uInt32 isUseYonger": 5, "optional uInt32 rating": 6, "optional uInt32 state": 7}, "message SinglePlayerInfo": {"optional uInt32 type": 1, "optional uInt32 value": 2, "repeated uInt32 array": 3}, "message BuyInfo": {"optional uInt32 Id": 1, "optional uInt32 num": 2}, "message OneActAward": {"optional uInt32 type": 1, "optional uInt32 itemId": 2, "optional uInt32 count": 3}, "message CountDown": {"optional uInt32 day": 1, "optional uInt32 hour": 2, "optional uInt32 minutes": 3, "optional uInt32 second": 4}, "message ActTag": {"optional string tagName": 1, "repeated OneActAward awardList": 2, "optional uInt32 btnStatus": 3, "optional uInt32 currValue": 4, "optional uInt32 totalValue": 5, "optional uInt32 tagIndex": 7, "optional uInt32 coachAwardType": 8}, "message MonthCard": {"optional uInt32 cardTime": 1, "optional uInt32 isBuy": 2}, "message GoldCoach": {"optional uInt32 level": 1, "optional uInt32 currTask": 2, "optional uInt32 totalTask": 3, "optional uInt32 isBuyCoach": 4, "repeated Task commonTaskList": 5, "repeated Task goldTaskList": 6}, "message ContinueCharge": {"optional uInt32 tagIndex": 1}, "message PushPackage": {"optional uInt32 id": 1, "optional uInt32 endTime": 2, "optional uInt32 isBuy": 3, "optional uInt32 isTimeOut": 4}, "message OneActRecord": {"optional uInt32 actId": 1, "optional uInt32 actType": 2, "optional uInt32 startTime": 3, "optional uInt32 endTime": 4, "repeated ActTag tagList": 5, "optional CountDown countDown": 6, "optional MonthCard monthCard": 7, "optional GoldCoach goldCoach": 8, "optional ContinueCharge continueCharge": 9, "optional uInt32 NationalDay": 10, "repeated PushPackage pushGiftList": 11, "optional uInt32 TurntableNum": 12}, "message GoodsInfo": {"optional uInt32 id": 1, "optional uInt32 state": 2}, "message NewerGuide": {"optional uInt32 nextId": 1, "optional uInt32 type": 2}, "message EveryDayEnergy": {"optional uInt32 type": 1, "optional uInt32 btnStatus": 2}, "message AllActivity": {"optional uInt32 Id": 1, "optional uInt32 startTime": 2, "optional uInt32 endTime": 3}, "message SignInfo": {"optional uInt32 state": 1}, "message VipInfo": {"required string uid": 1, "optional uInt32 level": 2, "optional uInt32 exp": 3, "optional uInt32 faceIcon": 4}, "message WorldCup": {"repeated uInt32 team0": 1, "repeated uInt32 team1": 2, "repeated uInt32 team2": 3, "repeated uInt32 team3": 4, "repeated uInt32 team4": 5, "repeated uInt32 team5": 6, "repeated uInt32 team6": 7, "repeated uInt32 team7": 8}, "message GroupMatchInfo": {"optional uInt32 id": 1, "optional uInt32 fumble": 2, "optional uInt32 goal": 3, "optional uInt32 score": 4, "optional uInt32 winNum": 5, "optional uInt32 loseNum": 6, "optional uInt32 sameNum": 7}, "message GroupMatch": {"repeated GroupMatchInfo team0": 1, "repeated GroupMatchInfo team1": 2, "repeated GroupMatchInfo team2": 3, "repeated GroupMatchInfo team3": 4, "repeated GroupMatchInfo team4": 5, "repeated GroupMatchInfo team5": 6, "repeated GroupMatchInfo team6": 7, "repeated GroupMatchInfo team7": 8}, "message EliminateMatchInfo": {"optional uInt32 id": 1, "optional uInt32 goal": 2, "optional uInt32 fumble": 3, "optional uInt32 isWin": 4}, "message EliminateMatch": {"repeated EliminateMatchInfo team0": 1, "repeated EliminateMatchInfo team1": 2, "repeated EliminateMatchInfo team2": 3, "repeated EliminateMatchInfo team3": 4, "repeated EliminateMatchInfo team4": 5, "repeated EliminateMatchInfo team5": 6, "repeated EliminateMatchInfo team6": 7, "repeated EliminateMatchInfo team7": 8}, "message RewardInfo": {"optional uInt32 resId": 1, "optional uInt32 type": 2, "optional uInt32 num": 3}, "message GiftRewardInfo": {"optional uInt32 id": 1, "optional uInt32 type": 2, "optional uInt32 takeStatus": 3, "repeated RewardInfo rewardList": 4}, "message VipGiftInfo": {"optional uInt32 vipLevel": 1, "repeated GiftRewardInfo giftRewardList": 2}, "message OneChatMsg": {"required uInt32 channel": 1, "required uInt32 type": 2, "required string msg": 3, "optional string senderUid": 4, "optional string senderName": 5, "optional uInt32 faceIcon": 6, "optional uInt32 itemResId": 7, "optional string heroUid": 8, "optional uInt32 vip": 9, "optional string honorTitle": 10}, "message HospitalPosInfo": {"optional string uid": 1, "optional uInt32 state": 2, "optional uInt32 endTime": 3}, "message ChannelChatMsgList": {"repeated OneChatMsg chatMsgList": 1}, "message everyDayTask": {"optional uInt32 id": 1}, "message RedHint": {"optional uInt32 type": 1, "optional uInt32 state": 2}, "message DqMatchInfo": {"optional string homeUid": 1, "optional string homeFaceUrl": 2, "optional string homeName": 3, "optional uInt32 homeHomeScore": 4, "optional uInt32 homeAwayScore": 5, "optional string awayUid": 6, "optional string awayFaceUrl": 7, "optional string awayName": 8, "optional uInt32 awayHomeScore": 9, "optional uInt32 awayAwayScore": 10, "optional string winnerUid": 11, "optional string roomUid1": 12, "optional string roomUid2": 13, "optional uInt32 time1": 14, "optional uInt32 time2": 15}, "message DqPreBattle": {"repeated DqMatchInfo recordList": 1, "optional uInt32 winNum": 2, "optional uInt32 loseNum": 3, "optional uInt32 drawNum": 4}, "message DqMatchTopInfo": {"optional string uid": 1, "optional string faceUrl": 2, "optional string name": 3, "optional uInt32 cupNum": 4}, "message WorldBoss": {"optional uInt32 teamId": 1, "optional string killer": 2, "optional uInt32 rank": 3, "optional uInt32 boosGoal": 4, "optional uInt32 playerGoal": 5, "optional uInt32 nextTime": 6, "optional uInt32 nowTime": 7, "optional uInt32 isOpen": 8, "optional uInt32 bossTotal": 9, "repeated uInt32 joinBoss": 10}, "message WorldBossRank": {"optional string playerId": 1, "optional uInt32 goal": 2, "optional string faceUrl": 3, "optional string name": 4}, "gate.gateHandler.getServerList": {"optional uInt32 code": 1, "optional uInt32 num": 2, "optional OneServer serverList": 3}, "gate.gateHandler.productionEntry": {"required uInt32 code": 1, "optional OneServer serverInfo": 2}, "gate.gateHandler.getUserSignInfo": {"required uInt32 code": 1, "optional string sign": 2, "optional uInt32 timestamp": 3, "optional string nonceStr": 4, "optional string openId": 5, "optional string playerName": 6, "optional string faceUrl": 7, "optional uInt32 platform": 8}, "gconnector.entryHandler.login": {"required uInt32 code": 1, "optional string playerId": 2, "optional uInt32 isNew": 3, "optional uInt32 step": 4}, "game.playerHandler.getPlayerInfo": {"required uInt32 code": 1, "optional PlayerInfo playerInfo": 2}, "game.playerHandler.createRole": {"required uInt32 code": 1, "repeated Hero heroList": 2, "repeated OneTeamFormation oneTeam": 3}, "game.playerHandler.getHeroList": {"required uInt32 code": 1, "repeated Hero heroList": 2}, "game.playerHandler.addHero": {"required uInt32 code": 1, "optional Hero oneHero": 2}, "game.playerHandler.delHero": {"required uInt32 code": 1}, "game.playerHandler.csGetNewerHero": {"required uInt32 code": 1, "optional uInt32 beliefId": 2}, "game.playerHandler.getNewerHeroByOldPLayer": {"required uInt32 code": 1, "optional uInt32 beliefId": 2}, "game.playerHandler.getFormationList": {"required uInt32 code": 1, "repeated teamFormation teamList": 2}, "game.playerHandler.getTeamFormationList": {"required uInt32 code": 1, "optional string currTeamFormationId": 2, "optional string leagueTeamFormationId": 3, "repeated OneTeamFormation teamList": 4, "repeated uInt32 tacticsList": 5, "optional string playerUid": 6, "optional uInt32 type": 7, "optional string playerName": 8, "optional uInt32 totalValue": 9, "optional uInt32 beliefId": 10, "repeated uInt32 defTacticsList": 11, "optional uInt32 vip": 12, "optional uInt32 isShowTactics": 13, "optional string warOfFaithId": 14, "optional string honorTitle": 15}, "game.playerHandler.addTeamFormation": {"required uInt32 code": 1, "optional OneTeamFormation oneTeam": 2}, "game.playerHandler.setFormation": {"required uInt32 code": 1, "optional OneTeamFormation oneTeam": 2}, "game.playerHandler.createInitTeamFormation": {"required uInt32 code": 1, "optional OneTeamFormation initTeam": 2}, "game.playerHandler.setInitTeamFormationId": {"required uInt32 code": 1, "optional string name": 2, "optional string uid": 3}, "game.playerHandler.delTeamFormation": {"required uInt32 code": 1, "optional string uid": 2, "repeated OneTeamFormation initTeam": 3, "optional string formationId": 4}, "game.playerHandler.addTeamFormationHero": {"required uInt32 code": 1, "optional OneTeamFormation oneTeam": 2}, "game.playerHandler.delTeamFormationHero": {"required uInt32 code": 1}, "game.playerHandler.pushOneTeamFormation": {"required uInt32 code": 1, "optional OneTeamFormation oneTeam": 2}, "game.playerHandler.teamFormationExchangeHero": {"required uInt32 code": 1, "optional Hero heroList": 2, "optional OneTeamFormation oneTeam": 3}, "game.playerHandler.getNoPositionHerosList": {"required uInt32 code": 1, "optional string teamFormationUid": 2, "repeated Hero heroList": 3}, "game.playerHandler.getLeagueCopyData": {"required uInt32 code": 1, "optional uInt32 nextLeagueId": 2, "optional uInt32 nextTeamCopyId": 3, "repeated OneLeague leagueCopyData": 4}, "game.playerHandler.getLeagueCopyDataByType": {"required uInt32 code": 1, "optional uInt32 nextLeagueId": 2, "optional uInt32 nextTeamCopyId": 3, "repeated OneLeague leagueCopyData": 4}, "game.playerHandler.PVELeagueCopyBattle": {"required uInt32 code": 1, "optional string roomUid": 2}, "game.playerHandler.updatePVELeagueCopyResult": {"required uInt32 selfScore": 1, "required uInt32 enemyScore": 2, "optional uInt32 finshedStar": 3, "optional uInt32 nextLeagueId": 4, "optional uInt32 nextTeamCopyId": 5, "repeated OneLeague leagueCopyData": 6}, "battle.battleHandler.PVELeagueCopyBattle": {"required uInt32 code": 1, "repeated PreBattleTeam preBattleInfo": 2, "repeated BattleRoundInfo battleRoundInfo": 3, "optional BattleEndInfo battleEndInfo": 4, "repeated BattleSkillRecord skillRecord": 5}, "battle.battleHandler.testBattle": {"required uInt32 code": 1, "repeated PreBattleTeam preBattleInfo": 2, "repeated BattleRoundInfo battleRoundInfo": 3, "optional BattleEndInfo battleEndInfo": 4, "repeated BattleSkillRecord skillRecord": 5}, "message FollowComRoleInfo": {"optional string playerUid": 1, "optional string name": 2, "optional uInt32 level": 3, "optional string faceUrl": 4, "optional uInt32 vip": 5, "optional uInt32 followTime": 6, "optional uInt32 giveEnergy": 7, "optional uInt32 giveTime": 8, "optional uInt32 onlineStatus": 9, "optional uInt32 messageCount": 10}, "message FollowPageInfo": {"optional uInt32 totalRecord": 1, "optional uInt32 totalPage": 2, "optional uInt32 maxResult": 3, "optional uInt32 currPage": 4, "repeated FollowComRoleInfo followComRoleList": 5}, "game.playerHandler.csGetBagList": {"required uInt32 code": 1, "repeated BookMark bagList": 2}, "game.playerHandler.csRemoveItem": {"required uInt32 code": 1}, "game.playerHandler.csSellItem": {"required uInt32 code": 1}, "game.playerHandler.csUseItem": {"required uInt32 code": 1, "repeated ItemResIdList itemResIdList": 2, "repeated HeroResIdList heroResIdList": 3, "optional uInt32 notifyBagFullMail": 4}, "game.playerHandler.csAddItem": {"required uInt32 code": 1}, "game.playerHandler.updateBag": {"repeated BookMark bagList": 1}, "game.playerHandler.upgradeBookMark": {"required uInt32 code": 1}, "game.playerHandler.updateHeros": {"repeated Hero heroList": 1}, "game.playerHandler.updateNewHeros": {"repeated Hero heroList": 1}, "game.playerHandler.CSCommonFunction": {"required uInt32 code": 1}, "game.playerHandler.CSGetMailList": {"required uInt32 code": 1, "repeated Mail mailList": 2}, "game.playerHandler.CSTakeMail": {"required uInt32 code": 1, "repeated TakeMail takeMailList": 2}, "game.playerHandler.CSDelMail": {"required uInt32 code": 1}, "game.playerHandler.PushUpdateMailList": {"repeated Mail mailList": 1}, "game.playerHandler.CSGetRobotList": {"required uInt32 code": 1, "repeated Robot robotList": 2}, "game.playerHandler.PushFuncSwitch": {"optional uInt32 type": 1, "optional uInt32 status": 2, "optional uInt32 openTime": 3}, "game.playerHandler.CSGetServerTime": {"required uInt32 code": 1, "optional uInt32 serverTime": 2}, "game.playerHandler.CSGetLeagueEnroll": {"required uInt32 code": 1, "optional uInt32 status": 2, "optional uInt32 beginTime": 3, "optional uInt32 endTime": 4, "optional uInt32 isEnroll": 5, "optional uInt32 isJoinRelay": 6}, "game.playerHandler.CSLeagueEnroll": {"required uInt32 code": 1}, "game.playerHandler.CSGetCurrLeagueData": {"required uInt32 code": 1, "optional uInt32 seasonId": 2, "optional roundScoreRankInfo roundScoreRankData": 3, "optional uInt32 isJoin": 4, "optional uInt32 currTypeId": 5}, "game.playerHandler.CSGetSchedule": {"optional uInt32 code": 1, "optional uInt32 seasonId": 2, "optional roundScheduleInfo roundScheduleData": 4}, "game.playerHandler.CSGetScoreRank": {"optional uInt32 code": 1, "optional uInt32 seasonId": 2, "optional roundScoreRankInfo roundScoreRankData": 4}, "game.playerHandler.CSSwitchSchedule": {"optional uInt32 code": 1, "optional uInt32 seasonId": 2, "optional roundScheduleInfo roundScheduleData": 4}, "game.playerHandler.CSSwitchScore": {"optional uInt32 code": 1, "optional uInt32 seasonId": 2, "optional roundScoreRankInfo roundScoreRankData": 4}, "game.playerHandler.CSSetLeagueTeamFormationId": {"optional uInt32 code": 1, "optional string uid": 2}, "game.playerHandler.CSGetPersonalHistory": {"required uInt32 code": 1, "repeated personalHistory personalHistoryInfo": 2}, "game.playerHandler.CSGetPrimaryHistory": {"required uInt32 code": 1, "repeated personalHistory PrimaryHistoryInfo": 2}, "game.playerHandler.PushLeaguePrepare": {"required string roomUid": 1, "optional string competitorName": 2, "optional SimpleTeamInfo teamA": 3, "optional SimpleTeamInfo teamB": 4, "optional uInt32 beginTime": 5, "optional string leagueName": 6, "optional uInt32 groupId": 7, "optional uInt32 round": 8}, "game.playerHandler.PushLeagueStart": {"required string roomUid": 1}, "game.playerHandler.CSWaitLookRecord": {"required uInt32 code": 1}, "message HeroRate": {"optional string uid": 1, "optional uInt32 strength": 2, "optional uInt32 star": 3}, "game.playerHandler.updateTeamFormations": {"repeated OneTeamFormation teamList": 1, "repeated uInt32 tacticsList": 2, "repeated uInt32 defTacticsList": 3}, "game.playerHandler.setFormationTactics": {"required uInt32 code": 1, "optional uInt32 resId": 2, "optional string uid": 3}, "game.playerHandler.getPersonInfo": {"required uInt32 code": 1, "optional PersonInfo personInfo": 2, "optional uInt32 totalValue": 3}, "game.playerHandler.modifyPlayerName": {"required uInt32 code": 1, "required string name": 2}, "game.playerHandler.modifyFaceIcon": {"required uInt32 code": 1, "optional uInt32 resId": 2}, "game.playerHandler.getAllFaceIcon": {"required uInt32 code": 1, "repeated AllFaceIcon allFaceIcon": 2}, "game.playerHandler.updatePlayer": {"repeated Individual player": 1}, "game.playerHandler.getScoutInfo": {"required uInt32 code": 1, "optional ScoutInfo scoutInfo": 2}, "game.playerHandler.addScoutEnergy": {"required uInt32 code": 1, "optional uInt32 scoutEnergy": 2, "optional uInt32 id": 3}, "game.playerHandler.exchangeScout": {"required uInt32 code": 1, "optional uInt32 resId": 2, "optional uInt32 scoutRp": 3, "optional uInt32 scoutEnergy": 4, "optional uInt32 type": 5}, "game.playerHandler.getScoutReward": {"required uInt32 code": 1, "optional uInt32 resId": 2, "optional uInt32 scoutRp": 3, "optional uInt32 scoutEnergy": 4, "optional uInt32 getTime": 5, "optional uInt32 sysTime": 6, "optional uInt32 type": 7, "optional uInt32 index": 8, "optional uInt32 count": 9}, "game.playerHandler.buyScoutReward": {"required uInt32 code": 1, "optional uInt32 index": 2}, "game.playerHandler.upPlayerInfo": {"repeated SinglePlayerInfo type": 1}, "game.playerHandler.getEnergyInfo": {"required uInt32 code": 1, "optional uInt32 energy": 2, "optional uInt32 energyReTime": 3, "optional uInt32 sysTime": 4, "optional uInt32 continuedBuff": 5, "optional uInt32 continuedBuffEndTime": 6, "optional uInt32 alreadyAddCount": 7, "optional uInt32 canAddCount": 8}, "game.playerHandler.updateEnergyInfo": {"required uInt32 code": 1, "optional uInt32 energy": 2, "optional uInt32 energyReTime": 3, "optional uInt32 sysTime": 4, "optional uInt32 continuedBuff": 5, "optional uInt32 continuedBuffEndTime": 6, "optional uInt32 alreadyAddCount": 7, "optional uInt32 canAddCount": 8}, "game.playerHandler.buyEnergy": {"required uInt32 code": 1, "optional uInt32 buyEnergyCount": 2, "optional uInt32 energy": 3, "optional uInt32 gold": 4}, "game.playerHandler.getEnergyReward": {"required uInt32 code": 1, "optional uInt32 isGet": 2, "optional uInt32 sysTime": 3}, "game.playerHandler.addResource": {"required uInt32 code": 1}, "game.playerHandler.exchangeGiftBag": {"required uInt32 code": 1, "optional string gift": 2}, "game.playerHandler.getBuyEnergyCount": {"required uInt32 code": 1, "optional uInt32 buyEnergyCount": 2}, "message EveryDayGift": {"optional uInt32 id": 1, "optional uInt32 num": 2, "optional uInt32 index": 3, "optional uInt32 status": 4, "optional uInt32 isGet": 5}, "game.playerHandler.getAllTaskList": {"required uInt32 code": 1, "repeated TaskList allTaskList": 2, "optional uInt32 reTaskCount": 3, "repeated uInt32 finishTask": 4, "optional uInt32 firstGearDay": 5, "optional uInt32 secondGearDay": 6, "repeated EveryDayGift everyDayGift": 7, "optional uInt32 everyDayTaskNum": 8}, "game.playerHandler.getGiftReward": {"optional uInt32 code": 1, "optional uInt32 index": 2}, "game.playerHandler.updateTaskInfo": {"repeated TaskList taskList": 1, "repeated uInt32 finishTask": 2}, "game.playerHandler.getTaskReward": {"required uInt32 code": 1, "optional uInt32 resId": 2}, "message TaskRewardInfo": {"optional uInt32 ResId": 1, "optional uInt32 Num": 2}, "game.playerHandler.ghostGetTaskReward": {"required uInt32 code": 1, "repeated TaskRewardInfo awardList": 2}, "game.playerHandler.refreshTask": {"required uInt32 code": 1, "optional uInt32 resId": 2, "optional uInt32 reTaskCount": 3}, "game.playerHandler.csSetTaskFinish": {"required uInt32 code": 1}, "game.playerHandler.upgradeFormation": {"required uInt32 code": 1, "optional uInt32 resId": 2}, "game.playerHandler.resetGradeFormation": {"required uInt32 code": 1, "required uInt32 resId": 2}, "game.playerHandler.getBattleRecord": {"required uInt32 code": 1, "required uInt32 startTime": 2, "repeated PreBattleTeam preBattleInfo": 3, "repeated BattleRoundInfo battleRoundInfo": 4, "optional BattleEndInfo battleEndInfo": 5, "repeated BattleSkillRecord skillRecord": 6}, "game.playerHandler.sendChat": {"required uInt32 code": 1}, "game.playerHandler.updateChat": {"required OneChatMsg oneMsg": 1, "optional uInt32 rollingTime": 2}, "game.playerHandler.cultivateHero": {"required uInt32 code": 1, "repeated uInt32 random": 2, "optional Hero hero": 3}, "game.playerHandler.breakOutHero": {"required uInt32 code": 1, "optional uInt32 num": 2, "optional Hero hero": 3}, "game.playerHandler.reBreakOutHero": {"required uInt32 code": 1, "optional uInt32 num": 2, "optional Hero hero": 3}, "game.playerHandler.upAddHero": {"optional Hero hero": 1}, "game.playerHandler.upDelHero": {"required uInt32 uid": 1}, "game.playerHandler.getTrainInfo": {"required uInt32 code": 1, "optional HeroTrain heroTrain": 2}, "game.playerHandler.trainHero": {"required uInt32 code": 1, "repeated uInt32 property": 2, "repeated uInt32 random": 3, "optional uInt32 index": 4}, "game.playerHandler.replaceHeroTrain": {"required uInt32 code": 1, "optional uInt32 index": 2}, "game.playerHandler.heroUpStar": {"required uInt32 code": 1, "optional uInt32 star": 2, "optional uInt32 isSuccess": 3}, "game.playerHandler.getHeroSkillInfo": {"required uInt32 code": 1, "optional HeroSkill heroSkill": 2, "repeated uInt32 unLockList": 3}, "game.playerHandler.activateHeroSkill": {"required uInt32 code": 1, "optional HeroSkill heroSkill": 2, "optional string uid": 3, "repeated uInt32 skillList": 4}, "game.playerHandler.getGroundAllInfo": {"optional uInt32 code": 1, "optional AllGround allGround": 2, "optional string uid": 3, "optional string name": 4, "optional uInt32 faceIcon": 5}, "game.playerHandler.getGroundInfoByType": {"optional uInt32 code": 1, "optional GroundInfo ground": 2, "optional string uid": 3, "optional uInt32 type": 4}, "game.playerHandler.getGroundRewardByType": {"optional uInt32 code": 1, "optional string uid": 2, "optional uInt32 type": 3, "repeated uInt32 rewardNum": 4}, "game.playerHandler.upgradeGroundByType": {"optional uInt32 code": 1, "optional string uid": 2, "optional uInt32 type": 3, "optional uInt32 upTime": 4}, "game.playerHandler.getBusinessMatch": {"optional uInt32 code": 1, "optional FanRankInfo fanRankInfo": 2, "optional FightTimesInfo fightTimes": 3, "repeated MatchRecord lastMatchRecord": 4, "optional uInt32 searchCost": 5, "repeated GlobalFanRank rankList": 6}, "game.playerHandler.businessSearch": {"optional uInt32 code": 1, "optional SimpleTeamInfo enemyInfo": 2, "optional FanRankInfo enemyFanRankInfo": 3, "optional MatchResult result": 4}, "game.playerHandler.businessMatch": {"optional uInt32 code": 1, "optional SimpleTeamInfo enemyInfo": 2, "optional FanRankInfo enemyFanRankInfo": 3, "optional MatchResult result": 4}, "game.playerHandler.getHeroTrainInfo": {"optional uInt32 code": 1, "repeated TrainPos trainPos": 2}, "game.playerHandler.heroTrainInGround": {"optional uInt32 code": 1, "optional uInt32 beginTime": 2, "optional string heroUid": 3, "optional uInt32 index": 4, "optional uInt32 state": 5, "optional uInt32 isLockTrain": 6}, "game.playerHandler.getHeroTrainReward": {"optional uInt32 code": 1, "optional uInt32 index": 2, "optional uInt32 rand": 3, "optional Hero hero": 4, "optional uInt32 type": 5}, "game.playerHandler.unlockTrainPos": {"optional uInt32 code": 1, "optional uInt32 index": 2, "optional uInt32 state": 3}, "game.playerHandler.getTransferTrainerInfo": {"optional uInt32 code": 1, "repeated TransferPos trainerList": 2, "optional uInt32 allActual": 3, "optional string teamUid": 4}, "game.playerHandler.addTrainInTransfer": {"optional uInt32 code": 1, "repeated TransferPos trainerList": 2, "optional uInt32 allActual": 3, "optional string teamUid": 4}, "game.playerHandler.getAllTrainer": {"optional uInt32 code": 1, "repeated Trainer trainerList": 2}, "game.playerHandler.unlockTransferPos": {"optional uInt32 code": 1, "optional uInt32 state": 2, "optional uInt32 index": 3}, "game.playerHandler.compoundTrainer": {"optional uInt32 code": 1, "optional Trainer trainer": 2}, "game.playerHandler.getBallHandbook": {"optional uInt32 code": 1, "repeated uInt32 ball": 2, "repeated Handbook ballCom": 3, "optional uInt32 ballActual": 4, "optional uInt32 ballComActual": 5, "optional uInt32 ballComCount": 6}, "game.playerHandler.activeBallHandbook": {"optional uInt32 code": 1, "optional uInt32 heroNum": 2, "optional uInt32 handbookNum": 3, "optional uInt32 ballComCount": 4, "optional uInt32 ballCount": 5, "optional uInt32 resId": 6}, "message Retirement": {"optional string uid": 1, "optional uInt32 resId": 2, "optional uInt32 star": 3, "optional uInt32 trainCount": 4, "optional uInt32 isUseYonger": 5, "optional uInt32 rating": 6}, "game.playerHandler.getRetirementList": {"optional uInt32 code": 1, "repeated Retirement retirementList": 2}, "game.playerHandler.getNotablePosInfo": {"optional uInt32 code": 1, "repeated NotablePos notablePos": 2, "optional uInt32 notablePrestige": 3, "repeated Retirement retirementList": 4}, "game.playerHandler.updateRetirementHero": {"repeated Retirement retirementList": 1}, "game.playerHandler.inputNotablePos": {"optional uInt32 code": 1, "optional uInt32 notablePrestige": 2, "repeated Retirement retirementList": 3, "repeated NotablePos notablePos": 4}, "game.playerHandler.getTrophy": {"optional uInt32 code": 1, "repeated Trophy trophyInfo": 2, "optional uInt32 limitType": 3}, "game.playerHandler.getTrophyTeam": {"optional uInt32 code": 1, "repeated TrophyTeam trophyTeamInfo": 2}, "game.playerHandler.trophyBattle": {"required uInt32 code": 1, "optional string roomUid": 2}, "game.playerHandler.updateTrophyResult": {"required uInt32 selfScore": 1, "required uInt32 enemyScore": 2, "optional uInt32 trophyId": 3, "optional uInt32 teamId": 4, "repeated TrophyTeam trophyTeamInfo": 5, "optional Trophy trophyInfo": 6}, "game.playerHandler.trophyPurchase": {"required uInt32 code": 1, "optional uInt32 trophyId": 2, "optional LimitNum limitInfo": 3}, "game.playerHandler.trophyTeamPurchase": {"required uInt32 code": 1, "optional uInt32 trophyId": 2, "optional uInt32 teamId": 3, "optional LimitNum limitInfo": 4}, "game.playerHandler.getStoreInfo": {"optional uInt32 code": 1, "repeated BuyInfo allBuyInfo": 2, "repeated BuyInfo allSerBuyInfo": 3}, "game.playerHandler.buyItem": {"optional uInt32 code": 1}, "game.playerHandler.rechargeCreateOrder": {"required uInt32 code": 1, "optional string url": 2, "optional uInt32 payType": 3}, "game.playerHandler.getFollowList": {"required uInt32 code": 1, "optional FollowPageInfo followPageInfo": 2, "optional FollowPageInfo fansPageInfo": 3, "optional uInt32 giveEnergyNum": 4, "optional uInt32 getEnergyNum": 5}, "game.playerHandler.getFollowDetail": {"required uInt32 code": 1, "required uInt32 typeId": 2, "optional FollowPageInfo comPageInfo": 3}, "game.playerHandler.updateFollowDetail": {"required uInt32 typeId": 1, "optional FollowPageInfo comPageInfo": 2}, "game.playerHandler.follow": {"required uInt32 code": 1}, "game.playerHandler.unFollow": {"required uInt32 code": 1}, "game.playerHandler.updateAllActivity": {"required uInt32 code": 1, "repeated AllActivity allActivity": 2}, "game.playerHandler.getNewPlayerSignInfo": {"required uInt32 code": 1, "repeated SignInfo signInfo": 2}, "game.playerHandler.onNewPlayerSign": {"required uInt32 code": 1, "optional uInt32 state": 2}, "game.playerHandler.getSevenDaySignInfo": {"required uInt32 code": 1, "repeated SignInfo signInfo": 2}, "game.playerHandler.onSevenDaySign": {"required uInt32 code": 1, "optional uInt32 state": 2}, "game.playerHandler.getEveryDaySignInfo": {"required uInt32 code": 1, "repeated SignInfo signInfo": 2, "optional uInt32 signDay": 3}, "game.playerHandler.onEveryDaySign": {"required uInt32 code": 1, "optional uInt32 state": 2}, "game.playerHandler.getVipShopInfo": {"required uInt32 code": 1, "optional PersonInfo vipInfo": 2, "repeated VipGiftInfo giftRewardList": 3}, "game.playerHandler.buyVipShop": {"required uInt32 code": 1, "required uInt32 id": 2, "required uInt32 type": 3, "repeated ItemResIdList itemResIdList": 4, "repeated HeroResIdList heroResIdList": 5}, "game.playerHandler.updateVipShopInfo": {"optional PersonInfo vipInfo": 1, "repeated VipGiftInfo giftRewardList": 2}, "game.playerHandler.getActList": {"optional uInt32 code": 1, "repeated OneActRecord actList": 2, "repeated uInt32 redTipsList": 3}, "game.playerHandler.updateActList": {"optional uInt32 code": 1, "repeated OneActRecord actList": 2, "repeated uInt32 redTipsList": 3}, "game.playerHandler.actTakeAward": {"optional uInt32 code": 1, "optional uInt32 actId": 2, "optional uInt32 tagIndex": 3, "repeated OneActAward awardList": 4}, "game.playerHandler.upgradeGoldCoach": {"optional uInt32 code": 1, "optional uInt32 actId": 2}, "game.playerHandler.getSeasonStoreInfo": {"required uInt32 code": 1, "repeated GoodsInfo goodsInfo": 2}, "game.playerHandler.seasonStoreBuyItem": {"required uInt32 code": 1, "required uInt32 id": 2, "required uInt32 num": 3}, "game.playerHandler.refreshSeasonStore": {"required uInt32 code": 1, "repeated GoodsInfo goodsInfo": 2}, "game.playerHandler.getMonthCardInfo": {"required uInt32 code": 1, "optional uInt32 state": 2, "optional uInt32 cardTime": 3, "optional uInt32 isBuy": 4}, "game.playerHandler.getMonthCardReward": {"required uInt32 code": 1, "optional uInt32 cardTime": 2}, "game.playerHandler.buyMonthCard": {"required uInt32 code": 1}, "gate.gateHandler.getShareBattle": {"required uInt32 code": 1, "repeated PreBattleTeam preBattleInfo": 2, "repeated BattleRoundInfo battleRoundInfo": 3, "optional BattleEndInfo battleEndInfo": 4, "repeated BattleSkillRecord skillRecord": 5}, "game.playerHandler.getLimitStoreInfo": {"optional uInt32 code": 1, "repeated BuyInfo allBuyInfo": 2, "repeated BuyInfo allSerBuyInfo": 3}, "game.playerHandler.buyLimitStoreItem": {"optional uInt32 code": 1}, "game.playerHandler.updateNewerGuide": {"optional uInt32 code": 1, "repeated ItemResIdList itemResIdList": 2}, "game.playerHandler.ntfNewerGuide": {"repeated NewerGuide guideList": 1, "repeated everyDayTask finishEveryDayList": 2}, "game.playerHandler.joinMatch": {"optional uInt32 code": 1, "optional WorldCup allTeam": 2}, "game.playerHandler.getGroupMatchRank": {"optional uInt32 code": 1, "optional GroupMatch allTeam": 2}, "game.playerHandler.getBuyCount": {"optional uInt32 code": 1, "optional uInt32 buyCount": 2}, "game.playerHandler.buyWorldCupJoinNum": {"optional uInt32 code": 1, "optional uInt32 joinCount": 2}, "game.playerHandler.getWorldCupInfo": {"optional uInt32 code": 1, "optional uInt32 joinCount": 2, "optional uInt32 isGetReward": 3, "optional uInt32 isJoin": 4, "optional uInt32 groupNum": 5, "optional uInt32 worldCupId": 6, "optional WorldCup groupList": 7, "optional WorldCup eliminateList": 8}, "game.playerHandler.getEliminateMatchList": {"optional uInt32 code": 1, "repeated EliminateMatch allTeam": 2}, "game.playerHandler.getWorldCupReward": {"optional uInt32 code": 1}, "game.playerHandler.worldCupBattle": {"optional uInt32 code": 1, "optional string roomUid": 2}, "game.playerHandler.updateWorldCupResult": {"optional uInt32 isGetReward": 1, "optional uInt32 groupNum": 2, "optional uInt32 isOut": 3, "optional WorldCup groupList": 4, "optional WorldCup eliminateList": 5, "optional uInt32 worldCupId": 6}, "message ItemInfo": {"optional uInt32 id": 1, "optional uInt32 num": 2}, "game.playerHandler.batchDismissHero": {"required uInt32 code": 1, "repeated ItemInfo itemInfo": 2, "optional uInt32 type": 3, "optional uInt32 index": 4, "repeated string heroUidList": 5}, "game.playerHandler.oneKeySuperMan": {"optional uInt32 code": 1}, "game.playerHandler.getScoutPackInfo": {"required uInt32 code": 1, "repeated ScoutHeroInfo scoutPack": 2}, "game.playerHandler.delScoutPackHero": {"required uInt32 code": 1, "repeated uInt32 index": 2}, "game.playerHandler.signScoutHero": {"required uInt32 code": 1, "optional uInt32 resId": 2}, "game.playerHandler.getEveryDayEnergy": {"required uInt32 code": 1, "repeated EveryDayEnergy energyList": 2}, "game.playerHandler.takeEveryDayEnergy": {"required uInt32 code": 1, "repeated EveryDayEnergy energyList": 2}, "game.playerHandler.useDeemCode": {"required uInt32 code": 1, "repeated ItemResIdList itemResIdList": 2, "repeated HeroResIdList heroResIdList": 3, "optional uInt32 notifyBagFullMail": 4}, "game.playerHandler.getOtherHeroInfo": {"required uInt32 code": 1, "optional Hero heroInfo": 2, "optional string playerUid": 3, "optional HeroTrain heroTrain": 4}, "game.playerHandler.promoteHeroStatus": {"required uInt32 code": 1, "optional uInt32 status": 2, "optional string uid": 3, "optional uInt32 itemId": 4}, "game.playerHandler.getChatMsgList": {"required uInt32 code": 1, "repeated ChannelChatMsgList allList": 2}, "game.playerHandler.renewTheContract": {"required uInt32 code": 1}, "game.playerHandler.syncShareEvent": {"required uInt32 code": 1}, "game.playerHandler.getFirstGiftReward": {"required uInt32 code": 1, "required uInt32 cashNum": 2}, "game.playerHandler.updateHospitalPosInfo": {"repeated HospitalPosInfo hospitalPosInfo": 1}, "game.playerHandler.getHospitalPosInfo": {"required uInt32 code": 1, "repeated HospitalPosInfo hospitalPosInfo": 2}, "game.playerHandler.inputHospitalPos": {"required uInt32 code": 1}, "game.playerHandler.accelerationHospitalPosTime": {"required uInt32 code": 1}, "game.playerHandler.getHospitalPosHero": {"required uInt32 code": 1}, "game.playerHandler.buyBusinessMatch": {"required uInt32 code": 1, "optional uInt32 fightTimes": 2, "optional uInt32 buyTimes": 3}, "game.playerHandler.businessGetRival": {"required uInt32 code": 1, "optional uInt32 weekFansRank": 2, "optional uInt32 Attack": 3, "optional uInt32 Defend": 4, "optional uInt32 ActualStrength": 5, "repeated MatchGetRival GetRivalList": 6}, "game.playerHandler.slogLoadResourceTime": {"required uInt32 code": 1}, "game.playerHandler.updateRedDotHintState": {"repeated RedHint redHintList": 1}, "game.playerHandler.getBallFansReward": {"required uInt32 code": 1, "optional uInt32 num": 2}, "game.playerHandler.seckillGroundTime": {"required uInt32 code": 1, "optional GroundInfo groundInfo": 2}, "game.playerHandler.autoSetFormation": {"required uInt32 code": 1}, "game.playerHandler.updateLeagueEnrollTips": {"required uInt32 isEnroll": 1, "optional uInt32 beginTime": 2, "optional uInt32 endTime": 3, "optional uInt32 relayIsJoin": 4}, "game.playerHandler.battleSkip": {"required uInt32 code": 1}, "game.playerHandler.getBusinessRecord": {"required uInt32 code": 1, "repeated MatchRecord matchRecordList": 2}, "game.playerHandler.buyCoachReward": {"required uInt32 code": 1, "optional uInt32 isBuyedCoach": 2}, "game.playerHandler.updateFirstGetHeroList": {"repeated uInt32 firstGetHero": 1}, "game.playerHandler.updateNewerTask": {"optional uInt32 resId": 1, "optional uInt32 num": 2, "optional uInt32 status": 3}, "game.playerHandler.getNewerTaskReward": {"required uInt32 code": 1, "optional uInt32 resId": 2}, "game.playerHandler.heroAdvanceStage": {"required uInt32 code": 1, "optional uInt32 type": 2}, "game.playerHandler.dqMatchEnroll": {"required uInt32 code": 1}, "game.playerHandler.getDqMatchInfo": {"required uInt32 code": 1, "required uInt32 isEnroll": 2, "required uInt32 matchPeriod": 3, "required uInt32 enrollEndTime": 4, "optional DqPreBattle preBattle": 5, "repeated DqMatchInfo match64List": 6, "repeated DqMatchInfo match32List": 7, "repeated DqMatchInfo match16List": 8, "repeated DqMatchInfo match8List": 9, "repeated DqMatchInfo match4List": 10, "repeated DqMatchInfo match2List": 11, "optional string championUid": 12}, "message awardList": {"optional uInt32 bonus": 1, "optional uInt32 added": 2}, "message teamList": {"optional string id": 1, "optional string Name": 2, "optional string IconID": 3, "optional uInt32 Formation": 4, "optional uInt32 OffensiveID": 5}, "game.playerHandler.getSelectInterface": {"required uInt32 code": 1, "optional uInt32 isBegin": 2, "optional uInt32 SponsorID": 3, "repeated teamList teamList": 4, "optional uInt32 contestNum": 5, "repeated uInt32 rivalTeamList": 6, "repeated awardList awardList": 7}, "game.playerHandler.MiddleEastCupJoinMatch": {"required uInt32 code": 1, "repeated uInt32 rivalTeamList": 2, "repeated awardList awardList": 3}, "game.playerHandler.MiddleEastCupBattle": {"required uInt32 code": 1, "optional string roomUid": 2}, "game.playerHandler.updateMiddleEastCupResult": {"required uInt32 code": 1, "optional uInt32 isBegin": 2, "repeated uInt32 award": 3, "repeated uInt32 awardNum": 4}, "game.playerHandler.getDqMatchTopList": {"required uInt32 code": 1, "repeated DqMatchTopInfo topList": 2}, "game.playerHandler.worldBossBattle": {"required uInt32 code": 1, "optional string roomUid": 2}, "game.playerHandler.killWorldBossTime": {"required uInt32 code": 1}, "game.playerHandler.getWorldBossInfo": {"required uInt32 code": 1, "repeated WorldBoss worldBossInfo": 2, "optional uInt32 attack": 3, "optional uInt32 defend": 4, "optional uInt32 actualStrength": 5}, "game.playerHandler.getWorldBossRank": {"required uInt32 code": 1, "repeated WorldBossRank rankList": 2}, "game.playerHandler.updateWorldBossResult": {"required uInt32 code": 1, "repeated WorldBoss worldBossInfo": 2, "optional uInt32 isKiller": 3, "optional uInt32 attack": 4, "optional uInt32 defend": 5, "optional uInt32 actualStrength": 6}, "game.playerHandler.notifyWorldBossKill": {"optional uInt32 bossId": 1, "optional string killer": 2}, "game.playerHandler.notifyBossStartInfo": {"optional uInt32 time": 1}, "game.playerHandler.buyWorldBossItem": {"required uInt32 code": 1}, "game.playerHandler.clockGetWorldBossInfo": {"required uInt32 code": 1, "optional uInt32 bossId": 2, "optional uInt32 boosGoal": 3, "optional uInt32 bossTotal": 4, "optional uInt32 rank": 5}, "game.playerHandler.updatePushGift": {"optional uInt32 id": 1, "optional uInt32 endTime": 2, "optional uInt32 isBuy": 3, "optional uInt32 isTimeOut": 4}, "game.playerHandler.buyPushGift": {"required uInt32 code": 1}, "game.playerHandler.updateHeroHealth": {"optional string uid": 1, "optional uInt32 value": 2}, "message gulfCombatList": {"optional teamList TeamA": 1, "optional teamList TeamB": 2}, "game.playerHandler.getGulfSelectInterface": {"required uInt32 code": 1, "required uInt32 isBegin": 2, "required uInt32 conditionID": 3, "repeated teamList teamList": 4, "repeated gulfCombatList CombatList": 5, "required uInt32 contestNum": 6}, "game.playerHandler.gulfCupJoinMatch": {"required uInt32 code": 1, "required uInt32 conditionID": 2, "repeated gulfCombatList CombatList": 3}, "game.playerHandler.updateGulfCupResult": {"required uInt32 code": 1, "repeated uInt32 award": 2, "repeated uInt32 awardNum": 3}, "game.playerHandler.decomposeItem": {"required uInt32 code": 1, "optional uInt32 num": 2}, "game.playerHandler.compoundItem": {"required uInt32 code": 1, "optional uInt32 resId": 2, "optional uInt32 num": 3}, "game.playerHandler.ghostDecomposeItem": {"required uInt32 code": 1, "optional uInt32 num": 2}, "message ExchangeList": {"optional uInt32 id": 1, "optional uInt32 isBuy": 2}, "message ExchangeNumList": {"optional uInt32 type": 1, "optional uInt32 num": 2}, "game.playerHandler.getExchangeHallInfo": {"required uInt32 code": 1, "repeated ExchangeList itemList": 2, "optional uInt32 chip": 3, "repeated ExchangeNumList exchangeNumList": 4}, "game.playerHandler.exchangeItem": {"required uInt32 code": 1, "optional uInt32 id": 2}, "game.playerHandler.flushExchangeHall": {"required uInt32 code": 1, "repeated ExchangeList itemList": 2, "optional uInt32 num": 3, "optional uInt32 type": 4}, "game.playerHandler.createAssociation": {"required uInt32 code": 1}, "message Association": {"optional uInt32 associationId": 1, "optional string creator": 2, "optional uInt32 level": 3, "optional uInt32 exp": 4, "optional string associationName": 5, "optional uInt32 playerNum": 6, "optional uInt32 faceId": 7}, "game.playerHandler.getAllAssociation": {"required uInt32 code": 1, "repeated Association associationInfo": 2, "repeated uInt32 leagueList": 3}, "game.playerHandler.exitAssociation": {"required uInt32 code": 1}, "message AssociationPlayer": {"optional string playerName": 1, "optional uInt32 leaveTime": 2, "optional uInt32 isOnLine": 3, "optional uInt32 exp": 4, "optional uInt32 pos": 5, "optional uInt32 strength": 6, "optional uInt32 contribute": 7, "optional string faceUrl": 8, "optional string playerId": 9}, "game.playerHandler.getAssociationPlayerList": {"required uInt32 code": 1, "repeated AssociationPlayer playerList": 2}, "game.playerHandler.joinAssociation": {"required uInt32 code": 1, "optional uInt32 associationId": 2}, "game.playerHandler.removeJoinAssociation": {"required uInt32 code": 1, "optional uInt32 associationId": 2}, "game.playerHandler.agreeJoinAssociation": {"required uInt32 code": 1, "optional uInt32 type": 2}, "message OneAssociation": {"optional uInt32 faceId": 1, "optional uInt32 associationId": 2, "optional uInt32 playerNum": 3, "optional string notice": 4, "optional uInt32 level": 5, "optional uInt32 exp": 6, "optional string creator": 7, "optional string associationName": 8}, "game.playerHandler.getAssociationInfo": {"required uInt32 code": 1, "optional OneAssociation associationInfo": 2}, "message ApprovalList": {"optional string playerId": 1, "optional string playerName": 2, "optional string faceUrl": 3, "optional uInt32 strength": 4}, "game.playerHandler.getApprovalList": {"required uInt32 code": 1, "repeated ApprovalList approvalList": 2}, "game.playerHandler.changeAssociationPosition": {"required uInt32 code": 1, "optional uInt32 changePos": 2}, "game.playerHandler.updateLeagueSeasonId": {"required uInt32 seasonId": 1}, "message HistoryInfo": {"optional uInt32 typeId": 1, "optional uInt32 groupId": 2, "optional uInt32 roundId": 3, "optional uInt32 beginTime": 4, "optional SimpleTeamInfo teamA": 5, "optional SimpleTeamInfo teamB": 6, "optional uInt32 teamAScore": 7, "optional uInt32 teamBScore": 8, "optional uInt32 status": 9, "optional uInt32 isNull": 10, "optional uInt32 seasonId": 11, "optional uInt32 state": 12}, "game.playerHandler.buyRelay": {"required uInt32 code": 1}, "game.playerHandler.getBroadcastInterface": {"required uInt32 code": 1, "optional uInt32 typeId": 2, "optional uInt32 isJoin": 3, "optional uInt32 integral": 4, "repeated HistoryInfo HistoryInfo": 5, "optional string buyRelayEndTime": 6, "optional string nowTime": 7, "repeated Task taskList": 8}, "game.playerHandler.receiveAward": {"required uInt32 code": 1, "optional uInt32 awardId": 2, "optional uInt32 awardNum": 3}, "message relayAward": {"optional uInt32 awardId": 1, "optional uInt32 awardNum": 2}, "game.playerHandler.receiveAllAward": {"required uInt32 code": 1, "repeated relayAward awardList": 2}, "game.playerHandler.convertibility": {"required uInt32 code": 1, "optional uInt32 awardId": 2, "optional uInt32 awardNum": 3, "optional uInt32 integral": 4}, "game.playerHandler.updateRelayEndTime": {"required uInt32 endTime": 1}, "game.playerHandler.upgradeTactics": {"required uInt32 code": 1}, "message MLSCombatList": {"optional teamList TeamA": 1, "optional teamList TeamB": 2}, "game.playerHandler.getMLSSelectInterface": {"required uInt32 code": 1, "required uInt32 isBegin": 2, "required uInt32 conditionID": 3, "repeated teamList teamList": 4, "repeated MLSCombatList CombatList": 5, "required uInt32 contestNum": 6}, "game.playerHandler.MLSJoinMatch": {"required uInt32 code": 1, "required uInt32 conditionID": 2, "repeated MLSCombatList CombatList": 3}, "game.playerHandler.updateMLSResult": {"required uInt32 code": 1, "repeated uInt32 award": 2, "repeated uInt32 awardNum": 3}, "game.playerHandler.resetTactics": {"required uInt32 code": 1}, "game.playerHandler.buyBestFootball": {"required uInt32 code": 1, "repeated uInt32 itemList": 2}, "game.playerHandler.getSignInfo": {"required uInt32 code": 1, "repeated SignInfo signInfo": 2}, "game.playerHandler.onSign": {"required uInt32 code": 1, "optional uInt32 state": 2}, "message ConsumeInfo": {"optional uInt32 status": 1}, "game.playerHandler.getConsumeInfo": {"required uInt32 code": 1, "repeated ConsumeInfo consumeInfo": 2, "optional uInt32 energyVal": 3}, "game.playerHandler.getConsumeReward": {"required uInt32 code": 1, "optional uInt32 status": 2}, "game.playerHandler.buyRegression": {"required uInt32 code": 1, "repeated uInt32 itemIdList": 2}, "message Turntable": {"optional uInt32 ItemId": 1, "optional uInt32 Num": 2, "optional uInt32 Id": 3}, "game.playerHandler.buyTurntable": {"required uInt32 code": 1, "repeated Turntable itemIdList": 2, "optional uInt32 status": 3}, "game.playerHandler.buySlots": {"required uInt32 code": 1, "repeated Turntable itemIdList": 2}, "game.playerHandler.getLotteryPrizeInfo": {"required uInt32 code": 1, "repeated Turntable itemIdList": 2}, "game.playerHandler.getLotteryPrizeReward": {"required uInt32 code": 1}, "game.playerHandler.getTurntable": {"required uInt32 code": 1, "optional uInt32 oneFreeNum": 2, "optional uInt32 moreFreeNum": 3}, "game.playerHandler.getSlots": {"required uInt32 code": 1, "optional uInt32 num": 2}, "game.playerHandler.getMvpHeroId": {"required uInt32 code": 1, "optional uInt32 mvpHeroId": 2}, "game.playerHandler.buyMvpHero": {"required uInt32 code": 1}, "game.playerHandler.getFormationPlaceKickList": {"required uInt32 code": 1, "optional string FreeKick": 2, "optional string Penalties": 3, "optional string CornerKick": 4}, "game.playerHandler.setFormationPlaceKickDuty": {"required uInt32 code": 1, "optional string footballerUid": 2, "optional uInt32 duty": 3}, "game.playerHandler.addHeroLeftDay": {"required uInt32 code": 1}, "game.playerHandler.giveFollowEnergy": {"required uInt32 code": 1}, "game.playerHandler.getFollowEnergy": {"required uInt32 code": 1}, "game.playerHandler.getAllFollowEnergy": {"required uInt32 code": 1, "required uInt32 Num": 2}, "game.playerHandler.giveAllFollowEnergy": {"required uInt32 code": 1, "required uInt32 Num": 2}, "game.playerHandler.addHeroToRetirementList": {"required uInt32 code": 1, "repeated Retirement retirementList": 2}, "game.playerHandler.upgradeTrainerStar": {"required uInt32 code": 1, "optional Trainer trainer": 2}, "game.playerHandler.upgradeTrainerLevel": {"required uInt32 code": 1, "optional Trainer trainer": 2}, "message LeagueChampion": {"optional uInt32 seasonId": 1, "optional string playerUid": 2, "optional string Name": 3, "optional string faceUrl": 4, "optional uInt32 num": 5}, "game.playerHandler.CSSwitchLeagueChampion": {"required uInt32 code": 1, "repeated LeagueChampion ChampionList": 2}, "message BeliefChangeRecord": {"optional uInt32 newBeliefId": 1, "optional uInt32 oldBeliefId": 2, "optional uInt32 mvTime": 3}, "game.playerHandler.getBeliefChangeRecord": {"repeated BeliefChangeRecord changeRecord": 1}, "game.playerHandler.modifyBeliefChangeRecord": {"required uInt32 code": 1, "optional uInt32 beliefId": 2, "optional uInt32 mvTime": 3}, "game.playerHandler.updateOneTrainer": {"optional Trainer trainer": 1}, "game.playerHandler.updateAllTrainer": {"repeated Trainer trainerList": 1}, "message leaderInfo": {"optional string uid": 1, "optional string gid": 2, "optional uInt32 pos": 3}, "message BeliefNotify": {"optional uInt32 time": 1, "optional string msg": 2}, "message BeliefInfo": {"optional uInt32 beliefId": 1, "optional string notice": 2, "repeated string playerList": 3, "optional uInt32 beliefRank": 4, "optional uInt32 beliefGold": 5, "repeated leaderInfo leader": 6, "repeated BeliefNotify notify": 7, "optional uInt32 level": 8, "optional uInt32 beliefExp": 9, "optional uInt32 todayGoldNum": 10}, "game.playerHandler.getBeliefInfo": {"required uInt32 code": 1, "optional BeliefInfo beliefInfo": 2}, "game.playerHandler.donateBeliefGold": {"required uInt32 code": 1, "optional uInt32 beliefGold": 2, "optional uInt32 beliefNum": 3}, "game.playerHandler.modifyBeliefNotice": {"required uInt32 code": 1, "optional string notice": 2}, "game.playerHandler.setIsShowTactics": {"required uInt32 code": 1, "optional uInt32 type": 2}, "game.playerHandler.chairmanMatchEnroll": {"required uInt32 code": 1}, "game.playerHandler.getChairmanMatchInfo": {"required uInt32 code": 1, "optional uInt32 isEnroll": 2, "optional uInt32 matchPeriod": 3, "optional uInt32 enrollEndTime": 4, "optional DqPreBattle preBattle": 5, "repeated DqMatchInfo match64List": 6, "repeated DqMatchInfo match32List": 7, "repeated DqMatchInfo match16List": 8, "repeated DqMatchInfo match8List": 9, "repeated DqMatchInfo match4List": 10, "repeated DqMatchInfo match3List": 11, "repeated DqMatchInfo match2List": 12, "repeated chMatchTopInfo campaignList": 13, "optional chMatchTopInfo chairman": 14, "optional chMatchTopInfo coChairman": 15, "optional chMatchTopInfo secondPlace": 16, "optional chMatchTopInfo thirdPlace": 17}, "message chMatchTopInfo": {"optional string uid": 1, "optional string faceUrl": 2, "optional string name": 3, "optional uInt32 time": 4, "optional uInt32 num": 5, "repeated uInt32 history": 6}, "message chMatchAllTopInfo": {"optional uInt32 time": 1, "optional chMatchTopInfo chairman": 2, "optional chMatchTopInfo coChairman": 3, "optional chMatchTopInfo secondPlace": 4, "optional chMatchTopInfo thirdPlace": 5}, "game.playerHandler.getChairmanMatchTopList": {"required uInt32 code": 1, "repeated chMatchAllTopInfo topList": 2}, "game.playerHandler.coChairmanCampaign": {"required uInt32 code": 1, "repeated chMatchTopInfo campaignList": 2}, "message pkMatchInfo": {"optional string homeUid": 1, "optional string homeFaceUrl": 2, "optional string homeName": 3, "optional uInt32 homeScore": 4, "optional string awayUid": 5, "optional string awayFaceUrl": 6, "optional string awayName": 7, "optional uInt32 awayScore": 8, "optional string winnerUid": 9, "optional string roomUid": 10, "optional uInt32 time": 11}, "game.playerHandler.getPeakMatchInfo": {"required uInt32 code": 1, "optional uInt32 matchPeriod": 2, "optional uInt32 openTime": 3, "repeated pkMatchTopInfo participant": 4, "repeated pkMatchInfo match32List": 5, "repeated pkMatchInfo match16List": 6, "repeated pkMatchInfo match8List": 7, "repeated pkMatchInfo match4List": 8, "repeated pkMatchInfo match2List": 9, "optional pkMatchTopInfo chairman": 10, "optional pkMatchTopInfo coChairman": 11, "optional uInt32 total": 12, "optional uInt32 fightTime": 13}, "message pkMatchTopInfo": {"optional string uid": 1, "optional string faceUrl": 2, "optional string name": 3, "optional uInt32 beliefId": 4, "optional uInt32 ActualStrength": 5}, "message pkMatchAllTopInfo": {"optional uInt32 time": 1, "optional pkMatchTopInfo chairman": 2}, "game.playerHandler.getPeakMatchTopList": {"required uInt32 code": 1, "repeated pkMatchAllTopInfo topList": 2}, "game.playerHandler.StraightBet": {"required uInt32 code": 1, "optional uInt32 total": 2}, "message betOnInfo": {"optional string uid": 1, "optional string name": 2, "optional uInt32 beliefId": 3, "optional uInt32 beliefTotal": 4, "optional uInt32 MAX": 5, "optional uInt32 playerBetOn": 6}, "game.playerHandler.getBetOnList": {"required uInt32 code": 1, "repeated betOnInfo betOnInfoList": 2}, "game.playerHandler.beliefMatchEnroll": {"required uInt32 code": 1, "optional uInt32 type": 2}, "game.playerHandler.challengeBelief": {"required uInt32 code": 1, "optional uInt32 otherBeliefId": 2}, "message AttackerList": {"optional uInt32 beliefId": 1, "optional uInt32 attackTime": 2}, "message WarOfFaith": {"optional uInt32 beliefId": 1, "repeated AttackerList attackerList": 2, "optional uInt32 holderId": 3, "optional uInt32 atkNum": 4, "optional uInt32 defNum": 5, "optional uInt32 matchPeriod": 6}, "game.playerHandler.getAllWarOfFaith": {"required uInt32 code": 1, "repeated WarOfFaith allWarOfFaith": 2}, "message WarOfFaithBattleTeamInfo": {"optional uInt32 attackId": 1, "optional string attackPlayerId": 2, "optional string attackPlayerName": 3, "optional string attackFaceUrl": 4, "optional uInt32 attackStrength": 5, "optional uInt32 defendId": 6, "optional string defendPlayerId": 7, "optional string defendPlayerName": 8, "optional string defendFaceUrl": 9, "optional uInt32 defendStrength": 10, "optional string roomUid": 11, "optional uInt32 attackScore": 12, "optional uInt32 defendScore": 13, "optional uInt32 attackInspireNum": 14, "optional uInt32 defendInspireNum": 15}, "game.playerHandler.getWarOfFaithNextBattleInfo": {"required uInt32 code": 1, "optional WarOfFaithBattleTeamInfo nextBattleTeam": 2, "optional WarOfFaithBattleTeamInfo beforeBattleTeam": 3, "optional uInt32 nextBattleTime": 4, "optional uInt32 beliefId": 5, "optional uInt32 group": 6, "optional uInt32 attackNum": 7, "optional uInt32 defendNum": 8, "repeated uInt32 isFinish": 9}, "message WarOfFaithPlayerInfo": {"optional string faceUrl": 1, "optional string playerName": 2, "optional string playerId": 3}, "message WarOfFaithBattleInfo": {"optional uInt32 attackBeliefId": 1, "repeated WarOfFaithPlayerInfo attackTeam": 2, "optional uInt32 defendBeliefId": 3, "repeated WarOfFaithPlayerInfo defendTeam": 4, "optional uInt32 attackNum": 5, "optional uInt32 defendNum": 6}, "game.playerHandler.getWarOfFaithBattleInfo": {"required uInt32 code": 1, "repeated WarOfFaithBattleInfo allBattleTeam": 2, "optional uInt32 beliefId": 3, "repeated uInt32 isFinish": 4, "optional uInt32 getStatus": 5}, "game.playerHandler.getWarOfFaithBattleResultInfo": {"required uInt32 code": 1, "repeated WarOfFaithBattleTeamInfo battleResult": 2, "optional uInt32 beliefId": 3, "optional uInt32 group": 4}, "game.playerHandler.setWarOfFaithTeamFormation": {"required uInt32 code": 1, "optional string uid": 2}, "game.playerHandler.buyBeliefMvpHero": {"required uInt32 code": 1, "optional uInt32 id": 2}, "message ChallengeList": {"optional uInt32 atkId": 1, "optional uInt32 defId": 2, "optional uInt32 time": 3}, "game.playerHandler.sendWarOfFaithNotify": {"repeated ChallengeList challengeList": 1}, "message GroundMatchReportInfo": {"optional string name": 1, "optional uInt32 time": 2}, "message OneGroundFieldInfo": {"optional uInt32 resId": 1, "optional string name": 2, "optional string faceUrl": 3, "optional uInt32 formationResId": 4, "optional uInt32 attack": 5, "optional uInt32 defend": 6, "optional uInt32 atkTactic": 7, "optional uInt32 defTactic": 8, "optional uInt32 occupyTime": 9, "optional uInt32 expectCash": 10, "optional string ownerTeamUid": 11, "optional uInt32 teamIndex": 12, "optional uInt32 leftProtectTime": 13, "optional uInt32 leftProtectType": 14, "optional uInt32 str": 15, "repeated GroundMatchReportInfo reportList": 16}, "game.playerHandler.getMyGroundFieldInfo": {"required uInt32 code": 1, "repeated OneGroundFieldInfo fieldList": 2, "required uInt32 cash": 3, "required uInt32 reportLeftTime": 4, "required uInt32 reportNum": 5}, "message GroundMatchRecord": {"optional string roomUid": 1, "optional string uidA": 2, "optional string nameA": 3, "optional string faceUrlA": 4, "optional uInt32 scoreA": 5, "optional string uidB": 6, "optional string nameB": 7, "optional string faceUrlB": 8, "optional uInt32 scoreB": 9, "optional uInt32 time": 10}, "message OneGroundOccupyInfo": {"optional uInt32 resId": 1, "optional string ownerUid": 2, "optional string ownerName": 3, "optional string ownerTeamUid": 4, "optional string ownerFaceUrl": 5, "optional string occupyUid": 6, "optional string occupyName": 7, "optional string occupyFaceUrl": 8, "optional string occupyTeamUid": 9, "optional uInt32 occupyTime": 10, "optional string teamUid": 11, "repeated GroundMatchReportInfo reportList": 12, "optional uInt32 leftProtectTime": 13, "optional uInt32 leftProtectType": 14, "optional uInt32 expectCash": 15, "optional uInt32 teamIndex": 16, "repeated GroundMatchRecord recordList": 17, "optional uInt32 str": 18, "optional uInt32 attack": 19, "optional uInt32 defend": 20}, "game.playerHandler.getMyGroundMatchCash": {"required uInt32 code": 1}, "game.playerHandler.getMyGroundOccupyInfo": {"required uInt32 code": 1, "repeated OneGroundOccupyInfo occupyList": 2}, "game.playerHandler.setGroupMatchTeam": {"required uInt32 code": 1}, "game.playerHandler.groundMatchRandomMatch": {"required uInt32 code": 1, "repeated OneGroundOccupyInfo searchList": 2}, "game.playerHandler.groundMatchByName": {"required uInt32 code": 1, "repeated OneGroundOccupyInfo searchList": 2}, "game.playerHandler.groundMatchRob": {"required uInt32 code": 1, "optional string roomUid": 2}, "game.playerHandler.groundMatchDriveAway": {"required uInt32 code": 1, "optional string roomUid": 2}, "game.playerHandler.groundMatchLeaveAway": {"required uInt32 code": 1}, "game.playerHandler.groundMatchReport": {"required uInt32 code": 1}, "message GroundMatchToplistOneInfo": {"optional string name": 1, "optional string faceUrl": 2, "optional uInt32 resId": 3, "optional uInt32 assets": 4, "optional uInt32 rank": 5}, "game.playerHandler.getGroundMatchToplist": {"required uInt32 code": 1, "repeated GroundMatchToplistOneInfo toplist": 2, "optional GroundMatchToplistOneInfo myRank": 3, "optional uInt32 type": 4}, "game.playerHandler.groundMatchProtect": {"required uInt32 code": 1}, "game.playerHandler.groundMatchGetRecord": {"required uInt32 code": 1, "repeated GroundMatchRecord recordList": 2}, "message BeliefLeader": {"optional string uid": 1, "optional uInt32 pos": 2}, "game.playerHandler.getBeliefLeader": {"required uInt32 code": 1, "repeated BeliefLeader leader": 2}, "message BeliefBuyNumInfo": {"optional uInt32 itemId": 1, "optional uInt32 num": 2}, "message BeliefLevelInfo": {"optional uInt32 beliefId": 1, "optional uInt32 level": 2}, "game.playerHandler.getBeliefShopInfo": {"required uInt32 code": 1, "optional uInt32 shopPeriod": 2, "optional uInt32 isChairman": 3, "optional uInt32 alter": 4, "repeated ShopPricing Pricing": 5, "optional uInt32 startTime": 6, "optional uInt32 endTime": 7, "repeated BeliefBuyNumInfo buyNum": 8, "repeated BeliefLevelInfo beliefLevel": 9}, "message ShopPricing": {"optional int32 itemId": 1, "optional int32 num": 2, "optional int32 refreshTime": 3, "repeated beliefItemInfo itemList": 4}, "message beliefItemInfo": {"optional uInt32 selfId": 1, "optional uInt32 beliefId": 2, "optional uInt32 price": 3}, "game.playerHandler.BeliefShopPricing": {"required uInt32 code": 1}, "game.playerHandler.BeliefShopPurchase": {"required uInt32 code": 1}, "game.playerHandler.updatePeakMatchTips": {"required uInt32 period": 1}, "message ActiveRank": {"optional string playerId": 1, "optional string playerName": 2, "optional string faceUrl": 3, "optional uInt32 todayNum": 4, "optional uInt32 historyNum": 5}, "game.playerHandler.getActiveRankInfo": {"required uInt32 code": 1, "repeated ActiveRank allPlayer": 2, "optional uInt32 todayNum": 3, "optional uInt32 historyNum": 4}, "game.playerHandler.getBeliefTask": {"required uInt32 code": 1, "repeated Task taskInfo": 2}, "message RateList": {"optional double rate": 1, "optional uInt32 num": 2}, "game.playerHandler.donateGoldTask": {"required uInt32 code": 1, "optional uInt32 level": 2, "optional uInt32 beliefExp": 3, "optional uInt32 num": 4, "optional uInt32 goldNum": 5, "optional uInt32 addExp": 6, "repeated RateList rate": 7}, "message BeliefSkill": {"optional uInt32 skillId": 1, "optional uInt32 status": 2, "optional uInt32 level": 3}, "game.playerHandler.getBeliefSkillList": {"required uInt32 code": 1, "repeated BeliefSkill skillList": 2}, "game.playerHandler.upgradeBeliefSkill": {"required uInt32 code": 1, "optional uInt32 level": 2, "optional uInt32 skillId": 3}, "game.playerHandler.costCashTask": {"required uInt32 code": 1}, "message BeliefList": {"optional uInt32 beliefId": 1, "optional uInt32 level": 2, "optional uInt32 weekExp": 3, "optional uInt32 beliefExp": 4, "optional string name": 5, "optional string uid": 6}, "game.playerHandler.getBeliefList": {"required uInt32 code": 1, "repeated BeliefList beliefList": 2}, "game.playerHandler.inspirePlayer": {"required uInt32 code": 1, "optional WarOfFaithBattleTeamInfo nextBattleTeam": 2}, "message WarOfFaithReward": {"optional uInt32 beliefId": 1, "optional uInt32 level": 2, "optional uInt32 value": 3, "optional uInt32 otherBeliefId": 4, "optional uInt32 otherBeliefLevel": 5, "optional uInt32 otherBeliefValue": 6, "optional uInt32 contribution": 7, "optional uInt32 otherContribution": 8, "optional uInt32 cash": 9, "optional uInt32 otherCash": 10}, "game.playerHandler.getBeliefReward": {"required uInt32 code": 1, "optional uInt32 num": 2, "optional uInt32 getStatus": 3, "optional WarOfFaithReward rewardList": 4, "optional uInt32 beliefLivenessNum": 5, "optional uInt32 cashNum": 6}, "game.playerHandler.kingStore": {"required uInt32 code": 1}, "game.playerHandler.buyTeamFormationGift": {"required uInt32 code": 1, "optional uInt32 num": 2}, "game.playerHandler.weekDayEncore": {"required uInt32 code": 1, "optional uInt32 itemId": 2}, "game.playerHandler.oneKeyCultivateHero": {"required uInt32 code": 1, "optional Hero hero": 2}, "game.playerHandler.updateRechargeResult": {"required string result": 1, "optional uInt32 addGold": 2, "optional uInt32 beforeGold": 3, "optional uInt32 rechargeId": 4}, "game.playerHandler.updateConfigByName": {"optional string tableName": 1}, "game.playerHandler.delTeamFormationByTeamId": {"optional uInt32 code": 1}, "game.playerHandler.delTeamFormationByUid": {"optional uInt32 code": 1}, "game.playerHandler.setMatchTeamFormation": {"optional uInt32 code": 1, "optional uInt32 matchType": 2, "optional uInt32 teamId": 3}, "message MatchTeamInfo": {"optional uInt32 matchType": 1, "optional uInt32 teamId": 2}, "game.playerHandler.getMatchTeamFormation": {"optional uInt32 code": 1, "repeated MatchTeamInfo matchTeamInfo": 2}, "message leagueRanking": {"optional uInt32 typeId": 1, "optional uInt32 rank": 2}, "message seasonList": {"optional uInt32 seasonId": 1, "optional uInt32 rank": 2, "optional uInt32 pos": 3, "optional uInt32 win": 4, "optional uInt32 live": 5, "optional uInt32 attack": 6, "optional uInt32 defend": 7, "optional uInt32 typeId": 8, "optional uInt32 type": 9, "optional uInt32 time": 10}, "message honorWallData": {"optional uInt32 joinNum": 1, "optional uInt32 ranking": 2, "optional uInt32 eight": 3, "optional uInt32 three": 4, "optional uInt32 two": 5, "optional uInt32 one": 6, "repeated seasonList seasonList": 7, "optional uInt32 officeNum": 8, "optional uInt32 winNum": 9, "optional uInt32 liveNum": 10, "optional uInt32 defendNum": 11, "optional uInt32 attackNum": 12, "optional leagueRanking leagueRanking": 13, "optional uInt32 superFirstNum": 14, "optional uInt32 reelected": 15, "optional uInt32 progress": 16}, "game.playerHandler.getHonorWallInfo": {"required uInt32 code": 1, "optional string title": 2, "optional uInt32 integral": 3, "optional honorWallData peakMatchData": 4, "optional honorWallData chairmanMatchData": 5, "optional honorWallData beliefMatchData": 6, "optional honorWallData leagueData": 7, "optional honorWallData dqCupMatchData": 8, "repeated Task taskList": 9, "optional string name": 10}, "game.playerHandler.getHonorTaskReward": {"required uInt32 code": 1, "optional uInt32 resId": 2}}