#!/usr/bin/env node

/**
 * API功能测试脚本
 */

const axios = require('axios');
require('dotenv').config();

// 配置axios默认选项
axios.defaults.family = 4; // 强制使用IPv4
axios.defaults.timeout = 10000;

// 配置
const config = {
  authBaseUrl: `http://localhost:${process.env.AUTH_PORT || 3001}`,
  gatewayBaseUrl: `http://localhost:${process.env.GATEWAY_PORT || 3000}`,
  timeout: 10000
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试用例
class ApiTester {
  constructor() {
    this.testResults = [];
    this.testUser = null;
    this.authToken = null;
  }

  async runTest(name, testFn) {
    try {
      log(`🧪 ${name}...`, 'blue');
      const start = Date.now();
      const result = await testFn();
      const duration = Date.now() - start;
      
      this.testResults.push({
        name,
        status: 'passed',
        duration,
        result
      });
      
      log(`✅ ${name} - 通过 (${duration}ms)`, 'green');
      return result;
    } catch (error) {
      this.testResults.push({
        name,
        status: 'failed',
        error: error.message
      });
      
      log(`❌ ${name} - 失败: ${error.message}`, 'red');
      throw error;
    }
  }

  // 测试健康检查
  async testHealthChecks() {
    await this.runTest('认证服务健康检查', async () => {
      const response = await axios.get(`${config.authBaseUrl}/api/health/liveness`, {
        timeout: config.timeout,
        family: 4 // 强制使用IPv4
      });
      return response.data;
    });

    await this.runTest('网关服务健康检查', async () => {
      const response = await axios.get(`${config.gatewayBaseUrl}/health`, {
        timeout: config.timeout,
        family: 4 // 强制使用IPv4
      });
      return response.data;
    });
  }

  // 测试用户注册
  async testUserRegistration() {
    const userData = {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'SecureP@ssw0rd!',
      confirmPassword: 'SecureP@ssw0rd!',
      acceptTerms: true,
      profile: {
        firstName: 'Test',
        lastName: 'User'
      }
    };

    const result = await this.runTest('用户注册', async () => {
      const response = await axios.post(
        `${config.authBaseUrl}/api/auth/register`,
        userData,
        { timeout: config.timeout }
      );
      return response.data;
    });

    this.testUser = { ...userData, ...result };
    return result;
  }

  // 测试用户登录
  async testUserLogin() {
    if (!this.testUser) {
      throw new Error('需要先注册用户');
    }

    const result = await this.runTest('用户登录', async () => {
      const response = await axios.post(
        `${config.authBaseUrl}/api/auth/login`,
        {
          identifier: this.testUser.username,
          password: this.testUser.password
        },
        { timeout: config.timeout }
      );
      return response.data;
    });

    this.authToken = result.token || result.access_token;
    return result;
  }

  // 测试获取用户信息
  async testGetUserProfile() {
    if (!this.authToken) {
      throw new Error('需要先登录');
    }

    return await this.runTest('获取用户信息', async () => {
      const response = await axios.get(
        `${config.authBaseUrl}/api/users/me`,
        {
          headers: {
            Authorization: `Bearer ${this.authToken}`
          },
          timeout: config.timeout
        }
      );
      return response.data;
    });
  }

  // 测试网关路由
  async testGatewayRouting() {
    return await this.runTest('网关路由测试', async () => {
      const routes = [
        '/health',
        '/api/health'
      ];

      const results = [];

      for (const route of routes) {
        try {
          const response = await axios.get(
            `${config.gatewayBaseUrl}${route}`,
            { timeout: config.timeout }
          );
          results.push({ route, status: response.status, success: true });
        } catch (error) {
          results.push({
            route,
            status: error.response?.status || 0,
            success: false,
            error: error.message
          });
        }
      }

      return { routes: results };
    });
  }

  // 测试MFA功能
  async testMFAFeatures() {
    if (!this.authToken) {
      throw new Error('需要先登录');
    }

    return await this.runTest('MFA功能测试', async () => {
      // 设置MFA
      const setupResponse = await axios.post(
        `${config.authBaseUrl}/auth/mfa/setup`,
        {},
        {
          headers: {
            Authorization: `Bearer ${this.authToken}`
          },
          timeout: config.timeout
        }
      );

      return {
        mfaSetup: !!setupResponse.data.data.secret,
        qrCode: !!setupResponse.data.data.qrCodeUrl,
        backupCodes: setupResponse.data.data.backupCodes?.length || 0
      };
    });
  }

  // 测试权限管理
  async testPermissionManagement() {
    if (!this.authToken) {
      throw new Error('需要先登录');
    }

    return await this.runTest('权限管理测试', async () => {
      // 获取用户权限
      const permissionsResponse = await axios.get(
        `${config.authBaseUrl}/auth/permissions`,
        {
          headers: {
            Authorization: `Bearer ${this.authToken}`
          },
          timeout: config.timeout
        }
      );

      return {
        permissions: permissionsResponse.data.data?.permissions || [],
        roles: permissionsResponse.data.data?.roles || []
      };
    });
  }

  // 测试网关路由
  async testGatewayRouting() {
    return await this.runTest('网关路由测试', async () => {
      const routes = [
        '/auth/health',
        '/api/health',
        '/health'
      ];

      const results = [];

      for (const route of routes) {
        try {
          const response = await axios.get(
            `${config.gatewayBaseUrl}${route}`,
            { timeout: config.timeout }
          );
          results.push({ route, status: response.status, success: true });
        } catch (error) {
          results.push({
            route,
            status: error.response?.status || 0,
            success: false,
            error: error.message
          });
        }
      }

      return { routes: results };
    });
  }

  // 测试缓存功能
  async testCacheFeatures() {
    if (!this.authToken) {
      throw new Error('需要先登录');
    }

    return await this.runTest('缓存功能测试', async () => {
      // 第一次请求（应该从数据库获取）
      const start1 = Date.now();
      const response1 = await axios.get(
        `${config.authBaseUrl}/auth/profile`,
        {
          headers: {
            Authorization: `Bearer ${this.authToken}`
          },
          timeout: config.timeout
        }
      );
      const duration1 = Date.now() - start1;

      // 第二次请求（应该从缓存获取）
      const start2 = Date.now();
      const response2 = await axios.get(
        `${config.authBaseUrl}/auth/profile`,
        {
          headers: {
            Authorization: `Bearer ${this.authToken}`
          },
          timeout: config.timeout
        }
      );
      const duration2 = Date.now() - start2;

      return {
        firstRequest: duration1,
        secondRequest: duration2,
        cacheImprovement: duration1 > duration2,
        improvementRatio: duration1 / duration2
      };
    });
  }

  // 测试错误处理
  async testErrorHandling() {
    await this.runTest('404错误处理', async () => {
      try {
        await axios.get(`${config.authBaseUrl}/nonexistent`, {
          timeout: config.timeout
        });
        throw new Error('应该返回404错误');
      } catch (error) {
        if (error.response && error.response.status === 404) {
          return { status: '正确返回404' };
        }
        throw error;
      }
    });

    await this.runTest('无效登录处理', async () => {
      try {
        await axios.post(
          `${config.authBaseUrl}/auth/login`,
          {
            username: 'nonexistent',
            password: 'wrongpassword'
          },
          { timeout: config.timeout }
        );
        throw new Error('应该返回认证错误');
      } catch (error) {
        if (error.response && error.response.status === 401) {
          return { status: '正确返回401' };
        }
        throw error;
      }
    });
  }

  // 生成测试报告
  generateReport() {
    const passed = this.testResults.filter(t => t.status === 'passed').length;
    const failed = this.testResults.filter(t => t.status === 'failed').length;
    const total = this.testResults.length;

    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total,
        passed,
        failed,
        passRate: total > 0 ? Math.round((passed / total) * 100) : 0
      },
      tests: this.testResults
    };

    return report;
  }

  // 运行所有测试
  async runAllTests() {
    log('🚀 开始API功能测试', 'blue');
    console.log('');

    try {
      // 基础测试
      await this.testHealthChecks();
      await this.testGatewayRouting();

      // 认证流程测试
      await this.testUserRegistration();
      await this.testUserLogin();
      await this.testGetUserProfile();

      // 高级功能测试
      await this.testMFAFeatures();
      await this.testPermissionManagement();
      await this.testGatewayRouting();
      await this.testCacheFeatures();

      // 错误处理测试
      await this.testErrorHandling();

    } catch (error) {
      log(`测试中断: ${error.message}`, 'red');
    }

    console.log('');
    
    // 生成报告
    const report = this.generateReport();
    
    if (report.summary.failed === 0) {
      log(`✅ 所有测试通过 (${report.summary.passed}/${report.summary.total})`, 'green');
    } else {
      log(`❌ 测试失败 (${report.summary.failed}/${report.summary.total} 失败)`, 'red');
    }

    // 输出详细报告
    if (process.argv.includes('--verbose')) {
      console.log('\n📊 详细报告:');
      console.log(JSON.stringify(report, null, 2));
    }

    // 保存报告
    if (process.argv.includes('--save')) {
      const fs = require('fs');
      const reportPath = `api-test-report-${Date.now()}.json`;
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      log(`📄 报告已保存: ${reportPath}`, 'blue');
    }

    return report.summary.failed === 0;
  }
}

// 主函数
async function main() {
  const tester = new ApiTester();
  
  try {
    const success = await tester.runAllTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    log(`API测试失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 使用说明
function showUsage() {
  console.log(`
使用方法: node api-test.js [选项]

选项:
  --verbose  显示详细测试报告
  --save     保存测试报告到文件
  --help     显示此帮助信息

示例:
  node api-test.js
  node api-test.js --verbose --save
`);
}

if (process.argv.includes('--help')) {
  showUsage();
  process.exit(0);
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = ApiTester;
