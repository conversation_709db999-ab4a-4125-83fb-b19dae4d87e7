const io = require('socket.io-client');
const axios = require('axios');

async function debugRoomTest() {
  try {
    console.log('🔍 开始WebSocket房间管理调试测试...\n');

    // 1. 获取认证令牌
    console.log('1️⃣ 获取认证令牌...');
    const loginResponse = await axios.post('http://127.0.0.1:3001/api/auth/login', {
      identifier: 'archtest',
      password: 'SecureP@ssw0rd!'
    });
    const token = loginResponse.data.data.tokens.accessToken;

    // 获取用户信息
    const userResponse = await axios.get('http://127.0.0.1:3001/api/users/me', {
      headers: { Authorization: `Bearer ${token}` }
    });
    const userId = userResponse.data.data.id;
    
    console.log(`✅ 认证成功 - 用户ID: ${userId}`);
    console.log(`✅ 令牌获取成功: ${token.substring(0, 30)}...\n`);

    // 2. 创建WebSocket连接
    console.log('2️⃣ 创建WebSocket连接...');
    const socket = io('http://127.0.0.1:3000', {
      auth: { token },
      transports: ['websocket']
    });

    // 3. 监听连接事件
    socket.on('connect', () => {
      console.log(`✅ WebSocket连接成功 - Socket ID: ${socket.id}\n`);
      
      // 4. 测试房间加入
      console.log('3️⃣ 测试房间加入...');
      const roomId = `user:${userId}`;
      console.log(`📤 发送 join_room 事件 - 房间ID: ${roomId}`);
      
      socket.emit('join_room', { 
        room: roomId,
        debug: true  // 添加调试标志
      });
    });

    // 5. 监听房间事件
    socket.on('room_joined', (data) => {
      console.log('✅ 成功加入房间:', data);
      
      // 测试离开房间
      console.log('\n4️⃣ 测试房间离开...');
      console.log('📤 发送 leave_room 事件');
      socket.emit('leave_room', { 
        room: data.room,
        debug: true
      });
    });

    socket.on('room_left', (data) => {
      console.log('✅ 成功离开房间:', data);
      console.log('\n🎉 房间管理测试完成！');
      socket.disconnect();
    });

    // 6. 监听错误事件
    socket.on('error', (error) => {
      console.log('❌ WebSocket错误:', error);
      socket.disconnect();
    });

    socket.on('room_error', (error) => {
      console.log('❌ 房间错误:', error);
      socket.disconnect();
    });

    socket.on('disconnect', (reason) => {
      console.log(`🔌 WebSocket连接断开: ${reason}`);
      process.exit(0);
    });

    // 7. 超时处理
    setTimeout(() => {
      console.log('❌ 测试超时 - 可能的问题:');
      console.log('   - 网关服务房间管理功能异常');
      console.log('   - 会话服务无法找到Socket会话');
      console.log('   - 房间权限验证失败');
      socket.disconnect();
      process.exit(1);
    }, 15000);

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    process.exit(1);
  }
}

// 运行测试
debugRoomTest();
