#!/usr/bin/env node

/**
 * 服务健康检查脚本
 */

const axios = require('axios');
const Redis = require('ioredis');
const { MongoClient } = require('mongodb');
require('dotenv').config();

// 配置
const config = {
  authPort: process.env.AUTH_PORT || 3001,
  gatewayPort: process.env.GATEWAY_PORT || 3000,
  redisHost: process.env.REDIS_HOST || '***************',
  redisPort: process.env.REDIS_PORT || 6379,
  redisPassword: process.env.REDIS_PASSWORD || '123456',
  mongoUri: process.env.MONGODB_URI,
  timeout: 5000
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查HTTP服务
async function checkHttpService(name, url) {
  try {
    const start = Date.now();
    const response = await axios.get(url, {
      timeout: config.timeout,
      family: 4 // 强制使用IPv4
    });
    const duration = Date.now() - start;
    
    if (response.status === 200) {
      log(`✅ ${name}: 正常 (${duration}ms)`, 'green');
      return { status: 'healthy', duration, data: response.data };
    } else {
      log(`❌ ${name}: 异常 (状态码: ${response.status})`, 'red');
      return { status: 'unhealthy', error: `HTTP ${response.status}` };
    }
  } catch (error) {
    log(`❌ ${name}: 连接失败 (${error.message})`, 'red');
    return { status: 'unhealthy', error: error.message };
  }
}

// 检查Redis连接
async function checkRedis() {
  let redis = null;
  try {
    const start = Date.now();
    redis = new Redis({
      host: config.redisHost,
      port: config.redisPort,
      password: config.redisPassword,
      connectTimeout: config.timeout,
      lazyConnect: true
    });
    
    await redis.connect();
    const pong = await redis.ping();
    const duration = Date.now() - start;
    
    if (pong === 'PONG') {
      log(`✅ Redis: 正常 (${duration}ms)`, 'green');
      return { status: 'healthy', duration };
    } else {
      log(`❌ Redis: 响应异常`, 'red');
      return { status: 'unhealthy', error: 'Invalid ping response' };
    }
  } catch (error) {
    log(`❌ Redis: 连接失败 (${error.message})`, 'red');
    return { status: 'unhealthy', error: error.message };
  } finally {
    if (redis) {
      redis.disconnect();
    }
  }
}

// 检查MongoDB连接
async function checkMongoDB() {
  let client = null;
  try {
    const start = Date.now();
    client = new MongoClient(config.mongoUri, {
      serverSelectionTimeoutMS: config.timeout,
      connectTimeoutMS: config.timeout
    });
    
    await client.connect();
    await client.db().admin().ping();
    const duration = Date.now() - start;
    
    log(`✅ MongoDB: 正常 (${duration}ms)`, 'green');
    return { status: 'healthy', duration };
  } catch (error) {
    log(`❌ MongoDB: 连接失败 (${error.message})`, 'red');
    return { status: 'unhealthy', error: error.message };
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// 检查系统资源
function checkSystemResources() {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  log(`📊 系统资源:`, 'blue');
  log(`   内存使用: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB / ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`);
  log(`   CPU使用: ${Math.round(cpuUsage.user / 1000)}ms user, ${Math.round(cpuUsage.system / 1000)}ms system`);
  
  return {
    memory: memUsage,
    cpu: cpuUsage
  };
}

// 生成健康报告
function generateHealthReport(results) {
  const report = {
    timestamp: new Date().toISOString(),
    overall: 'healthy',
    services: results,
    summary: {
      total: Object.keys(results).length,
      healthy: 0,
      unhealthy: 0
    }
  };
  
  for (const [name, result] of Object.entries(results)) {
    if (result.status === 'healthy') {
      report.summary.healthy++;
    } else {
      report.summary.unhealthy++;
      report.overall = 'unhealthy';
    }
  }
  
  return report;
}

// 主函数
async function main() {
  const mode = process.argv[2] || 'all';
  
  log('🔍 开始健康检查...', 'blue');
  log(`检查模式: ${mode}`, 'blue');
  console.log('');
  
  const results = {};
  
  try {
    if (mode === 'services' || mode === 'all') {
      // 检查HTTP服务
      results.authService = await checkHttpService(
        '认证服务',
        `http://localhost:${config.authPort}/api/health/liveness`
      );
      
      results.gatewayService = await checkHttpService(
        '网关服务',
        `http://localhost:${config.gatewayPort}/health`
      );
    }
    
    if (mode === 'databases' || mode === 'all') {
      // 检查数据库
      results.redis = await checkRedis();
      results.mongodb = await checkMongoDB();
    }
    
    if (mode === 'system' || mode === 'all') {
      // 检查系统资源
      results.system = checkSystemResources();
    }
    
    console.log('');
    
    // 生成报告
    const report = generateHealthReport(results);
    
    if (report.overall === 'healthy') {
      log(`✅ 整体状态: 健康 (${report.summary.healthy}/${report.summary.total})`, 'green');
    } else {
      log(`❌ 整体状态: 异常 (${report.summary.unhealthy}/${report.summary.total} 服务异常)`, 'red');
    }
    
    // 输出详细报告
    if (process.argv.includes('--json')) {
      console.log(JSON.stringify(report, null, 2));
    }
    
    // 保存报告
    if (process.argv.includes('--save')) {
      const fs = require('fs');
      const reportPath = `health-report-${Date.now()}.json`;
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      log(`📄 报告已保存: ${reportPath}`, 'blue');
    }
    
    process.exit(report.overall === 'healthy' ? 0 : 1);
    
  } catch (error) {
    log(`❌ 健康检查失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 使用说明
function showUsage() {
  console.log(`
使用方法: node health-check.js [模式] [选项]

模式:
  all        检查所有组件 (默认)
  services   只检查HTTP服务
  databases  只检查数据库
  system     只检查系统资源

选项:
  --json     输出JSON格式报告
  --save     保存报告到文件
  --help     显示此帮助信息

示例:
  node health-check.js
  node health-check.js services
  node health-check.js --json --save
`);
}

if (process.argv.includes('--help')) {
  showUsage();
  process.exit(0);
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  checkHttpService,
  checkRedis,
  checkMongoDB,
  checkSystemResources,
  generateHealthReport
};
