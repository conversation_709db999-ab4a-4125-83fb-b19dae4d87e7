@echo off
REM 快速启动脚本 - Windows批处理文件

echo ========================================
echo 足球经理游戏服务器 - 本机测试
echo ========================================
echo.

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装或不在PATH中
    pause
    exit /b 1
)

echo [INFO] Node.js 版本:
node --version

REM 检查npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm 未安装或不在PATH中
    pause
    exit /b 1
)

echo [INFO] npm 版本:
npm --version
echo.

REM 检查依赖
if not exist "node_modules" (
    echo [INFO] 安装项目依赖...
    npm install
    if errorlevel 1 (
        echo [ERROR] 依赖安装失败
        pause
        exit /b 1
    )
)

REM 构建项目
echo [INFO] 构建项目...
npm run build
if errorlevel 1 (
    echo [ERROR] 项目构建失败
    pause
    exit /b 1
)

echo [SUCCESS] 项目构建完成
echo.

REM 健康检查
echo [INFO] 检查数据库连接...
npm run health:databases
if errorlevel 1 (
    echo [WARNING] 数据库连接检查失败，但继续启动服务
)

echo.
echo ========================================
echo 选择操作:
echo 1. 启动服务
echo 2. 运行健康检查
echo 3. 运行API测试
echo 4. 运行WebSocket测试
echo 5. 运行安全测试
echo 6. 运行综合测试
echo 7. 退出
echo ========================================
echo.

set /p choice="请选择操作 (1-7): "

if "%choice%"=="1" goto start_services
if "%choice%"=="2" goto health_check
if "%choice%"=="3" goto api_test
if "%choice%"=="4" goto websocket_test
if "%choice%"=="5" goto security_test
if "%choice%"=="6" goto comprehensive_test
if "%choice%"=="7" goto exit
goto invalid_choice

:start_services
echo [INFO] 启动服务...
echo [INFO] 认证服务将在端口 3002 启动
echo [INFO] 网关服务将在端口 3001 启动
echo.
echo 按 Ctrl+C 停止服务
npm run start:all
goto end

:health_check
echo [INFO] 运行健康检查...
npm run health
pause
goto end

:api_test
echo [INFO] 运行API测试...
npm run test:api
pause
goto end

:websocket_test
echo [INFO] 运行WebSocket测试...
npm run test:websocket
pause
goto end

:security_test
echo [INFO] 运行安全测试...
npm run test:security
pause
goto end

:comprehensive_test
echo [INFO] 运行综合测试...
npm run test:comprehensive
pause
goto end

:invalid_choice
echo [ERROR] 无效选择，请重新运行脚本
pause
goto end

:exit
echo [INFO] 退出
goto end

:end
