#!/usr/bin/env node

/**
 * 安全功能测试脚本
 */

const axios = require('axios');
const crypto = require('crypto');
require('dotenv').config();

// 配置axios默认选项
axios.defaults.family = 4; // 强制使用IPv4
axios.defaults.timeout = 10000;

// 配置
const config = {
  authBaseUrl: `http://localhost:${process.env.AUTH_PORT || 3001}`,
  gatewayBaseUrl: `http://localhost:${process.env.GATEWAY_PORT || 3000}`,
  timeout: 10000
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 安全测试类
class SecurityTester {
  constructor() {
    this.testResults = [];
    this.testUsers = [];
  }

  async runTest(name, testFn) {
    try {
      log(`🔒 ${name}...`, 'blue');
      const start = Date.now();
      const result = await testFn();
      const duration = Date.now() - start;
      
      this.testResults.push({
        name,
        status: 'passed',
        duration,
        result
      });
      
      log(`✅ ${name} - 通过 (${duration}ms)`, 'green');
      return result;
    } catch (error) {
      this.testResults.push({
        name,
        status: 'failed',
        error: error.message
      });
      
      log(`❌ ${name} - 失败: ${error.message}`, 'red');
      throw error;
    }
  }

  // 创建测试用户
  async createTestUser(overrides = {}) {
    const userData = {
      username: `sectest_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
      email: `sectest_${Date.now()}@example.com`,
      password: 'SecureP@ssw0rd!',
      confirmPassword: 'SecureP@ssw0rd!',
      acceptTerms: true,
      profile: {
        firstName: 'Security',
        lastName: 'Test'
      },
      ...overrides
    };

    try {
      const response = await axios.post(`${config.authBaseUrl}/api/auth/register`, userData);
      this.testUsers.push(userData);
      return { userData, response: response.data };
    } catch (error) {
      throw new Error(`创建测试用户失败: ${error.message}`);
    }
  }

  // 测试密码强度验证
  async testPasswordStrength() {
    return await this.runTest('密码强度验证', async () => {
      const weakPasswords = [
        '123',
        'password',
        '12345678',
        'qwerty',
        'abc123'
      ];

      const results = [];

      for (const weakPassword of weakPasswords) {
        try {
          await this.createTestUser({
            password: weakPassword,
            confirmPassword: weakPassword
          });
          results.push({ password: weakPassword, rejected: false });
        } catch (error) {
          // 检查是否是密码相关的错误
          if (error.message.includes('密码') ||
              error.message.includes('password') ||
              error.message.includes('400')) {
            results.push({ password: weakPassword, rejected: true });
          } else {
            // 如果是其他错误，继续抛出
            results.push({ password: weakPassword, rejected: true, error: error.message });
          }
        }
      }

      const rejectedCount = results.filter(r => r.rejected).length;
      log(`  ✓ 弱密码拒绝率: ${rejectedCount}/${weakPasswords.length}`, 'green');

      return { results, rejectedCount, totalTested: weakPasswords.length };
    });
  }

  // 测试暴力破解防护
  async testBruteForceProtection() {
    return await this.runTest('暴力破解防护', async () => {
      const { userData } = await this.createTestUser();
      const maxAttempts = 5;
      let lockedOut = false;

      // 尝试多次错误登录
      for (let i = 0; i < maxAttempts + 2; i++) {
        try {
          await axios.post(`${config.authBaseUrl}/api/auth/login`, {
            identifier: userData.username,
            password: 'wrongpassword'
          });
        } catch (error) {
          if (error.response && error.response.status === 423) {
            // 账户被锁定
            lockedOut = true;
            log(`  ✓ 账户在第${i + 1}次尝试后被锁定`, 'green');
            break;
          }
        }
      }

      // 验证正确密码也无法登录
      if (lockedOut) {
        try {
          await axios.post(`${config.authBaseUrl}/api/auth/login`, {
            identifier: userData.username,
            password: userData.password
          });
          throw new Error('锁定后仍能登录');
        } catch (error) {
          if (error.response && error.response.status === 423) {
            log(`  ✓ 正确密码也被拒绝`, 'green');
          }
        }
      }

      return { lockedOut, attempts: maxAttempts + 2 };
    });
  }

  // 测试JWT令牌安全
  async testJWTSecurity() {
    return await this.runTest('JWT令牌安全', async () => {
      const { userData } = await this.createTestUser();
      
      // 正常登录获取令牌
      const loginResponse = await axios.post(`${config.authBaseUrl}/api/auth/login`, {
        identifier: userData.username,
        password: userData.password
      });

      const validToken = loginResponse.data.data.tokens.accessToken;

      // 测试无效令牌
      const invalidTokens = [
        'invalid.token.here',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature',
        '',
        'Bearer invalid',
        validToken + 'tampered'
      ];

      const results = [];

      for (const invalidToken of invalidTokens) {
        try {
          await axios.get(`${config.authBaseUrl}/api/users/me`, {
            headers: { Authorization: `Bearer ${invalidToken}` }
          });
          results.push({ token: invalidToken.substring(0, 20) + '...', rejected: false });
        } catch (error) {
          if (error.response && error.response.status === 401) {
            results.push({ token: invalidToken.substring(0, 20) + '...', rejected: true });
          }
        }
      }

      const rejectedCount = results.filter(r => r.rejected).length;
      log(`  ✓ 无效令牌拒绝率: ${rejectedCount}/${invalidTokens.length}`, 'green');

      return { results, rejectedCount, totalTested: invalidTokens.length };
    });
  }

  // 测试输入验证
  async testInputValidation() {
    return await this.runTest('输入验证防护', async () => {
      const maliciousInputs = [
        // SQL注入尝试
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "admin'--",
        
        // XSS尝试
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "<img src=x onerror=alert('xss')>",
        
        // 命令注入尝试
        "; ls -la",
        "| cat /etc/passwd",
        "&& rm -rf /",
        
        // 路径遍历尝试
        "../../../etc/passwd",
        "..\\..\\..\\windows\\system32\\config\\sam",
        
        // 超长输入
        'A'.repeat(10000)
      ];

      const results = [];

      for (const maliciousInput of maliciousInputs) {
        try {
          await this.createTestUser({ 
            username: maliciousInput,
            email: `test${Date.now()}@example.com`
          });
          results.push({ input: maliciousInput.substring(0, 50) + '...', rejected: false });
        } catch (error) {
          results.push({ input: maliciousInput.substring(0, 50) + '...', rejected: true });
        }
      }

      const rejectedCount = results.filter(r => r.rejected).length;
      log(`  ✓ 恶意输入拒绝率: ${rejectedCount}/${maliciousInputs.length}`, 'green');

      return { results, rejectedCount, totalTested: maliciousInputs.length };
    });
  }

  // 测试会话安全
  async testSessionSecurity() {
    return await this.runTest('会话安全管理', async () => {
      const { userData } = await this.createTestUser();
      
      // 登录获取令牌
      const loginResponse = await axios.post(`${config.authBaseUrl}/api/auth/login`, {
        identifier: userData.username,
        password: userData.password
      });

      const tokens = loginResponse.data.data.tokens;

      // 验证令牌有效性
      const profileResponse = await axios.get(`${config.authBaseUrl}/api/users/me`, {
        headers: { Authorization: `Bearer ${tokens.accessToken}` }
      });

      log(`  ✓ 令牌验证成功`, 'green');

      // 测试登出
      await axios.post(`${config.authBaseUrl}/api/auth/logout`, {}, {
        headers: { Authorization: `Bearer ${tokens.accessToken}` }
      });

      // 验证登出后令牌失效
      let logoutTokenInvalid = false;
      try {
        await axios.get(`${config.authBaseUrl}/api/users/me`, {
          headers: { Authorization: `Bearer ${tokens.accessToken}` }
        });
      } catch (error) {
        if (error.response && error.response.status === 401) {
          logoutTokenInvalid = true;
          log(`  ✓ 登出后令牌失效`, 'green');
        }
      }

      return {
        tokenValid: !!profileResponse.data,
        logoutTokenInvalid
      };
    });
  }

  // 测试权限控制
  async testAccessControl() {
    return await this.runTest('权限访问控制', async () => {
      // 创建普通用户
      const { userData: normalUser } = await this.createTestUser();
      
      // 登录普通用户
      const loginResponse = await axios.post(`${config.authBaseUrl}/auth/login`, {
        identifier: normalUser.username,
        password: normalUser.password
      });

      const userToken = loginResponse.data.data.tokens.accessToken;

      // 尝试访问管理员接口
      const adminEndpoints = [
        '/api/admin/users',
        '/api/admin/roles',
        '/api/admin/permissions',
        '/api/admin/system'
      ];

      const results = [];

      for (const endpoint of adminEndpoints) {
        try {
          await axios.get(`${config.gatewayBaseUrl}${endpoint}`, {
            headers: { Authorization: `Bearer ${userToken}` }
          });
          results.push({ endpoint, blocked: false });
        } catch (error) {
          if (error.response && error.response.status === 403) {
            results.push({ endpoint, blocked: true });
            log(`  ✓ 管理员接口 ${endpoint} 被正确拒绝`, 'green');
          } else {
            results.push({ endpoint, blocked: false, error: error.message });
          }
        }
      }

      const blockedCount = results.filter(r => r.blocked).length;

      return { results, blockedCount, totalTested: adminEndpoints.length };
    });
  }

  // 测试限流安全
  async testRateLimitSecurity() {
    return await this.runTest('限流安全防护', async () => {
      const { userData } = await this.createTestUser();
      const requests = [];
      let rateLimitTriggered = false;

      // 快速发送大量请求
      for (let i = 0; i < 100; i++) {
        requests.push(
          axios.post(`${config.authBaseUrl}/auth/login`, {
            identifier: userData.username,
            password: 'wrongpassword'
          }).catch(error => {
            if (error.response && error.response.status === 429) {
              rateLimitTriggered = true;
            }
            return error.response;
          })
        );
      }

      await Promise.all(requests);

      if (rateLimitTriggered) {
        log(`  ✓ 限流机制触发`, 'green');
      } else {
        log(`  ⚠ 限流机制未触发，可能需要调整配置`, 'yellow');
      }

      return { rateLimitTriggered, requestCount: 100 };
    });
  }

  // 生成测试报告
  generateReport() {
    const passed = this.testResults.filter(t => t.status === 'passed').length;
    const failed = this.testResults.filter(t => t.status === 'failed').length;
    const total = this.testResults.length;

    return {
      timestamp: new Date().toISOString(),
      summary: {
        total,
        passed,
        failed,
        passRate: total > 0 ? Math.round((passed / total) * 100) : 0
      },
      tests: this.testResults
    };
  }

  // 运行所有安全测试
  async runAllTests() {
    log('🔐 开始安全功能测试', 'blue');
    console.log('');

    try {
      // 密码安全测试
      await this.testPasswordStrength();
      
      // 暴力破解防护测试
      await this.testBruteForceProtection();
      
      // JWT令牌安全测试
      await this.testJWTSecurity();
      
      // 输入验证测试
      await this.testInputValidation();
      
      // 会话安全测试
      await this.testSessionSecurity();
      
      // 权限控制测试
      await this.testAccessControl();
      
      // 限流安全测试
      await this.testRateLimitSecurity();

    } catch (error) {
      log(`测试中断: ${error.message}`, 'red');
    }

    console.log('');
    
    // 生成报告
    const report = this.generateReport();
    
    if (report.summary.failed === 0) {
      log(`✅ 所有安全测试通过 (${report.summary.passed}/${report.summary.total})`, 'green');
    } else {
      log(`❌ 安全测试失败 (${report.summary.failed}/${report.summary.total} 失败)`, 'red');
    }

    // 输出详细报告
    if (process.argv.includes('--verbose')) {
      console.log('\n📊 详细报告:');
      console.log(JSON.stringify(report, null, 2));
    }

    // 保存报告
    if (process.argv.includes('--save')) {
      const fs = require('fs');
      const reportPath = `security-test-report-${Date.now()}.json`;
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      log(`📄 报告已保存: ${reportPath}`, 'blue');
    }

    return report.summary.failed === 0;
  }
}

// 主函数
async function main() {
  const tester = new SecurityTester();
  
  try {
    const success = await tester.runAllTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    log(`安全测试失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 使用说明
function showUsage() {
  console.log(`
使用方法: node security-test.js [选项]

选项:
  --verbose  显示详细测试报告
  --save     保存测试报告到文件
  --help     显示此帮助信息

示例:
  node security-test.js
  node security-test.js --verbose --save
`);
}

if (process.argv.includes('--help')) {
  showUsage();
  process.exit(0);
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = SecurityTester;
