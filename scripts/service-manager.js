#!/usr/bin/env node

/**
 * 服务管理脚本
 * 负责启动、停止和重启服务，确保端口不冲突
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class ServiceManager {
  constructor() {
    this.services = {
      auth: {
        name: 'auth',
        port: 3001,
        command: 'npm run start:auth',
        healthPath: '/health'
      },
      gateway: {
        name: 'gateway',
        port: 3000,
        command: 'npm run start:gateway',
        healthPath: '/health'
      }
    };
    
    this.runningProcesses = new Map();
    this.pidFile = path.join(__dirname, '../.service-pids.json');
  }

  // 日志输出
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '🔧',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      start: '🚀',
      stop: '🛑'
    }[type] || '🔧';
    
    console.log(`[${timestamp}] ${prefix} ${message}`);
  }

  // 检查端口是否被占用
  async checkPort(port) {
    return new Promise((resolve) => {
      const command = process.platform === 'win32' 
        ? `netstat -ano | findstr :${port}`
        : `lsof -ti:${port}`;
        
      exec(command, (error, stdout) => {
        if (error || !stdout.trim()) {
          resolve(null); // 端口未被占用
        } else {
          // 解析PID
          if (process.platform === 'win32') {
            const lines = stdout.trim().split('\n');
            const listening = lines.find(line => line.includes('LISTENING'));
            if (listening) {
              const pid = listening.trim().split(/\s+/).pop();
              resolve(parseInt(pid));
            } else {
              resolve(null);
            }
          } else {
            const pid = parseInt(stdout.trim().split('\n')[0]);
            resolve(pid);
          }
        }
      });
    });
  }

  // 杀死进程
  async killProcess(pid) {
    return new Promise((resolve) => {
      const command = process.platform === 'win32'
        ? `taskkill /PID ${pid} /F`
        : `kill -9 ${pid}`;
        
      exec(command, (error) => {
        if (error) {
          this.log(`杀死进程 ${pid} 失败: ${error.message}`, 'warning');
        } else {
          this.log(`成功杀死进程 ${pid}`, 'success');
        }
        resolve(!error);
      });
    });
  }

  // 清理端口
  async cleanupPort(port) {
    this.log(`检查端口 ${port}...`);
    
    const pid = await this.checkPort(port);
    if (pid) {
      this.log(`端口 ${port} 被进程 ${pid} 占用，正在清理...`, 'warning');
      await this.killProcess(pid);
      
      // 等待端口释放
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 再次检查
      const stillOccupied = await this.checkPort(port);
      if (stillOccupied) {
        this.log(`端口 ${port} 仍被占用`, 'error');
        return false;
      }
    }
    
    this.log(`端口 ${port} 已清理`, 'success');
    return true;
  }

  // 保存PID信息
  savePids() {
    const pids = {};
    this.runningProcesses.forEach((process, serviceName) => {
      pids[serviceName] = {
        pid: process.pid,
        port: this.services[serviceName].port,
        startTime: process.startTime
      };
    });
    
    try {
      fs.writeFileSync(this.pidFile, JSON.stringify(pids, null, 2));
    } catch (error) {
      this.log(`保存PID文件失败: ${error.message}`, 'error');
    }
  }

  // 加载PID信息
  loadPids() {
    try {
      if (fs.existsSync(this.pidFile)) {
        const pids = JSON.parse(fs.readFileSync(this.pidFile, 'utf8'));
        return pids;
      }
    } catch (error) {
      this.log(`加载PID文件失败: ${error.message}`, 'warning');
    }
    return {};
  }

  // 清理旧进程
  async cleanupOldProcesses() {
    this.log('清理旧进程...');
    
    const oldPids = this.loadPids();
    
    for (const [serviceName, info] of Object.entries(oldPids)) {
      if (info.pid) {
        this.log(`清理旧的 ${serviceName} 进程 (PID: ${info.pid})`);
        await this.killProcess(info.pid);
      }
      
      if (info.port) {
        await this.cleanupPort(info.port);
      }
    }
    
    // 清理PID文件
    if (fs.existsSync(this.pidFile)) {
      fs.unlinkSync(this.pidFile);
    }
  }

  // 启动单个服务
  async startService(serviceName) {
    const service = this.services[serviceName];
    if (!service) {
      this.log(`未知服务: ${serviceName}`, 'error');
      return false;
    }

    this.log(`启动 ${service.name}...`, 'start');

    // 清理端口
    const portClean = await this.cleanupPort(service.port);
    if (!portClean) {
      this.log(`无法清理端口 ${service.port}，启动失败`, 'error');
      return false;
    }

    return new Promise((resolve) => {
      const [command, ...args] = service.command.split(' ');
      
      const childProcess = spawn(command, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
        detached: false
      });

      childProcess.startTime = Date.now();
      
      let started = false;
      let output = '';

      // 监听输出
      childProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        
        // 检查启动成功的标志
        if (!started && (
          text.includes('successfully started') ||
          text.includes('服务已启动') ||
          text.includes(`端口: ${service.port}`) ||
          text.includes(`listening on`) ||
          text.includes(`server started`)
        )) {
          started = true;
          this.runningProcesses.set(serviceName, childProcess);
          this.savePids();
          this.log(`${service.name} 启动成功 (PID: ${childProcess.pid})`, 'success');
          resolve(true);
        }
      });

      childProcess.stderr.on('data', (data) => {
        const text = data.toString();
        output += text;
        
        // 检查端口冲突错误
        if (text.includes('EADDRINUSE') || text.includes('address already in use')) {
          this.log(`${service.name} 启动失败: 端口 ${service.port} 被占用`, 'error');
          if (!started) {
            started = true;
            resolve(false);
          }
        }
      });

      // 进程退出
      childProcess.on('close', (code) => {
        this.runningProcesses.delete(serviceName);
        this.savePids();
        
        if (!started) {
          this.log(`${service.name} 启动失败 (退出码: ${code})`, 'error');
          resolve(false);
        } else if (code !== 0) {
          this.log(`${service.name} 异常退出 (退出码: ${code})`, 'warning');
        }
      });

      childProcess.on('error', (error) => {
        this.log(`${service.name} 进程错误: ${error.message}`, 'error');
        if (!started) {
          started = true;
          resolve(false);
        }
      });

      // 超时检查
      setTimeout(() => {
        if (!started) {
          this.log(`${service.name} 启动超时`, 'error');
          childProcess.kill();
          resolve(false);
        }
      }, 60000); // 60秒超时
    });
  }

  // 停止单个服务
  async stopService(serviceName) {
    const process = this.runningProcesses.get(serviceName);
    const service = this.services[serviceName];
    
    if (process) {
      this.log(`停止 ${service.name}...`, 'stop');
      
      // 优雅关闭
      process.kill('SIGTERM');
      
      // 等待进程结束
      await new Promise(resolve => {
        const timeout = setTimeout(() => {
          // 强制杀死
          process.kill('SIGKILL');
          resolve();
        }, 5000);
        
        process.on('close', () => {
          clearTimeout(timeout);
          resolve();
        });
      });
      
      this.runningProcesses.delete(serviceName);
      this.savePids();
      this.log(`${service.name} 已停止`, 'success');
    }
    
    // 确保端口被清理
    await this.cleanupPort(service.port);
  }

  // 检查服务健康状态
  async checkHealth(serviceName) {
    const service = this.services[serviceName];
    const axios = require('axios');
    
    try {
      const response = await axios.get(
        `http://localhost:${service.port}${service.healthPath}`,
        { timeout: 5000 }
      );
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  // 启动所有服务
  async startAll() {
    this.log('启动所有服务...');
    
    // 先清理旧进程
    await this.cleanupOldProcesses();
    
    const results = {};
    
    // 按顺序启动服务（认证服务先启动）
    for (const serviceName of ['auth', 'gateway']) {
      results[serviceName] = await this.startService(serviceName);
      
      if (results[serviceName]) {
        // 等待服务完全启动
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 健康检查
        const healthy = await this.checkHealth(serviceName);
        if (healthy) {
          this.log(`${this.services[serviceName].name} 健康检查通过`, 'success');
        } else {
          this.log(`${this.services[serviceName].name} 健康检查失败`, 'warning');
        }
      }
    }
    
    return results;
  }

  // 停止所有服务
  async stopAll() {
    this.log('停止所有服务...');
    
    // 按相反顺序停止服务
    for (const serviceName of ['gateway', 'auth']) {
      await this.stopService(serviceName);
    }
    
    // 清理PID文件
    if (fs.existsSync(this.pidFile)) {
      fs.unlinkSync(this.pidFile);
    }
  }

  // 重启所有服务
  async restartAll() {
    this.log('重启所有服务...');
    await this.stopAll();
    await new Promise(resolve => setTimeout(resolve, 2000));
    return await this.startAll();
  }

  // 获取服务状态
  getStatus() {
    const status = {};
    
    for (const [serviceName, service] of Object.entries(this.services)) {
      const process = this.runningProcesses.get(serviceName);
      status[serviceName] = {
        name: service.name,
        port: service.port,
        running: !!process,
        pid: process ? process.pid : null,
        uptime: process ? Date.now() - process.startTime : 0
      };
    }
    
    return status;
  }

  // 打印状态
  printStatus() {
    const status = this.getStatus();
    
    console.log('\n' + '='.repeat(60));
    console.log('🔧 服务状态');
    console.log('='.repeat(60));
    
    for (const [serviceName, info] of Object.entries(status)) {
      const statusIcon = info.running ? '✅' : '❌';
      const uptime = info.running ? `${Math.floor(info.uptime / 1000)}s` : 'N/A';
      
      console.log(`${statusIcon} ${info.name}`);
      console.log(`   端口: ${info.port}`);
      console.log(`   PID: ${info.pid || 'N/A'}`);
      console.log(`   运行时间: ${uptime}`);
      console.log('');
    }
    
    console.log('='.repeat(60));
  }
}

// 命令行接口
async function main() {
  const manager = new ServiceManager();
  const command = process.argv[2];
  const serviceName = process.argv[3];

  try {
    switch (command) {
      case 'start':
        if (serviceName) {
          await manager.startService(serviceName);
        } else {
          await manager.startAll();
        }
        break;
        
      case 'stop':
        if (serviceName) {
          await manager.stopService(serviceName);
        } else {
          await manager.stopAll();
        }
        break;
        
      case 'restart':
        if (serviceName) {
          await manager.stopService(serviceName);
          await new Promise(resolve => setTimeout(resolve, 2000));
          await manager.startService(serviceName);
        } else {
          await manager.restartAll();
        }
        break;
        
      case 'status':
        manager.printStatus();
        break;
        
      case 'cleanup':
        await manager.cleanupOldProcesses();
        break;
        
      default:
        console.log('用法:');
        console.log('  node service-manager.js start [service]    # 启动服务');
        console.log('  node service-manager.js stop [service]     # 停止服务');
        console.log('  node service-manager.js restart [service]  # 重启服务');
        console.log('  node service-manager.js status             # 查看状态');
        console.log('  node service-manager.js cleanup            # 清理旧进程');
        console.log('');
        console.log('可用服务: auth, gateway');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = ServiceManager;
