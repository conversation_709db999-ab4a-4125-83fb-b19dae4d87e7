@echo off
chcp 65001 >nul

echo 🚀 开始设置足球经理游戏微服务数据库...

REM MongoDB服务器配置
set MONGO_HOST=***************
set MONGO_PORT=27017

echo 📡 连接到MongoDB服务器: %MONGO_HOST%:%MONGO_PORT%

REM 执行用户创建脚本
echo 👤 创建微服务数据库用户...
mongosh --host %MONGO_HOST% --port %MONGO_PORT% --file scripts/create-microservice-users.js

if %errorlevel% equ 0 (
    echo ✅ 微服务数据库用户创建成功！
) else (
    echo ❌ 微服务数据库用户创建失败！
    pause
    exit /b 1
)

echo.
echo 🎉 微服务数据库设置完成！
echo.
echo 📋 创建的数据库和用户：
echo   - auth_db (auth-admin)
echo   - character_db (character-admin)
echo   - hero_db (hero-admin)
echo   - economy_db (economy-admin)
echo   - social_db (social-admin)
echo   - activity_db (activity-admin)
echo   - old_game_db (old-game-admin)
echo.
echo 🔧 现在可以启动微服务了！
echo.
pause
