#!/bin/bash

# 足球经理游戏微服务数据库设置脚本

echo "🚀 开始设置足球经理游戏微服务数据库..."

# MongoDB服务器配置
MONGO_HOST="***************"
MONGO_PORT="27017"

echo "📡 连接到MongoDB服务器: $MONGO_HOST:$MONGO_PORT"

# 执行用户创建脚本
echo "👤 创建微服务数据库用户..."
mongosh --host $MONGO_HOST --port $MONGO_PORT --file scripts/create-microservice-users.js

if [ $? -eq 0 ]; then
    echo "✅ 微服务数据库用户创建成功！"
else
    echo "❌ 微服务数据库用户创建失败！"
    exit 1
fi

echo ""
echo "🎉 微服务数据库设置完成！"
echo ""
echo "📋 创建的数据库和用户："
echo "  - auth_db (auth-admin)"
echo "  - character_db (character-admin)"
echo "  - hero_db (hero-admin)"
echo "  - economy_db (economy-admin)"
echo "  - social_db (social-admin)"
echo "  - activity_db (activity-admin)"
echo "  - old_game_db (old-game-admin)"
echo ""
echo "🔧 现在可以启动微服务了！"
