/**
 * 基础认证测试脚本
 *
 * 测试目标：
 * 1. 新路由系统验证 (系统路由 vs 微服务路由)
 * 2. 网关HTTP代理功能 (认证API)
 * 3. 路径重写功能验证
 * 4. 认证服务基本功能
 * 5. JWT令牌管理
 * 6. 错误处理机制
 */

const axios = require('axios');
const chalk = require('chalk');

// 配置
const CONFIG = {
  GATEWAY_URL: 'http://127.0.0.1:3000',
  AUTH_URL: 'http://127.0.0.1:3001',
  TEST_USER: {
    username: 'testuser_' + Date.now(),
    email: 'test_' + Date.now() + '@example.com',
    password: 'SecureP@ssw0rd!',
    confirmPassword: 'SecureP@ssw0rd!',
    acceptTerms: true,
    profile: {
      firstName: 'Test',
      lastName: 'User'
    }
  }
};

class BasicAuthTester {
  constructor() {
    this.testResults = [];
    this.authToken = null;
  }

  /**
   * 运行基础测试
   */
  async runBasicTests() {
    console.log(chalk.blue('🚀 开始基础认证测试 (新路由系统)\n'));

    try {
      // 1. 测试新路由系统
      await this.testNewRoutingSystem();

      // 2. 测试服务健康状态
      await this.testServiceHealth();

      // 3. 测试路径重写功能
      await this.testPathRewriting();

      // 4. 测试用户注册
      await this.testUserRegistration();

      // 5. 测试用户登录
      await this.testUserLogin();

      // 6. 测试令牌验证
      await this.testTokenValidation();

      // 7. 测试受保护的端点
      await this.testProtectedEndpoint();

      // 8. 测试错误处理
      await this.testErrorHandling();

      // 输出结果
      this.printResults();

    } catch (error) {
      console.error(chalk.red('❌ 测试执行失败:'), error.message);
    }
  }

  /**
   * 测试新路由系统
   */
  async testNewRoutingSystem() {
    console.log(chalk.yellow('🛣️ 测试新路由系统...'));

    // 测试系统路由 - 网关健康检查
    try {
      const response = await axios.get(`${CONFIG.GATEWAY_URL}/health`, {
        timeout: 5000
      });
      this.addResult('系统路由 - /health', response.status === 200, '网关健康检查');
    } catch (error) {
      this.addResult('系统路由 - /health', false, error.message);
    }

    // 测试系统路由 - Swagger 文档 (新路径)
    try {
      const response = await axios.get(`${CONFIG.GATEWAY_URL}/docs`, {
        timeout: 5000
      });
      this.addResult('系统路由 - /docs', response.status === 200, 'Swagger 文档访问');
    } catch (error) {
      this.addResult('系统路由 - /docs', false, error.message);
    }

    // 测试微服务路由 - 认证服务健康检查
    try {
      const response = await axios.get(`${CONFIG.GATEWAY_URL}/api/auth/health`, {
        timeout: 5000
      });
      this.addResult('微服务路由 - /api/auth/health', response.status === 200, '认证服务健康检查');
    } catch (error) {
      this.addResult('微服务路由 - /api/auth/health', false, error.message);
    }

    // 测试未知服务错误处理
    try {
      await axios.get(`${CONFIG.GATEWAY_URL}/api/unknown/test`, {
        timeout: 5000
      });
      this.addResult('错误处理 - 未知服务', false, '应该返回404但成功了');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        this.addResult('错误处理 - 未知服务', true, '正确返回404 Not Found');
      } else {
        this.addResult('错误处理 - 未知服务', false, error.message);
      }
    }
  }

  /**
   * 测试服务健康状态
   */
  async testServiceHealth() {
    console.log(chalk.yellow('🏥 测试服务健康状态...'));

    // 测试网关系统路由
    try {
      const gatewayResponse = await axios.get(`${CONFIG.GATEWAY_URL}/health`, {
        timeout: 5000
      });
      this.addResult('网关健康检查 (系统路由)', gatewayResponse.status === 200, gatewayResponse.data);
    } catch (error) {
      this.addResult('网关健康检查 (系统路由)', false, `连接失败: ${error.message}`);
    }

    // 测试认证服务直接访问 (无前缀)
    try {
      const authResponse = await axios.get(`${CONFIG.AUTH_URL}/health`, {
        timeout: 5000
      });
      this.addResult('认证服务健康检查 (直接访问)', authResponse.status === 200, authResponse.data);
    } catch (error) {
      this.addResult('认证服务健康检查 (直接访问)', false, `连接失败: ${error.message}`);
    }

    // 测试通过网关代理访问认证服务
    try {
      const proxyResponse = await axios.get(`${CONFIG.GATEWAY_URL}/api/auth/health`, {
        timeout: 5000
      });
      this.addResult('认证服务健康检查 (网关代理)', proxyResponse.status === 200, proxyResponse.data);
    } catch (error) {
      this.addResult('认证服务健康检查 (网关代理)', false, `连接失败: ${error.message}`);
    }
  }

  /**
   * 测试路径重写功能
   */
  async testPathRewriting() {
    console.log(chalk.yellow('🔄 测试路径重写功能...'));

    try {
      // 请求 /api/auth/health 应该被重写为 /health 并代理到认证服务
      const response = await axios.get(`${CONFIG.GATEWAY_URL}/api/auth/health`, {
        timeout: 5000
      });

      // 检查响应是否来自认证服务
      const isFromAuthService = response.data &&
        (response.data.service === 'auth' ||
         response.data.info ||
         response.data.status === 'ok');

      this.addResult('路径重写 - /api/auth/health -> /health',
        response.status === 200 && isFromAuthService,
        {
          status: response.status,
          isFromAuthService,
          responseData: response.data
        });
    } catch (error) {
      this.addResult('路径重写功能', false, error.message);
    }
  }

  /**
   * 测试用户注册
   */
  async testUserRegistration() {
    console.log(chalk.yellow('👤 测试用户注册...'));

    try {
      const response = await axios.post(`${CONFIG.GATEWAY_URL}/api/auth/auth/register`, CONFIG.TEST_USER, {
        timeout: 10000
      });

      const success = response.status === 201;
      this.addResult('用户注册', success, response.data);

    } catch (error) {
      if (error.response) {
        this.addResult('用户注册', false, `HTTP ${error.response.status}: ${error.response.data?.message || error.response.statusText}`);
      } else {
        this.addResult('用户注册', false, error.message);
      }
    }
  }

  /**
   * 测试用户登录
   */
  async testUserLogin() {
    console.log(chalk.yellow('🔐 测试用户登录...'));

    try {
      const response = await axios.post(`${CONFIG.GATEWAY_URL}/api/auth/auth/login`, {
        identifier: CONFIG.TEST_USER.username,  // 使用 identifier 而不是 username
        password: CONFIG.TEST_USER.password
      }, {
        timeout: 10000
      });

      // 正确处理嵌套的响应数据结构
      const accessToken = response.data.data?.tokens?.accessToken;
      const success = response.status === 200 && !!accessToken;

      if (success) {
        this.authToken = accessToken;
      }

      this.addResult('用户登录', success, {
        status: response.status,
        hasToken: !!accessToken,
        tokenLength: accessToken?.length || 0,
        responseStructure: response.data.data ? '有数据' : '无数据'
      });

    } catch (error) {
      if (error.response) {
        this.addResult('用户登录', false, `HTTP ${error.response.status}: ${error.response.data?.message || error.response.statusText}`);
      } else {
        this.addResult('用户登录', false, error.message);
      }
    }
  }

  /**
   * 测试令牌验证
   */
  async testTokenValidation() {
    console.log(chalk.yellow('🔍 测试令牌验证...'));

    if (!this.authToken) {
      this.addResult('令牌验证', false, '没有可用的认证令牌');
      return;
    }

    try {
      // 使用正确的路由规范：/api/auth/users/me 代理到认证服务的 /users/me
      const response = await axios.get(`${CONFIG.GATEWAY_URL}/api/auth/users/me`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        },
        timeout: 10000
      });

      const success = response.status === 200;
      this.addResult('令牌验证 - /api/auth/users/me', success, response.data);

    } catch (error) {
      if (error.response) {
        this.addResult('令牌验证', false, `HTTP ${error.response.status}: ${error.response.data?.message || error.response.statusText}`);
      } else {
        this.addResult('令牌验证', false, error.message);
      }
    }
  }

  /**
   * 测试受保护的端点
   */
  async testProtectedEndpoint() {
    console.log(chalk.yellow('🛡️ 测试受保护的端点...'));

    // 测试无令牌访问
    try {
      await axios.get(`${CONFIG.GATEWAY_URL}/api/auth/users/me`, {
        timeout: 5000
      });
      this.addResult('无令牌访问保护端点', false, '应该被拒绝但成功了');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        this.addResult('无令牌访问保护端点', true, '正确拒绝了未授权访问');
      } else {
        this.addResult('无令牌访问保护端点', false, error.message);
      }
    }

    // 测试无效令牌访问
    try {
      await axios.get(`${CONFIG.GATEWAY_URL}/api/auth/users/me`, {
        headers: {
          'Authorization': 'Bearer invalid-token'
        },
        timeout: 5000
      });
      this.addResult('无效令牌访问保护端点', false, '应该被拒绝但成功了');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        this.addResult('无效令牌访问保护端点', true, '正确拒绝了无效令牌');
      } else {
        this.addResult('无效令牌访问保护端点', false, error.message);
      }
    }
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    console.log(chalk.yellow('⚠️ 测试错误处理...'));

    // 测试服务不可用 (user服务未启动)
    try {
      await axios.get(`${CONFIG.GATEWAY_URL}/api/user/health`, {
        timeout: 5000
      });
      this.addResult('服务不可用处理', false, '应该返回502但成功了');
    } catch (error) {
      if (error.response && error.response.status === 502) {
        this.addResult('服务不可用处理', true, '正确返回502 Bad Gateway');
      } else {
        this.addResult('服务不可用处理', false, `期望502，实际: ${error.response?.status || error.message}`);
      }
    }

    // 测试旧的API文档路径 (应该404)
    try {
      await axios.get(`${CONFIG.GATEWAY_URL}/api/docs`, {
        timeout: 5000
      });
      this.addResult('旧API文档路径处理', false, '旧路径/api/docs应该404但成功了');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        this.addResult('旧API文档路径处理', true, '正确拒绝旧路径/api/docs');
      } else {
        this.addResult('旧API文档路径处理', false, error.message);
      }
    }
  }

  /**
   * 添加测试结果
   */
  addResult(testName, success, details) {
    this.testResults.push({
      name: testName,
      success,
      details,
      timestamp: new Date()
    });

    const status = success ? chalk.green('✅') : chalk.red('❌');
    console.log(`  ${status} ${testName}`);
    
    if (details && typeof details === 'object') {
      console.log(`     ${JSON.stringify(details, null, 2)}`);
    } else if (details) {
      console.log(`     ${details}`);
    }
  }

  /**
   * 打印测试结果
   */
  printResults() {
    console.log('\n' + chalk.blue('📊 测试结果汇总'));
    console.log('='.repeat(50));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${chalk.green(passedTests)}`);
    console.log(`失败: ${chalk.red(failedTests)}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n' + chalk.red('失败的测试:'));
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  ❌ ${r.name}: ${typeof r.details === 'string' ? r.details : JSON.stringify(r.details)}`);
        });
    }

    console.log('\n' + chalk.blue('测试完成!'));
  }
}

// 运行测试
if (require.main === module) {
  const tester = new BasicAuthTester();
  tester.runBasicTests().catch(console.error);
}

module.exports = BasicAuthTester;
