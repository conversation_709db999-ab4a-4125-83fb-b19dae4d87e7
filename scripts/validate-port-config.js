/**
 * 端口配置验证脚本
 *
 * 验证目标：
 * 1. .env 文件中的端口配置完整性
 * 2. 端口冲突检测
 * 3. Redis连接配置验证
 * 4. 微服务通信配置验证
 * 5. 网关代理配置验证
 */

require('dotenv').config();
const chalk = require('chalk');

// 微服务名称和对应的环境变量
const MICROSERVICE_PORT_MAP = {
  'gateway': 'GATEWAY_PORT',
  'auth': 'AUTH_PORT',
  'user': 'USER_PORT',
  'game': 'GAME_PORT',
  'club': 'CLUB_PORT',
  'match': 'MATCH_PORT',
  'card': 'CARD_PORT',
  'player': 'PLAYER_PORT',
  'notification': 'NOTIFICATION_PORT',
};

class PortConfigValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.ports = {};
  }

  /**
   * 运行完整的端口配置验证
   */
  validate() {
    console.log(chalk.blue('🔌 验证微服务端口配置\n'));

    // 1. 检查环境变量是否存在
    this.checkEnvironmentVariables();

    // 2. 验证端口号有效性
    this.validatePortNumbers();

    // 3. 检查端口冲突
    this.checkPortConflicts();

    // 4. 检查端口范围
    this.checkPortRanges();

    // 5. 输出结果
    this.printResults();

    return {
      valid: this.errors.length === 0,
      errors: this.errors,
      warnings: this.warnings,
      ports: this.ports
    };
  }

  /**
   * 检查环境变量是否存在
   */
  checkEnvironmentVariables() {
    console.log(chalk.yellow('📋 检查环境变量...'));

    Object.entries(MICROSERVICE_PORT_MAP).forEach(([serviceName, envVar]) => {
      const value = process.env[envVar];
      
      if (!value) {
        this.errors.push(`缺少环境变量: ${envVar} (${serviceName} 服务)`);
      } else {
        console.log(`  ✅ ${envVar}: ${value}`);
        this.ports[serviceName] = value;
      }
    });
  }

  /**
   * 验证端口号有效性
   */
  validatePortNumbers() {
    console.log(chalk.yellow('\n🔢 验证端口号有效性...'));

    Object.entries(this.ports).forEach(([serviceName, portStr]) => {
      const port = parseInt(portStr, 10);
      
      if (isNaN(port)) {
        this.errors.push(`无效的端口号: ${serviceName} = "${portStr}" (不是数字)`);
      } else if (port <= 0 || port > 65535) {
        this.errors.push(`端口号超出范围: ${serviceName} = ${port} (必须在 1-65535 之间)`);
      } else {
        console.log(`  ✅ ${serviceName}: ${port}`);
        this.ports[serviceName] = port; // 转换为数字
      }
    });
  }

  /**
   * 检查端口冲突
   */
  checkPortConflicts() {
    console.log(chalk.yellow('\n⚠️ 检查端口冲突...'));

    const portUsage = {};
    const conflicts = [];

    Object.entries(this.ports).forEach(([serviceName, port]) => {
      if (typeof port === 'number') {
        if (portUsage[port]) {
          conflicts.push({
            port,
            services: [portUsage[port], serviceName]
          });
        } else {
          portUsage[port] = serviceName;
        }
      }
    });

    if (conflicts.length > 0) {
      conflicts.forEach(conflict => {
        this.errors.push(`端口冲突: 端口 ${conflict.port} 被多个服务使用: ${conflict.services.join(', ')}`);
      });
    } else {
      console.log('  ✅ 没有端口冲突');
    }
  }

  /**
   * 检查端口范围
   */
  checkPortRanges() {
    console.log(chalk.yellow('\n📊 检查端口范围...'));

    const portNumbers = Object.values(this.ports).filter(p => typeof p === 'number');
    
    if (portNumbers.length === 0) {
      return;
    }

    const minPort = Math.min(...portNumbers);
    const maxPort = Math.max(...portNumbers);

    console.log(`  端口范围: ${minPort} - ${maxPort}`);

    // 检查是否使用了系统保留端口
    portNumbers.forEach(port => {
      if (port < 1024) {
        this.warnings.push(`使用了系统保留端口: ${port} (小于 1024)`);
      }
    });

    // 检查端口是否连续
    const sortedPorts = [...portNumbers].sort((a, b) => a - b);
    let isConsecutive = true;
    for (let i = 1; i < sortedPorts.length; i++) {
      if (sortedPorts[i] !== sortedPorts[i-1] + 1) {
        isConsecutive = false;
        break;
      }
    }

    if (isConsecutive) {
      console.log('  ✅ 端口号连续分配');
    } else {
      this.warnings.push('端口号不连续，建议使用连续端口便于管理');
    }
  }

  /**
   * 打印验证结果
   */
  printResults() {
    console.log('\n' + chalk.blue('📊 端口配置验证结果'));
    console.log('='.repeat(50));

    // 统计信息
    const totalServices = Object.keys(MICROSERVICE_PORT_MAP).length;
    const configuredServices = Object.keys(this.ports).length;
    const validPorts = Object.values(this.ports).filter(p => typeof p === 'number').length;

    console.log(`总服务数: ${totalServices}`);
    console.log(`已配置: ${configuredServices}`);
    console.log(`有效端口: ${validPorts}`);
    console.log(`错误数: ${chalk.red(this.errors.length)}`);
    console.log(`警告数: ${chalk.yellow(this.warnings.length)}`);

    // 错误信息
    if (this.errors.length > 0) {
      console.log('\n' + chalk.red('❌ 错误:'));
      this.errors.forEach(error => {
        console.log(`  • ${error}`);
      });
    }

    // 警告信息
    if (this.warnings.length > 0) {
      console.log('\n' + chalk.yellow('⚠️ 警告:'));
      this.warnings.forEach(warning => {
        console.log(`  • ${warning}`);
      });
    }

    // 端口分配表
    if (validPorts > 0) {
      console.log('\n' + chalk.green('✅ 端口分配:'));
      Object.entries(this.ports)
        .filter(([, port]) => typeof port === 'number')
        .sort(([, a], [, b]) => a - b)
        .forEach(([service, port]) => {
          console.log(`  ${service.padEnd(15)} → ${port}`);
        });
    }

    // 总结
    console.log('\n' + chalk.blue('📋 总结:'));
    if (this.errors.length === 0) {
      console.log(chalk.green('✅ 端口配置验证通过!'));
    } else {
      console.log(chalk.red('❌ 端口配置存在问题，请修复后重试'));
    }
  }
}

// 运行验证
if (require.main === module) {
  const validator = new PortConfigValidator();
  const result = validator.validate();
  
  // 设置退出码
  process.exit(result.valid ? 0 : 1);
}

module.exports = PortConfigValidator;
