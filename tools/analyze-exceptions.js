#!/usr/bin/env node

/**
 * 异常分析工具
 * 
 * 功能：
 * 1. 分析项目中的throw语句分布
 * 2. 按文件统计throw数量
 * 3. 识别简单和复杂的throw模式
 * 4. 推荐处理优先级
 */

const fs = require('fs');
const path = require('path');
const { Project, SyntaxKind } = require('ts-morph');

class ExceptionAnalyzer {
  constructor() {
    this.project = new Project({
      tsConfigFilePath: './tsconfig.json',
    });
    
    this.analysis = {
      totalFiles: 0,
      filesWithThrows: 0,
      totalThrows: 0,
      fileStats: [],
      throwPatterns: {
        simple: 0,      // throw new Exception("message")
        complex: 0,     // throw new Exception({ code, message })
        unknown: 0,     // 其他模式
      },
    };
  }

  /**
   * 分析项目异常使用情况
   */
  async analyze() {
    console.log('🔍 开始分析项目异常使用情况...');
    
    // 获取所有TypeScript文件
    const sourceFiles = this.project.getSourceFiles('apps/**/*.ts');
    this.analysis.totalFiles = sourceFiles.length;
    
    console.log(`📁 扫描 ${sourceFiles.length} 个TypeScript文件`);
    
    // 分析每个文件
    for (const sourceFile of sourceFiles) {
      this.analyzeFile(sourceFile);
    }
    
    // 排序和分类
    this.analysis.fileStats.sort((a, b) => b.throwCount - a.throwCount);
    
    // 生成报告
    this.generateReport();
    
    // 推荐处理方案
    this.generateRecommendations();
  }

  /**
   * 分析单个文件
   */
  analyzeFile(sourceFile) {
    const filePath = sourceFile.getFilePath();
    const relativePath = path.relative(process.cwd(), filePath);
    
    // 查找所有throw语句
    const throwStatements = sourceFile.getDescendantsOfKind(SyntaxKind.ThrowStatement);
    
    if (throwStatements.length === 0) {
      return;
    }
    
    this.analysis.filesWithThrows++;
    this.analysis.totalThrows += throwStatements.length;
    
    const fileStat = {
      path: relativePath,
      throwCount: throwStatements.length,
      throws: [],
      complexity: 'unknown',
      priority: 'low',
    };
    
    // 分析每个throw语句
    for (const throwStmt of throwStatements) {
      const throwInfo = this.analyzeThrowStatement(throwStmt);
      fileStat.throws.push(throwInfo);
      
      // 统计模式
      this.analysis.throwPatterns[throwInfo.pattern]++;
    }
    
    // 计算文件复杂度
    fileStat.complexity = this.calculateComplexity(fileStat);
    fileStat.priority = this.calculatePriority(fileStat);
    
    this.analysis.fileStats.push(fileStat);
  }

  /**
   * 分析单个throw语句
   */
  analyzeThrowStatement(throwStmt) {
    const lineNumber = throwStmt.getStartLineNumber();
    const throwText = throwStmt.getText();
    const expression = throwStmt.getExpression();
    
    const throwInfo = {
      line: lineNumber,
      text: throwText,
      pattern: 'unknown',
      exceptionType: 'unknown',
      canAutoConvert: false,
    };
    
    try {
      // 检查是否是 new Exception() 模式
      if (expression.getKind() === SyntaxKind.NewExpression) {
        const newExpr = expression;
        const identifier = newExpr.getExpression();
        
        if (identifier.getKind() === SyntaxKind.Identifier) {
          throwInfo.exceptionType = identifier.getText();
          
          // 检查是否是NestJS异常
          if (this.isNestJSException(throwInfo.exceptionType)) {
            const args = newExpr.getArguments();
            
            if (args.length === 0) {
              throwInfo.pattern = 'simple';
              throwInfo.canAutoConvert = true;
            } else if (args.length === 1) {
              const firstArg = args[0];
              
              if (firstArg.getKind() === SyntaxKind.StringLiteral) {
                throwInfo.pattern = 'simple';
                throwInfo.canAutoConvert = true;
              } else if (firstArg.getKind() === SyntaxKind.ObjectLiteralExpression) {
                throwInfo.pattern = 'complex';
                throwInfo.canAutoConvert = this.canAutoConvertComplexThrow(firstArg);
              }
            }
          }
        }
      }
    } catch (error) {
      // 忽略分析错误，保持unknown状态
    }
    
    return throwInfo;
  }

  /**
   * 检查是否是NestJS异常
   */
  isNestJSException(exceptionType) {
    const nestjsExceptions = [
      'BadRequestException',
      'NotFoundException',
      'ForbiddenException',
      'UnauthorizedException',
      'ConflictException',
      'InternalServerErrorException',
      'NotImplementedException',
      'BadGatewayException',
      'ServiceUnavailableException',
      'GatewayTimeoutException',
    ];
    
    return nestjsExceptions.includes(exceptionType);
  }

  /**
   * 检查复杂throw是否可以自动转换
   */
  canAutoConvertComplexThrow(objectExpr) {
    const properties = objectExpr.getProperties();
    let hasCode = false;
    let hasMessage = false;
    
    for (const prop of properties) {
      if (prop.getKind() === SyntaxKind.PropertyAssignment) {
        const propAssign = prop;
        const name = propAssign.getName();
        
        if (name === 'code') hasCode = true;
        if (name === 'message') hasMessage = true;
      }
    }
    
    return hasCode && hasMessage;
  }

  /**
   * 计算文件复杂度
   */
  calculateComplexity(fileStat) {
    const autoConvertable = fileStat.throws.filter(t => t.canAutoConvert).length;
    const autoConvertRate = autoConvertable / fileStat.throwCount;
    
    if (autoConvertRate >= 0.8) return 'simple';
    if (autoConvertRate >= 0.5) return 'medium';
    return 'complex';
  }

  /**
   * 计算处理优先级
   */
  calculatePriority(fileStat) {
    // 检查文件类型，某些类型不适合Result模式
    if (this.isIncompatibleFileType(fileStat.path)) {
      return 'incompatible';  // 不兼容的文件类型
    }

    // 基于throw数量和复杂度计算优先级
    if (fileStat.throwCount <= 5 && fileStat.complexity === 'simple') {
      return 'high';  // 简单文件，优先处理
    }

    if (fileStat.throwCount <= 10 && fileStat.complexity !== 'complex') {
      return 'medium';
    }

    return 'low';  // 复杂文件，后期处理
  }

  /**
   * 检查文件类型是否与Result模式不兼容
   */
  isIncompatibleFileType(filePath) {
    const incompatiblePatterns = [
      /\.guard\.ts$/,           // Guards必须返回boolean
      /\.pipe\.ts$/,            // Pipes有固定返回类型
      /\.filter\.ts$/,          // Exception Filters有固定签名
      /\.interceptor\.ts$/,     // Interceptors有固定返回类型
      /\.middleware\.ts$/,      // Middleware有固定签名
      /\.strategy\.ts$/,        // Passport策略有固定返回类型
    ];

    return incompatiblePatterns.some(pattern => pattern.test(filePath));
  }

  /**
   * 获取文件类型不兼容的原因
   */
  getFileTypeReason(filePath) {
    if (/\.guard\.ts$/.test(filePath)) return 'Guard必须返回boolean';
    if (/\.pipe\.ts$/.test(filePath)) return 'Pipe有固定返回类型';
    if (/\.filter\.ts$/.test(filePath)) return 'Filter有固定异常处理签名';
    if (/\.interceptor\.ts$/.test(filePath)) return 'Interceptor有固定返回类型';
    if (/\.middleware\.ts$/.test(filePath)) return 'Middleware有固定签名';
    if (/\.strategy\.ts$/.test(filePath)) return 'Passport策略有固定返回类型';
    return '架构不兼容';
  }

  /**
   * 生成分析报告
   */
  generateReport() {
    console.log('\n📊 异常使用分析报告');
    console.log('='.repeat(60));
    
    console.log(`总文件数: ${this.analysis.totalFiles}`);
    console.log(`包含throw的文件: ${this.analysis.filesWithThrows}`);
    console.log(`总throw语句: ${this.analysis.totalThrows}`);
    
    console.log('\n📈 Throw模式分布:');
    console.log(`  简单模式: ${this.analysis.throwPatterns.simple} (${((this.analysis.throwPatterns.simple / this.analysis.totalThrows) * 100).toFixed(1)}%)`);
    console.log(`  复杂模式: ${this.analysis.throwPatterns.complex} (${((this.analysis.throwPatterns.complex / this.analysis.totalThrows) * 100).toFixed(1)}%)`);
    console.log(`  未知模式: ${this.analysis.throwPatterns.unknown} (${((this.analysis.throwPatterns.unknown / this.analysis.totalThrows) * 100).toFixed(1)}%)`);
    
    console.log('\n🏆 TOP 10 文件 (按throw数量排序):');
    this.analysis.fileStats.slice(0, 10).forEach((file, index) => {
      const autoConvertable = file.throws.filter(t => t.canAutoConvert).length;
      console.log(`${index + 1}. ${file.path}`);
      console.log(`   Throws: ${file.throwCount} | 可自动转换: ${autoConvertable} | 复杂度: ${file.complexity} | 优先级: ${file.priority}`);
    });
  }

  /**
   * 生成处理建议
   */
  generateRecommendations() {
    console.log('\n🎯 处理建议');
    console.log('='.repeat(60));
    
    // 过滤掉不兼容的文件
    const incompatibleFiles = this.analysis.fileStats.filter(f => f.priority === 'incompatible');
    if (incompatibleFiles.length > 0) {
      console.log('\n🚫 不兼容Result模式的文件 (需要保留异常):');
      incompatibleFiles.slice(0, 5).forEach(file => {
        console.log(`  ${file.path} (${file.throwCount} throws) - ${this.getFileTypeReason(file.path)}`);
      });
    }

    // 高优先级文件（简单且数量少，且兼容）
    const highPriorityFiles = this.analysis.fileStats.filter(f => f.priority === 'high');
    if (highPriorityFiles.length > 0) {
      console.log('\n✅ 建议优先处理 (简单且兼容的Service文件):');
      highPriorityFiles.slice(0, 5).forEach(file => {
        console.log(`  ${file.path} (${file.throwCount} throws)`);
      });

      // 生成命令
      const fileList = highPriorityFiles.slice(0, 3).map(f => f.path).join(',');
      console.log('\n🚀 推荐命令:');
      console.log(`node tools/refactor-exceptions.js --files "${fileList}" --dry-run --verbose`);
    }
    
    // 中等优先级文件
    const mediumPriorityFiles = this.analysis.fileStats.filter(f => f.priority === 'medium');
    if (mediumPriorityFiles.length > 0) {
      console.log('\n⚡ 中等优先级文件:');
      mediumPriorityFiles.slice(0, 5).forEach(file => {
        console.log(`  ${file.path} (${file.throwCount} throws, ${file.complexity})`);
      });
    }
    
    // 复杂文件警告
    const complexFiles = this.analysis.fileStats.filter(f => f.complexity === 'complex');
    if (complexFiles.length > 0) {
      console.log('\n⚠️  需要手动处理的复杂文件:');
      complexFiles.slice(0, 5).forEach(file => {
        console.log(`  ${file.path} (${file.throwCount} throws)`);
      });
    }
    
    // 保存详细报告
    const reportPath = `exception-analysis-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(this.analysis, null, 2));
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  }
}

// CLI接口
if (require.main === module) {
  const analyzer = new ExceptionAnalyzer();
  analyzer.analyze().catch(console.error);
}

module.exports = ExceptionAnalyzer;
