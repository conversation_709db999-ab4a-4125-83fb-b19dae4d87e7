/**
 * 接口生成工具
 * 自动分析JSON配置文件并生成TypeScript接口定义
 * 使用项目根目录的node_modules
 */

const fs = require('../../node_modules/fs-extra');
const path = require('path');
const glob = require('../../node_modules/glob');
const chalk = require('../../node_modules/chalk');

class InterfaceGenerator {
  constructor() {
    this.configDir = path.resolve('libs/game-config/src/data');
    this.outputDir = path.resolve('libs/game-config/src/interfaces');

    // TypeScript类型映射
    this.typeMapping = {
      'string': 'string',
      'number': 'number',
      'boolean': 'boolean',
      'object': 'any',
      'array': 'any[]'
    };

    // HeroPosition位置常量集合 - 性能优化：使用Set进行O(1)查找
    this.heroPositions = new Set([
      'GK',   // 门将
      'DC',   // 中后卫
      'DL',   // 左后卫
      'DR',   // 右后卫
      'DM',   // 后腰
      'MC',   // 中前卫
      'ML',   // 左前卫
      'MR',   // 右前卫
      'AM',   // 前腰
      'ST',   // 中锋
      'WL',   // 左边锋
      'WR',   // 右边锋
    ]);

    // 语义优化映射规则
    this.semanticMappings = {
      // 通用字段优化
      'ID': 'id',
      'Id': 'id',
      'NAME': 'name',
      'Name': 'name',
      'DESC': 'description',
      'Desc': 'description',
      'DESCRIPTION': 'description',
      'Description': 'description',
      'TYPE': 'type',
      'Type': 'type',
      'LEVEL': 'level',
      'Level': 'level',
      'VALUE': 'value',
      'Value': 'value',
      'PRICE': 'price',
      'Price': 'price',
      'COST': 'cost',
      'Cost': 'cost',
      'COUNT': 'count',
      'Count': 'count',
      'MAX': 'maxValue',
      'Max': 'maxValue',
      'MIN': 'minValue',
      'Min': 'minValue',
      'RATE': 'rate',
      'Rate': 'rate',
      'TIME': 'time',
      'Time': 'time',
      'ICON': 'icon',
      'Icon': 'icon',
      'IMAGE': 'image',
      'Image': 'image',
      'URL': 'url',
      'Url': 'url',
      'STATUS': 'status',
      'Status': 'status',
      'STATE': 'state',
      'State': 'state',
      'QUALITY': 'quality',
      'Quality': 'quality',
      'RARITY': 'rarity',
      'Rarity': 'rarity',
      'WEIGHT': 'weight',
      'Weight': 'weight',
      'SORT': 'sortOrder',
      'Sort': 'sortOrder',
      'ORDER': 'order',
      'Order': 'order',
      'INDEX': 'index',
      'Index': 'index',
      'FLAG': 'flag',
      'Flag': 'flag',
      'ENABLE': 'enabled',
      'Enable': 'enabled',
      'DISABLE': 'disabled',
      'Disable': 'disabled',
      'ACTIVE': 'active',
      'Active': 'active',

      // 游戏特定字段优化
      'HEROID': 'heroId',
      'HeroID': 'heroId',
      'HeroId': 'heroId',
      'HERONAME': 'heroName',
      'HeroName': 'heroName',
      'FOOTBALLERID': 'heroId',
      'FootballerID': 'heroId',
      'FootballerId': 'heroId',
      'FOOTBALLERNAME': 'heroName',
      'FootballerName': 'heroName',
      'ITEMID': 'itemId',
      'ItemID': 'itemId',
      'ItemId': 'itemId',
      'ITEMNAME': 'itemName',
      'ItemName': 'itemName',
      'SKILLID': 'skillId',
      'SkillID': 'skillId',
      'SkillId': 'skillId',
      'SKILLNAME': 'skillName',
      'SkillName': 'skillName',
      'TEAMID': 'teamId',
      'TeamID': 'teamId',
      'TeamId': 'teamId',
      'TEAMNAME': 'teamName',
      'TeamName': 'teamName',
      'PLAYERID': 'playerId',
      'PlayerID': 'playerId',
      'PlayerId': 'playerId',
      'PLAYERNAME': 'playerName',
      'PlayerName': 'playerName',
      'MATCHID': 'matchId',
      'MatchID': 'matchId',
      'MatchId': 'matchId',
      'LEAGUEID': 'leagueId',
      'LeagueID': 'leagueId',
      'LeagueId': 'leagueId',
      'GUILDID': 'guildId',
      'GuildID': 'guildId',
      'GuildId': 'guildId',
      'GUILDNAME': 'guildName',
      'GuildName': 'guildName',
      'UNIONID': 'guildId',
      'UnionID': 'guildId',
      'UnionId': 'guildId',
      'UNIONNAME': 'guildName',
      'UnionName': 'guildName',
      'POSITION': 'position',
      'Position': 'position',
      'POS': 'position',
      'Pos': 'position',
      'OVERALL': 'overall',
      'Overall': 'overall',
      'ATTACK': 'attack',
      'Attack': 'attack',
      'DEFENSE': 'defense',
      'Defense': 'defense',
      'SPEED': 'speed',
      'Speed': 'speed',
      'POWER': 'power',
      'Power': 'power',
      'SKILL': 'skill',
      'Skill': 'skill',
      'ENERGY': 'energy',
      'Energy': 'energy',
      'EXP': 'experience',
      'Exp': 'experience',
      'EXPERIENCE': 'experience',
      'Experience': 'experience',
      'COIN': 'coin',
      'Coin': 'coin',
      'GOLD': 'gold',
      'Gold': 'gold',
      'DIAMOND': 'diamond',
      'Diamond': 'diamond',
      'CRYSTAL': 'crystal',
      'Crystal': 'crystal',

      // 时间相关优化
      'STARTTIME': 'startTime',
      'StartTime': 'startTime',
      'ENDTIME': 'endTime',
      'EndTime': 'endTime',
      'CREATETIME': 'createTime',
      'CreateTime': 'createTime',
      'UPDATETIME': 'updateTime',
      'UpdateTime': 'updateTime',
      'COOLDOWN': 'cooldown',
      'CoolDown': 'cooldown',
      'DURATION': 'duration',
      'Duration': 'duration',

      // 条件和需求相关
      'REQUIRE': 'requirement',
      'Require': 'requirement',
      'REQUIREMENT': 'requirement',
      'Requirement': 'requirement',
      'CONDITION': 'condition',
      'Condition': 'condition',
      'UNLOCK': 'unlock',
      'Unlock': 'unlock',
      'UNLOCKTYPE': 'unlockType',
      'UnlockType': 'unlockType',
      'UNLOCKVALUE': 'unlockValue',
      'UnlockValue': 'unlockValue',

      // 奖励相关
      'REWARD': 'reward',
      'Reward': 'reward',
      'REWARDTYPE': 'rewardType',
      'RewardType': 'rewardType',
      'REWARDVALUE': 'rewardValue',
      'RewardValue': 'rewardValue',
      'REWARDCOUNT': 'rewardCount',
      'RewardCount': 'rewardCount',
      'DROP': 'drop',
      'Drop': 'drop',
      'DROPRATE': 'dropRate',
      'DropRate': 'dropRate',
      'DROPTYPE': 'dropType',
      'DropType': 'dropType',

      // 配置相关
      'CONFIG': 'config',
      'Config': 'config',
      'CONFIGID': 'configId',
      'ConfigID': 'configId',
      'ConfigId': 'configId',
      'PARAM': 'parameter',
      'Param': 'parameter',
      'PARAMETER': 'parameter',
      'Parameter': 'parameter',
      'SETTING': 'setting',
      'Setting': 'setting',
      'OPTION': 'option',
      'Option': 'option'
    };
  }

  /**
   * 清空输出目录
   */
  async cleanOutputDirectory() {
    console.log(chalk.gray('🧹 清空旧的接口文件...'));

    try {
      // 检查目录是否存在
      if (await fs.pathExists(this.outputDir)) {
        // 读取目录中的所有文件
        const files = await fs.readdir(this.outputDir);

        // 只删除.interface.ts文件和index.ts，保留其他文件
        const filesToDelete = files.filter(file =>
          file.endsWith('.interface.ts') || file === 'index.ts'
        );

        if (filesToDelete.length > 0) {
          console.log(chalk.gray(`   删除 ${filesToDelete.length} 个旧接口文件...`));

          for (const file of filesToDelete) {
            const filePath = path.join(this.outputDir, file);
            await fs.remove(filePath);
            console.log(chalk.gray(`   - 删除: ${file}`));
          }
        } else {
          console.log(chalk.gray('   没有找到需要删除的旧接口文件'));
        }
      } else {
        console.log(chalk.gray('   输出目录不存在，将创建新目录'));
      }
    } catch (error) {
      console.log(chalk.yellow(`⚠️  清空目录时出错: ${error.message}`));
      // 不抛出错误，继续执行
    }
  }

  /**
   * 生成所有接口
   */
  async generateAll() {
    console.log(chalk.blue('🚀 开始生成TypeScript接口...'));

    try {
      // 清空并重新创建输出目录
      await this.cleanOutputDirectory();

      // 确保输出目录存在
      await fs.ensureDir(this.outputDir);
      
      // 查找所有JSON配置文件 (使用相对路径)
      const pattern = 'libs/game-config/src/data/*.json';
      const files = glob.sync(pattern);
      
      if (files.length === 0) {
        console.log(chalk.yellow('⚠️  未找到配置文件，请先运行迁移工具'));
        return { success: false, message: '未找到配置文件' };
      }
      
      const report = {
        totalFiles: files.length,
        generatedInterfaces: [],
        errors: [],
        totalFields: 0
      };
      
      // 逐个生成接口
      for (const file of files) {
        try {
          const tableName = path.basename(file, '.json');
          console.log(chalk.gray(`生成: ${tableName}`));
          
          const result = await this.generateInterface(file, tableName);
          report.generatedInterfaces.push(result);
          report.totalFields += result.fieldsCount;
          
        } catch (error) {
          report.errors.push({
            file: path.basename(file),
            error: error.message
          });
          console.log(chalk.red(`❌ ${path.basename(file)}: ${error.message}`));
        }
      }
      
      // 生成统一导出文件
      if (report.generatedInterfaces.length > 0) {
        await this.generateIndexFile(report.generatedInterfaces);
      }
      
      // 输出报告
      this.printGenerationReport(report);
      
      return {
        success: report.errors.length === 0,
        report
      };
      
    } catch (error) {
      console.log(chalk.red('❌ 生成过程中发生错误:'), error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * 生成单个接口
   */
  async generateInterface(jsonFile, tableName) {
    // 读取JSON数据
    const jsonData = await fs.readJson(jsonFile);

    if (!Array.isArray(jsonData)) {
      throw new Error(`配置文件格式错误，期望数组格式: ${tableName}`);
    }

    // 应用项目命名规范到表名
    const normalizedTableName = this.normalizeTableName(tableName);

    if (jsonData.length === 0) {
      console.log(chalk.yellow(`⚠️  配置文件为空: ${tableName}`));
      // 生成空接口
      const interfaceCode = this.generateEmptyInterface(normalizedTableName, tableName);
      const outputFile = path.join(this.outputDir, `${this.toKebabCase(normalizedTableName)}.interface.ts`);
      await fs.writeFile(outputFile, interfaceCode);

      return {
        tableName: normalizedTableName,
        originalTableName: tableName,
        interfaceFile: outputFile,
        fieldsCount: 0,
        isEmpty: true
      };
    }

    // 分析字段结构
    const fields = this.analyzeFields(jsonData);

    // 生成接口代码
    const interfaceCode = this.generateInterfaceCode(normalizedTableName, fields, tableName);

    // 保存接口文件
    const outputFile = path.join(this.outputDir, `${this.toKebabCase(normalizedTableName)}.interface.ts`);
    await fs.writeFile(outputFile, interfaceCode);

    console.log(chalk.green(`✅ ${tableName} → ${normalizedTableName} (${fields.length} 个字段)`));

    return {
      tableName: normalizedTableName,
      originalTableName: tableName,
      interfaceFile: outputFile,
      fieldsCount: fields.length,
      fields: fields.map(f => f.name)
    };
  }

  /**
   * 规范化表名（应用项目命名规范）
   */
  normalizeTableName(tableName) {
    // 项目命名规范转换
    const nameRules = [
      // Footballer -> Hero
      { pattern: /^Footballer(.*)$/, replacement: 'Hero$1' },
      { pattern: /^(.*)Footballer(.*)$/, replacement: '$1Hero$2' },
      { pattern: /^(.*)Footballer$/, replacement: '$1Hero' },

      // Association -> Guild
      { pattern: /^Association(.*)$/, replacement: 'Guild$1' },
      { pattern: /^(.*)Association(.*)$/, replacement: '$1Guild$2' },
      { pattern: /^(.*)Association$/, replacement: '$1Guild' },

      // NPCFootballer -> NPCHero
      { pattern: /^NPCFootballer(.*)$/, replacement: 'NPCHero$1' },
      { pattern: /^(.*)NPCFootballer(.*)$/, replacement: '$1NPCHero$2' },
      { pattern: /^(.*)NPCFootballer$/, replacement: '$1NPCHero' }
    ];

    let normalizedName = tableName;

    for (const rule of nameRules) {
      if (rule.pattern.test(tableName)) {
        normalizedName = tableName.replace(rule.pattern, rule.replacement);
        break;
      }
    }

    return normalizedName;
  }

  /**
   * 分析字段类型并优化字段名
   */
  analyzeFields(data) {
    const fieldMap = new Map();

    // 分析所有记录的字段
    for (const item of data) {
      for (const [originalKey, value] of Object.entries(item)) {
        // 优化字段名
        const optimizedKey = this.optimizeFieldName(originalKey);

        if (!fieldMap.has(optimizedKey)) {
          fieldMap.set(optimizedKey, {
            name: optimizedKey,
            originalName: originalKey,
            type: this.inferType(value),
            required: false,
            nullable: false,
            description: this.generateDescription(optimizedKey, value),
            examples: new Set(),
            isRenamed: optimizedKey !== originalKey
          });
        }

        const field = fieldMap.get(optimizedKey);

        // 检查字段是否必需
        if (value !== null && value !== undefined && value !== '') {
          field.required = true;
        }

        // 检查是否可为空
        if (value === null || value === undefined) {
          field.nullable = true;
        }

        // 收集示例值
        if (value !== null && value !== undefined && field.examples.size < 3) {
          field.examples.add(value);
        }

        // 类型推断优化
        const currentType = this.inferType(value);
        if (currentType !== field.type && currentType !== 'null') {
          // 如果类型不一致，使用更通用的类型
          if (field.type === 'number' && currentType === 'string') {
            field.type = 'string | number';
          } else if (field.type === 'string' && currentType === 'number') {
            field.type = 'string | number';
          }
        }
      }
    }

    // 转换为数组并排序
    const fields = Array.from(fieldMap.values());

    // 按重要性排序：id字段优先，然后是name字段，最后按字母顺序
    fields.sort((a, b) => {
      if (a.name.includes('id') || a.name.includes('Id')) return -1;
      if (b.name.includes('id') || b.name.includes('Id')) return 1;
      if (a.name.includes('name') || a.name.includes('Name')) return -1;
      if (b.name.includes('name') || b.name.includes('Name')) return 1;
      return a.name.localeCompare(b.name);
    });

    return fields;
  }

  /**
   * 优化字段名：语义分析 + 驼峰命名法
   */
  optimizeFieldName(originalName) {
    // 1. 首先检查是否有直接的语义映射
    if (this.semanticMappings[originalName]) {
      return this.semanticMappings[originalName];
    }

    // 2. 检查是否为HeroPosition中定义的位置常量，如果是则保持不变
    if (this.isHeroPosition(originalName)) {
      return originalName;
    }

    // 3. 如果没有直接映射，进行驼峰命名法转换
    let optimizedName = this.toCamelCase(originalName);

    // 4. 进行语义分析和优化
    optimizedName = this.analyzeSemantics(optimizedName, originalName);

    return optimizedName;
  }

  /**
   * 检查是否为HeroPosition中定义的位置常量
   * 性能优化：使用预初始化的Set进行O(1)时间复杂度查找
   */
  isHeroPosition(fieldName) {
    return this.heroPositions.has(fieldName);
  }

  /**
   * 转换为驼峰命名法
   */
  toCamelCase(str) {
    // 处理数字开头的字段名
    if (/^\d/.test(str)) {
      str = 'field' + str;
    }

    // 处理各种分隔符：下划线、连字符、空格等
    return str
      .replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '')
      .replace(/^[A-Z]/, char => char.toLowerCase());
  }

  /**
   * 语义分析和优化
   */
  analyzeSemantics(fieldName, originalName) {
    // 项目特定的命名转换规则
    const projectSpecificRules = [
      // 数字开头字段的特殊处理
      { pattern: /^(\d+)PercentWeight$/i, replacement: 'percent$1Weight' },
      { pattern: /^(\d+)Percent(.*)$/i, replacement: 'percent$1$2' },
      { pattern: /^(\d+)Level(.*)$/i, replacement: 'level$1$2' },
      { pattern: /^(\d+)Star(.*)$/i, replacement: 'star$1$2' },
      { pattern: /^(\d+)(.*)$/i, replacement: 'field$1$2' },

      // Footballer -> Hero 转换
      { pattern: /^footballer(.*)$/i, replacement: 'hero$1' },
      { pattern: /^(.*)footballer(.*)$/i, replacement: '$1hero$2' },
      { pattern: /^(.*)footballer$/i, replacement: '$1hero' },

      // Union -> Guild 转换
      { pattern: /^union(.*)$/i, replacement: 'guild$1' },
      { pattern: /^(.*)union(.*)$/i, replacement: '$1guild$2' },
      { pattern: /^(.*)union$/i, replacement: '$1guild' },
    ];

    // 常见的语义优化规则
    const semanticRules = [
      // ID相关优化
      { pattern: /^(.+)ID$/i, replacement: '$1Id' },
      { pattern: /^(.+)Id$/i, replacement: '$1Id' },

      // Name相关优化
      { pattern: /^(.+)NAME$/i, replacement: '$1Name' },
      { pattern: /^(.+)Name$/i, replacement: '$1Name' },

      // Type相关优化
      { pattern: /^(.+)TYPE$/i, replacement: '$1Type' },
      { pattern: /^(.+)Type$/i, replacement: '$1Type' },

      // 时间相关优化
      { pattern: /^(.+)TIME$/i, replacement: '$1Time' },
      { pattern: /^(.+)Time$/i, replacement: '$1Time' },

      // 数值相关优化
      { pattern: /^MAX(.+)$/i, replacement: 'max$1' },
      { pattern: /^MIN(.+)$/i, replacement: 'min$1' },
      { pattern: /^(.+)MAX$/i, replacement: '$1Max' },
      { pattern: /^(.+)MIN$/i, replacement: '$1Min' },

      // 状态相关优化
      { pattern: /^IS(.+)$/i, replacement: 'is$1' },
      { pattern: /^HAS(.+)$/i, replacement: 'has$1' },
      { pattern: /^CAN(.+)$/i, replacement: 'can$1' },

      // 计数相关优化
      { pattern: /^(.+)COUNT$/i, replacement: '$1Count' },
      { pattern: /^(.+)NUM$/i, replacement: '$1Number' },
      { pattern: /^(.+)CNT$/i, replacement: '$1Count' },

      // 比率相关优化
      { pattern: /^(.+)RATE$/i, replacement: '$1Rate' },
      { pattern: /^(.+)RATIO$/i, replacement: '$1Ratio' },
      { pattern: /^(.+)PCT$/i, replacement: '$1Percentage' },

      // 位置相关优化
      { pattern: /^(.+)POS$/i, replacement: '$1Position' },
      { pattern: /^(.+)LOC$/i, replacement: '$1Location' },

      // 配置相关优化
      { pattern: /^(.+)CFG$/i, replacement: '$1Config' },
      { pattern: /^(.+)CONF$/i, replacement: '$1Config' },

      // 描述相关优化
      { pattern: /^(.+)DESC$/i, replacement: '$1Description' },
      { pattern: /^(.+)INFO$/i, replacement: '$1Info' },

      // 图标相关优化
      { pattern: /^(.+)ICO$/i, replacement: '$1Icon' },
      { pattern: /^(.+)IMG$/i, replacement: '$1Image' },

      // 链接相关优化
      { pattern: /^(.+)LINK$/i, replacement: '$1Link' },
      { pattern: /^(.+)REF$/i, replacement: '$1Reference' }
    ];

    let result = fieldName;

    // 首先应用项目特定规则
    for (const rule of projectSpecificRules) {
      if (rule.pattern.test(originalName)) {
        result = originalName.replace(rule.pattern, rule.replacement);
        result = this.toCamelCase(result);
        break;
      }
    }

    // 如果项目特定规则没有匹配，应用通用语义规则
    if (result === fieldName) {
      for (const rule of semanticRules) {
        if (rule.pattern.test(originalName)) {
          result = originalName.replace(rule.pattern, rule.replacement);
          result = this.toCamelCase(result);
          break;
        }
      }
    }

    // 确保首字母小写
    result = result.charAt(0).toLowerCase() + result.slice(1);

    return result;
  }

  /**
   * 推断字段类型
   */
  inferType(value) {
    if (value === null || value === undefined) return 'null';
    
    const type = typeof value;
    
    if (type === 'object') {
      if (Array.isArray(value)) {
        if (value.length === 0) return 'any[]';
        const elementType = this.inferType(value[0]);
        return elementType === 'null' ? 'any[]' : `${elementType}[]`;
      }
      return 'any';
    }
    
    return this.typeMapping[type] || 'any';
  }

  /**
   * 生成字段描述
   */
  generateDescription(fieldName, value) {
    const descriptions = {
      // 基础字段
      'id': '唯一标识符',
      'name': '名称',
      'description': '描述信息',
      'type': '类型',
      'level': '等级',
      'value': '数值',
      'count': '数量',
      'index': '索引',
      'order': '顺序',
      'sortOrder': '排序顺序',
      'flag': '标志',
      'status': '状态',
      'state': '状态',
      'active': '是否激活',
      'enabled': '是否启用',
      'disabled': '是否禁用',

      // 游戏相关
      'heroId': '英雄ID',
      'heroName': '英雄名称',
      'footballerId': '英雄ID', // 兼容旧命名
      'footballerName': '英雄名称', // 兼容旧命名
      'itemId': '物品ID',
      'itemName': '物品名称',
      'skillId': '技能ID',
      'skillName': '技能名称',
      'teamId': '队伍ID',
      'teamName': '队伍名称',
      'playerId': '玩家ID',
      'playerName': '玩家名称',
      'matchId': '比赛ID',
      'leagueId': '联赛ID',
      'guildId': '工会ID',
      'guildName': '工会名称',
      'unionId': '工会ID', // 兼容旧命名
      'unionName': '工会名称', // 兼容旧命名
      'configId': '配置ID',

      // 属性相关
      'position': '位置',
      'quality': '品质',
      'rarity': '稀有度',
      'overall': '综合评分',
      'attack': '攻击力',
      'defense': '防御力',
      'speed': '速度',
      'power': '力量',
      'skill': '技能',
      'energy': '精力',
      'experience': '经验值',

      // 经济相关
      'price': '价格',
      'cost': '消耗',
      'coin': '金币',
      'gold': '金币',
      'diamond': '钻石',
      'crystal': '水晶',

      // 数值相关
      'maxValue': '最大值',
      'minValue': '最小值',
      'rate': '比率',
      'percentage': '百分比',
      'weight': '权重',

      // 时间相关
      'time': '时间',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'createTime': '创建时间',
      'updateTime': '更新时间',
      'cooldown': '冷却时间',
      'duration': '持续时间',

      // 条件相关
      'requirement': '需求条件',
      'condition': '条件',
      'unlock': '解锁条件',
      'unlockType': '解锁类型',
      'unlockValue': '解锁值',

      // 奖励相关
      'reward': '奖励',
      'rewardType': '奖励类型',
      'rewardValue': '奖励值',
      'rewardCount': '奖励数量',
      'drop': '掉落',
      'dropRate': '掉落率',
      'dropType': '掉落类型',

      // 界面相关
      'icon': '图标',
      'image': '图片',
      'url': '链接地址',

      // 配置相关
      'config': '配置',
      'parameter': '参数',
      'setting': '设置',
      'option': '选项'
    };

    const lowerName = fieldName.toLowerCase();

    // 精确匹配
    if (descriptions[fieldName]) {
      return descriptions[fieldName];
    }

    // 模糊匹配
    for (const [key, desc] of Object.entries(descriptions)) {
      if (lowerName.includes(key.toLowerCase())) {
        return desc;
      }
    }

    // 根据字段名模式生成描述
    if (lowerName.endsWith('id')) return 'ID标识符';
    if (lowerName.endsWith('name')) return '名称';
    if (lowerName.endsWith('type')) return '类型';
    if (lowerName.endsWith('time')) return '时间';
    if (lowerName.endsWith('count')) return '数量';
    if (lowerName.endsWith('rate')) return '比率';
    if (lowerName.endsWith('value')) return '数值';
    if (lowerName.startsWith('is')) return '是否';
    if (lowerName.startsWith('has')) return '是否拥有';
    if (lowerName.startsWith('can')) return '是否可以';
    if (lowerName.startsWith('max')) return '最大';
    if (lowerName.startsWith('min')) return '最小';

    // 根据值类型生成描述
    if (typeof value === 'boolean') return '布尔值';
    if (typeof value === 'number') return '数值';
    if (typeof value === 'string') return '字符串';
    if (Array.isArray(value)) return '数组';

    return '配置项';
  }

  /**
   * 生成接口代码
   */
  generateInterfaceCode(tableName, fields, originalTableName = null) {
    const interfaceName = `${tableName}Definition`;
    const metaName = `${tableName}Meta`;
    const dataFileName = originalTableName || tableName;

    let code = `// Auto-generated from ${dataFileName}.json\n`;
    code += `// Generated at: ${new Date().toISOString()}\n`;
    code += `// 字段名已优化为驼峰命名法，原始字段名映射见元数据\n`;
    if (originalTableName && originalTableName !== tableName) {
      code += `// 表名已规范化: ${originalTableName} → ${tableName}\n`;
    }
    code += `\n`;

    // 生成基础接口
    code += `export interface ${interfaceName} {\n`;

    for (const field of fields) {
      const optional = field.required ? '' : '?';
      const nullable = field.nullable ? ' | null' : '';
      const examples = Array.from(field.examples).slice(0, 2);
      // 限制示例文本长度，避免包含过长的内容，并处理换行符
      const truncatedExamples = examples.map(example => {
        const str = String(example)
          .replace(/\r?\n/g, ' ')  // 替换换行符为空格
          .replace(/\s+/g, ' ')    // 合并多个空格为一个
          .trim();                 // 去除首尾空格
        return str.length > 30 ? str.substring(0, 27) + '...' : str;
      });
      const exampleText = truncatedExamples.length > 0 ? ` 例: ${truncatedExamples.join(', ')}` : '';
      const renameNote = field.isRenamed ? ` (原: ${field.originalName})` : '';

      code += `  ${field.name}${optional}: ${field.type}${nullable}; // ${field.description}${exampleText}${renameNote}\n`;
    }

    code += `}\n\n`;

    // 生成字段映射
    const fieldMappings = this.generateFieldMappings(fields);
    if (Object.keys(fieldMappings).length > 0) {
      code += `// 字段映射：新字段名 -> 原始字段名\n`;
      code += `export const ${tableName}FieldMappings = {\n`;
      for (const [newName, originalName] of Object.entries(fieldMappings)) {
        code += `  ${newName}: '${originalName}',\n`;
      }
      code += `} as const;\n\n`;

      // 生成反向映射
      code += `// 反向映射：原始字段名 -> 新字段名\n`;
      code += `export const ${tableName}ReverseFieldMappings = {\n`;
      for (const [newName, originalName] of Object.entries(fieldMappings)) {
        code += `  '${originalName}': '${newName}',\n`;
      }
      code += `} as const;\n\n`;
    }

    // 生成配置元数据
    const primaryKey = this.findPrimaryKey(fields);
    const searchFields = this.findSearchFields(fields);
    const renamedFields = fields.filter(f => f.isRenamed);

    code += `export const ${metaName} = {\n`;
    code += `  tableName: '${tableName}',\n`;
    if (originalTableName && originalTableName !== tableName) {
      code += `  originalTableName: '${originalTableName}',\n`;
      code += `  dataFileName: '${originalTableName}.json',\n`;
    } else {
      code += `  dataFileName: '${tableName}.json',\n`;
    }
    code += `  primaryKey: '${primaryKey}',\n`;
    code += `  searchFields: [${searchFields.map(f => `'${f}'`).join(', ')}],\n`;
    code += `  fieldsCount: ${fields.length},\n`;
    code += `  requiredFields: [${fields.filter(f => f.required).map(f => `'${f.name}'`).join(', ')}],\n`;
    code += `  optionalFields: [${fields.filter(f => !f.required).map(f => `'${f.name}'`).join(', ')}],\n`;
    code += `  renamedFieldsCount: ${renamedFields.length},\n`;
    code += `  hasFieldMappings: ${Object.keys(fieldMappings).length > 0},\n`;
    code += `  isTableRenamed: ${originalTableName && originalTableName !== tableName},\n`;

    if (Object.keys(fieldMappings).length > 0) {
      code += `  fieldMappings: ${tableName}FieldMappings,\n`;
      code += `  reverseFieldMappings: ${tableName}ReverseFieldMappings,\n`;
    }

    code += `} as const;\n\n`;

    // 生成类型导出
    code += `export type ${tableName}ConfigMeta = typeof ${metaName};\n`;

    // 生成字段映射类型
    if (Object.keys(fieldMappings).length > 0) {
      code += `export type ${tableName}FieldMapping = typeof ${tableName}FieldMappings;\n`;
      code += `export type ${tableName}ReverseFieldMapping = typeof ${tableName}ReverseFieldMappings;\n`;
    }

    return code;
  }

  /**
   * 生成字段映射对象
   */
  generateFieldMappings(fields) {
    const mappings = {};

    for (const field of fields) {
      if (field.isRenamed) {
        mappings[field.name] = field.originalName;
      }
    }

    return mappings;
  }

  /**
   * 生成空接口
   */
  generateEmptyInterface(tableName, originalTableName = null) {
    const interfaceName = `${tableName}Definition`;
    const metaName = `${tableName}Meta`;
    const dataFileName = originalTableName || tableName;

    let code = `// Auto-generated from ${dataFileName}.json (empty)\n`;
    code += `// Generated at: ${new Date().toISOString()}\n`;
    if (originalTableName && originalTableName !== tableName) {
      code += `// 表名已规范化: ${originalTableName} → ${tableName}\n`;
    }
    code += `\n`;

    code += `export interface ${interfaceName} {\n`;
    code += `  // 配置文件为空，请添加配置数据后重新生成\n`;
    code += `}\n\n`;

    code += `export const ${metaName} = {\n`;
    code += `  tableName: '${tableName}',\n`;
    if (originalTableName && originalTableName !== tableName) {
      code += `  originalTableName: '${originalTableName}',\n`;
      code += `  dataFileName: '${originalTableName}.json',\n`;
    } else {
      code += `  dataFileName: '${tableName}.json',\n`;
    }
    code += `  primaryKey: 'id',\n`;
    code += `  searchFields: [],\n`;
    code += `  fieldsCount: 0,\n`;
    code += `  isEmpty: true,\n`;
    code += `  isTableRenamed: ${originalTableName && originalTableName !== tableName}\n`;
    code += `} as const;\n`;

    return code;
  }

  /**
   * 查找主键字段
   * 基于old项目的主键识别策略：Id > entityId > id > ID > ModeID
   */
  findPrimaryKey(fields) {
    // 基于old项目readFile函数的主键识别优先级
    // if(oneData["Id"] || oneData["entityId"] || oneData["id"] || oneData["ID"] || oneData["ModeID"])
    const oldProjectPrimaryKeys = [
      'id',        // 对应原始的 Id (迁移时已转换为小写)
      'entityId',  // 对应原始的 entityId
      'ID',        // 对应原始的 ID (保持大写)
      'modeId'     // 对应原始的 ModeID (已转换为驼峰)
    ];

    // 1. 优先按照old项目的主键策略查找
    for (const keyName of oldProjectPrimaryKeys) {
      const field = fields.find(f => f.name === keyName);
      if (field && field.type === 'number') {
        return field.name;
      }
    }

    // 2. 查找其他常见的主键字段（优化后的字段名）
    const commonPrimaryKeys = [
      'heroId', 'itemId', 'skillId', 'shopId', 'teamId', 'playerId',
      'matchId', 'leagueId', 'guildId', 'configId', 'soleId'
    ];

    for (const keyName of commonPrimaryKeys) {
      const field = fields.find(f => f.name === keyName);
      if (field && field.type === 'number') {
        return field.name;
      }
    }

    // 3. 查找包含id的数字字段
    const idField = fields.find(f =>
      f.name.toLowerCase().includes('id') && f.type === 'number'
    );
    if (idField) {
      return idField.name;
    }

    // 4. 查找第一个数字类型且可能是主键的字段
    const numberFields = fields.filter(f => f.type === 'number');
    if (numberFields.length > 0) {
      // 优先选择名称暗示为主键的字段
      const keyLikeField = numberFields.find(f => {
        const lowerName = f.name.toLowerCase();
        return lowerName.includes('key') || lowerName.includes('index') ||
               lowerName.includes('no') || lowerName.includes('code') ||
               lowerName === 'type' || lowerName === 'level';
      });

      if (keyLikeField) {
        return keyLikeField.name;
      }

      // 否则返回第一个数字字段
      return numberFields[0].name;
    }

    // 5. 最后返回第一个字段或默认值
    return fields.length > 0 ? fields[0].name : 'id';
  }

  /**
   * 查找搜索字段
   */
  findSearchFields(fields) {
    const searchablePatterns = [
      'name', 'title', 'description', 'desc', 'info',
      'heroName', 'itemName', 'skillName', 'teamName', 'playerName', 'guildName'
    ];

    return fields
      .filter(f => {
        if (f.type !== 'string') return false;

        const lowerName = f.name.toLowerCase();
        return searchablePatterns.some(pattern =>
          lowerName.includes(pattern.toLowerCase())
        );
      })
      .map(f => f.name);
  }

  /**
   * 转换为kebab-case
   */
  toKebabCase(str) {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .toLowerCase();
  }

  /**
   * 生成统一导出文件
   */
  async generateIndexFile(generatedInterfaces) {
    let indexContent = '// Auto-generated index file\n';
    indexContent += `// Generated at: ${new Date().toISOString()}\n`;
    indexContent += '// 表名已根据项目命名规范进行规范化\n\n';

    // 导出所有接口
    for (const { tableName } of generatedInterfaces) {
      const fileName = this.toKebabCase(tableName);
      indexContent += `export * from './${fileName}.interface';\n`;
    }

    indexContent += '\n// 配置表映射（规范化后的表名）\n';
    indexContent += 'export const CONFIG_TABLES = {\n';

    for (const { tableName } of generatedInterfaces) {
      indexContent += `  ${tableName}: '${tableName}',\n`;
    }

    indexContent += '} as const;\n\n';

    // 添加原始表名到规范化表名的映射
    const hasRenamedTables = generatedInterfaces.some(i => i.originalTableName && i.originalTableName !== i.tableName);

    if (hasRenamedTables) {
      indexContent += '// 原始表名到规范化表名的映射\n';
      indexContent += 'export const ORIGINAL_TO_NORMALIZED_TABLE_NAMES = {\n';

      for (const { tableName, originalTableName } of generatedInterfaces) {
        if (originalTableName && originalTableName !== tableName) {
          indexContent += `  '${originalTableName}': '${tableName}',\n`;
        }
      }

      indexContent += '} as const;\n\n';
    }

    indexContent += 'export type ConfigTableName = keyof typeof CONFIG_TABLES;\n';

    const indexFile = path.join(this.outputDir, 'index.ts');
    await fs.writeFile(indexFile, indexContent);

    console.log(chalk.green('✅ 生成统一导出文件: index.ts'));
  }

  /**
   * 打印生成报告
   */
  printGenerationReport(report) {
    console.log('\n' + chalk.blue('📊 接口生成报告'));
    console.log(chalk.gray('='.repeat(50)));
    
    console.log(`总文件数: ${chalk.cyan(report.totalFiles)}`);
    console.log(`成功生成: ${chalk.green(report.generatedInterfaces.length)}`);
    console.log(`失败数量: ${chalk.red(report.errors.length)}`);
    console.log(`总字段数: ${chalk.cyan(report.totalFields)}`);
    
    if (report.generatedInterfaces.length > 0) {
      console.log(`\n${chalk.blue('生成的接口:')}`);
      report.generatedInterfaces.forEach(({ tableName, fieldsCount, isEmpty }) => {
        const status = isEmpty ? chalk.yellow('(空)') : chalk.green(`(${fieldsCount} 字段)`);
        console.log(`  ${chalk.green('✓')} ${tableName}Definition ${status}`);
      });
    }
    
    if (report.errors.length > 0) {
      console.log(`\n${chalk.red('失败文件:')}`);
      report.errors.forEach(error => {
        console.log(`  ${chalk.red(error.file)}: ${error.error}`);
      });
    }
    
    console.log(chalk.gray('='.repeat(50)));
    
    if (report.errors.length === 0) {
      console.log(chalk.green('🎉 所有接口生成成功！'));
    } else {
      console.log(chalk.yellow('⚠️  部分接口生成失败，请检查错误信息'));
    }
  }
}

module.exports = InterfaceGenerator;
