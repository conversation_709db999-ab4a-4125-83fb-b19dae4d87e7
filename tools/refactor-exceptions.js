#!/usr/bin/env node

/**
 * 异常重构工具
 * 
 * 功能：
 * 1. 扫描项目中的throw语句
 * 2. 自动转换为Result模式
 * 3. 生成重构报告
 * 4. 支持批量处理和回滚
 */

const fs = require('fs');
const path = require('path');
const { Project, SyntaxKind } = require('ts-morph');
const glob = require('glob');

class ExceptionRefactorTool {
  constructor() {
    this.project = new Project({
      tsConfigFilePath: './tsconfig.json',
    });

    this.stats = {
      totalFiles: 0,
      processedFiles: 0,
      totalThrows: 0,
      convertedThrows: 0,
      errors: [],
      fileDetails: [], // 新增：详细的文件处理信息
    };

    this.dryRun = false;
    this.verbose = false;
    this.targetFiles = []; // 新增：指定处理的文件列表
    this.targetDirectories = []; // 新增：指定处理的目录列表
  }

  /**
   * 主要重构方法
   */
  async refactor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.verbose = options.verbose || false;
    this.targetFiles = options.files || [];
    this.targetDirectories = options.directories || [];
    this.batchType = options.batch || null;

    console.log('🚀 开始异常重构...');
    console.log(`模式: ${this.dryRun ? '预览模式' : '实际修改'}`);

    // 1. 获取要处理的文件
    let sourceFiles = [];

    // 批量处理模式
    if (this.batchType) {
      sourceFiles = await this.getBatchFiles(this.batchType);
    }
    // 处理指定文件
    else if (this.targetFiles.length > 0) {
      console.log(`🎯 指定文件模式: ${this.targetFiles.length} 个文件`);
      const fileSourceFiles = this.targetFiles.map(filePath => {
        const sourceFile = this.project.getSourceFile(filePath);
        if (!sourceFile) {
          console.warn(`⚠️  文件不存在: ${filePath}`);
          return null;
        }
        return sourceFile;
      }).filter(Boolean);
      sourceFiles.push(...fileSourceFiles);
    }
    // 处理指定目录
    else if (this.targetDirectories.length > 0) {
      console.log(`📁 指定目录模式: ${this.targetDirectories.length} 个目录`);
      for (const directory of this.targetDirectories) {
        const dirSourceFiles = await this.getSourceFilesFromDirectory(directory);
        sourceFiles.push(...dirSourceFiles);
        console.log(`  📂 ${directory}: 发现 ${dirSourceFiles.length} 个文件`);
      }
    }
    // 如果没有指定文件或目录，扫描所有文件
    else {
      console.log('📁 扫描所有TypeScript文件...');
      sourceFiles = this.project.getSourceFiles('apps/**/*.ts');
    }

    // 去重
    sourceFiles = this.deduplicateSourceFiles(sourceFiles);

    this.stats.totalFiles = sourceFiles.length;
    console.log(`📁 将处理 ${sourceFiles.length} 个TypeScript文件`);

    // 2. 处理每个文件
    for (const sourceFile of sourceFiles) {
      await this.processFile(sourceFile);
    }

    // 3. 生成报告
    this.generateReport();

    // 4. 保存更改（如果不是预览模式）
    if (!this.dryRun && this.stats.convertedThrows > 0) {
      await this.project.save();
      console.log('✅ 所有更改已保存');
    }
  }

  /**
   * 处理单个文件
   */
  async processFile(sourceFile) {
    const filePath = sourceFile.getFilePath();
    const relativePath = path.relative(process.cwd(), filePath);

    if (this.verbose) {
      console.log(`🔍 处理文件: ${relativePath}`);
    }

    try {
      // 查找所有throw语句
      const throwStatements = sourceFile.getDescendantsOfKind(SyntaxKind.ThrowStatement);

      if (throwStatements.length === 0) {
        if (this.verbose) {
          console.log(`  ✅ ${relativePath}: 无throw语句`);
        }
        return;
      }

      this.stats.processedFiles++;
      this.stats.totalThrows += throwStatements.length;

      console.log(`📄 ${relativePath}: ${throwStatements.length} 个throw语句`);

      // 记录文件详情
      const fileDetail = {
        path: relativePath,
        totalThrows: throwStatements.length,
        convertedThrows: 0,
        throwDetails: [],
      };

      // 第一轮：处理标准的new Exception()语句
      for (const throwStmt of throwStatements) {
        const converted = await this.processThrowStatement(throwStmt, sourceFile, fileDetail);
        if (converted) {
          fileDetail.convertedThrows++;
        }
      }

      // 更新方法签名（如果有转换）
      if (fileDetail.convertedThrows > 0) {
        this.updateMethodSignatures(sourceFile, fileDetail);
      }

      // 第二轮：处理throw error;语句（现在方法签名已经是Result类型了）
      // 重新获取throw语句，因为第一轮可能已经修改了AST
      const allThrows = sourceFile.getDescendantsOfKind(SyntaxKind.ThrowStatement);
      const errorThrows = allThrows.filter(throwStmt => {
        const expression = throwStmt.getExpression();
        return expression.getKind() === SyntaxKind.Identifier && expression.getText() === 'error';
      });

      if (this.verbose && errorThrows.length > 0) {
        console.log(`    🔄 第二轮处理: 发现 ${errorThrows.length} 个throw error语句`);
      }

      for (const throwStmt of errorThrows) {
        const converted = this.processCatchBlockThrow(throwStmt, sourceFile, fileDetail);
        if (converted) {
          fileDetail.convertedThrows++;
        }
      }

      // 第三轮：处理成功返回语句和旧格式返回语句
      if (fileDetail.convertedThrows > 0) {
        this.processReturnStatements(sourceFile, fileDetail);
      }

      // 添加导入语句
      if (fileDetail.convertedThrows > 0) {
        this.addRequiredImports(sourceFile);
      }

      this.stats.fileDetails.push(fileDetail);

      // 显示文件处理结果
      if (fileDetail.convertedThrows > 0) {
        console.log(`  ✅ 转换: ${fileDetail.convertedThrows}/${fileDetail.totalThrows} 个throw语句`);
      } else {
        console.log(`  ⚠️  无法自动转换的throw语句`);
      }

    } catch (error) {
      this.stats.errors.push({
        file: filePath,
        error: error.message,
      });
      console.error(`❌ 处理文件失败: ${relativePath}`, error.message);
    }
  }

  /**
   * 处理单个throw语句
   */
  async processThrowStatement(throwStmt, sourceFile, fileDetail) {
    try {
      const expression = throwStmt.getExpression();
      const lineNumber = throwStmt.getStartLineNumber();
      const throwText = throwStmt.getText();

      // 检查是否是 throw error; 模式（在catch块中重新抛出）
      if (expression.getKind() === SyntaxKind.Identifier && expression.getText() === 'error') {
        return this.processCatchBlockThrow(throwStmt, sourceFile, fileDetail);
      }

      // 检查是否是 new Exception() 模式
      if (expression.getKind() !== SyntaxKind.NewExpression) {
        fileDetail.throwDetails.push({
          line: lineNumber,
          original: throwText,
          converted: false,
          reason: '不是new Exception()模式',
        });
        return false;
      }

      const newExpr = expression;
      const identifier = newExpr.getExpression();

      if (identifier.getKind() !== SyntaxKind.Identifier) {
        fileDetail.throwDetails.push({
          line: lineNumber,
          original: throwText,
          converted: false,
          reason: '异常类型不是标识符',
        });
        return false;
      }

      const exceptionType = identifier.getText();

      // 只处理NestJS异常
      if (!this.isNestJSException(exceptionType)) {
        fileDetail.throwDetails.push({
          line: lineNumber,
          original: throwText,
          converted: false,
          reason: `不是NestJS异常: ${exceptionType}`,
        });
        return false;
      }

      // 提取异常参数
      const args = newExpr.getArguments();
      const exceptionData = this.extractExceptionData(args);

      // 生成Result返回语句
      const resultStatement = this.generateResultStatement(exceptionData);

      if (this.verbose) {
        console.log(`    🔄 第${lineNumber}行: throw new ${exceptionType}() -> return ResultUtils.failure()`);
      }

      // 记录转换详情
      fileDetail.throwDetails.push({
        line: lineNumber,
        original: throwText,
        converted: true,
        result: resultStatement,
        exceptionType,
        code: exceptionData.code,
        message: exceptionData.message,
      });

      // 替换throw语句
      if (!this.dryRun) {
        throwStmt.replaceWithText(resultStatement);
      }

      this.stats.convertedThrows++;
      return true;

    } catch (error) {
      console.error(`❌ 处理throw语句失败:`, error.message);
      fileDetail.throwDetails.push({
        line: throwStmt.getStartLineNumber(),
        original: throwStmt.getText(),
        converted: false,
        reason: `处理失败: ${error.message}`,
      });
      return false;
    }
  }

  /**
   * 处理catch块中的throw error语句
   */
  processCatchBlockThrow(throwStmt, sourceFile, fileDetail) {
    try {
      const lineNumber = throwStmt.getStartLineNumber();
      const throwText = throwStmt.getText();

      // 查找包含此throw语句的方法
      const method = this.findContainingMethod(throwStmt);
      if (!method) {
        fileDetail.throwDetails.push({
          line: lineNumber,
          original: throwText,
          converted: false,
          reason: '无法找到包含的方法',
        });
        return false;
      }

      // 检查方法返回类型是否已经是Result类型
      const returnType = method.getReturnTypeNode();
      if (!returnType || !returnType.getText().includes('Result<')) {
        fileDetail.throwDetails.push({
          line: lineNumber,
          original: throwText,
          converted: false,
          reason: '方法返回类型不是Result类型',
        });
        return false;
      }

      // 将throw error转换为return ResultUtils.failure
      // 根据方法名生成更有意义的错误信息
      const methodName = method.getName();
      const errorMessage = this.generateErrorMessageFromMethodName(methodName);

      const newStatement = `return ResultUtils.failure(
        CommonErrorCodes.UNKNOWN_ERROR,
        '${errorMessage}'
      );`;

      if (!this.dryRun) {
        throwStmt.replaceWithText(newStatement);
      }

      fileDetail.throwDetails.push({
        line: lineNumber,
        original: throwText,
        converted: true,
        result: newStatement,
        exceptionType: 'CatchBlockThrow',
        code: 'CommonErrorCodes.UNKNOWN_ERROR',
        message: '操作失败',
      });

      if (this.verbose) {
        console.log(`    🔄 第${lineNumber}行: ${throwText} -> return ResultUtils.failure()`);
      }

      return true;
    } catch (error) {
      console.error(`处理catch块throw失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 查找包含指定节点的方法
   */
  findContainingMethod(node) {
    let current = node.getParent();
    while (current) {
      if (current.getKind() === SyntaxKind.MethodDeclaration) {
        return current;
      }
      current = current.getParent();
    }
    return null;
  }

  /**
   * 根据方法名生成有意义的错误信息
   */
  generateErrorMessageFromMethodName(methodName) {
    const patterns = [
      { pattern: /^create/, message: '创建失败' },
      { pattern: /^update/, message: '更新失败' },
      { pattern: /^delete/, message: '删除失败' },
      { pattern: /^get/, message: '获取失败' },
      { pattern: /^find/, message: '查找失败' },
      { pattern: /^save/, message: '保存失败' },
      { pattern: /^remove/, message: '移除失败' },
      { pattern: /^add/, message: '添加失败' },
      { pattern: /^set/, message: '设置失败' },
      { pattern: /^calculate/, message: '计算失败' },
      { pattern: /^process/, message: '处理失败' },
      { pattern: /^validate/, message: '验证失败' },
      { pattern: /^check/, message: '检查失败' },
      { pattern: /^train/, message: '训练失败' },
      { pattern: /^evolve/, message: '升星失败' },
      { pattern: /^breakthrough/, message: '突破失败' },
      { pattern: /^treat/, message: '治疗失败' },
      { pattern: /^renew/, message: '续约失败' },
    ];

    for (const { pattern, message } of patterns) {
      if (pattern.test(methodName)) {
        return message;
      }
    }

    return '操作失败';
  }

  /**
   * 从指定目录获取TypeScript源文件
   */
  async getSourceFilesFromDirectory(directory) {
    try {
      // 规范化目录路径
      const normalizedDir = directory.replace(/\\/g, '/');

      // 构建glob模式
      const patterns = [
        `${normalizedDir}/**/*.ts`,
        `${normalizedDir}/*.ts`
      ];

      const sourceFiles = [];

      for (const pattern of patterns) {
        // 使用glob查找文件
        const files = glob.sync(pattern, {
          ignore: [
            '**/node_modules/**',
            '**/*.d.ts',
            '**/dist/**',
            '**/build/**'
          ]
        });

        // 转换为SourceFile对象
        for (const filePath of files) {
          const sourceFile = this.project.getSourceFile(filePath);
          if (sourceFile) {
            sourceFiles.push(sourceFile);
          } else {
            // 如果文件不在项目中，尝试添加
            try {
              const addedSourceFile = this.project.addSourceFileAtPath(filePath);
              if (addedSourceFile) {
                sourceFiles.push(addedSourceFile);
              }
            } catch (error) {
              if (this.verbose) {
                console.warn(`⚠️  无法添加文件到项目: ${filePath}`);
              }
            }
          }
        }
      }

      return sourceFiles;
    } catch (error) {
      console.error(`扫描目录失败: ${directory}`, error.message);
      return [];
    }
  }

  /**
   * 去重SourceFile数组
   */
  deduplicateSourceFiles(sourceFiles) {
    const seen = new Set();
    return sourceFiles.filter(sourceFile => {
      const filePath = sourceFile.getFilePath();
      if (seen.has(filePath)) {
        return false;
      }
      seen.add(filePath);
      return true;
    });
  }

  /**
   * 批量处理模式：根据模式筛选目录
   */
  async getBatchFiles(batchType) {
    console.log(`🎯 批量处理模式: ${batchType}`);

    let targetDirectories = [];

    switch (batchType) {
      case 'simple':
        // 简单模块：通常是工具类和小模块
        targetDirectories = [
          'apps/auth/src/domain/permissions',
          'apps/hero/src/modules/career',
          'apps/hero/src/modules/cultivation',
          'apps/economy/src/modules/shop'
        ];
        console.log(`📋 简单模块: ${targetDirectories.length} 个目录`);
        break;

      case 'medium':
        // 中等模块：业务逻辑模块
        targetDirectories = [
          'apps/character/src/modules/character',
          'apps/economy/src/modules/exchange',
          'apps/economy/src/modules/relay',
          'apps/activity/src/modules'
        ];
        console.log(`📋 中等模块: ${targetDirectories.length} 个目录`);
        break;

      case 'complex':
        // 复杂模块：核心业务模块
        targetDirectories = [
          'apps/hero/src/modules/hero',
          'apps/social/src/modules/guild',
          'apps/match/src/modules'
        ];
        console.log(`📋 复杂模块: ${targetDirectories.length} 个目录`);
        break;

      case 'auth':
        // 认证相关模块
        targetDirectories = [
          'apps/auth/src/domain',
          'apps/gateway/src/domain'
        ];
        console.log(`📋 认证模块: ${targetDirectories.length} 个目录`);
        break;

      default:
        console.error(`❌ 未知的批量处理类型: ${batchType}`);
        return [];
    }

    // 扫描目录获取文件
    let sourceFiles = [];
    for (const directory of targetDirectories) {
      const dirSourceFiles = await this.getSourceFilesFromDirectory(directory);
      sourceFiles.push(...dirSourceFiles);
      console.log(`  📂 ${directory}: 发现 ${dirSourceFiles.length} 个文件`);
    }

    return this.deduplicateSourceFiles(sourceFiles);
  }

  /**
   * 获取包含已转换throw语句的方法
   */
  getConvertedMethods(sourceFile, fileDetail) {
    const methods = sourceFile.getDescendantsOfKind(SyntaxKind.MethodDeclaration);
    const convertedMethods = [];

    for (const method of methods) {
      const methodStart = method.getStartLineNumber();
      const methodEnd = method.getEndLineNumber();

      const hasConvertedThrows = fileDetail.throwDetails.some(detail =>
        detail.converted && detail.line >= methodStart && detail.line <= methodEnd
      );

      // 只处理有转换throw语句且应该处理返回语句的方法
      if (hasConvertedThrows) {
        const shouldProcess = this.shouldProcessMethodReturns(method);
        if (shouldProcess) {
          convertedMethods.push(method);
        } else if (this.verbose) {
          console.log(`    ⏭️  跳过方法返回处理: ${method.getName()} (不符合处理条件)`);
        }
      }
    }

    return convertedMethods;
  }

  /**
   * 检查方法是否有Result返回类型
   */
  hasResultReturnType(method) {
    const returnType = method.getReturnTypeNode();
    if (!returnType) return false;

    const returnTypeText = returnType.getText();
    return returnTypeText.includes('Result<');
  }

  /**
   * 检查方法是否应该处理返回语句
   */
  shouldProcessMethodReturns(method) {
    // 1. 检查是否是私有方法
    if (method.hasModifier(SyntaxKind.PrivateKeyword)) {
      return false;
    }

    // 2. 检查是否有Result返回类型或者应该转换为Result类型
    const hasResultType = this.hasResultReturnType(method);
    const shouldConvertType = this.shouldConvertMethodReturnType(method, method.getReturnTypeNode()?.getText() || '');

    if (!hasResultType && !shouldConvertType) {
      return false;
    }

    // 3. 检查方法名模式 - 排除工具方法
    const methodName = method.getName();
    const excludeMethodPatterns = [
      /^to[A-Z]/, // toXxxDto 转换方法
      /^calculate/, // calculate 计算方法
      /^validate/, // validate 验证方法
      /^format/, // format 格式化方法
      /^parse/, // parse 解析方法
      /^is[A-Z]/, // isXxx 判断方法
      /^has[A-Z]/, // hasXxx 判断方法
      /^get.*Config$/, // getXxxConfig 配置获取方法（如果是纯计算）
      /^generate/, // generate 生成方法
      /^map/, // map 映射方法
      /^transform/, // transform 转换方法
    ];

    if (excludeMethodPatterns.some(pattern => pattern.test(methodName))) {
      return false;
    }

    return true;
  }

  /**
   * 检查是否是旧格式返回语句
   */
  isOldFormatReturn(returnText) {
    // 检查是否是包含code和message的对象字面量
    const trimmed = returnText.trim();

    if (!trimmed.startsWith('{') || !trimmed.endsWith('}')) {
      return false;
    }

    // 检查是否包含code字段（数字或变量）
    const hasCode = /code\s*:\s*(-?\d+|[^,}]+)/.test(trimmed);

    // 检查是否包含message字段（字符串或变量）
    const hasMessage = /message\s*:\s*(['"`][^'"`]*['"`]|[^,}]+)/.test(trimmed);

    // 更严格的检查：确保不是简单的数据对象
    const isDataObject = /^\s*\{\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*:\s*[^,}]+(\s*,\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*:\s*[^,}]+)*\s*\}\s*$/.test(trimmed);

    return hasCode && hasMessage && !isDataObject;
  }

  /**
   * 转换旧格式返回语句
   */
  convertOldFormatReturn(returnText, methodName) {
    try {
      // 提取code和message
      const codeMatch = returnText.match(/code\s*:\s*(-?\d+|[^,}]+)/);
      const messageMatch = returnText.match(/message\s*:\s*(['"`])([^'"`]*)\1/);

      if (!codeMatch || !messageMatch) {
        return null;
      }

      const code = codeMatch[1].trim();
      const message = messageMatch[2];

      // 判断是成功还是失败
      const isSuccess = code === '0' || code === 'SUCCESS';

      if (isSuccess) {
        // 提取其他数据字段
        const dataFields = this.extractDataFields(returnText);
        if (dataFields) {
          return `ResultUtils.success(${dataFields}, '${message}')`;
        } else {
          return `ResultUtils.success(undefined, '${message}')`;
        }
      } else {
        // 失败情况
        const errorCode = this.mapErrorCode(code, methodName);
        return `ResultUtils.failure(${errorCode}, '${message}')`;
      }
    } catch (error) {
      console.warn(`转换旧格式返回失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 提取数据字段
   */
  extractDataFields(returnText) {
    try {
      // 移除code和message字段，保留其他字段
      let dataText = returnText
        .replace(/code\s*:\s*[^,}]+,?\s*/g, '')
        .replace(/message\s*:\s*['"`][^'"`]*['"`],?\s*/g, '')
        .replace(/,\s*}/g, '}')
        .replace(/{\s*,/g, '{')
        .trim();

      // 如果只剩下空对象，返回null
      if (dataText === '{}' || dataText === '') {
        return null;
      }

      return dataText;
    } catch (error) {
      return null;
    }
  }

  /**
   * 映射错误码
   */
  mapErrorCode(code, methodName) {
    // 数字错误码映射到通用错误码
    if (code === '-1' || code === '1') {
      // 根据方法名推断错误类型
      if (methodName.includes('insufficient') || methodName.includes('not enough')) {
        return 'CommonErrorCodes.INSUFFICIENT_RESOURCES';
      }
      if (methodName.includes('not found') || methodName.includes('notfound')) {
        return 'CommonErrorCodes.RESOURCE_NOT_FOUND';
      }
      if (methodName.includes('conflict') || methodName.includes('exists')) {
        return 'CommonErrorCodes.RESOURCE_CONFLICT';
      }
      return 'CommonErrorCodes.BUSINESS_RULE_VIOLATION';
    }

    // 如果已经是字符串错误码，直接使用
    if (code.includes('ErrorCode.') || code.includes('CommonErrorCodes.')) {
      return code;
    }

    return 'CommonErrorCodes.UNKNOWN_ERROR';
  }

  /**
   * 检查是否是NestJS异常
   */
  isNestJSException(exceptionType) {
    const nestjsExceptions = [
      'BadRequestException',
      'NotFoundException',
      'ForbiddenException',
      'UnauthorizedException',
      'ConflictException',
      'InternalServerErrorException',
      'NotImplementedException',
      'BadGatewayException',
      'ServiceUnavailableException',
      'GatewayTimeoutException',
    ];
    
    return nestjsExceptions.includes(exceptionType);
  }

  /**
   * 提取异常数据
   */
  extractExceptionData(args) {
    if (args.length === 0) {
      return { code: 'UNKNOWN_ERROR', message: '未知错误' };
    }
    
    const firstArg = args[0];
    
    // 如果是字符串
    if (firstArg.getKind() === SyntaxKind.StringLiteral) {
      return {
        code: 'UNKNOWN_ERROR',
        message: firstArg.getLiteralValue(),
      };
    }
    
    // 如果是对象
    if (firstArg.getKind() === SyntaxKind.ObjectLiteralExpression) {
      const obj = firstArg;
      const properties = obj.getProperties();
      
      let code = 'UNKNOWN_ERROR';
      let message = '未知错误';
      
      for (const prop of properties) {
        if (prop.getKind() === SyntaxKind.PropertyAssignment) {
          const propAssign = prop;
          const name = propAssign.getName();
          const value = propAssign.getInitializer();
          
          if (name === 'code' && value) {
            code = this.extractValue(value);
          } else if (name === 'message' && value) {
            message = this.extractValue(value);
          }
        }
      }
      
      return { code, message };
    }
    
    return { code: 'UNKNOWN_ERROR', message: '未知错误' };
  }

  /**
   * 提取值
   */
  extractValue(node) {
    if (node.getKind() === SyntaxKind.StringLiteral) {
      return node.getLiteralValue();
    }
    
    if (node.getKind() === SyntaxKind.PropertyAccessExpression) {
      return node.getText();
    }
    
    if (node.getKind() === SyntaxKind.ElementAccessExpression) {
      return node.getText();
    }
    
    return node.getText();
  }

  /**
   * 生成Result返回语句
   */
  generateResultStatement(exceptionData) {
    // 处理ErrorCode枚举值，需要转换为字符串
    let codeValue = exceptionData.code;
    if (codeValue.startsWith('ErrorCode.')) {
      codeValue = `${codeValue}.toString()`;
    }

    // 处理ErrorMessages数组访问
    let messageValue = exceptionData.message;
    if (messageValue.startsWith('ErrorMessages[')) {
      // 保持原样，不需要JSON.stringify
      return `return ResultUtils.failure(${codeValue}, ${messageValue});`;
    }

    return `return ResultUtils.failure(${codeValue}, ${JSON.stringify(messageValue)});`;
  }

  /**
   * 更新方法签名，将返回类型包装为Result<T>
   */
  updateMethodSignatures(sourceFile, fileDetail) {
    try {
      // 获取所有方法声明
      const methods = sourceFile.getDescendantsOfKind(SyntaxKind.MethodDeclaration);

      for (const method of methods) {
        // 检查方法是否包含被转换的throw语句
        const methodStart = method.getStartLineNumber();
        const methodEnd = method.getEndLineNumber();

        const hasConvertedThrows = fileDetail.throwDetails.some(detail =>
          detail.converted && detail.line >= methodStart && detail.line <= methodEnd
        );

        if (hasConvertedThrows) {
          this.updateMethodReturnType(method);
        }
      }
    } catch (error) {
      console.warn(`更新方法签名失败: ${error.message}`);
    }
  }

  /**
   * 更新单个方法的返回类型
   */
  updateMethodReturnType(method) {
    try {
      const returnType = method.getReturnTypeNode();
      if (!returnType) {
        if (this.verbose) {
          console.log(`    ⚠️  方法 ${method.getName()} 没有返回类型声明，跳过`);
        }
        return;
      }

      const returnTypeText = returnType.getText();

      // 如果已经是Result类型，跳过
      if (returnTypeText.includes('Result<')) {
        if (this.verbose) {
          console.log(`    ✅ 方法 ${method.getName()} 已经是Result类型，跳过`);
        }
        return;
      }

      // 检查是否应该转换此方法
      if (!this.shouldConvertMethodReturnType(method, returnTypeText)) {
        if (this.verbose) {
          console.log(`    🚫 方法 ${method.getName()} 不适合转换: ${returnTypeText}`);
        }
        return;
      }

      let newReturnType;

      // 如果是Promise<T>，转换为Promise<Result<T>>
      if (returnTypeText.startsWith('Promise<')) {
        const innerType = returnTypeText.slice(8, -1); // 提取T

        // 处理特殊的any类型
        if (innerType === 'any') {
          console.warn(`    ⚠️  方法 ${method.getName()} 返回Promise<any>，建议先定义明确的类型`);
          newReturnType = `Promise<Result<any>>`;
        } else {
          newReturnType = `Promise<Result<${innerType}>>`;
        }
      }
      // 如果是普通类型T，转换为Result<T>
      else {
        // 处理特殊的any类型
        if (returnTypeText === 'any') {
          console.warn(`    ⚠️  方法 ${method.getName()} 返回any，建议先定义明确的类型`);
          newReturnType = `Result<any>`;
        } else {
          newReturnType = `Result<${returnTypeText}>`;
        }
      }

      if (!this.dryRun) {
        returnType.replaceWithText(newReturnType);
      }

      if (this.verbose) {
        console.log(`    📝 更新返回类型: ${returnTypeText} -> ${newReturnType}`);
      }
    } catch (error) {
      console.warn(`更新方法返回类型失败: ${error.message}`);
    }
  }

  /**
   * 检查方法是否应该转换返回类型
   */
  shouldConvertMethodReturnType(method, returnTypeText) {
    // 1. 检查是否是私有方法
    if (method.hasModifier(SyntaxKind.PrivateKeyword)) {
      return false;
    }

    // 2. 检查方法名模式 - 排除工具方法
    const methodName = method.getName();
    const excludeMethodPatterns = [
      /^to[A-Z]/, // toXxxDto 转换方法
      /^calculate/, // calculate 计算方法
      /^validate/, // validate 验证方法
      /^format/, // format 格式化方法
      /^parse/, // parse 解析方法
      /^is[A-Z]/, // isXxx 判断方法
      /^has[A-Z]/, // hasXxx 判断方法
      /^get.*Config$/, // getXxxConfig 配置获取方法（如果是纯计算）
    ];

    if (excludeMethodPatterns.some(pattern => pattern.test(methodName))) {
      return false;
    }

    // 3. 检查返回类型 - 排除基础类型
    const excludeReturnTypes = [
      /^number$/,
      /^string$/,
      /^boolean$/,
      /^void$/,
      /^Observable</,
      /^EventEmitter</,
      /^Subject</,
    ];

    if (excludeReturnTypes.some(pattern => pattern.test(returnTypeText))) {
      return false;
    }

    // 4. 检查是否包含Promise<void> - 这种通常是操作方法，应该转换
    if (returnTypeText === 'Promise<void>') {
      return true;
    }

    return true;
  }

  /**
   * 处理返回语句：包装成功返回和转换旧格式返回
   */
  processReturnStatements(sourceFile, fileDetail) {
    try {
      if (this.verbose) {
        console.log(`    🔄 第三轮处理: 处理返回语句`);
      }

      // 获取所有已转换throw语句的方法
      const convertedMethods = this.getConvertedMethods(sourceFile, fileDetail);

      let successReturnsProcessed = 0;
      let oldFormatReturnsProcessed = 0;

      for (const method of convertedMethods) {
        const methodName = method.getName();

        if (this.verbose) {
          console.log(`    🔍 处理方法: ${methodName}`);
        }

        // 处理方法中的返回语句
        const returnStatements = method.getDescendantsOfKind(SyntaxKind.ReturnStatement);

        // 从后往前处理，避免位置偏移
        for (let i = returnStatements.length - 1; i >= 0; i--) {
          const returnStmt = returnStatements[i];
          const expression = returnStmt.getExpression();

          if (!expression) continue;

          const returnText = expression.getText();
          const lineNumber = returnStmt.getStartLineNumber();

          // 跳过已经是ResultUtils的返回语句
          if (returnText.includes('ResultUtils.')) continue;

          // 检查是否是旧格式返回（包含code和message字段的对象）
          if (this.isOldFormatReturn(returnText)) {
            const newReturn = this.convertOldFormatReturn(returnText, methodName);
            if (newReturn) {
              if (!this.dryRun) {
                expression.replaceWithText(newReturn);
              }

              if (this.verbose) {
                console.log(`    🔄 转换旧格式返回 (第${lineNumber}行): ${methodName}`);
              }

              oldFormatReturnsProcessed++;
              continue;
            }
          }

          // 检查是否是需要包装的成功返回
          if (this.shouldWrapSuccessReturn(returnText)) {
            const wrappedReturn = `ResultUtils.success(${returnText})`;

            if (!this.dryRun) {
              expression.replaceWithText(wrappedReturn);
            }

            if (this.verbose) {
              console.log(`    🎁 包装成功返回 (第${lineNumber}行): ${methodName} -> ${returnText.substring(0, 30)}...`);
            }

            successReturnsProcessed++;
          } else if (this.verbose) {
            console.log(`    ⏭️  跳过返回 (第${lineNumber}行): ${methodName} -> ${returnText.substring(0, 30)}...`);
          }
        }
      }

      if (this.verbose) {
        console.log(`    ✅ 返回语句处理完成: ${successReturnsProcessed}个成功返回, ${oldFormatReturnsProcessed}个旧格式返回`);
      }

    } catch (error) {
      console.warn(`处理返回语句失败: ${error.message}`);
    }
  }

  /**
   * 收集方法中需要更新的返回语句
   */
  collectMethodReturnUpdates(method) {
    const updates = [];

    try {
      // 获取方法中的所有return语句
      const returnStatements = method.getDescendantsOfKind(SyntaxKind.ReturnStatement);

      for (const returnStmt of returnStatements) {
        const expression = returnStmt.getExpression();
        if (!expression) continue;

        const returnText = expression.getText();
        const lineNumber = returnStmt.getStartLineNumber();

        // 跳过已经是ResultUtils的返回语句
        if (returnText.includes('ResultUtils.')) continue;

        // 跳过throw语句（这些已经被处理了）
        if (returnText.includes('throw')) continue;

        // 检查是否是简单的数据返回
        if (this.shouldWrapSuccessReturn(returnText)) {
          const wrappedReturn = `ResultUtils.success(${returnText})`;

          updates.push({
            node: expression,
            line: lineNumber,
            originalText: returnText,
            newText: wrappedReturn,
          });
        }
      }
    } catch (error) {
      console.warn(`收集方法返回语句失败: ${error.message}`);
    }

    return updates;
  }

  /**
   * 更新单个方法的成功返回语句
   */
  updateMethodSuccessReturns(method) {
    try {
      // 获取方法中的所有return语句
      const returnStatements = method.getDescendantsOfKind(SyntaxKind.ReturnStatement);

      for (const returnStmt of returnStatements) {
        const expression = returnStmt.getExpression();
        if (!expression) continue;

        const returnText = expression.getText();

        // 跳过已经是ResultUtils的返回语句
        if (returnText.includes('ResultUtils.')) continue;

        // 跳过throw语句（这些已经被处理了）
        if (returnText.includes('throw')) continue;

        // 检查是否是简单的数据返回
        if (this.shouldWrapSuccessReturn(returnText)) {
          const wrappedReturn = `ResultUtils.success(${returnText})`;

          if (!this.dryRun) {
            expression.replaceWithText(wrappedReturn);
          }

          if (this.verbose) {
            console.log(`    🎁 包装成功返回: return ${returnText} -> return ${wrappedReturn}`);
          }
        }
      }
    } catch (error) {
      console.warn(`更新方法成功返回失败: ${error.message}`);
    }
  }

  /**
   * 检查是否应该包装成功返回语句
   */
  shouldWrapSuccessReturn(returnText) {
    const trimmed = returnText.trim();

    // 排除已经包装的返回
    if (trimmed.includes('ResultUtils.')) return false;

    // 排除简单的字面量
    if (/^(true|false|null|undefined|\d+|'[^']*'|"[^"]*")$/.test(trimmed)) {
      return false;
    }

    // 排除旧格式返回（包含code和message的对象）
    if (this.isOldFormatReturn(trimmed)) {
      return false;
    }

    // 排除空返回
    if (trimmed === '' || trimmed === 'void 0') {
      return false;
    }

    // 排除await表达式中的Promise.reject
    if (trimmed.includes('Promise.reject')) {
      return false;
    }

    // 排除数学计算表达式
    if (/^[\d\s+\-*/().]+$/.test(trimmed)) {
      return false;
    }

    // 排除简单的属性访问（如: hero.id, config.value），但保留复杂变量名
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*\.[a-zA-Z_$][a-zA-Z0-9_$]*(\.[a-zA-Z_$][a-zA-Z0-9_$]*)*$/.test(trimmed)) {
      return false;
    }

    // 排除数组索引访问（如: items[0], config[key]）
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*\[[^\]]+\]$/.test(trimmed)) {
      return false;
    }

    // 包装以下类型的返回：
    // 1. 复杂变量名 (如: savedHero, updatedHero)
    // 2. 方法调用 (如: this.toHeroInfoDto(hero))
    // 3. 对象字面量 (如: { heroId, status })
    // 4. 数组 (如: [hero1, hero2])
    // 5. await表达式 (如: await this.repository.save())

    return true;
  }

  /**
   * 添加必要的导入语句
   */
  addRequiredImports(sourceFile) {
    try {
      // 检查是否已经有Result相关导入
      const imports = sourceFile.getImportDeclarations();
      const hasResultImport = imports.some(imp =>
        imp.getModuleSpecifierValue().includes('@common/types/result.type')
      );

      if (!hasResultImport) {
        const importStatement = "import { ResultUtils, CommonErrorCodes, Result } from '@common/types/result.type';";

        if (!this.dryRun) {
          if (imports.length > 0) {
            // 在最后一个import语句后添加新的import
            const lastImport = imports[imports.length - 1];
            const insertPosition = lastImport.getEnd();
            sourceFile.insertText(insertPosition, '\n' + importStatement);
          } else {
            // 如果没有import语句，添加到文件开头
            sourceFile.insertText(0, importStatement + '\n');
          }
        }

        if (this.verbose) {
          console.log(`    📦 添加导入: ${importStatement}`);
        }
      }
    } catch (error) {
      console.warn(`添加导入语句失败: ${error.message}`);
    }
  }

  /**
   * 生成重构报告
   */
  generateReport() {
    console.log('\n📊 重构报告');
    console.log('='.repeat(50));
    console.log(`总文件数: ${this.stats.totalFiles}`);
    console.log(`处理文件数: ${this.stats.processedFiles}`);
    console.log(`总throw语句: ${this.stats.totalThrows}`);
    console.log(`已转换: ${this.stats.convertedThrows}`);
    console.log(`转换率: ${((this.stats.convertedThrows / this.stats.totalThrows) * 100).toFixed(2)}%`);
    
    if (this.stats.errors.length > 0) {
      console.log(`\n❌ 错误 (${this.stats.errors.length}):`);
      this.stats.errors.forEach(error => {
        console.log(`  ${error.file}: ${error.error}`);
      });
    }
    
    // 保存报告到文件
    const reportPath = `refactor-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(this.stats, null, 2));
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  }
}

// CLI接口
if (require.main === module) {
  const args = process.argv.slice(2);

  // 解析命令行参数
  const options = {
    dryRun: args.includes('--dry-run'),
    verbose: args.includes('--verbose'),
    files: [],
  };

  // 解析文件参数
  const fileIndex = args.indexOf('--files');
  if (fileIndex !== -1 && fileIndex + 1 < args.length) {
    const filesArg = args[fileIndex + 1];
    options.files = filesArg.split(',').map(f => f.trim());
  }

  // 解析目录参数
  const dirIndex = args.indexOf('--dirs');
  if (dirIndex !== -1 && dirIndex + 1 < args.length) {
    const dirsArg = args[dirIndex + 1];
    options.directories = dirsArg.split(',').map(d => d.trim());
  }

  // 解析批量处理参数
  const batchIndex = args.indexOf('--batch');
  if (batchIndex !== -1 && batchIndex + 1 < args.length) {
    const batchType = args[batchIndex + 1];
    options.batch = batchType; // 'simple', 'medium', 'complex', 'high-priority'
  }

  // 解析Controller适配参数
  options.adaptControllers = args.includes('--adapt-controllers');

  // 显示帮助信息
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🛠️  异常重构工具使用说明

用法:
  node tools/refactor-exceptions.js [选项]

选项:
  --dry-run                预览模式，不实际修改文件
  --verbose                详细输出模式
  --files <file1,file2>    指定要处理的文件列表（逗号分隔）
  --dirs <dir1,dir2>       指定要处理的目录列表（逗号分隔）
  --batch <type>           批量处理模式 (simple|medium|complex|high-priority)
  --adapt-controllers      同时适配相关的Controller文件
  --help, -h               显示此帮助信息

示例:
  # 预览所有文件
  node tools/refactor-exceptions.js --dry-run --verbose

  # 处理指定文件
  node tools/refactor-exceptions.js --files "apps/hero/src/modules/hero/hero.service.ts" --dry-run

  # 处理指定目录
  node tools/refactor-exceptions.js --dirs "apps/hero/src/modules,apps/character/src/modules" --dry-run

  # 处理单个目录
  node tools/refactor-exceptions.js --dirs "apps/hero" --verbose

  # 批量处理简单文件
  node tools/refactor-exceptions.js --batch simple --dry-run

  # 批量处理高优先级文件
  node tools/refactor-exceptions.js --batch high-priority --verbose

  # 实际修改指定目录
  node tools/refactor-exceptions.js --dirs "apps/economy"
`);
    process.exit(0);
  }

  const tool = new ExceptionRefactorTool();
  tool.refactor(options).catch(console.error);
}

module.exports = ExceptionRefactorTool;
