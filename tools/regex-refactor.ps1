# PowerShell异常重构脚本
# 使用正则表达式批量替换throw语句

param(
    [string]$Path = "apps",
    [switch]$DryRun = $false,
    [switch]$Verbose = $false,
    [string]$BackupDir = "backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
)

Write-Host "🚀 异常重构脚本启动" -ForegroundColor Green
Write-Host "路径: $Path" -ForegroundColor Cyan
Write-Host "模式: $(if($DryRun) {'预览模式'} else {'实际修改'})" -ForegroundColor Cyan

# 统计信息
$stats = @{
    TotalFiles = 0
    ProcessedFiles = 0
    TotalThrows = 0
    ConvertedThrows = 0
    Errors = @()
}

# 创建备份目录
if (-not $DryRun) {
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
    Write-Host "📁 备份目录: $BackupDir" -ForegroundColor Yellow
}

# 异常类型映射
$exceptionMappings = @{
    'NotFoundException' = @{
        Pattern = 'throw new NotFoundException\(\s*\{\s*code:\s*([^,]+),\s*message:\s*([^}]+)\s*\}\s*\);?'
        Replacement = 'return ResultUtils.failure($1, $2);'
    }
    'BadRequestException' = @{
        Pattern = 'throw new BadRequestException\(\s*\{\s*code:\s*([^,]+),\s*message:\s*([^}]+)\s*\}\s*\);?'
        Replacement = 'return ResultUtils.failure($1, $2);'
    }
    'ForbiddenException' = @{
        Pattern = 'throw new ForbiddenException\(\s*\{\s*code:\s*([^,]+),\s*message:\s*([^}]+)\s*\}\s*\);?'
        Replacement = 'return ResultUtils.failure($1, $2);'
    }
    'UnauthorizedException' = @{
        Pattern = 'throw new UnauthorizedException\(\s*\{\s*code:\s*([^,]+),\s*message:\s*([^}]+)\s*\}\s*\);?'
        Replacement = 'return ResultUtils.failure($1, $2);'
    }
    'ConflictException' = @{
        Pattern = 'throw new ConflictException\(\s*\{\s*code:\s*([^,]+),\s*message:\s*([^}]+)\s*\}\s*\);?'
        Replacement = 'return ResultUtils.failure($1, $2);'
    }
}

# 简单字符串异常模式
$simplePatterns = @{
    'NotFoundException' = @{
        Pattern = 'throw new NotFoundException\(\s*([''"][^''"]*[''"])\s*\);?'
        Replacement = 'return ResultUtils.failure(CommonErrorCodes.RESOURCE_NOT_FOUND, $1);'
    }
    'BadRequestException' = @{
        Pattern = 'throw new BadRequestException\(\s*([''"][^''"]*[''"])\s*\);?'
        Replacement = 'return ResultUtils.failure(CommonErrorCodes.INVALID_PARAMETER, $1);'
    }
}

# 获取所有TypeScript文件
$files = Get-ChildItem -Path $Path -Recurse -Filter "*.ts" | Where-Object { 
    $_.FullName -notmatch "node_modules" -and 
    $_.FullName -notmatch "\.d\.ts$" 
}

$stats.TotalFiles = $files.Count
Write-Host "📁 发现 $($files.Count) 个TypeScript文件" -ForegroundColor Cyan

foreach ($file in $files) {
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        $fileThrows = 0
        $fileConverted = 0
        
        # 统计当前文件的throw语句
        $throwMatches = [regex]::Matches($content, 'throw new \w+Exception')
        $fileThrows = $throwMatches.Count
        
        if ($fileThrows -eq 0) {
            continue
        }
        
        $stats.ProcessedFiles++
        $stats.TotalThrows += $fileThrows
        
        Write-Host "📄 处理: $($file.Name) ($fileThrows 个throw)" -ForegroundColor White
        
        # 备份原文件
        if (-not $DryRun) {
            $backupPath = Join-Path $BackupDir $file.Name
            Copy-Item -Path $file.FullName -Destination $backupPath -Force
        }
        
        # 应用复杂模式替换
        foreach ($exceptionType in $exceptionMappings.Keys) {
            $mapping = $exceptionMappings[$exceptionType]
            $matches = [regex]::Matches($content, $mapping.Pattern, [System.Text.RegularExpressions.RegexOptions]::Multiline)
            
            if ($matches.Count -gt 0) {
                $content = [regex]::Replace($content, $mapping.Pattern, $mapping.Replacement, [System.Text.RegularExpressions.RegexOptions]::Multiline)
                $fileConverted += $matches.Count
                
                if ($Verbose) {
                    Write-Host "  🔄 转换 $($matches.Count) 个 $exceptionType (复杂模式)" -ForegroundColor Green
                }
            }
        }
        
        # 应用简单模式替换
        foreach ($exceptionType in $simplePatterns.Keys) {
            $mapping = $simplePatterns[$exceptionType]
            $matches = [regex]::Matches($content, $mapping.Pattern, [System.Text.RegularExpressions.RegexOptions]::Multiline)
            
            if ($matches.Count -gt 0) {
                $content = [regex]::Replace($content, $mapping.Pattern, $mapping.Replacement, [System.Text.RegularExpressions.RegexOptions]::Multiline)
                $fileConverted += $matches.Count
                
                if ($Verbose) {
                    Write-Host "  🔄 转换 $($matches.Count) 个 $exceptionType (简单模式)" -ForegroundColor Green
                }
            }
        }
        
        # 添加必要的导入
        if ($fileConverted -gt 0 -and $content -notmatch "import.*ResultUtils") {
            $importLine = "import { ResultUtils, CommonErrorCodes } from '@common/types/result.type';"
            
            # 查找最后一个import语句的位置
            $importMatches = [regex]::Matches($content, "import.*from.*[;']", [System.Text.RegularExpressions.RegexOptions]::Multiline)
            if ($importMatches.Count -gt 0) {
                $lastImport = $importMatches[$importMatches.Count - 1]
                $insertPosition = $lastImport.Index + $lastImport.Length
                $content = $content.Insert($insertPosition, "`n$importLine")
            } else {
                # 如果没有import语句，添加到文件开头
                $content = "$importLine`n$content"
            }
        }
        
        $stats.ConvertedThrows += $fileConverted
        
        # 保存修改后的文件
        if (-not $DryRun -and $content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            Write-Host "  ✅ 已保存 ($fileConverted 个转换)" -ForegroundColor Green
        } elseif ($DryRun -and $content -ne $originalContent) {
            Write-Host "  👀 预览: 将转换 $fileConverted 个throw语句" -ForegroundColor Yellow
        }
        
    } catch {
        $stats.Errors += @{
            File = $file.FullName
            Error = $_.Exception.Message
        }
        Write-Host "❌ 处理失败: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 生成报告
Write-Host "`n📊 重构报告" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green
Write-Host "总文件数: $($stats.TotalFiles)" -ForegroundColor White
Write-Host "处理文件数: $($stats.ProcessedFiles)" -ForegroundColor White
Write-Host "总throw语句: $($stats.TotalThrows)" -ForegroundColor White
Write-Host "已转换: $($stats.ConvertedThrows)" -ForegroundColor White

if ($stats.TotalThrows -gt 0) {
    $conversionRate = [math]::Round(($stats.ConvertedThrows / $stats.TotalThrows) * 100, 2)
    Write-Host "转换率: $conversionRate%" -ForegroundColor White
}

if ($stats.Errors.Count -gt 0) {
    Write-Host "`n❌ 错误 ($($stats.Errors.Count)):" -ForegroundColor Red
    foreach ($error in $stats.Errors) {
        Write-Host "  $($error.File): $($error.Error)" -ForegroundColor Red
    }
}

# 保存报告
$reportPath = "refactor-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
$stats | ConvertTo-Json -Depth 3 | Set-Content -Path $reportPath -Encoding UTF8
Write-Host "`n📄 详细报告已保存到: $reportPath" -ForegroundColor Cyan

if (-not $DryRun) {
    Write-Host "`n🎯 下一步:" -ForegroundColor Yellow
    Write-Host "1. 检查编译错误: npm run build" -ForegroundColor White
    Write-Host "2. 运行测试: npm test" -ForegroundColor White
    Write-Host "3. 如有问题，从备份恢复: Copy-Item $BackupDir\* $Path -Recurse -Force" -ForegroundColor White
} else {
    Write-Host "`n🎯 要执行实际修改，请运行:" -ForegroundColor Yellow
    Write-Host ".\tools\regex-refactor.ps1 -Path apps" -ForegroundColor White
}
