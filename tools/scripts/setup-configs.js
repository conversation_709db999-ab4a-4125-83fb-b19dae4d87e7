#!/usr/bin/env node

/**
 * 配置设置脚本
 * 一键执行迁移、生成、验证全流程
 */

const path = require('path');
const chalk = require('../../node_modules/chalk');
const ora = require('../../node_modules/ora');

// 设置工作目录为项目根目录
process.chdir(path.resolve(__dirname, '../..'));

const migrateConfigs = require('./migrate-configs');
const generateInterfaces = require('./generate-interfaces');
const generateFacade = require('./generate-facade');
const validateConfigs = require('./validate-configs');

async function main() {
  console.log(chalk.blue.bold('🚀 配置设置工具'));
  console.log(chalk.gray('一键完成配置迁移、接口生成、验证全流程\n'));

  const startTime = Date.now();
  let totalErrors = 0;

  try {
    // 步骤1: 配置迁移
    console.log(chalk.cyan('📦 步骤 1/3: 配置迁移'));
    console.log(chalk.gray('='.repeat(50)));
    
    try {
      await migrateConfigs();
      console.log(chalk.green('✅ 配置迁移完成\n'));
    } catch (error) {
      console.log(chalk.red('❌ 配置迁移失败\n'));
      totalErrors++;
    }

    // 步骤2: 接口生成
    console.log(chalk.cyan('🔧 步骤 2/3: 接口生成'));
    console.log(chalk.gray('='.repeat(50)));
    
    try {
      await generateInterfaces();
      console.log(chalk.green('✅ 接口生成完成\n'));
    } catch (error) {
      console.log(chalk.red('❌ 接口生成失败\n'));
      totalErrors++;
    }

    // 步骤3: 配置验证
    console.log(chalk.cyan('🔍 步骤 3/3: 配置验证'));
    console.log(chalk.gray('='.repeat(50)));
    
    try {
      await validateConfigs();
      console.log(chalk.green('✅ 配置验证完成\n'));
    } catch (error) {
      console.log(chalk.red('❌ 配置验证失败\n'));
      totalErrors++;
    }

    // 总结
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(chalk.blue.bold('📊 执行总结'));
    console.log(chalk.gray('='.repeat(50)));
    console.log(`执行时间: ${chalk.cyan(duration)} 秒`);
    console.log(`错误数量: ${totalErrors > 0 ? chalk.red(totalErrors) : chalk.green(0)}`);
    
    if (totalErrors === 0) {
      console.log(chalk.green.bold('\n🎉 所有步骤执行成功！'));
      console.log(chalk.gray('配置库已准备就绪，可以开始使用了。'));
      
      // 显示下一步操作建议
      console.log(chalk.blue('\n📋 下一步操作:'));
      console.log(chalk.gray('1. 检查生成的接口文件: libs/game-config/src/interfaces/'));
      console.log(chalk.gray('2. 查看迁移的配置文件: libs/game-config/src/data/'));
      console.log(chalk.gray('3. 开始实现配置库核心服务'));
      
    } else {
      console.log(chalk.red.bold(`\n❌ ${totalErrors} 个步骤执行失败`));
      console.log(chalk.gray('请检查上述错误信息并修复问题后重新运行。'));
      process.exit(1);
    }
    
  } catch (error) {
    console.error(chalk.red('❌ 执行过程中发生未预期的错误:'), error.message);
    console.error(chalk.gray(error.stack));
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ 未处理的Promise拒绝:'), reason);
  process.exit(1);
});

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = main;
