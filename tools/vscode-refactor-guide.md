# VS Code 批量重构指南

## 🎯 使用VS Code内置功能进行批量异常重构

### 方法1：全局搜索替换

#### 步骤1：打开全局搜索替换
- 快捷键：`Ctrl+Shift+H`
- 或菜单：View → Search → Replace in Files

#### 步骤2：启用正则表达式模式
- 点击搜索框右侧的 `.*` 按钮启用正则表达式

#### 步骤3：批量替换模式

**模式1：复杂对象异常**
```regex
搜索: throw new (NotFoundException|BadRequestException|ForbiddenException|UnauthorizedException|ConflictException)\(\s*\{\s*code:\s*([^,]+),\s*message:\s*([^}]+)\s*\}\s*\);?

替换: return ResultUtils.failure($2, $3);
```

**模式2：简单字符串异常**
```regex
搜索: throw new (NotFoundException|BadRequestException)\(\s*(['"][^'"]*['"])\s*\);?

替换: return ResultUtils.failure(CommonErrorCodes.RESOURCE_NOT_FOUND, $2);
```

**模式3：无参数异常**
```regex
搜索: throw new (NotFoundException|BadRequestException)\(\s*\);?

替换: return ResultUtils.failure(CommonErrorCodes.UNKNOWN_ERROR, '操作失败');
```

#### 步骤4：文件过滤
在 "files to include" 中输入：
```
apps/**/*.ts
```

在 "files to exclude" 中输入：
```
**/*.d.ts, **/node_modules/**, **/dist/**
```

### 方法2：使用VS Code扩展

#### 推荐扩展：
1. **Regex Previewer** - 预览正则表达式匹配结果
2. **Search and Replace** - 增强的搜索替换功能
3. **TypeScript Refactor** - TypeScript专用重构工具

#### 安装命令：
```bash
code --install-extension chrmarti.regex
code --install-extension ms-vscode.vscode-typescript-next
```

### 方法3：分步骤重构

#### 第1步：添加Result导入
搜索：
```regex
^(import.*from.*['"];?)$
```

在最后一个import后添加：
```typescript
import { ResultUtils, CommonErrorCodes } from '@common/types/result.type';
```

#### 第2步：转换NotFoundException
搜索：
```regex
throw new NotFoundException\(\s*\{\s*code:\s*(ErrorCode\.[A-Z_]+),\s*message:\s*(ErrorMessages\[ErrorCode\.[A-Z_]+\])\s*\}\s*\);?
```

替换：
```typescript
return ResultUtils.failure($1, $2);
```

#### 第3步：转换BadRequestException
搜索：
```regex
throw new BadRequestException\(\s*\{\s*code:\s*(ErrorCode\.[A-Z_]+),\s*message:\s*(ErrorMessages\[ErrorCode\.[A-Z_]+\])\s*\}\s*\);?
```

替换：
```typescript
return ResultUtils.failure($1, $2);
```

#### 第4步：转换其他异常类型
重复上述模式，替换其他异常类型。

### 方法4：使用代码片段加速

#### 创建代码片段文件
文件路径：`.vscode/typescript.json`

```json
{
  "Result Failure": {
    "prefix": "rf",
    "body": [
      "return ResultUtils.failure($1, $2);"
    ],
    "description": "Return failure result"
  },
  "Result Success": {
    "prefix": "rs",
    "body": [
      "return ResultUtils.success($1);"
    ],
    "description": "Return success result"
  },
  "Result Import": {
    "prefix": "rimport",
    "body": [
      "import { ResultUtils, CommonErrorCodes, Result } from '@common/types/result.type';"
    ],
    "description": "Import Result utilities"
  }
}
```

## 🔧 自动化脚本集成

### 集成到package.json
```json
{
  "scripts": {
    "refactor:exceptions": "node tools/refactor-exceptions.js",
    "refactor:preview": "node tools/refactor-exceptions.js --dry-run --verbose",
    "refactor:regex": "powershell -ExecutionPolicy Bypass -File tools/regex-refactor.ps1",
    "refactor:backup": "powershell -ExecutionPolicy Bypass -File tools/regex-refactor.ps1 -DryRun"
  }
}
```

### 使用方法
```bash
# 预览模式
npm run refactor:preview

# 实际执行
npm run refactor:exceptions

# 使用正则表达式方法
npm run refactor:regex

# 备份模式
npm run refactor:backup
```

## 📋 重构检查清单

### 重构前检查
- [ ] 创建Git分支：`git checkout -b refactor/exceptions-to-result`
- [ ] 备份重要文件
- [ ] 确保所有测试通过
- [ ] 记录当前编译状态

### 重构过程
- [ ] 添加Result类型定义
- [ ] 批量替换throw语句
- [ ] 添加必要的导入语句
- [ ] 更新方法返回类型
- [ ] 修复编译错误

### 重构后验证
- [ ] 编译检查：`npm run build`
- [ ] 类型检查：`npx tsc --noEmit`
- [ ] 运行测试：`npm test`
- [ ] 代码格式化：`npm run format`
- [ ] ESLint检查：`npm run lint`

## 🚨 注意事项

### 需要手动处理的情况
1. **复杂的异常逻辑**：包含多层嵌套的try-catch
2. **异步异常处理**：Promise.reject()等
3. **第三方库异常**：非NestJS异常
4. **业务逻辑异常**：需要保留异常语义的场景

### 常见问题解决
1. **导入路径错误**：检查Result类型的导入路径
2. **返回类型不匹配**：更新方法签名的返回类型
3. **调用方未适配**：需要同步更新调用方代码
4. **测试失败**：更新测试用例的期望结果

## 📊 效果评估

### 性能指标
- 异常处理性能提升：预期30-50%
- 内存使用减少：预期10-20%
- 响应时间改善：预期5-15%

### 代码质量指标
- 代码可读性：显著提升
- 类型安全性：完全类型安全
- 维护成本：降低30-40%
- Bug率：预期减少20-30%
