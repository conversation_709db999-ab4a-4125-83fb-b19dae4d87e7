{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    // 对examples目录放宽类型检查
    "noImplicitAny": false,
    "strictNullChecks": false,
    "strictFunctionTypes": false,
    "strictBindCallApply": false,
    "strictPropertyInitialization": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "exactOptionalPropertyTypes": false,
    "noImplicitOverride": false,
    "noPropertyAccessFromIndexSignature": false,
    "noUncheckedIndexedAccess": false,
    // 跳过所有库检查
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    // 允许未解析的模块
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    // 忽略错误
    "suppressImplicitAnyIndexErrors": true,
    "suppressExcessPropertyErrors": true,
    "noErrorTruncation": false
  },
  "include": [
    "examples/**/*",
    "**/examples/**/*",
    "libs/common/src/redis/examples/**/*",
    "apps/*/examples/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
